# JWT Token Generation Scripts

This directory contains bash scripts for generating and verifying JWT tokens compatible with the SW Currency Exchange API, using only standard Unix tools.

## Files

- `generate-token.sh` - Generate JWT tokens using openssl and standard Unix tools
- `verify-token.sh` - Decode and verify JWT token contents
- `example-payload.json` - Example payload file for testing
- `TOKEN_SCRIPTS_README.md` - This documentation

## Prerequisites

The scripts require standard Unix tools that are available on most systems:

- `openssl` - For HMAC signature generation
- `base64` - For encoding/decoding
- `date` - For timestamp generation
- `python3` (optional) - For pretty-printing JSON in verification script

## Quick Start

### Generate a Token

```bash
# Simple token with inline JSON
./generate-token.sh '{"service": "currency-exchange", "userId": 123}'

# Token from file
./generate-token.sh -f example-payload.json

# Custom expiration (1 hour)
./generate-token.sh -e 3600 '{"service": "test"}'

# Different algorithm
./generate-token.sh -a HS512 '{"service": "secure"}'
```

### Verify a Token

```bash
# Verify and decode a token
./verify-token.sh eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Generate and immediately verify
TOKEN=$(./generate-token.sh '{"test": true}' | grep "Generated Token:" | awk '{print $3}')
./verify-token.sh "$TOKEN"
```

## Configuration

### Environment Variables

The scripts use the same environment variables as the main application:

```bash
export INTERNAL_SERVER_TOKEN_SECRET="your-secret-key"
export INTERNAL_SERVER_TOKEN_ALGORITHM="HS256"  # HS256, HS384, or HS512
export INTERNAL_SERVER_TOKEN_ISSUER="skywindgroup"
export INTERNAL_SERVER_TOKEN_EXPIRES_IN=300
```

### View Current Configuration

```bash
./generate-token.sh --env
```

## Examples

### Service Authentication Token

```bash
./generate-token.sh '{
  "service": "currency-exchange-api",
  "role": "internal-service",
  "permissions": ["read", "write"]
}'
```

### User Token with Custom Expiration

```bash
./generate-token.sh -e 7200 '{
  "userId": 12345,
  "username": "admin",
  "role": "administrator"
}'
```

### High-Security Token

```bash
./generate-token.sh -a HS512 -s "super-secret-key" '{
  "service": "critical-operation",
  "level": "high-security"
}'
```

## Token Verification

The verification script provides detailed information about tokens:

- **Header**: Algorithm and token type
- **Payload**: All claims and custom data
- **Timestamps**: Human-readable issued/expiry times
- **Status**: Whether the token is expired or still valid
- **Signature**: Base64url encoded signature

## Compatibility

The generated tokens are fully compatible with:

- The existing SW Currency Exchange API token verification
- Standard JWT libraries (jsonwebtoken, etc.)
- JWT.io and other online JWT tools

## Security Notes

- Default secret is for development only
- Use environment variables for production secrets
- Keep secret keys secure and rotate them regularly
- Use appropriate expiration times for your use case
- HS256 is sufficient for most use cases; use HS512 for higher security requirements

## Troubleshooting

### "Missing required tools"
Install the missing tools using your system's package manager:
- macOS: `brew install openssl`
- Ubuntu/Debian: `sudo apt-get install openssl`
- CentOS/RHEL: `sudo yum install openssl`

### "Invalid JSON payload"
Ensure your JSON is properly formatted:
- Use double quotes for strings
- Properly escape special characters
- Validate JSON syntax

### "Permission denied"
Make scripts executable:
```bash
chmod +x generate-token.sh verify-token.sh
```

### Token verification fails in application
Ensure you're using the same secret and algorithm as configured in the application.
