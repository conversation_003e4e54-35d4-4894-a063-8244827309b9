#!/bin/bash

# Token Verification Script for SW Currency Exchange API
# This script decodes and displays JWT token contents

set -e

# Function to base64url decode
base64url_decode() {
    local input="$1"
    # Add padding if needed
    local padding=$((4 - ${#input} % 4))
    if [[ $padding -ne 4 ]]; then
        input="${input}$(printf '%*s' $padding | tr ' ' '=')"
    fi
    # Convert base64url to base64 and decode
    echo "$input" | tr '_-' '/+' | base64 -d 2>/dev/null
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [TOKEN]"
    echo ""
    echo "Decode and display JWT token contents"
    echo ""
    echo "Examples:"
    echo "  $0 eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    echo "  echo 'token' | $0"
    echo ""
    echo "The script will display:"
    echo "  - Token header (algorithm, type)"
    echo "  - Token payload (claims and data)"
    echo "  - Token signature (base64url encoded)"
}

# Function to decode and display token
decode_token() {
    local token="$1"
    
    # Split token into parts
    IFS='.' read -ra PARTS <<< "$token"
    
    if [[ ${#PARTS[@]} -ne 3 ]]; then
        echo "Error: Invalid JWT token format. Expected 3 parts separated by dots."
        exit 1
    fi
    
    local header="${PARTS[0]}"
    local payload="${PARTS[1]}"
    local signature="${PARTS[2]}"
    
    echo "JWT Token Analysis"
    echo "=================="
    echo ""
    
    echo "Header:"
    echo "-------"
    local decoded_header=$(base64url_decode "$header")
    if [[ $? -eq 0 ]] && [[ -n "$decoded_header" ]]; then
        echo "$decoded_header" | python3 -m json.tool 2>/dev/null || echo "$decoded_header"
    else
        echo "Failed to decode header"
    fi
    echo ""
    
    echo "Payload:"
    echo "--------"
    local decoded_payload=$(base64url_decode "$payload")
    if [[ $? -eq 0 ]] && [[ -n "$decoded_payload" ]]; then
        echo "$decoded_payload" | python3 -m json.tool 2>/dev/null || echo "$decoded_payload"
        
        # Extract and display timestamps if present
        echo ""
        echo "Timestamp Information:"
        echo "---------------------"
        
        # Extract iat (issued at)
        local iat=$(echo "$decoded_payload" | grep -o '"iat":[0-9]*' | cut -d':' -f2)
        if [[ -n "$iat" ]]; then
            echo "Issued At (iat): $iat ($(date -r "$iat" 2>/dev/null || echo "invalid timestamp"))"
        fi
        
        # Extract exp (expires)
        local exp=$(echo "$decoded_payload" | grep -o '"exp":[0-9]*' | cut -d':' -f2)
        if [[ -n "$exp" ]]; then
            echo "Expires (exp): $exp ($(date -r "$exp" 2>/dev/null || echo "invalid timestamp"))"
            
            # Check if token is expired
            local current_time=$(date +%s)
            if [[ $exp -lt $current_time ]]; then
                echo "Status: EXPIRED"
            else
                local remaining=$((exp - current_time))
                echo "Status: Valid for $remaining more seconds"
            fi
        fi
        
    else
        echo "Failed to decode payload"
    fi
    echo ""
    
    echo "Signature:"
    echo "----------"
    echo "$signature"
    echo ""
    
    echo "Token Length: ${#token} characters"
}

# Main script logic
if [[ $# -eq 0 ]]; then
    # Read from stdin if no arguments
    if [[ -t 0 ]]; then
        show_usage
        exit 1
    else
        token=$(cat)
    fi
elif [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
    show_usage
    exit 0
else
    token="$1"
fi

# Remove any whitespace
token=$(echo "$token" | tr -d '[:space:]')

if [[ -z "$token" ]]; then
    echo "Error: No token provided"
    show_usage
    exit 1
fi

decode_token "$token"
