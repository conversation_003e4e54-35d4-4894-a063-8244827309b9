# Token Generation Script

This directory contains a bash script for generating JWT tokens compatible with the SW Currency Exchange API.

## Files

- `generate-token.sh` - Main token generation script
- `example-payload.json` - Example payload file
- `TOKEN_GENERATION.md` - This documentation file

## Prerequisites

- Node.js must be installed and available in PATH
- The `jsonwebtoken` npm package must be available (installed via `npm install`)

## Usage

### Basic Usage

Generate a token with a simple JSON payload:

```bash
./generate-token.sh '{"service": "currency-exchange", "userId": 123}'
```

### Using a Payload File

Create a JSON file with your payload and use the `-f` option:

```bash
./generate-token.sh -f example-payload.json
```

### Custom Configuration

Override default token settings:

```bash
# Custom expiration time (1 hour)
./generate-token.sh -e 3600 '{"service": "test"}'

# Custom algorithm
./generate-token.sh -a HS512 '{"service": "test"}'

# Custom issuer
./generate-token.sh -i "my-company" '{"service": "test"}'

# Custom secret
./generate-token.sh -s "my-secret-key" '{"service": "test"}'
```

### Environment Variables

The script respects the same environment variables as the main application:

```bash
export INTERNAL_SERVER_TOKEN_SECRET="your-secret-key"
export INTERNAL_SERVER_TOKEN_ALGORITHM="HS256"
export INTERNAL_SERVER_TOKEN_ISSUER="skywindgroup"
export INTERNAL_SERVER_TOKEN_EXPIRES_IN=300

./generate-token.sh '{"service": "test"}'
```

### View Current Configuration

Check what configuration values are being used:

```bash
./generate-token.sh --env
```

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `-h, --help` | Show help message | - |
| `-s, --secret SECRET` | JWT secret key | From env or built-in default |
| `-a, --algorithm ALG` | JWT algorithm | HS256 |
| `-i, --issuer ISSUER` | JWT issuer | skywindgroup |
| `-e, --expires SECONDS` | Token expiration in seconds | 300 |
| `-p, --payload JSON` | JSON payload for the token | - |
| `-f, --file FILE` | Read payload from JSON file | - |
| `--env` | Show current configuration | - |

## Examples

### Service Authentication Token

```bash
./generate-token.sh '{
  "service": "currency-exchange-api",
  "role": "internal-service",
  "permissions": ["read", "write"]
}'
```

### User Token

```bash
./generate-token.sh '{
  "userId": 12345,
  "username": "admin",
  "role": "administrator",
  "permissions": ["admin"]
}'
```

### Temporary Token

```bash
./generate-token.sh -e 60 '{
  "temp": true,
  "action": "password-reset",
  "userId": 123
}'
```

## Token Verification

The generated tokens can be verified using the same configuration in your application code:

```typescript
import { verifyToken } from './src/skywind/token';

const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
const decoded = await verifyToken(token);
console.log(decoded);
```

## Security Notes

- Keep your secret keys secure and never commit them to version control
- Use environment variables for production secrets
- Consider using longer expiration times only when necessary
- The default secret in the script is for development only

## Troubleshooting

### "Node.js is not installed"
Install Node.js from https://nodejs.org/ or use a package manager like brew, apt, or yum.

### "Cannot find module 'jsonwebtoken'"
Run `npm install` in the project directory to install dependencies.

### "Invalid JSON payload"
Ensure your JSON is properly formatted. Use a JSON validator if needed.

### "Permission denied"
Make sure the script is executable: `chmod +x generate-token.sh`
