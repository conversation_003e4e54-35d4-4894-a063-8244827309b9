import * as request from "request";
import { IncomingMessage } from "http";
import { MerchantAdapterImpl, } from "./model";
import {
    Balance,
    BalanceRequestWithoutToken,
    Balances,
    BrandFinalizationType,
    CannotCompletePayment,
    ClientMessageType,
    DeferredPaymentOperation,
    ExtraData,
    ExtraMessageImpl,
    FinalizeGameRequest,
    FreeBetInfo,
    FreeBetInfoRequest,
    GameState,
    GameLogoutRequest,
    GameLogoutResponse,
    getDomain,
    ITrxId,
    JackpotDetails,
    JackpotIdDetails,
    JackpotInfo,
    JackpotPoolDetails,
    LoginTerminalRequest,
    LoginTerminalResponse,
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantGameTokenInfo,
    MerchantGameURLInfo,
    MerchantInfo,
    MerchantPageRequest,
    MerchantPageResponse,
    MerchantParams,
    MerchantBonusApiAdditionalFields,
    MerchantStartGameTokenData,
    MerchantTransferRequest,
    MrchExtraDataImpl,
    OfflineBonusInfo,
    OfflineBonusPaymentRequest,
    PaymentRequest,
    PaymentRequestWithoutToken,
    PlayerInfo,
    PopupButtonGameAction,
    PopupButtonGameActions,
    PopupButtonImpl,
    RefundBetRequest,
    RequireRefundBetError,
    RollbackBetRequest,
    RollbackWithoutTokenRequest,
    RegisterRoundRequest,
    RegisterRoundResponse,
    InterruptSocket
} from "@skywind-group/sw-wallet-adapter-core";
import { AdapterConfig as config } from "./config";
import { calculation, keepalive, logging, measures, publicId, retry } from "@skywind-group/sw-utils";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { IPMErrorToSWError, isDuplicateTrxError } from "./errors/ipmErrors";
import { AdapterErrors } from "./errors/errors";
import { HIDDEN_PASSWORD } from "./constants";
import { updateHash } from "./utils";
import measure = measures.measure;
import GameTokenExpired = AdapterErrors.GameTokenExpired;
import MerchantInternalError = AdapterErrors.MerchantInternalError;
import SWBaseAdapterError = AdapterErrors.SWBaseAdapterError;

const log = logging.logger("sw-management-adapters:ipm-adapter");

interface IPMParams extends MerchantParams {
    password: string;
    serverUrl: string;
    shortCurrencyEnabled?: boolean;
}

export interface IPMGameInitRequest extends MerchantGameInitRequest {
    /**
     * IPM ticket, used for authentication and getting session Id
     */
    ticket?: string;
    customerSessionId?: string;
    ip?: string;
    language?: string;
    merch_login_url?: string;
    lobby?: string;
    cashier?: string;
    ipmSessionId?: string;
    gameGroup?: string;
    currency?: string;
    playerCode?: string;
    oldGameCode?: string;
}

interface IPMSessionData {
    /**
     * IPM session id, used for debit/credit/balance operations
     */
    ipmSessionId: string;
}

export interface OperatorInfo {
    siteUrl?: string;
}

export interface IPMStartGameTokenData extends MerchantStartGameTokenData, IPMSessionData, OperatorInfo {
    oldGameCode?: string;
    disablePlayerPhantomFeatures?: boolean;
}

export interface IPMAuthTokenData extends MerchantGameTokenData, IPMSessionData {
    disablePlayerPhantomFeatures?: boolean;
}

export interface IPMRenewSessionRequest {
    old_game_code: string;
    new_game_code: string;
    merch_id: string;
    merch_pwd: string;
    cust_id: string;
    cust_session_id: string;
}

export interface IPMRenewSessionResponse {
    new_cust_session_id: string;
}

interface IPMGetPlayerResponse {
    error_code: number;
    cust_id: string;
    game_group: string;
    cust_login: string;
    currency_code: string;
    language: string;
    country: string;
    first_name: string;
    last_name: string;
    nickname?: string;
    email: string;
    test_cust: string;
    max_total_bet?: number;
}

/**
 * This is mapping for integration protocol response for validateTicket method.
 */
export interface ValidateTicketResponse extends IPMGetPlayerResponse {
    cust_session_id: string;
    rce?: number; // reality check elapsed time, in minutes
    rci?: number;
    disable_offers?: boolean;
}

interface IPMMessage {
    msgType: string; // Message type. Currently, can be 'message' only.
    message: string; // Message to present to player. Can be both plain string or HTML code. Message must be localized.
    nonIntrusive?: boolean; // If specified and true, message can be shown in a non-intrusive fashion. False by default.
    title?: string; // Optional title string to use for popup when presenting CMA message to player.
                    // If not specified, default title is used. If present, must be localized.
}

interface GetBalanceResponse {
    error_code: number;
    balance: number;
    currency_code: string;
    free_bet_count?: number;
    extraData?: ExtraData;
    messages?: IPMMessage[];
}

interface IPMPaymentResponse {
    error_code: number;
    balance: number;
    trx_id: string;
    free_bet_count?: number;
    extraData?: ExtraData;
    messages?: IPMMessage[];
}

interface FreeBetResponse {
    error_code: number;
    free_bet_count?: number;
    free_bet_coin?: number;
}

interface IPMPageResponse extends MerchantPageResponse {
    error_code?: number;
}

interface OfflineBonusRequest {
    merch_id: string;
    merch_pwd: string;
    cust_id: string;
    amount: number;
    trx_id: string | number;
    currency_code: string;
    timestamp: number;
    promo_id: string;
    promo_type: string;
    promo_pid?: string;
    operation_ts?: number;
    distribution_type?: string;
}

interface OfflineBonusResponse {
    error_code: number;
    error_msg?: string;
    balance: number;
    trx_id: string;
}

interface DebitContributionDetails {
    jackpot_id: string;
    pools: {
        pool_id: string;
        is_global_pool: boolean;
        jp_contribution: number;
    }[];
    jp_contribution: number;
}

interface JackpotWinDetails {
    jackpot_id: string;
    pools: {
        pool_id: string;
        win: number;
    }[];
    jp_win: number;
}

class IPMCurrencyConverter {
    // IDS and VNS currencies which are stored in system already in thousands because of their huge exchange rate
    private static fromIPMCurrencyMap = new Map<string, string>([
        ["IDR", "IDS"],
        ["VND", "VNS"],
    ]);

    private static toIPMCurrencyMap = new Map<string, string>([
        ["IDS", "IDR"],
        ["VNS", "VND"],
    ]);

    public static fromIPMCurrency(currencyCode: string, shortCurrencyEnabled?: boolean): string {
        if (shortCurrencyEnabled) {
            return this.fromIPMCurrencyMap.get(currencyCode) || currencyCode;
        }
        return currencyCode;
    }

    public static toIPMCurrency(currencyCode: string, shortCurrencyEnabled?: boolean) {
        if (shortCurrencyEnabled) {
            return this.toIPMCurrencyMap.get(currencyCode) || currencyCode;
        }
        return currencyCode;
    }

}

const DEFAULT_PRECISION = 6;

// todo since we have integrated with several providers we will convert the roundId to number
export class IPMAdapter extends MerchantAdapterImpl<IPMGameInitRequest, IPMStartGameTokenData, IPMAuthTokenData> {
    private static FREEBET_WIN_EVENT_TYPE = "free-bet-win";
    private static FREEBET_EVENT_TYPE = "free-bet";
    private static ROUND_STATISTICS_EVENT_TYPE = "round-statistics";
    private static FORCE_FINISH_EVENT_TYPE = "force-finish";
    private static WIN_EVENT_TYPE = "win";
    private static BET_EVENT_TYPE = "bet";
    private static BONUS_EVENT_TYPE = "bonus";
    private static HTTP_AGENT = keepalive.createAgent(config.ipm.keepAlive, false);
    private static HTTPS_AGENT = keepalive.createAgent(config.ipm.keepAlive, true);

    constructor(private readonly responseParser: (body: any) => any,
                private responseIsJson: boolean = false,
                private requestIsJson: boolean = false) {
        super();
    }

    /**
     * Validate ticket and create start game token, that is specific for IPM integration
     *
     * @param merchant merchant
     * @param gameCode game code
     * @param providerCode provider code
     * @param providerGameCode provider game code
     * @param initRequest game init request, contains ticket and customer IP
     * @returns {IPMStartGameTokenData}
     */
    @measure({ name: "IPMAdapter.getStartGameTokenData", isAsync: true })
    public async getStartGameTokenData(merchant: MerchantInfo,
                                       gameCode: string,
                                       providerCode: string,
                                       providerGameCode: string,
                                       initRequest: IPMGameInitRequest): Promise<IPMStartGameTokenData> {
        if (!initRequest.ticket) {
            return Promise.reject(new AdapterErrors.MerchantIntegrationError("missing ticket parameter"));
        }

        const response: ValidateTicketResponse = await this.post<ValidateTicketResponse>(
            merchant,
            "/api/validate_ticket",
            {
                ticket: initRequest.ticket,
                merch_id: merchant.code,
                merch_pwd: merchant.params.password,
                ip: initRequest.ip,
            }
        );

        const currencyCode: string = IPMCurrencyConverter.fromIPMCurrency(response.currency_code,
            merchant.params.shortCurrencyEnabled);
        const isTestPlayer = String(response.test_cust).toLowerCase() === "true";
        const playerCode = response.cust_id.toString();

        const result = {
            ipmSessionId: response.cust_session_id,
            brandId: merchant.brandId,
            merchantType: merchant.type,
            merchantCode: merchant.code,
            playerCode: playerCode,
            gameGroup: response.game_group,
            gameCode: gameCode,
            providerGameCode: providerGameCode,
            providerCode: providerCode,
            currency: currencyCode,
            test: isTestPlayer,
            country: response.country,
            language: initRequest.language || response.language,
            playmode: initRequest.playmode,
            siteUrl: getDomain(initRequest.lobby || initRequest.cashier),
            ...(response.disable_offers !== undefined &&
                { disablePlayerPhantomFeatures: response.disable_offers })
        } as IPMStartGameTokenData;

        if (response.rci && !Number.isNaN(+response.rci)) {
            result.rci = +response.rci;
            result.rce = +response.rce || 0;
        }

        if (response.nickname) {
            result.nickname = response.nickname;
        }

        if (response.max_total_bet) {
            result.dynamicMaxTotalBetLimit = response.max_total_bet;
        }

        return result;
    }

    private async updateStartGameToken(merchant: MerchantInfo,
                                       tokenData: IPMStartGameTokenData,
                                       gameCode: string,
                                       providerCode: string,
                                       providerGameCode: string,
                                       initRequest: IPMGameInitRequest): Promise<IPMStartGameTokenData> {
        const sessionId = await this.getCustomerSessionId(merchant, gameCode, {
            ...initRequest,
            gameCode: tokenData.gameCode,
            playerCode: tokenData.playerCode,
            ipmSessionId: tokenData.ipmSessionId
        });
        const result = {
            ipmSessionId: sessionId,
            brandId: tokenData.brandId,
            merchantType: tokenData.merchantType,
            merchantCode: tokenData.merchantCode,
            playerCode: tokenData.playerCode,
            gameGroup: tokenData.gameGroup,
            rci: tokenData.rci,
            rce: tokenData.rce,
            gameCode,
            providerGameCode,
            providerCode,
            currency: tokenData.currency,
            test: tokenData.test,
            country: tokenData.country,
            language: tokenData.language,
            envId: tokenData.envId,
            playmode: initRequest.playmode || tokenData.playmode,
            siteUrl: tokenData.siteUrl,
            oldGameCode: gameCode,
            ...(tokenData.disablePlayerPhantomFeatures !== undefined &&
                { disablePlayerPhantomFeatures: tokenData.disablePlayerPhantomFeatures })
        } as IPMStartGameTokenData;

        if (tokenData.dynamicMaxTotalBetLimit) {
            result.dynamicMaxTotalBetLimit = tokenData.dynamicMaxTotalBetLimit;
        }

        return result;
    }

    @measure({ name: "IPMAdapter.getGameTokenData", isAsync: true })
    public async getGameTokenInfo(merchant: MerchantInfo,
                                  tokenData: IPMStartGameTokenData,
                                  currency: string,
                                  transferEnabled: boolean): Promise<MerchantGameTokenInfo<IPMAuthTokenData>> {

        const authTokenData = {
            ipmSessionId: tokenData.ipmSessionId,
            playerCode: tokenData.playerCode,
            gameCode: tokenData.gameCode,
            brandId: tokenData.brandId,
            currency: currency,
            merchantType: merchant.type,
            merchantCode: merchant.code,
            test: tokenData.test,
            transferEnabled,
            isPromoInternal: merchant.params.isPromoInternal || false,
            playmode: tokenData.playmode,
            walletPerGame: transferEnabled && merchant.params.walletPerGame,
            oldGameCode: tokenData.oldGameCode,
            ...(tokenData.disablePlayerPhantomFeatures !== undefined &&
                { disablePlayerPhantomFeatures: tokenData.disablePlayerPhantomFeatures })
        } as any;

        if (tokenData.envId) {
            authTokenData.envId = tokenData.envId;
        }

        if (tokenData.nickname) {
            authTokenData.nickname = tokenData.nickname;
        }

        return {
            gameTokenData: authTokenData,
            operatorSiteExternalCode: tokenData.siteUrl
        };
    }

    @measure({ name: "IPMAdapter.createGameUrl", isAsync: true })
    public async createGameUrl(merchant: MerchantInfo,
                               gameCode: string,
                               providerCode: string,
                               providerGameCode: string,
                               initRequest: IPMGameInitRequest): Promise<MerchantGameURLInfo> {
        // validate params
        await validateInitRequest(initRequest);

        const gameUrlInfo: MerchantGameURLInfo = {} as MerchantGameURLInfo;

        gameUrlInfo.tokenData = await this.getStartGameToken(merchant, gameCode,
            providerCode, providerGameCode, initRequest);

        // all params are optional - will be appended to game url if present
        const language = !!initRequest.previousStartTokenData ?
                         initRequest.previousStartTokenData.language :
                         initRequest.language;
        gameUrlInfo.urlParams = {
            language,
            playmode: initRequest.playmode,
            merch_login_url: initRequest.merch_login_url || undefined,
            lobby: initRequest.lobby || undefined,
            cashier: initRequest.cashier || undefined,
        };

        return gameUrlInfo;
    }

    private async createStartGameTokenBySessionId(
        merchant: MerchantInfo,
        gameCode: string,
        providerCode: string,
        providerGameCode: string,
        initRequest: IPMGameInitRequest): Promise<IPMStartGameTokenData> {
        const params = merchant.params as IPMParams;

        const url = params.serverUrl;
        const password = params.password;

        if (!url || !password) {
            return Promise.reject(new AdapterErrors.MerchantMisconfiguration());
        }

        const sessionId = await this.getCustomerSessionId(merchant, gameCode, initRequest);
        return {
            ipmSessionId: sessionId,
            brandId: merchant.brandId,
            merchantType: merchant.type,
            merchantCode: merchant.code,
            gameCode: gameCode,
            providerGameCode: providerGameCode,
            providerCode: providerCode,
            playmode: initRequest.playmode,
            siteUrl: getDomain(initRequest.lobby || initRequest.cashier),
            playerCode: initRequest.playerCode,
            oldGameCode: gameCode
        } as IPMStartGameTokenData;
    }

    private async getStartGameToken(merchant: MerchantInfo,
                                    gameCode: string,
                                    providerCode: string,
                                    providerGameCode: string,
                                    initRequest: IPMGameInitRequest) {
        if (initRequest.previousStartTokenData) {
            const ipmTokenData = initRequest.previousStartTokenData as IPMStartGameTokenData;
            return this.updateStartGameToken(merchant,
                ipmTokenData,
                gameCode,
                providerCode,
                providerGameCode,
                initRequest);
        }

        if (initRequest.ticket) {
            return this.getStartGameTokenData(merchant,
                gameCode,
                providerCode,
                providerGameCode,
                initRequest);
        }
        if (initRequest.customerSessionId || initRequest.ipmSessionId) {
            return this.createStartGameTokenBySessionId(merchant,
                gameCode,
                providerCode,
                providerGameCode,
                initRequest);
        }
        if (initRequest.playmode === "fun") {
            return {
                playerCode: `player-${Date.now()}`,
                brandId: merchant.brandId,
                playmode: initRequest.playmode,
                language: initRequest.language,
                gameCode,
                providerGameCode,
                currency: initRequest.currency,
                gameGroup: initRequest.gameGroup
            } as IPMStartGameTokenData;
        }
    }

    @measure({ name: "IPMAdapter.commitPayment", isAsync: true })
    public async commitPayment(merchant: MerchantInfo,
                               authToken: IPMAuthTokenData,
                               paymentRequest: PaymentRequest, bonus?: boolean): Promise<Balance> {

        const bet = paymentRequest.bet || 0;
        const win = paymentRequest.win || 0;

        const debitResult = this.isZeroBetPaymentThatMustNotBeSent(paymentRequest, merchant) ? undefined :
                            await IPMAdapter.wrapDebit(merchant, paymentRequest,
                                this.postDebit(merchant, authToken, paymentRequest, bet, bonus));

        let creditResult: IPMPaymentResponse | GetBalanceResponse;
        try {
            if (this.isZeroWinPaymentThatMustNotBeSent(paymentRequest, merchant)) {
                creditResult = await this.doGetIPMBalance(merchant, authToken);
            } else {
                creditResult = await this.postCredit(merchant, authToken, paymentRequest, win, bonus);
            }
        } catch (err) {
            if (err instanceof AdapterErrors.GameTokenExpired) {
                return Promise.reject(new CannotCompletePayment());
            }
            return Promise.reject(err);
        }

        const balanceAfterWin: Balance = await this.checkAndParseBalance(creditResult, authToken, merchant);
        const balanceAfterBet: Balance = debitResult ?
                                         IPMAdapter.parseBalance(debitResult, authToken) :
                                         balanceAfterWin;

        const result = { ...balanceAfterBet, ...balanceAfterWin };
        // predict previous value of the balance
        const previousValue = paymentRequest.freeBetCoin ?
                              balanceAfterWin.main - win : balanceAfterWin.main - win + bet;
        result.previousValue = Currencies.get(authToken.currency).toFixedByExponent(previousValue);
        return result;
    }

    private isZeroBetPaymentThatMustNotBeSent(paymentRequest: PaymentRequest, merchant: MerchantInfo): boolean {
        return merchant.params.dontSendZeroPayments && !paymentRequest.bet && paymentRequest.eventId !== 0;
    }

    // helper method to apply the same error handling logic to /debit callers
    private static async wrapDebit(merchant: MerchantInfo,
                                   paymentRequest: PaymentRequest,
                                   debitPromise: Promise<IPMPaymentResponse>): Promise<IPMPaymentResponse> {
        try {
            return await debitPromise;
        } catch (err) {
            if (err instanceof AdapterErrors.GameTokenExpired) {
                if (paymentRequest.offlineRetry) {
                    return Promise.reject(new CannotCompletePayment(err.message));
                }
                if (paymentRequest.retry) {
                    return Promise.reject(new AdapterErrors.MerchantInternalError(err.message));
                }
            } else if (err instanceof AdapterErrors.MerchantInternalError && merchant.params.refundBetInsteadOfRetry) {
                return Promise.reject(new RequireRefundBetError(err.message));
            }
            return Promise.reject(err);
        }

    }

    @measure({ name: "IPMAdapter.commitBetPayment", isAsync: true })
    public async commitBetPayment(merchant: MerchantInfo, authToken: IPMAuthTokenData,
                                  paymentRequest: PaymentRequest): Promise<Balance> {

        if (this.isZeroBetPaymentThatMustNotBeSent(paymentRequest, merchant)) {
            return this.getBetBalance(merchant, authToken, paymentRequest);
        }

        const bet = paymentRequest.bet || 0;
        const ipmDebit: IPMPaymentResponse = await IPMAdapter.wrapDebit(merchant, paymentRequest,
            this.postDebit(merchant, authToken, paymentRequest, bet));

        try {
            const result: Balance = await this.checkAndParseBalance(ipmDebit, authToken, merchant);
            const balance: number = result.main;

            // predict previous value of the balance
            const previousValue = paymentRequest.freeBetCoin ? balance : balance + bet;
            result.previousValue = Currencies.get(authToken.currency).toFixedByExponent(previousValue);
            return result;
        } catch (err) {
            if (err instanceof GameTokenExpired && ipmDebit.error_code === 1) {
                // Handle case when bet retried - duplicate transaction error code, but we cannot get balance
                // This payment will be retried on next login
                throw new CannotCompletePayment();
            }
            throw err;
        }
    }

    private async getBetBalance(merchant: MerchantInfo,
                                authToken: IPMAuthTokenData,
                                paymentRequest: PaymentRequest): Promise<Balance> {
        try {
            const balance = await this.checkAndParseBalance({} as any, authToken, merchant);
            balance.previousValue = balance.main;
            return balance;
        } catch (err) {
            // Return zero balance in case of token expired errors during offline retries or finalization payments
            if (err instanceof GameTokenExpired && paymentRequest.offlineRetry) {
                return {
                    main: 0
                };
            }
            throw err;
        }
    }

    @measure({ name: "IPMAdapter.commitWinPayment", isAsync: true })
    public async commitWinPayment(merchant: MerchantInfo, authToken: IPMAuthTokenData,
                                  paymentRequest: PaymentRequest): Promise<Balance> {

        const win = paymentRequest.win || 0;
        let ipmCredit: GetBalanceResponse | IPMPaymentResponse;

        try {
            if (this.isZeroWinPaymentThatMustNotBeSent(paymentRequest, merchant)) {
                ipmCredit = await this.doGetIPMBalance(merchant, authToken);
            } else {
                await this.sendZeroBetForSrtWinIfNeeded(merchant, authToken, paymentRequest);
                ipmCredit = await this.postCredit(merchant, authToken, paymentRequest, win);
            }
        } catch (err) {
            if (err instanceof AdapterErrors.GameTokenExpired) {
                return Promise.reject(new CannotCompletePayment());
            }
            return Promise.reject(err);
        }

        const result: Balance = await this.checkAndParseBalance(ipmCredit, authToken, merchant);
        // predict previous value of the balance
        result.previousValue = Currencies.get(authToken.currency).toFixedByExponent(result.main - win);
        return result;
    }

    /**
     * This method is to cover scenarios for SRT redeem payouts, when 'credit' payout that is not accompanied by
     * preceeding bet is rejected by operator, so we explicitly send the 0-bet to try cover this gap.
     */
    private async sendZeroBetForSrtWinIfNeeded(merchant: MerchantInfo,
                                               authToken: IPMAuthTokenData,
                                               creditRequest: PaymentRequest): Promise<void> {
        // do nothing if its not SRT payment or operator explicitly requested about no Zero bets
        if (!creditRequest.grcRedeemInfo || merchant.params.dontSendZeroPayments) {
            return;
        }

        try {
            await this.postDebit(merchant, authToken, creditRequest, 0);
        } catch (error) {
            // suppress - this bet is synthetic and may have been not needed
            log.warn(error, "Zero bet for SRT payout failed");
        }
    }

    private isZeroWinPaymentThatMustNotBeSent(paymentRequest: PaymentRequest, merchant: MerchantInfo): boolean {
        return merchant.params.dontSendZeroPayments &&
            !paymentRequest.win &&
            this.getGameStatus(paymentRequest) !== "settled";
    }

    private async postDebit(merchant: MerchantInfo,
                            authToken: IPMAuthTokenData,
                            debitRequest: PaymentRequest,
                            bet: number,
                            bonus?: boolean) {

        const eventType = debitRequest.promoType || (IPMAdapter.isFreeBet(debitRequest) ?
                                                     IPMAdapter.FREEBET_EVENT_TYPE :
                                                     IPMAdapter.BET_EVENT_TYPE);

        const debitData: any = {
            amount: bet,
            event_type: bonus ? IPMAdapter.BONUS_EVENT_TYPE : eventType,
            free_bet_coin: debitRequest.freeBetCoin,
            operation_ts: debitRequest.transactionId.timestamp,
            ...(debitRequest.extraData && { extraData: debitRequest.extraData })
        };
        if (bonus) {
            debitData.sub_trx_type = debitRequest.promoType;
        }
        if (bet && merchant.params.reportJPContributionOnDebitForSeamless) {
            if (debitRequest.totalJpContribution) {
                debitData.jp_contribution = debitRequest.totalJpContribution;
            }

            // jackpotDetails field should be present if entity has flag deferredContribution
            if (debitRequest.jackpotDetails) {
                this.decorateDebitDataWithContributionStatistic(debitData, debitRequest.jackpotDetails);
            }
        }

        this.addMerchantAndPlayerData(debitData, merchant, authToken);
        this.addCommonPaymentData(debitData, merchant, authToken, debitRequest);
        this.addDeferredPaymentData(debitData, merchant, debitRequest as DeferredPaymentOperation);

        return this.post<IPMPaymentResponse>(merchant, "/api/debit", debitData);
    }

    // maps jackpotDetails field to something more readable for seamless operators
    // example: [{
    //    jackpot_id: "SW-SUPER-LION_Solid_AFUN",
    //    pools: [{
    //        poolId: "main",
    //        is_global_pool: true,
    //        jp_contribution: 0.004 - pool's contribution
    //    }],
    //    jp_contribution: 0.004 - sum of pools contributions
    // }]
    private decorateDebitDataWithContributionStatistic(paymentData: any, jackpotDetails: JackpotDetails) {
        const jackpotArray: DebitContributionDetails[] = [];
        const jackpots = jackpotDetails && jackpotDetails.jackpots || {};
        const precision = jackpotDetails?.contributionPrecision || DEFAULT_PRECISION;
        for (const jackpotId of Object.keys(jackpots)) {
            const jackpotInfo: DebitContributionDetails = {
                jackpot_id: jackpotId,
                pools: [],
                jp_contribution: 0
            };
            jackpotArray.push(jackpotInfo);

            const pools: JackpotIdDetails = jackpots[jackpotId];
            for (const poolId of Object.keys(pools)) {
                const pool: JackpotPoolDetails = pools[poolId];
                const progressive = pool?.contribution?.progressive || 0;
                const seed = pool?.contribution?.seed || 0;
                const normalizedContribution = calculation.normalizeAmountByPrecision(precision, seed + progressive);

                jackpotInfo.jp_contribution = calculation.normalizeAmountByPrecision(precision,
                    jackpotInfo.jp_contribution + normalizedContribution);
                jackpotInfo.pools.push({
                    pool_id: poolId,
                    is_global_pool: !pool.isLocal,
                    jp_contribution: normalizedContribution
                });
            }
        }

        if (jackpotArray.length) {
            paymentData.jp_contribution_details = jackpotArray;
        }
    }

    private async postCredit(merchant: MerchantInfo,
                             authToken: IPMAuthTokenData,
                             creditRequest: PaymentRequest,
                             win: number,
                             bonus?: boolean) {

        const eventType = creditRequest.promoType ||
            (IPMAdapter.isFreeBet(creditRequest) ? IPMAdapter.FREEBET_WIN_EVENT_TYPE : IPMAdapter.WIN_EVENT_TYPE);

        const creditData: any = {
            amount: win,
            event_type: bonus ? IPMAdapter.BONUS_EVENT_TYPE : eventType,
            game_status: this.getGameStatus(creditRequest),
            operation_ts: creditRequest.transactionId.timestamp,
            ...(creditRequest.extraData && { extraData: creditRequest.extraData })
        };

        if (bonus) {
            creditData.sub_trx_type = creditRequest.promoType;
        }

        if (creditRequest.grcRedeemInfo) {
            creditData.grc_redeem_info = {
                stars: creditRequest.grcRedeemInfo.stars,
                amount: creditRequest.grcRedeemInfo.amount,
            };
        }

        if (creditRequest.isJPWin) {
            creditData.jp_win = true;
            creditData.jp_ids = IPMAdapter.getWinJackpotIds(creditRequest);
            if (merchant.params.reportJPWinStatisticOnCreditForSeamless) {
                this.decorateRequestWithJpWinStatistic(creditData, creditRequest.jackpotDetails);
            }
        } else {
            creditData.jp_win = undefined;
            creditData.jp_ids = undefined;
        }

        this.addMerchantAndPlayerData(creditData, merchant, authToken);
        this.addCommonPaymentData(creditData, merchant, authToken, creditRequest);
        this.addSmResult(creditData, creditRequest);
        this.addDeferredPaymentData(creditData, merchant, creditRequest as DeferredPaymentOperation);

        try {
            return await this.post<IPMPaymentResponse>(
                merchant,
                "/api/credit",
                creditData,
                this.shouldSkipRetries(authToken)
            );
        } catch (err) {
            this.interruptSocketForLiveGame(creditRequest, err, authToken);
            throw err;
        }
    }

    @measure({ name: "IPMAdapter.transfer", isAsync: true })
    public async transfer(merchant: MerchantInfo,
                          authToken: IPMAuthTokenData,
                          transferRequest: MerchantTransferRequest): Promise<Balance> {

        let balance: Balance;

        if (transferRequest.operation === "transfer-in") {
            const transferInData = {
                amount: transferRequest.amount,
                event_type: "transfer-in",
                game_status: transferRequest.gameStatus || "freegame",
                operation_ts: transferRequest.transactionId.timestamp
            };

            this.addMerchantAndPlayerData(transferInData, merchant, authToken);
            this.addCommonPaymentData(transferInData, merchant, authToken, transferRequest);

            const ipmDebit = await IPMAdapter.wrapDebit(merchant, transferRequest,
                this.post<IPMPaymentResponse>(merchant, "/api/debit", transferInData));

            balance = await this.checkAndParseBalance(ipmDebit, authToken, merchant);
            const previousValue = balance.main + transferRequest.amount;
            balance.previousValue = Currencies.get(authToken.currency).toFixedByExponent(previousValue);

        } else if (transferRequest.operation === "transfer-out") {
            // we transferRequest protects us in case when player try to make several consequent transfer-out

            const transferInData = {
                amount: 0,
                event_type: "transfer-in",
                game_status: transferRequest.gameStatus || "freegame",
                operation_ts: transferRequest.transactionId.timestamp
            };

            this.addMerchantAndPlayerData(transferInData, merchant, authToken);
            this.addCommonPaymentData(transferInData, merchant, authToken, transferRequest);

            try {
                await this.post<IPMPaymentResponse>(merchant, "/api/debit", transferInData);
            } catch (err) {
                if (err instanceof AdapterErrors.GameTokenExpired) {
                    return Promise.reject(new CannotCompletePayment());
                }

                return Promise.reject(err);
            }

            const transferOutData = {
                amount: transferRequest.amount,
                left_amount: transferRequest.previousAmount - transferRequest.amount || 0,
                event_type: "transfer-out",
                actual_bet_amount: transferRequest.actualBetAmount !== undefined ? transferRequest.actualBetAmount : 0,
                actual_win_amount: transferRequest.actualWinAmount !== undefined ? transferRequest.actualWinAmount : 0,
                jp_win_amount: transferRequest.jpWinAmount !== undefined ? transferRequest.jpWinAmount : 0,
                jp_total_contribution: transferRequest.totalJpContribution !== undefined
                                       ? transferRequest.totalJpContribution : 0,
                game_status: this.getGameStatus(transferRequest),
                actual_bet_count: transferRequest.betsCount,
                operation_ts: transferRequest.transactionId.timestamp
            };

            this.addMerchantAndPlayerData(transferOutData, merchant, authToken);
            this.addCommonPaymentData(transferOutData, merchant, authToken, transferRequest);
            this.addSmResult(transferOutData, transferRequest);
            transferOutData["game_type"] = "normal";

            // we check reportJPContributionOnDebitForSeamless flag as having the same info in transfer-out request
            // was asked by the same operator that wanted it on /debit
            if (merchant.params.reportJPContributionOnDebitForSeamless && transferRequest.jackpotDetails) {
                this.decorateDebitDataWithContributionStatistic(transferOutData, transferRequest.jackpotDetails);
                this.decorateRequestWithJpWinStatistic(transferOutData, transferRequest.jackpotDetails);
            }

            let ipmCredit: IPMPaymentResponse;

            try {
                ipmCredit = await this.post<IPMPaymentResponse>(
                    merchant,
                    "/api/credit",
                    transferOutData,
                    this.shouldSkipRetries(authToken)
                );
            } catch (err) {
                this.interruptSocketForLiveGame(transferRequest, err, authToken);
                if (err instanceof AdapterErrors.GameTokenExpired) {
                    return Promise.reject(new CannotCompletePayment());
                }

                return Promise.reject(err);
            }

            balance = await this.checkAndParseBalance(ipmCredit, authToken, merchant);
            const previousValue = balance.main - transferRequest.amount;
            balance.previousValue = Currencies.get(authToken.currency).toFixedByExponent(previousValue);
        }

        return balance;
    }

    @measure({ name: "IPMAdapter.getBalances", isAsync: true })
    public async getBalances(merchant: MerchantInfo, authToken: IPMAuthTokenData): Promise<Balances> {
        const params = merchant.params as IPMParams;

        const ipmBalance: GetBalanceResponse = await this.doGetIPMBalance(merchant, authToken);
        const currencyCode = IPMCurrencyConverter.fromIPMCurrency(ipmBalance.currency_code,
            params.shortCurrencyEnabled);
        return {
            [currencyCode]: IPMAdapter.parseBalance(ipmBalance, authToken),
        };
    }

    private async doGetIPMBalance(merchant: MerchantInfo, authToken: IPMAuthTokenData): Promise<GetBalanceResponse> {
        const params = merchant.params as IPMParams;

        return this.post<GetBalanceResponse>(
            merchant,
            "/api/get_balance",
            {
                merch_id: merchant.code,
                merch_pwd: params.password,
                cust_id: authToken.playerCode,
                cust_session_id: authToken.ipmSessionId,
                game_code: authToken.gameCode,
                platform: this.getPlatform(authToken.deviceId)
            });
    }

    @measure({ name: "IPMAdapter.registerRound", isAsync: true })
    public async registerRound(merchant: MerchantInfo, req: RegisterRoundRequest): Promise<RegisterRoundResponse> {
        return this.put<RegisterRoundResponse>(
            merchant,
            "/api/register_round",
            req);
    }

    @measure({ name: "IPMAdapter.getPlayerInfo", isAsync: true })
    public async getPlayerInfo(merchant: MerchantInfo, authToken: IPMAuthTokenData): Promise<PlayerInfo> {
        const params = merchant.params as IPMParams;
        const response: IPMGetPlayerResponse = await this.post<IPMGetPlayerResponse>(
            merchant,
            "/api/get_player",
            {
                merch_id: merchant.code,
                merch_pwd: params.password,
                cust_id: authToken.playerCode,
                cust_session_id: authToken.ipmSessionId
            }
        );
        const currencyCode: string = IPMCurrencyConverter.fromIPMCurrency(response.currency_code,
            params.shortCurrencyEnabled);

        const info = {
            code: response.cust_id,
            status: "normal",
            firstName: response.first_name,
            lastName: response.last_name,
            email: response.email,
            currency: currencyCode,
            language: response.language,
            country: response.country,
            gameGroup: response.game_group,
            isTest: response.test_cust === "true",
            brandId: publicId.instance.encode(merchant.brandId),
            brandTitle: merchant.brandTitle
        } as PlayerInfo;

        if (response.nickname) {
            info.nickname = response.nickname;
        }

        return info;
    }

    @measure({ name: "IPMAdapter.getFreeBetInfo", isAsync: true })
    public async getFreeBetInfo(merchant: MerchantInfo,
                                authToken: IPMAuthTokenData,
                                freeBetRequest: FreeBetInfoRequest): Promise<FreeBetInfo> {
        const params = merchant.params as IPMParams;

        const response: FreeBetResponse = await this.post<FreeBetResponse>(
            merchant,
            "/api/get_free_bet",
            {
                merch_id: merchant.code,
                merch_pwd: params.password,
                cust_id: authToken.playerCode,
                cust_session_id: authToken.ipmSessionId,
                game_code: authToken.gameCode,
                coin_multiplier: freeBetRequest.coinMultiplier,
                stake_all: freeBetRequest.stakeAll.join(",")
            }
        );

        return {
            amount: +response.free_bet_count || 0,
            coin: +response.free_bet_coin || 0
        };
    }

    /**
     * Validate ticket and return LoginData, that is specific for IPM integration
     */
    @measure({ name: "IPMAdapdater.loginTerminalPlayer", isAsync: true })
    public async loginTerminalPlayer(merchant: MerchantInfo, initRequest: LoginTerminalRequest):
        Promise<LoginTerminalResponse<IPMStartGameTokenData>> {
        if (!initRequest.ticket) {
            return Promise.reject(new AdapterErrors.ValidationError("missing ticket parameter"));
        }

        const ticketUrl = merchant.params.sameUrlForTerminalLoginAndTicket ?
                          "/api/validate_ticket" :
                          "/api/validate_terminal_ticket";

        const response: ValidateTicketResponse = await this.post<ValidateTicketResponse>(
            merchant,
            ticketUrl,
            {
                ticket: initRequest.ticket,
                merch_id: merchant.code,
                merch_pwd: merchant.params.password,
            }
        );

        const playerCode = response.cust_id.toString();

        return {
            tokenData: {
                ipmSessionId: response.cust_session_id,
                brandId: merchant.brandId,
                playerCode: playerCode,
                test: String(response.test_cust).toLowerCase() === "true",
                country: response.country,
                gameGroup: response.game_group,
                language: response.language,
                currency: response.currency_code,
                ...(response.disable_offers !== undefined && { disablePlayerPhantomFeatures: response.disable_offers })
            } as any,
            sessionId: response.cust_session_id
        };
    }

    @measure({ name: "IPMAdapdater.getPage", isAsync: true })
    public async getPage(merchant: MerchantInfo,
                         pageRequest: MerchantPageRequest): Promise<IPMPageResponse> {
        if (!pageRequest.pageType) {
            return Promise.reject(new AdapterErrors.ValidationError("missing page type"));
        }
        if (!pageRequest.token && pageRequest.pageType === "playerinfo") {
            return Promise.reject(new AdapterErrors.ValidationError("missing player token"));
        }

        const params = merchant.params as IPMParams;

        const url = params.serverUrl;
        const password = params.password;

        if (!url || !password) {
            return Promise.reject(new AdapterErrors.MerchantMisconfiguration());
        }

        const response = await this.post<MerchantPageResponse>(
            merchant,
            "/api/get_page",
            {
                merch_id: merchant.code,
                merch_pwd: password,
                token: pageRequest.token,
                lobby_id: pageRequest.lobbyId,
                page_type: pageRequest.pageType,
                language: pageRequest.language
            }
        );

        return {
            url: response.url,
            size: response.size
        };
    }

    @measure({ name: "IPMAdapter.rollbackBetPayment", isAsync: true })
    public async rollbackBetPayment(merchant: MerchantInfo, authToken: IPMAuthTokenData,
                                    rollbackBetRequest: RollbackBetRequest & GameState): Promise<Balance> {
        const params = merchant.params as IPMParams;
        const currencyCode: string = IPMCurrencyConverter.toIPMCurrency(authToken.currency,
            params.shortCurrencyEnabled);
        let rollbackResponse: IPMPaymentResponse;

        try {
            rollbackResponse = await this.post<IPMPaymentResponse>(merchant, "/api/rollback", {
                merch_id: merchant.code,
                merch_pwd: params.password,
                cust_id: authToken.playerCode,
                cust_session_id: authToken.ipmSessionId,
                currency_code: currencyCode,
                game_code: authToken.gameCode,
                trx_id: this.getTrxIdForPayload(merchant, rollbackBetRequest.originalTransactionId),
                game_id: +rollbackBetRequest.roundId,
                round_id: rollbackBetRequest.roundPID || rollbackBetRequest.roundId,
                event_type: "rollback",
                game_type: this.getGameType(rollbackBetRequest),
                game_status: this.getGameStatus(rollbackBetRequest),
                timestamp: IPMAdapter.getTimestamp(),
                event_id: 0, // should it be 0 as we send for debit credit or some other code?
                operation_ts: rollbackBetRequest.originalTransactionId.timestamp
            }, this.shouldSkipRetries(authToken));
        } catch (err) {
            this.interruptSocketForLiveGame(rollbackBetRequest, err, authToken);
            if (err instanceof AdapterErrors.GameTokenExpired) {
                return Promise.reject(new CannotCompletePayment());
            }
            if (err instanceof AdapterErrors.TransactionNotFound) {
                log.info(`Nothing to rollback for trx ${rollbackBetRequest.originalTransactionId}`);
                return { main: 0 };
            }
            return Promise.reject(err);
        }

        return this.checkAndParseBalance(rollbackResponse, authToken, merchant);
    }

    @measure({ name: "IPMAdapter.getPlayerBalanceWithoutToken", isAsync: true })
    public async getPlayerBalanceWithoutToken(merchant: MerchantInfo,
                                              req: BalanceRequestWithoutToken): Promise<Balance> {
        const params = merchant.params as IPMParams;

        const ipmBalance: GetBalanceResponse = await this.post<GetBalanceResponse>(
            merchant,
            "/api/get_balance",
            {
                merch_id: merchant.code,
                merch_pwd: params.password,
                cust_id: req.code,
                currency_code: req.currency,
                game_code: req.gameCode
            }
        );

        return IPMAdapter.parseBalance(ipmBalance);
    }

    @measure({ name: "IPMAdapter.rollbackBetPaymentWithoutToken", isAsync: true })
    public async rollbackBetPaymentWithoutToken(merchant: MerchantInfo,
                                                rollbackRequest: RollbackWithoutTokenRequest): Promise<Balance> {
        const params = merchant.params as IPMParams;
        let ipmCredit: IPMPaymentResponse;

        try {
            ipmCredit = await this.post<IPMPaymentResponse>(merchant, "/api/rollback", {
                merch_id: merchant.code,
                merch_pwd: params.password,
                trx_id: this.getTrxIdForPayload(merchant, rollbackRequest.originalTransactionId),
                game_id: +rollbackRequest.roundId,
                round_id: rollbackRequest.roundPID,
                event_type: "rollback",
                timestamp: IPMAdapter.getTimestamp(),
                event_id: 0, // should it be 0 as we send for debit credit or some other code?
                operation_ts: rollbackRequest.originalTransactionId.timestamp
            });
        } catch (err) {
            if (err instanceof AdapterErrors.GameTokenExpired) {
                return Promise.reject(new CannotCompletePayment());
            }
            return Promise.reject(err);
        }

        return IPMAdapter.parseBalance(ipmCredit);
    }

    @measure({ name: "IPMAdapter.commitWinWithoutToken", isAsync: true })
    public async commitWinWithoutToken(merchant: MerchantInfo,
                                       paymentRequest: PaymentRequestWithoutToken): Promise<Balance> {
        const params = merchant.params as IPMParams;
        const currencyCode: string = IPMCurrencyConverter.toIPMCurrency(paymentRequest.currency,
            params.shortCurrencyEnabled);
        let ipmCreditResponse: IPMPaymentResponse;
        try {
            ipmCreditResponse = await this.post<IPMPaymentResponse>(merchant, "/api/credit", {
                merch_id: merchant.code,
                merch_pwd: params.password,
                cust_id: paymentRequest.code,
                amount: paymentRequest.amount,
                currency_code: currencyCode,
                game_code: paymentRequest.gameCode,
                trx_id: this.getTrxIdForPayload(merchant, paymentRequest.transactionId),
                game_id: +paymentRequest.roundId,
                event_type: IPMAdapter.isFreeBet(paymentRequest) ? IPMAdapter.FREEBET_WIN_EVENT_TYPE
                                                                 : IPMAdapter.WIN_EVENT_TYPE,

                jp_win: paymentRequest.isJPWin ? true : undefined,
                timestamp: IPMAdapter.getTimestamp(),
                event_id: 0,
                platform: this.getPlatform(paymentRequest.deviceId),
                game_type: this.getGameType(paymentRequest),
                game_status: this.getGameStatus(paymentRequest),
                round_id: paymentRequest.roundPID || paymentRequest.roundId,
                jp_ids: IPMAdapter.getWinJackpotIds(paymentRequest),
                operation_ts: paymentRequest.transactionId.timestamp,
            });
        } catch (err) {
            if (err instanceof AdapterErrors.GameTokenExpired) {
                return Promise.reject(new CannotCompletePayment());
            }
            return Promise.reject(err);
        }

        return IPMAdapter.checkAndParseBalanceWithoutToken(ipmCreditResponse);
    }

    @measure({ name: "IPMAdapter.commitBonusPayment", isAsync: true })
    public async commitBonusPayment(merchant: MerchantInfo,
                                    authToken: IPMAuthTokenData,
                                    paymentRequest: PaymentRequest): Promise<Balance> {
        return this.commitPayment(merchant, authToken, paymentRequest, true);
    }

    @measure({ name: "IPMAdapter.finalizeGame", isAsync: true })
    public async finalizeGame(merchant: MerchantInfo,
                              gameTokenData: MerchantGameTokenData,
                              finalizeGameRequest: FinalizeGameRequest): Promise<Balance> {
        const authToken = gameTokenData as IPMAuthTokenData;
        if (finalizeGameRequest.finalizationType === BrandFinalizationType.OFFLINE_PAYMENTS) {
            try {
                const ipmBalance: GetBalanceResponse = await this.doGetIPMBalance(merchant, authToken);
                return IPMAdapter.parseBalance(ipmBalance, authToken);
            } catch (e) {
                return {
                    main: 0
                };
            }
        }
        if (finalizeGameRequest.finalizationType === BrandFinalizationType.ROUND_STATISTICS) {
            const finalizeRoundData = this.makeResolveRoundIPMrequest(
                merchant,
                authToken,
                finalizeGameRequest,
                IPMAdapter.ROUND_STATISTICS_EVENT_TYPE);
            const ipmBalance: GetBalanceResponse = await this.commitRoundResolve(merchant, finalizeRoundData);
            return IPMAdapter.parseBalance(ipmBalance, authToken);
        }
        if (finalizeGameRequest.finalizationType === BrandFinalizationType.FORCE_FINISH) {
            const finalizeRoundData = this.makeResolveRoundIPMrequest(
                merchant,
                authToken,
                finalizeGameRequest,
                IPMAdapter.FORCE_FINISH_EVENT_TYPE);
            const ipmBalance: GetBalanceResponse = await this.commitRoundResolve(merchant, finalizeRoundData);
            return IPMAdapter.parseBalance(ipmBalance, authToken);
        }

        log.warn(`Finalization type ${finalizeGameRequest.finalizationType} is not supported`);
    }

    private makeResolveRoundIPMrequest(merchant: MerchantInfo,
                                       authToken: IPMAuthTokenData,
                                       finalizeRequest: FinalizeGameRequest,
                                       finalizationEventType: string): any {
        const data = {
            merch_id: merchant.code,
            merch_pwd: merchant.params.password,

            cust_id: authToken.playerCode,
            game_code: authToken.gameCode,

            game_id: +finalizeRequest.roundId,
            round_id: finalizeRequest.roundPID || finalizeRequest.roundId,
            trx_id: this.getTrxIdForPayload(merchant, finalizeRequest.transactionId),

            total_bet: finalizeRequest.roundStatistics?.totalBet,
            total_win: finalizeRequest.roundStatistics?.totalWin,
            currency_code: IPMCurrencyConverter.toIPMCurrency(authToken.currency, merchant.params.shortCurrencyEnabled),

            game_status: "settled",
            event_type: finalizationEventType,

            timestamp: IPMAdapter.getTimestamp(),
            is_finalization_payment: true
        } as any;

        if (finalizeRequest.roundStatistics?.totalJpContribution) {
            data.total_jp_contribution = finalizeRequest.roundStatistics.totalJpContribution;
        }
        if (finalizeRequest.roundStatistics?.totalJpWin) {
            data.total_jp_win = finalizeRequest.roundStatistics.totalJpWin;
            this.decorateRequestWithJpWinStatistic(
                data,
                { jackpots: finalizeRequest.roundStatistics.jpStatistic, jackpotTypes: undefined });
        }

        if (finalizeRequest.roundStatistics?.jpStatistic) {
            this.decorateDebitDataWithContributionStatistic(
                data,
                { jackpots: finalizeRequest.roundStatistics.jpStatistic, jackpotTypes: undefined });
        }

        if (finalizeRequest.roundStatistics?.smResult) {
            data.sm_result = finalizeRequest.roundStatistics.smResult;
        }

        return data;
    }

    /**
     * Sends round statistics data to IPM. Occurs during finalization process as an attempt to close round.
     * In case of successful round closure - player's balance is expected to be returned.
     * @param merchant
     * @param finalizeRoundData
     * @private
     */
    private async commitRoundResolve(merchant: MerchantInfo, finalizeRoundData): Promise<GetBalanceResponse> {
        return this.post<GetBalanceResponse>(
            merchant,
            "/api/resolve_round",
            finalizeRoundData);
    }

    @measure({ name: "IPMAdapter.commitOfflineBonusPayment", isAsync: true })
    public async commitOfflineBonusPayment(merchant: MerchantInfo,
                                           bonusRequest: OfflineBonusPaymentRequest): Promise<OfflineBonusInfo> {
        const data: OfflineBonusRequest = {
            merch_id: merchant.code,
            merch_pwd: merchant.params.password,
            cust_id: bonusRequest.playerCode,
            amount: bonusRequest.amount,
            currency_code: bonusRequest.currencyCode,
            promo_type: bonusRequest.promoType,
            trx_id: this.getTrxIdForPayload(merchant, bonusRequest.transactionId),
            promo_id: bonusRequest.promoId,
            timestamp: IPMAdapter.getTimestamp(),
        };

        this.additionalOfflineBonusPaymentData(data, merchant, bonusRequest);

        if (bonusRequest.externalPromoId) {
            data.promo_id = bonusRequest.externalPromoId + "";
        } else if (bonusRequest.promoId && Number.isFinite(+bonusRequest.promoId)) {
            data.promo_pid = publicId.instance.encode(+bonusRequest.promoId);
        }

        const response = await this.post<OfflineBonusResponse>(
            merchant,
            "/api/bonus",
            {
                ...data,
                hash: merchant.params.isPasswordBonusAuth ? undefined : updateHash(data, data.merch_pwd)
            });
        return {
            balance: { main: response.balance },
            externalTrxId: response.trx_id
        };
    }

    @measure({ name: "IPMAdapter.refundBetPayment", isAsync: true })
    public async refundBetPayment(merchant: MerchantInfo,
                                  authToken: IPMAuthTokenData, refundRequest: RefundBetRequest): Promise<Balance> {
        return this.rollbackBetPayment(merchant, authToken, {
            originalTransactionId: refundRequest.transactionId,
            amount: refundRequest.bet,
            gameToken: refundRequest.gameToken,
            roundId: +refundRequest.roundId,
            roundPID: refundRequest.roundPID,
            extTransactionId: refundRequest.extTransactionId,
            gameType: refundRequest.gameType,
            gameStatus: refundRequest.gameStatus
        });
    }

    @measure({ name: "IPMAdapter.logoutGame", isAsync: true })
    public async logoutGame(merchant: MerchantInfo,
                            gameTokenData: IPMAuthTokenData,
                            gameRequest: GameLogoutRequest): Promise<GameLogoutResponse> {
        const params = merchant.params;
        if (!params.gameLogoutOptions) {
            return { requireLogin: false };
        } else {
            const logoutPlayerRequest = {
                logout_id: gameRequest.logoutId,
                game_id: +gameRequest.roundId,
                round_id: gameRequest.roundPID || gameRequest.roundId,
                game_code: gameTokenData.gameCode,
                round_state: gameRequest.state,
                timestamp: IPMAdapter.getTimestamp()
            };

            this.addMerchantAndPlayerData(logoutPlayerRequest, merchant, gameTokenData);

            await this.post(
                merchant,
                "/api/logout_player",
                logoutPlayerRequest);

            return { requireLogin: false };
        }
    }

    private async put<T>(merchant: MerchantInfo,
                         url: string,
                         data: any): Promise<T> {
        return this.httpCall(merchant, url, data, "PUT");
    }

    private async post<T>(merchant: MerchantInfo,
                          url: string,
                          data: any,
                          skipRetries?: boolean): Promise<T> {
        return this.httpCall(merchant, url, data, "POST", skipRetries);
    }

    private async httpCall<T>(merchant: MerchantInfo,
                              url: string,
                              data: any,
                              httpVerb: "POST" | "PUT" = "POST",
                              skipRetries?: boolean): Promise<T> {

        const password = merchant.params.password;
        const serverUrl = merchant.params.serverUrl;

        if (!serverUrl || !password) {
            return Promise.reject(new AdapterErrors.MerchantMisconfiguration());
        }

        const proxy = merchant?.proxy?.url || config.ipm.proxy;
        const timeout = merchant.params.apiTimeoutMsec || config.ipm.requestTimeout;

        const action = () => new Promise<T>((resolve, reject) => this.doRequest<T>(
            serverUrl,
            url,
            data,
            resolve,
            reject,
            proxy,
            timeout,
            httpVerb
        ));

        if (skipRetries) {
            return action();
        }

        if (url === "/api/debit") {
            return retry(config.ipm.retryPolicy, action, this.getBetRetryCondition(merchant));
        }
        return retry(config.ipm.retryPolicy, action, this.getRetryCondition);
    }

    private getRetryCondition = (error: SWBaseAdapterError) => error instanceof MerchantInternalError;

    private getBetRetryCondition(merchant: MerchantInfo) {
        return (error: SWBaseAdapterError) =>
            error instanceof MerchantInternalError && !merchant.params.refundBetInsteadOfRetry;
    }

    private shouldSkipRetries(authToken: IPMAuthTokenData): boolean {
        return authToken.isLiveGame || authToken.forbidOnlineRetries;
    }

    /**
     * Interrupt the socket for live game to prevent any other errors and popups
     * caused with retries or something else.
     */
    private interruptSocketForLiveGame(
        req: PaymentRequest | RollbackBetRequest,
        error: SWBaseAdapterError,
        authToken?: IPMAuthTokenData
    ) {
        if (!req["offlineRetry"] && authToken?.isLiveGame) {
            throw new InterruptSocket(error.message);
        }
    }

    private doRequest<T>(baseUrl: string,
                         url: string,
                         data: any, resolve, reject, proxy: string, timeout: number,
                         httpVerb: "POST" | "PUT" = "POST") {
        const reqLogData = {
            baseUrl,
            url,
            proxy,
            data: { ...data },
        };

        // Remove merchant password from log
        if (reqLogData.data.merch_pwd) {
            reqLogData.data.merch_pwd = HIDDEN_PASSWORD;
        }
        log.info({ request: reqLogData }, `send ${httpVerb} to %s`, url);

        const postOptions: request.Options = {
            baseUrl,
            url,
            timeout,
            agent: baseUrl.startsWith("https") ? IPMAdapter.HTTPS_AGENT : IPMAdapter.HTTP_AGENT
        };

        if (proxy) {
            postOptions.proxy = proxy;
        }

        if (this.requestIsJson) {
            postOptions["body"] = data;
            postOptions["json"] = true;
        } else {
            postOptions["form"] = data;
        }

        if (this.responseIsJson) {
            postOptions["headers"] = { "Accept": "application/json" };
        }
        const ts = new Date().getTime();
        let requestHttpActionFunction;
        switch (httpVerb) {
            case "POST":
                requestHttpActionFunction = request.post;
                break;
            case "PUT":
                requestHttpActionFunction = request.put;
                break;
            default:
                requestHttpActionFunction = request.post;
                break;
        }
        requestHttpActionFunction(postOptions, (err: any, incomingMessage: IncomingMessage, body: any) => {
            const logData = {
                request: reqLogData,
                responseCode: incomingMessage ? incomingMessage.statusCode : 0,
                responseBody: body,
                requestTime: this.getRequestTime(ts)
            } as any;

            if (err) {
                log.warn(err, "Error sending request");
                return reject(new AdapterErrors.MerchantInternalError(err.message));
            }

            const statusCode: number = incomingMessage.statusCode;

            if (statusCode === 200) {
                try {
                    const response = this.parseIncomingMessageBody(body);

                    logData.response = response;

                    const errorCode = response.error_code;
                    const errorMsg = response.error_msg;
                    const rci = response.rci; // Reality check interval
                    if (errorCode === 0) {
                        log.info(logData, "Parsed response");
                        return resolve(response as T);
                    } else if (isDuplicateTrxError(errorCode)) {
                        log.warn(logData,
                            "IPM duplicate transaction: %s %s",
                            errorCode,
                            errorMsg);
                        return resolve(response as T);
                    } else {
                        log.warn(logData, "IPMError: %s %s", errorCode, errorMsg);
                        return reject(IPMErrorToSWError(errorCode, errorMsg, rci));
                    }
                } catch (err) {
                    log.info(logData, "Unparsed response");
                    log.warn(err, "IPM Internal Error");
                    return reject(new AdapterErrors.MerchantInternalError(err.message));
                }
            } else if (statusCode >= 400 && statusCode < 500) {
                log.info(logData, "Unparsed response");
                log.warn(logData, "Error response: code=%s", statusCode);
                return reject(new AdapterErrors.MerchantInternalError(
                    `statusCode=${statusCode}, body=${JSON.stringify(body)}`));
            } else {
                // have rollback be retried
                if (statusCode === 500) {
                    try {
                        const response = this.parseIncomingMessageBody(body);
                        if (response.error_code === -4) {
                            return reject(IPMErrorToSWError(response.error_code, response.error_msg, response.rci));
                        }
                    } catch (err) {
                        log.info(logData, "Unparsed response");
                        log.warn(err, "IPM Internal Error");
                        return reject(new AdapterErrors.MerchantInternalError(err.message));
                    }
                }
                return reject(new AdapterErrors.MerchantInternalError(incomingMessage.statusMessage));
            }
        });
    }

    private parseIncomingMessageBody(body: any) {
        return this.responseParser(body);
    }

    private static parseBalance(response: GetBalanceResponse | IPMPaymentResponse,
                                authToken?: IPMAuthTokenData): Balance {
        const balance: Balance = {
            main: +response.balance
        };

        if (response.free_bet_count) {
            if (authToken && authToken.isPromoInternal) {
                log.warn("Merchant free bets are processed in internal wallet and will be ignored. " +
                    "Token: %j, response: %j", authToken, response);
            } else {
                balance.freeBets = {
                    amount: +response.free_bet_count
                };
            }
        }
        IPMAdapter.decorateBalanceWithCMAMessage(response, balance);
        return balance;
    }

    /**
     * Populates extraData field of balance with CMA message data if its present in IPM response
     */
    private static decorateBalanceWithCMAMessage(
        response: GetBalanceResponse | IPMPaymentResponse, balance: Balance): void {
        const extraData = CMAExtraData.getExtraDataFromResponse(response);
        if (extraData) {
            balance.extraData = extraData;
        }
    }

    /**
     * Check balance after payment operation - for some operators balance may be missing, for example, on duplicate trx
     */
    private async checkAndParseBalance(response: GetBalanceResponse | IPMPaymentResponse,
                                       authToken: IPMAuthTokenData,
                                       merchant: MerchantInfo): Promise<Balance> {
        if (response?.balance !== undefined) {
            return IPMAdapter.parseBalance(response, authToken);
        }

        const balances = await this.getBalances(merchant, authToken);
        return balances[Object.keys(balances)[0]];
    }

    private static async checkAndParseBalanceWithoutToken(
        response: GetBalanceResponse | IPMPaymentResponse): Promise<Balance> {

        if (response.balance !== undefined) {
            return IPMAdapter.parseBalance(response);
        }
        return { main: 0 };
    }

    private static getWinJackpotIds(payment: JackpotInfo): string[] {
        const jackpotStatistic = payment?.jackpotWinDetails;
        if (!jackpotStatistic) {
            return undefined;
        }

        const jpIds = Object.keys(jackpotStatistic);
        if (jpIds.length > 0) {
            return jpIds;
        } else {
            return undefined;
        }
    }

    public addMerchantAndPlayerData(requestData: any, merchant: MerchantInfo, authToken: IPMAuthTokenData): void {
        requestData.merch_id = merchant.code;
        requestData.merch_pwd = merchant.params.password;
        requestData.cust_id = authToken.playerCode;
        requestData.cust_session_id = authToken.ipmSessionId;
    }

    public addCommonPaymentData(requestData: any,
                                merchant: MerchantInfo,
                                authToken: IPMAuthTokenData,
                                payment: PaymentRequest): void {
        requestData.currency_code = IPMCurrencyConverter.toIPMCurrency(authToken.currency,
            merchant.params.shortCurrencyEnabled);
        requestData.game_code = authToken.gameCode;
        requestData.trx_id = this.getTrxIdForPayload(merchant, payment.transactionId);
        requestData.game_id = +payment.roundId;
        requestData.round_id = payment.roundPID || payment.roundId;

        requestData.timestamp = IPMAdapter.getTimestamp();
        requestData.event_id = payment.eventId || 0;
        requestData.platform = this.getPlatform(payment.deviceId);
        requestData.game_type = this.getGameType(payment);

        if (payment.promoId) {
            requestData.promo_id = payment.promoId;
            if (Number.isFinite(+payment.promoId)) {
                requestData.promo_pid = publicId.instance.encode(+payment.promoId);
            }
        }
        if (IPMAdapter.isFreeBet(payment) && merchant.params.isPromoInternal) {
            const promoId: number = payment?.freeBetBalance?.activePromoId as number;
            if (promoId !== undefined) {
                requestData.promo_id = promoId.toString();
                requestData.promo_pid = publicId.instance.encode(+promoId);
            }
        }

        if (payment.externalId) {
            requestData.promo_external_id = payment.externalId;
            requestData.promo_id = payment.externalId;
        }
        if (payment.finalizationType) {
            requestData.is_finalization_payment = true;
        }
    }

    private addSmResult(requestData: any,
                        payment: PaymentRequest): void {

        if (payment.roundEnded && payment.smResult) {
            requestData.sm_result = payment.smResult;
        }
    }

    private addDeferredPaymentData(
        data: any,
        merchant: MerchantInfo,
        deferredPaymentRequest: DeferredPaymentOperation
    ): void {
        if ((deferredPaymentRequest.operation as any) === "deferred-payment"
            && merchant.params.supportPromoDistributionType
        ) {
            data.distribution_type = deferredPaymentRequest.deferredPayment.distributionType;
        }
    }

    private additionalOfflineBonusPaymentData(
        data: any,
        merchant: MerchantInfo,
        bonusRequest: OfflineBonusPaymentRequest
    ): void {

        if (merchant.params.supportPromoDistributionType) {
            if (bonusRequest.distributionType) {
                data.distribution_type = bonusRequest.distributionType;
            } else {
                log.warn(
                    bonusRequest,
                    "supportPromoDistributionType is enabled in merchant params, " +
                    "but distributionType does not exist in the request"
                );
            }
        }

        const additionalFields: MerchantBonusApiAdditionalFields
            = merchant?.params?.bonusApiAdditionalFields;

        if (!additionalFields) {
            return;
        }

        if (additionalFields?.supportOperationTs) {
            const ts = bonusRequest?.transactionId?.timestamp;
            if (ts) {
                data.operation_ts = ts;
            } else {
                log.warn(
                    bonusRequest,
                    "supportOperationTs is enabled in merchant params, " +
                    "but timestamp does not exist in the request"
                );
            }
        }
    }

    private getTrxIdForPayload(merchant: MerchantInfo, trxId: ITrxId): string | number {
        if (merchant.params.sendNumericTrxId === true) {
            return trxId.serialId;
        }
        return trxId.publicId;
    }

    private static isFreeBet(payment: PaymentRequest | PaymentRequestWithoutToken) {
        return payment.freeBetCoin || payment.freeBetMode;
    }

    private static getTimestamp() {
        return Math.floor(Date.now() / 1000);
    }

    private async refreshSession(merchant: MerchantInfo,
                                 newGameCode: string,
                                 initRequest: IPMGameInitRequest): Promise<IPMRenewSessionResponse> {
        const data: IPMRenewSessionRequest = {
            old_game_code: initRequest.gameCode,
            new_game_code: newGameCode,
            merch_id: merchant.code,
            merch_pwd: merchant?.params?.password,
            cust_id: initRequest.playerCode,
            cust_session_id: initRequest.customerSessionId || initRequest.ipmSessionId
        };

        return this.post<IPMRenewSessionResponse>(merchant, "api/refresh_session", data);
    }

    private async getCustomerSessionId(merchant: MerchantInfo,
                                       gameCode: string,
                                       initRequest: IPMGameInitRequest): Promise<string> {
        if (merchant.params.refreshSessionForNewGame && initRequest.oldGameCode !== gameCode) {
            const data = await this.refreshSession(merchant, gameCode, initRequest);

            return data.new_cust_session_id;
        }

        return initRequest.customerSessionId || initRequest.ipmSessionId;
    }

    private decorateRequestWithJpWinStatistic(paymentData: any, jackpotDetails: JackpotDetails) {
        const jackpots = jackpotDetails && jackpotDetails.jackpots || {};
        const precision = jackpotDetails && jackpotDetails.contributionPrecision || DEFAULT_PRECISION;
        const result: JackpotWinDetails[] = Object.entries(jackpots)
            .map(([jackpotId, details]) => {
                const pools = Object.entries(details).map(([poolId, poolDetails]) => {
                    return {
                        pool_id: poolId,
                        win: calculation.normalizeAmountByPrecision(precision, poolDetails.win)
                    };
                });
                const jpWin = Object.values(details).reduce((acc: number, currentPool: JackpotPoolDetails) => {
                    return calculation.safeAddWithPrecision(precision, acc, currentPool.win);
                }, 0);
                const eachJpDetails: JackpotWinDetails = {
                    jackpot_id: jackpotId,
                    pools: pools,
                    jp_win: jpWin
                };
                return eachJpDetails;
            });

        if (result.length) {
            paymentData.jp_win_details = result;
        }
    }
}

async function validateInitRequest(initRequest: IPMGameInitRequest) {
    if (initRequest.previousStartTokenData) {
        return;
    }

    if (initRequest.playmode === "fun") {
        if (!initRequest.merch_login_url && !initRequest.ticket) {
            return Promise.reject(
                new AdapterErrors.ValidationError("one of parameters should be present: merch_login_url or ticket"));
        }
    } else {
        if (!initRequest.ticket && !(initRequest.customerSessionId || initRequest.ipmSessionId)) {
            return Promise.reject(new AdapterErrors.ValidationError("missing ticket parameter"));
        }
    }
}

export class CMAExtraData {
    private static getMsgType(message: IPMMessage): number {
        if ("nonIntrusive" in message) {
            if (message.nonIntrusive) {
                return ClientMessageType.toaster;
            } else {
                return ClientMessageType.info;
            }
        }
    }

    public static getExtraDataFromResponse(
        response: GetBalanceResponse | IPMPaymentResponse,
        gameAction: PopupButtonGameAction = PopupButtonGameActions.continue): ExtraData {

        if (!response || !response.messages) {
            return;
        }
        const okButton = PopupButtonImpl.create()
            .setLabel("OK")
            .setGameAction(gameAction)
            .setTranslate(true);

        const closePopupButton = PopupButtonImpl.create()
            .setLabel("Close")
            .setGameAction(gameAction)
            .setTranslate(true);

        const messageArray = response.messages;

        if (messageArray && messageArray.length) {
            const messages = messageArray.filter(msg => msg.msgType.toLowerCase() === "message");
            const ipmCMAExtraData: MrchExtraDataImpl = MrchExtraDataImpl.create();

            for (const message of messages) {
                const msgType = CMAExtraData.getMsgType(message);
                const ipmCMAMessage = ExtraMessageImpl.create()
                    .setButtons(msgType === ClientMessageType.toaster ? [closePopupButton] : [okButton])
                    .setTranslate(false)
                    .setMessage(message.message)
                    .setMessageType(msgType);

                if ("title" in message) {
                    ipmCMAMessage.setMessageTitle(message.title);
                }
                ipmCMAExtraData.addExtraMessage(ipmCMAMessage);
            }

            if (messages.length) {
                return ipmCMAExtraData;
            }
        }
    }
}
