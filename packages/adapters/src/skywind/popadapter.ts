import { MerchantAdapterImpl } from "./model";
import {
    AAMSTokenData,
    Balance,
    Balances,
    CannotCompletePayment,
    ClientMessageType,
    ExtraData,
    ExtraMessageImpl,
    FinalizeGameRequest,
    FreeBetInfoRequest,
    GameLoginRequest,
    GameLogoutGameState,
    GameLogoutRequest,
    GameLogoutResponse,
    getDomain,
    JackpotIdDetails,
    JackpotPoolDetails,
    MerchantCertSettings,
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantGameTokenInfo,
    MerchantGameURLInfo,
    MerchantInfo,
    MerchantParams,
    MerchantStartGameTokenData,
    MerchantTransferRequest,
    MrchExtraDataImpl,
    PaymentRequest,
    PlayerInfo,
    PlayerRegulatoryActionRequest,
    PlayerRegulatoryActionsAtServer,
    PopupButtonGameAction,
    PopupButtonGameActions,
    PopupButtonImpl,
    RefundBetRequest,
    ReportCriticalFilesToMerchantRequest,
    RequireRefundBetError,
    SegmentFilter,
    WALLET_TYPE
} from "@skywind-group/sw-wallet-adapter-core";
import { AdapterConfig as config } from "./config";
import { AdapterCertiticates as configCert } from "./config-cert";
import { IncomingMessage } from "http";
import * as nodeRequest from "request";
import { Currencies, Currency } from "@skywind-group/sw-currency-exchange";
import { isRetriablePOPError, nonRetriablePopErrorToSWError, POPErrors } from "./errors/popErrors";
import { calculation, keepalive, logging, measures } from "@skywind-group/sw-utils";
import { getPOPNotificationHelper, POPNotificationsHelper } from "./popNotificationsHelper";
import { MERCHANT_POP_TYPES, MerchantInfoHelper } from "./lookup";
import { OPERATION_ID } from "@skywind-group/sw-management-wallet";
import { AdapterErrors } from "./errors/errors";
import { getPOPGameRelaunchHelper } from "./popGameRelaunchHelper";
import measure = measures.measure;
import { isFreeBet } from "./utils";

const uuid = require("uuid");
const agentHttps = keepalive.createAgent(config.pop.keepAlive, true);
const agentHttp = keepalive.createAgent(config.pop.keepAlive, false);
const log = logging.logger("sw-management-adapters:pop-adapter");

export const MERCHANT_POP_MOORGATE_TYPE = "pop_moorgate";
export const MERCHANT_POP_MOORGATE_TYPE_EXT_ADAPTER = "pop_asia";

export interface POPMetaObject extends POPClientParams {
    gsId: string;
    gpId: string;
    requestId?: string;
    apiFeatures?: string[];
}

interface POPAccountBalance extends POPPlayerId {
    accountId?: string;
    currencyCode: string;
    balanceArray: [POPBalance];
    messageArray?: POPMessage[];
}

interface POPMessage {
    msgType: string;
    accountMsg: string;
    nonIntrusive?: boolean; // POP: If true, the message can be shown in a non modal, non intrusive fashion
}

interface POPBalance {
    balanceType: string;
    balanceAmt: number;
}

export interface POPMetaParam {
    _meta: POPMetaObject;
}

export interface POPLogoutRequest extends POPMetaParam, POPSkinId, POPPlayerId, POPSessionData {
}

interface POPLogoutResponse {
    logoutData: {
        command: string;
    };
}

// tslint:disable:interface-name
interface POPParams extends POPSkinId, MerchantParams {
    _meta: POPMetaObject;
    shortCurrencyEnabled?: boolean;
}

interface POPPlayerId {
    playerId: string;
}

export interface POPSkinId {
    skinId: string;
}

interface POPBasicParams {
    localeCode: string;
    accountId?: string;
}

interface POPSessionData {
    secureToken: string;
    sessionData?: {
        unfinishedGameCycleId?: string;
        bonusRoundRemaining?: number;
        maxAllowedBetAmt?: number;
    };
}

interface POPURLs {
    urls?: {
        [field: string]: string;
    };
}

interface PopSessionTimerData {
    timeInLoginSession?: number;
    startedAt?: number;
}

interface POPPlayerInfoForSegments {
    popPlayerInfo?: {
        vipLevel?: number;
        additionalInfo?: {
            "KAA/KioskXPath"?: number[];
            isInternalPlayer: boolean | string;

            [field: string]: any; // custom fields, format: custom0X x: [1, 20]
        };
    };
}

export interface POPClientParams {
    clientPlatform?: string;
    clientType?: string;
}

interface POPErrorData {
    errorCode: string;
    errorMsg: string;
}

interface POPRequestData extends POPSkinId, POPBasicParams, POPSessionData {
}

interface POPPlayerInfo extends PlayerInfo {
    playerId: string;
    countryCode: string;
    currencyCode: string;
    vipLevel: number;
    birthDay: string;
    gender: string;
    externalResellerPath?: number[];
    additionalInfo: {
        "KAA/KioskXPath"?: number[];
        isInternalPlayer: boolean | string;

        [field: string]: any; // custom fields, format: custom0X x: [1, 20]
    };
}

interface POPRealityCheckActionResponse {
    regulationTypeData: {
        regulationCommand: string; // "RealityCheckDialogResponseAck"
    };
}

interface POPInsightResponse {
    inSight: {
        type: string, // "response",
        correlationId: string, // "efgojcw3efn32"
        "data": {
            insightVersion: string, // "1.0.0"
            aamsSessionCode?: string, // "M4E302013A22D0RG"
            participationCode?: string, // "N4E34201024737JY"
            participationStartDate?: string // "2020-01-20"
        }
    };
}

interface POPTransactionRequestDetails {
    transSeq: number;
    transId: string;
    transAmt: number;
    transType: string;
    transDateTime: string;
    transCategory: string;
    transDesc: string;
    bonusData?: POPBonusGameRequestDetails;
}

interface POPTransactionRequest extends POPRequestData, POPPlayerId {
    _meta: POPMetaObject;
    gameId: string;
    gameCycleId: string;
    currencyCode: string;
    jackpotArray?: POPJackpotInfo[];
    jackpot?: POPJackpotInfo;
    moneyTransArray?: [POPTransactionRequestDetails];
    gameCycleStarted: boolean;
    gameCycleFinished: boolean;
    gameCycleStartDateTime?: string;
    gameCycleFinishDateTime?: string;
}

interface POPOfflineTransactionRequest extends POPRequestData, POPPlayerId {
    _meta: POPMetaObject;
    gameId?: string;
    gameCycleId?: string;
    currencyCode: string;
    jackpot?: POPJackpotInfo;
    moneyTransArray?: [POPTransactionRequestDetails];
    gameCycleStartDateTime?: string;
    gameCycleFinishDateTime?: string;
}

interface CommonForPOPTransactionRequest extends POPRequestData, POPPlayerId {
    _meta: POPMetaObject;
    gameId: string;
    gameCycleId: string;
    currencyCode: string;
}

interface POPSubscribeToMessages extends POPPlayerId, POPSkinId {
    _meta: POPMetaObject;
    secureToken: string;
    inSight: {
        type: "request",
        scope: "ims",
        operation: "subscribeToMessages",
        correlationId: string,
        data: {
            subscribedMessages: string[];
        }
    };
}

export interface POPJackpotInfo {
    pjsId: string; // Jackpot network identifier
    controllerId: string; // Jackpot pool identifier
    sharedJackpot: boolean; // Whether jackpot is shared between several operators (skinIds) or bound to a single skinId
    jackpotContribAmt: number;
    jackpotAwardAmt?: number;
    balanceBeforeAward?: number;
    balanceAfterAward?: number;
}

interface POPBonusGameRequestDetails {
    bonusReferenceId: string;
    remainingSpins?: number;
}

interface POPMoneyAck extends POPErrorData {
    transType: string;
    transAmt: number;
    transCategory: string;
    transId: string;
    referenceId: string;
    transSeq: number;
    moneyDetailArray: [POPTransactionDetails];
}

interface POPTransactionDetails {
    balanceType: string;
    detailAmt: number;
    detailType: string;
    balanceAmt: number;
}

interface POPTransactionResponse extends POPPlayerBalance {
    gameCycleId: string;
    playerId: string;
    accountId?: string;
    errorCode: string;
    errorMsg: string;
    moneyAckArray: [POPMoneyAck];
    logout?: boolean;
}

export interface POPPlayerBalance {
    accountBalance: POPAccountBalance;

    extraData?: {
        igpGameCycleId?: string; // "imsgamecode"
    };
}

interface OperatorInfo {
    siteUrl?: string;
}

export interface POPGameInitRequest extends MerchantGameInitRequest, POPClientParams, POPSkinId {
    username: string;
    history: string;
    accountId?: string;
    localeCode?: string;
    currency: string;
    backurl?: string;
    cashier?: string;
    getUrlsFromPOP?: boolean;
    crosslaunchurl?: string;
}

export interface POPStartGameTokenData extends MerchantStartGameTokenData,
    POPSessionData,
    POPClientParams,
    POPSkinId,
    POPBasicParams,
    POPURLs,
    PopSessionTimerData,
    POPPlayerInfoForSegments,
    OperatorInfo {
    segmentId?: number;
    crosslaunchurl?: string;
}

export interface POPValidateTicketResponse extends POPSessionData, POPURLs {
    gameId: string;
    accountBalance: POPAccountBalance;
    // TODO: ensure that POP sends this param for Sweden jrsdctn
    timeInLoginSession?: number; // Total login session time in seconds.
}

export interface POPAuthTokenData extends MerchantGameTokenData, POPRequestData, POPClientParams, PopSessionTimerData {
    segmentId?: number;
    gameVersion?: string;
}

interface POPCancelTransactionRequest {
    _meta: POPMetaObject;
    secureToken: string;
    playerId: string;
    gameId: string;
    gameCycleId: string;
    skinId: string;

    cancelTransArray?: [POPCancelTransactionDetails];
    gameCycleFinished: boolean;
    gameCycleFinishDateTime?: string;
}

interface POPCancelTransactionDetails {
    transSeq: number;
    transId: string;
    transDateTime: string;
    transCategory?: string;
    referenceId: string;
    originalTransDate: string;
}

interface POPCancelTransactionResponse extends POPPlayerBalance { // tslint:disable-line
}

export interface POPCreateBrokenGame extends POPSessionData, POPSkinId, POPPlayerId {
    brokenGameId: string;
    gameCycleId: string;
    // Fixed expiration date. When expiration date is reached broken game is closed and can not be resumed.
    expirationDateTime?: string;
    _meta: POPMetaObject;
}

interface POPResumeBrokenGame extends POPSessionData, POPSkinId, POPPlayerId {
    brokenGameId: string;
    gameCycleId: string;
    _meta: POPMetaObject;
}

interface POPRetryPolicy {
    delay: number;
    maxAttempts: number;
    cancelMaxAttempts: number;
}

interface POPTrxIOObject {
    request: POPTransactionRequest;
    response: POPTransactionResponse;
}

export interface POPVerifySessionRequest {
    _meta: POPMetaObject;
    skinId: string;
    playerId: string;
    secureToken: string;
    currencyCode: string;
    localeCode: string;
    gameId: string;
    includeUrls: boolean;
}

interface POPSetItalianCriticalFilesRequest {
    _meta: POPMetaObject;
    skinId: string;
    gameId?: string;
    files: [
        {
            name: string;
            hash: string;
        }
    ];
}

enum POP_WALLET_OPERATION {
    DEBIT,
    CREDIT
}

// this object contain more fields, check here if need something new
// https://pop-playtech.readme.io/docs/get-game-configuration
export interface POPGameConfigurationResponse extends POPPlayerId {
    currency: string;
    data: {
        currencyMultiplier: number;
        baseCurrency: string;
        configData: {
            bonus: {
                freeSpinCoinSize: number;
            };
        };
    };
}

export interface POPGameConfigurationRequest extends POPSessionData, POPPlayerId, POPSkinId {
    currency: string;
    gameDocumentId: string; // SKW_ + gameCode
    gameId: string;
    _meta: POPMetaObject;
}

export class POPAdapter extends MerchantAdapterImpl<POPGameInitRequest, POPStartGameTokenData, POPAuthTokenData> {
    public static BALANCE_NAME_MAIN = "cashable";
    public static BALANCE_NAME_FREEBET = "freespin";

    public static TRANS_TYPE_DEBIT = "debit";
    public static TRANS_TYPE_CREDIT = "credit";
    public static TRANS_TYPE_BONUS_CREDIT = "creditWithoutGGR";

    public static TRANS_CATEGORY_WAGER = "wager";
    public static TRANS_CATEGORY_WIN = "win";
    public static TRANS_CATEGORY_CANCEL = "cancel";
    public static TRANS_CATEGORY_EXTERNAL_FREE_SPIN_BET = "freeSpinBet";
    public static TRANS_CATEGORY_EXTERNAL_FREE_SPIN_WIN = "freeSpinWin";
    public static TRANS_CATEGORY_EXTERNAL_FREE_SPIN_CANCEL = "freeSpinCancel";

    public static TRANS_CATEGORY_INTERNAL_FREE_SPIN_BET = "bonusBet";
    public static TRANS_CATEGORY_INTERNAL_FREE_SPIN_WIN = "bonusWin";
    public static TRANS_CATEGORY_OFFLINE_WIN = "offlineCredit";

    public static TIME_TO_INCREASE_END_ROUND_DATE_MS = 10;

    private gsId: string;
    private gpId: string;
    private retryPolicy: POPRetryPolicy;
    private certSettings: MerchantCertSettings;

    public constructor(params: MerchantParams = {}) {
        super();
        // todo move to new version of typescript to get rid of typecasting
        this.gsId = params.gsId || config.pop.gsId;
        this.gpId = params.gpId || config.pop.gpId;
        this.retryPolicy = { ...config.pop.retryPolicy, ...params.retryPolicy };

        this.certSettings = {
            useCert: configCert.pop.useCert,
            settings: configCert.pop,
            ...params.certSettings
        };
    }

    @measure({ name: "POPAdapter.createGameUrl", isAsync: true })
    public async createGameUrl(merchant: MerchantInfo,
                               gameCode: string,
                               providerCode: string,
                               providerGameCode: string,
                               initRequest: POPGameInitRequest): Promise<MerchantGameURLInfo> {
        await this.validateInitRequest(initRequest);

        const gameUrlInfo: MerchantGameURLInfo = {} as MerchantGameURLInfo;

        let popStartGameData: POPStartGameTokenData;
        if (initRequest.previousStartTokenData) {
            popStartGameData = this.getStartGameTokenDataFromPreviousToken(
                initRequest.previousStartTokenData as POPStartGameTokenData,
                gameCode,
                providerCode,
                providerGameCode,
                initRequest);
        } else {
            popStartGameData = await this.getStartGameTokenData(merchant,
                gameCode,
                providerCode,
                providerGameCode,
                initRequest);
        }

        // all params are optional - will be appended to game url if present
        gameUrlInfo.urlParams = this.getUrlParams(merchant, initRequest, popStartGameData);
        // do not store urls from POP in token
        delete popStartGameData.urls;
        gameUrlInfo.tokenData = {
            currency: initRequest.currency,
            ...popStartGameData
        } as MerchantStartGameTokenData;

        return gameUrlInfo;
    }

    protected getStartGameTokenDataFromPreviousToken(startGameTokenData: POPStartGameTokenData,
                                                     gameCode: string,
                                                     providerCode: string,
                                                     providerGameCode: string,
                                                     initRequest: POPGameInitRequest): POPStartGameTokenData {

        if (initRequest.playmode === "fun") {
            return {
                playerCode: startGameTokenData.playerCode,
                gameCode,
                brandId: startGameTokenData.brandId,
                currency: startGameTokenData.currency,
                playmode: startGameTokenData.playmode
            } as POPStartGameTokenData;
        }

        const result: POPStartGameTokenData = {
            brandId: startGameTokenData.brandId,
            merchantType: startGameTokenData.merchantType,
            merchantCode: startGameTokenData.merchantCode,
            playerCode: startGameTokenData.playerCode,
            gameCode,
            providerCode,
            providerGameCode,
            currency: startGameTokenData.currency,
            envId: startGameTokenData.envId,
            secureToken: startGameTokenData.secureToken,
            skinId: startGameTokenData.skinId,
            localeCode: startGameTokenData.localeCode,
            accountId: startGameTokenData.accountId,
            clientPlatform: startGameTokenData.clientPlatform,
            clientType: startGameTokenData.clientType,
            country: startGameTokenData.country,
            language: startGameTokenData.language,
            playmode: initRequest.playmode,
            urls: startGameTokenData.urls,
            test: startGameTokenData.test,
            popPlayerInfo: startGameTokenData.popPlayerInfo
        };
        if (startGameTokenData.timeInLoginSession !== undefined) {
            result.timeInLoginSession = startGameTokenData.timeInLoginSession;
            result.startedAt = startGameTokenData.startedAt;
        }
        if (startGameTokenData.crosslaunchurl !== undefined) {
            result.crosslaunchurl = startGameTokenData.crosslaunchurl;
        }

        if (startGameTokenData.siteUrl) {
            result.siteUrl = startGameTokenData.siteUrl;
        }

        if (startGameTokenData.dynamicMaxTotalBetLimit) {
            result.dynamicMaxTotalBetLimit = startGameTokenData.dynamicMaxTotalBetLimit;
        }

        return result;
    }

    protected getUrlParams(merchant: MerchantInfo, initRequest: POPGameInitRequest, tokenData: POPStartGameTokenData) {
        const urlParams = {
            language: initRequest.language,
            playmode: initRequest.playmode
        } as any;

        const merchLoginUrl = initRequest.backurl || tokenData?.urls?.lobby;
        if (merchLoginUrl) {
            urlParams.merch_login_url = merchLoginUrl;
            urlParams.lobby = urlParams.merch_login_url;
        }
        const cashier = initRequest.cashier || tokenData?.urls?.cashier;
        const historyURL = tokenData?.urls?.history || initRequest.history;
        if (cashier) {
            urlParams.cashier = cashier;
            // rare case when casino is poorly configured, cashier url can be requested to be used for history
            if (merchant.params.useCashierUrlForHistory) {
                urlParams.history_url = decodeURIComponent(cashier);
            }
        }
        // case when we want operator's history to be used as in-game history
        if (historyURL && merchant.params.useMrchHistoryUrlForHistory) {
            urlParams.history_url = decodeURIComponent(historyURL);
        }

        return urlParams;
    }

    /**
     * Returns specific merchant start game token data
     *
     * @param merchant merchant entity
     * @param gameCode game code
     * @param providerCode provider code
     * @param providerGameCode provider game code
     * @param initRequest specific merchant start game request information (tickets, etc.)
     */
    @measure({ name: "POPAdapter.getStartGameTokenData", isAsync: true })
    public async getStartGameTokenData(merchant: MerchantInfo,
                                       gameCode: string,
                                       providerCode: string,
                                       providerGameCode: string,
                                       initRequest: POPGameInitRequest): Promise<POPStartGameTokenData> {

        if (initRequest.playmode === "fun") {
            return {
                playerCode: initRequest.username,
                gameCode: initRequest.gameCode,
                brandId: merchant.brandId,
                currency: initRequest.currency,
                playmode: initRequest.playmode,
                providerGameCode: providerGameCode,
                providerCode: providerCode
            } as POPStartGameTokenData;
        }

        const response: POPValidateTicketResponse = await this.verifyPlayerSession(merchant, initRequest);
        const result: POPStartGameTokenData = await this.makePOPStartGameTokenData(merchant,
            gameCode,
            providerCode,
            providerGameCode,
            initRequest,
            response);

        await this.getAndAddPlayerInfoToStartGameToken(merchant, result);

        return result;
    }

    protected async getAndAddPlayerInfoToStartGameToken(merchant: MerchantInfo,
                                                        startGameData: POPStartGameTokenData): Promise<void> {
        const playerInfo = await this.getPlayerInfo(merchant, startGameData as any);
        startGameData.test = playerInfo.isTest;
        startGameData.country = playerInfo.country || "";
        if (playerInfo.vipLevel || playerInfo.additionalInfo) {
            startGameData.popPlayerInfo = {};
            if (playerInfo.vipLevel !== undefined) {
                startGameData.popPlayerInfo.vipLevel = playerInfo.vipLevel;
            }
            if (playerInfo.additionalInfo !== undefined) {
                startGameData.popPlayerInfo.additionalInfo = playerInfo.additionalInfo;
            }
        }
    }

    private async makePOPStartGameTokenData(merchant: MerchantInfo,
                                            gameCode: string,
                                            providerCode: string,
                                            providerGameCode: string,
                                            initRequest: POPGameInitRequest,
                                            response: POPValidateTicketResponse): Promise<POPStartGameTokenData> {

        const result: POPStartGameTokenData = {
            brandId: merchant.brandId,
            merchantType: merchant.type,
            merchantCode: merchant.code,
            playerCode: response.accountBalance.playerId,
            gameCode: gameCode,
            providerGameCode: providerGameCode,
            providerCode: providerCode,
            currency: response.accountBalance.currencyCode,
            secureToken: response.secureToken,
            skinId: initRequest.skinId,
            localeCode: initRequest.localeCode,
            accountId: response.accountBalance.accountId || "",
            clientPlatform: initRequest.clientPlatform,
            clientType: initRequest.clientType,
            country: "",
            language: initRequest.language,
            playmode: initRequest.playmode,
            urls: response.urls
        };

        if (response.timeInLoginSession !== undefined) {
            result.timeInLoginSession = response.timeInLoginSession;
            result.startedAt = new Date().getTime();
        }

        if (initRequest.crosslaunchurl) {
            result.crosslaunchurl = initRequest.crosslaunchurl;
        }

        const lobby = initRequest.backurl || response?.urls?.lobby;
        const cashier = initRequest.cashier || response?.urls?.cashier;
        const siteUrl = getDomain(lobby || cashier);

        if (siteUrl) {
            result.siteUrl = siteUrl;
        }

        // https://pop-playtech.readme.io/v18.11/docs/spanish-regulation#section-verify-player-session-response
        if (MerchantInfoHelper.isSpanishRegulation(merchant) && response?.accountBalance?.messageArray?.length) {
            result["realityCheckMessage"] = response.accountBalance.messageArray[0].accountMsg;
        }

        if (response.sessionData?.maxAllowedBetAmt) {
            const currency = Currencies.get(initRequest.currency);
            result.dynamicMaxTotalBetLimit = currency.toMajorUnits(response.sessionData.maxAllowedBetAmt);
        }

        return result;
    }

    private async verifyPlayerSession(merchant: MerchantInfo,
                                      initRequest: POPGameInitRequest): Promise<POPValidateTicketResponse> {
        const requestData = this.buildVerifySessionRequestData(initRequest);
        try {
            const result = await this.doVerifyPlayerSession(merchant, requestData);
            await getPOPGameRelaunchHelper().storePopLaunchTicketAfterFirstUse(merchant, initRequest);
            return result;
        } catch (err) {
            if (err instanceof AdapterErrors.GameTokenExpired) {
                await getPOPGameRelaunchHelper().useCrossLaunchUrlIfTicketWasOnceUsed(merchant, initRequest);
            }
            return Promise.reject(err);
        }
    }

    protected async doVerifyPlayerSession(merchant: MerchantInfo,
                                          requestData: POPVerifySessionRequest): Promise<POPValidateTicketResponse> {
        const url = await this.getUrl(merchant);
        const response: POPValidateTicketResponse = await this.post<POPValidateTicketResponse>(
            url,
            "/player/verifyplayersession",
            requestData,
            merchant?.proxy?.url
        );
        if (!response.accountBalance) {
            return Promise.reject(new POPErrors.InvalidPOPValidateTicketResponse());
        }
        return response;
    }

    private buildVerifySessionRequestData(initRequest: POPGameInitRequest): POPVerifySessionRequest {
        return {
            _meta: this.getMeta(initRequest),
            skinId: initRequest.skinId,
            playerId: initRequest.username,
            secureToken: initRequest.ticket,
            currencyCode: initRequest.currency,
            localeCode: initRequest.localeCode,
            gameId: initRequest.gameCode,
            includeUrls: true
        };
    }

    private getSegmentFilter(merchant: MerchantInfo,
                             tokenData: POPAuthTokenData,
                             startGameTokenData: POPPlayerInfoForSegments): SegmentFilter {
        if (merchant.type === MERCHANT_POP_MOORGATE_TYPE) {
            const segmentFilter: SegmentFilter = {
                matchingData: {
                    clientType: tokenData.clientType,
                    clientPlatform: tokenData.clientPlatform,
                    vipLevel: `${startGameTokenData?.popPlayerInfo?.vipLevel}`,
                    currency: tokenData.currency
                }
            };

            if (startGameTokenData.popPlayerInfo && startGameTokenData.popPlayerInfo.additionalInfo) {
                for (const [key, value] of Object.entries(startGameTokenData.popPlayerInfo.additionalInfo)) {
                    if (key.startsWith("custom")) {
                        segmentFilter.matchingData[key] = value;
                    }
                }
            }

            segmentFilter.externalResellerPath = startGameTokenData?.popPlayerInfo?.additionalInfo?.["KAA/KioskXPath"];

            return segmentFilter;
        } else if (MERCHANT_POP_TYPES.includes(merchant.type)) {
            return {
                externalId: `${startGameTokenData?.popPlayerInfo?.vipLevel}`
            };
        }
    }

    /**
     * Validate correctness of merchant start game token and create game token. This is called on game start.
     */
    public async getGameTokenInfo(merchant: MerchantInfo,
                                  startToken: POPStartGameTokenData,
                                  currency: string,
                                  transferEnabled: boolean): Promise<MerchantGameTokenInfo<POPAuthTokenData>> {

        await this.manageGameClientRelaunch(merchant, startToken);

        const authToken: POPAuthTokenData = {
            playerCode: startToken.playerCode,
            gameCode: startToken.gameCode,
            brandId: startToken.brandId,
            currency: currency,
            transferEnabled,
            walletPerGame: transferEnabled && merchant.params.walletPerGame,
            merchantType: merchant.type,
            merchantCode: merchant.code,
            isPromoInternal: merchant.params.isPromoInternal || false,
            skinId: startToken.skinId,
            secureToken: startToken.secureToken,
            localeCode: startToken.localeCode,
            accountId: startToken.accountId || "",
            playmode: startToken.playmode,
            country: startToken.country,
            clientPlatform: startToken.clientPlatform,
            clientType: startToken.clientType
        } as any;

        if (startToken.test !== undefined) {
            authToken.test = startToken.test;
        }

        if (startToken.envId) {
            authToken.envId = startToken.envId;
        }

        if (startToken.timeInLoginSession !== undefined) {
            authToken.timeInLoginSession = startToken.timeInLoginSession;
            authToken.startedAt = startToken.startedAt;
        }

        try {
            await this.subscribePlayerToPOPNotifications(merchant, authToken);
        } catch (err) {
            log.warn(err, "Failed to subscribe user to pop notifications on game start.");
        }

        return {
            segmentFilter: this.getSegmentFilter(merchant, authToken, startToken),
            gameTokenData: authToken,
            operatorSiteExternalCode: startToken.siteUrl
        };
    }

    /**
     * Method helps to check and manage case when player makes a refresh in his browser window
     * (NOT GAME RESTART VIA POP LAUNCHER).
     * Stores POP's secureToken in redis on first game start.
     * It also checks if same POP's secureToken is already present in redis - if true, then its not the first game
     * launch.
     */
    private async manageGameClientRelaunch(merchant: MerchantInfo,
                                           startToken: POPStartGameTokenData): Promise<void> {
        const previousLaunchData = await getPOPGameRelaunchHelper().findPopToken(merchant, startToken);
        const isRelaunch: boolean = !!previousLaunchData;
        if (isRelaunch) {
            if (MerchantInfoHelper.shouldVerifyPlayerOnGameStart(merchant)) {
                if (startToken.crosslaunchurl) {
                    return Promise.reject(new AdapterErrors.GameClientRedirectRequiredError(startToken.crosslaunchurl));
                }
                return Promise.reject(new AdapterErrors.GameTokenExpired());
            }

            // no need to await
            getPOPGameRelaunchHelper().resetTtl(merchant.brandId, startToken.playerCode, startToken.secureToken);
            return;
        }

        await getPOPGameRelaunchHelper().storePopTokenAfterFirstGameStart(merchant, startToken);
    }

    @measure({ name: "POPAdapter.subscribePlayerToPOPNotifications", isAsync: true })
    private async subscribePlayerToPOPNotifications(merchant: MerchantInfo,
                                                    authToken: POPAuthTokenData): Promise<any> {
        let popMessageSubscriptions = ["alert", "bonus"];
        if (config.pop.userNotificationSubscriptions !== undefined) {
            popMessageSubscriptions = config.pop.userNotificationSubscriptions.split(",").map(item => item.trim());
        }
        const subscribeRequest: POPSubscribeToMessages = {
            _meta: this.getMeta(authToken),
            secureToken: authToken.secureToken,
            playerId: authToken.playerCode,
            skinId: authToken.skinId,
            inSight: {
                type: "request",
                scope: "ims",
                operation: "subscribeToMessages",
                correlationId: uuid.v4(),
                data: {
                    subscribedMessages: popMessageSubscriptions
                }
            }
        };

        await this.post<POPTransactionResponse>(
            merchant.params.serverUrl,
            "/insight",
            subscribeRequest,
            merchant?.proxy?.url
        );
    }

    /**
     *  Commit play bet/win
     */
    @measure({ name: "POPAdapter.commitPayment", isAsync: true })
    public async commitPayment(merchant: MerchantInfo,
                               authToken: POPAuthTokenData,
                               request: PaymentRequest,
                               isCycleFinished: boolean = null): Promise<Balance> {
        if (request.finalizationType) {
            return this.getBalanceForFinalize(merchant, authToken);
        }

        let balance: number;

        const bet = request.bet || 0;
        const win = request.win || 0;
        let result: Balance;
        let popWinResponse: POPTransactionResponse;

        const betResult: POPTrxIOObject = await this.wrapDebit(request,
            this.postDebit(merchant, authToken, request, bet));

        try {
            const creditResult = await this.postCredit(merchant, authToken, request, win, isCycleFinished);

            popWinResponse = creditResult.response;

            if (!popWinResponse) {
                return this.fetchAndParseBalance(merchant, authToken, win - bet);
            }

            const balanceAfterBet: Balance = await this.parseBalance(betResult.response,
                authToken, merchant.params, false);

            const balanceAfterWin: Balance = await this.parseBalance(popWinResponse,
                authToken, merchant.params);

            result = { ...balanceAfterBet, ...balanceAfterWin };
            // ensure that we don't loose message from bet operation and that both bet and win messages will reach
            // player
            if (balanceAfterBet?.extraData?.messageArray && balanceAfterWin?.extraData?.messageArray) {
                result.extraData.messageArray =
                    balanceAfterBet.extraData.messageArray.concat(balanceAfterWin.extraData.messageArray);
            }

            balance = result.main;
        } catch (err) {
            if (err instanceof AdapterErrors.GameTokenExpired) {
                return Promise.reject(new CannotCompletePayment());
            }

            return Promise.reject(err);
        }

        let previousValue;
        if (merchant.params.walletType === WALLET_TYPE.UPDATE_WIN_ON_ROUND_FINISHED) {
            const isFinished = isCycleFinished === null ? this.getGameStatus(request) === "settled" : isCycleFinished;
            previousValue = isFinished
                            ? balance - win + bet
                            : balance + bet;
        } else {
            previousValue = isFreeBet(request)
                            ? balance - win
                            : balance - win + bet;
        }
        result.previousValue = previousValue > 0 ?
                               Currencies.get(authToken.currency).toFixedByExponent(previousValue) : 0;
        return result;
    }

    // helper method to apply the same error handling logic to /debit callers
    private async wrapDebit(paymentRequest: PaymentRequest,
                            debitPromise: Promise<POPTrxIOObject>,
                            isCycleStarted?: boolean): Promise<POPTrxIOObject> {
        try {
            return await debitPromise;
        } catch (err) {
            if (err instanceof AdapterErrors.GameTokenExpired || err instanceof AdapterErrors.MerchantInternalError) {
                const isGameCycleStarted = isCycleStarted !== undefined ?
                                           isCycleStarted :
                                           this.getGameType(paymentRequest) === "normal";
                if (isGameCycleStarted) {
                    return Promise.reject(new RequireRefundBetError(err.message));
                }
                return Promise.reject(new CannotCompletePayment(err.message));
            }
            return Promise.reject(err);
        }
    }

    /**
     * This method implements two phase payment flow. (for bet action)
     * If in entity settings "splitPayment" flag enabled Falcon game server will send BET payment separately
     * This method also triggers on /v1/play payments
     *
     * @param {Merchant} merchant
     * @param {POPAuthTokenData} authToken
     * @param {PaymentRequest} request
     * @returns {Promise<Balance>}
     */
    @measure({ name: "POPAdapter.commitBetPayment", isAsync: true })
    public async commitBetPayment(merchant: MerchantInfo, authToken: POPAuthTokenData,
                                  request: PaymentRequest): Promise<Balance> {

        if (request.finalizationType) {
            return this.getBalanceForFinalize(merchant, authToken);
        }

        const bet = request.bet;
        const betResponse = await this.wrapDebit(request, this.postDebit(merchant, authToken, request, bet));

        const popResponse = betResponse.response;
        return this.getBalanceFromPopResponseOrFetch(
            popResponse,
            merchant,
            authToken,
            isFreeBet(request) ? 0 : bet
        );
    }

    /**
     * This method implements two phase payment flow. (for win action)
     * If in entity settings "splitPayment" flag enabled Falcon game server will send WIN payment separately
     * This method also triggers on /v1/play payments
     *
     * @param {Merchant} merchant
     * @param {POPAuthTokenData} authToken
     * @param {PaymentRequest} request
     * @returns {Promise<Balance>}
     */
    @measure({ name: "POPAdapter.commitWinPayment", isAsync: true })
    public async commitWinPayment(merchant: MerchantInfo, authToken: POPAuthTokenData,
                                  request: PaymentRequest): Promise<Balance> {

        const win = request.win;

        try {
            const creditResult = await this.postCredit(merchant, authToken, request, request.win);
            const popResponse = creditResult.response;

            // If we have no response from POP just fetch balance
            if (!popResponse) {
                return request.finalizationType ? await this.getBalanceForFinalize(merchant, authToken) :
                       await this.fetchAndParseBalance(merchant, authToken, -win);
            }

            if (!popResponse.accountBalance) {
                return Promise.reject(new POPErrors.InvalidPOPValidateTicketResponse());
            }

            const mainBalance = popResponse.accountBalance.balanceArray.find((e) => {
                return e.balanceType === POPAdapter.BALANCE_NAME_MAIN;
            });

            if (!mainBalance) {
                log.error("POP: Failed to find balance in credit response");
                return Promise.reject(
                    new CannotCompletePayment("Failed to find balance in credit response"));
            }

            const balance = mainBalance.balanceAmt;

            const currency = Currencies.get(popResponse.accountBalance.currencyCode);
            const previousValue = currency.toMajorUnits(balance) - win;

            const result = await this.parseBalance(popResponse, authToken, merchant.params);
            result.previousValue = previousValue > 0 ?
                                   Currencies.get(authToken.currency).toFixedByExponent(previousValue) : 0;

            return result;

        } catch (err) {
            if (err instanceof AdapterErrors.GameTokenExpired) {
                return Promise.reject(new CannotCompletePayment());
            }

            return Promise.reject(err);
        }
    }

    /**
     *  Transfer money in/out of merchant wallet
     */
    @measure({ name: "POPAdapter.transfer", isAsync: true })
    public async transfer(merchant: MerchantInfo,
                          authToken: POPAuthTokenData,
                          request: MerchantTransferRequest): Promise<Balance> {
        if (request.finalizationType) {
            return this.getBalanceForFinalize(merchant, authToken);
        }

        // NOTE: pop works only with full transfers and wallet per game
        if (request.operation === "transfer-in") {
            return this.transferIn(merchant, authToken, request);
        } else if (request.operation === "transfer-out") {
            return this.transferOut(merchant, authToken, request);
        }

        return Promise.reject(new AdapterErrors.OperationForbidden());
    }

    @measure({ name: "POPAdapter.getBalances", isAsync: true })
    public async getBalances(merchant: MerchantInfo, authToken: POPAuthTokenData): Promise<Balances> {
        const url = await this.getUrl(merchant);

        const postData = {
            _meta: this.getMeta(authToken),
            secureToken: authToken.secureToken,
            playerId: authToken.playerCode,
            skinId: authToken.skinId,
            localeCode: authToken.localeCode,
            accountId: authToken.accountId || ""
        };

        const balanceResponse: POPPlayerBalance = await this.post<POPPlayerBalance>(
            url,
            "/player/getplayerbalance",
            postData,
            merchant?.proxy?.url);

        const currency = balanceResponse?.accountBalance?.currencyCode || authToken.currency;

        return {
            [currency]: await this.parseBalance(balanceResponse, authToken, merchant.params),
        } as Balances;
    }

    @measure({ name: "POPAdapter.keepAlive", isAsync: true })
    public async keepAlive(merchant: MerchantInfo,
                           authToken: POPAuthTokenData): Promise<void> {
        const url = await this.getUrl(merchant);

        const postData = {
            _meta: this.getMeta(authToken),
            secureToken: authToken.secureToken,
            playerId: authToken.playerCode,
            skinId: authToken.skinId
        };

        await this.post<POPPlayerBalance>(
            url,
            "/player/keepalive",
            postData,
            merchant?.proxy?.url);
    }

    protected async getBalanceForFinalize(merchant: MerchantInfo, authToken: POPAuthTokenData) {
        try {
            const balances = await this.getBalances(merchant, authToken);
            return balances[authToken.currency];
        } catch (err) {
            if (!(err instanceof AdapterErrors.GameTokenExpired)) {
                log.warn(err, "Get balance for finalize request was failed");
            }
            return this.getFakeBalance();
        }
    }

    private getFakeBalance(): Balance {
        return {
            main: 0
        };
    }

    @measure({ name: "POPAdapter.getPlayerInfo", isAsync: true })
    public async getPlayerInfo(merchant: MerchantInfo, authToken: POPAuthTokenData): Promise<POPPlayerInfo> {
        const url = await this.getUrl(merchant);

        const postData = {
            _meta: this.getMeta(authToken),
            skinId: authToken.skinId,
            playerId: authToken.playerCode,
            secureToken: authToken.secureToken
        };

        const playerInfo: POPPlayerInfo = await this.post<POPPlayerInfo>(
            url,
            "/player/getplayerinfo",
            postData,
            merchant?.proxy?.url
        );

        const isTestPlayer = playerInfo.additionalInfo &&
            (playerInfo.additionalInfo.isInternalPlayer === "1" || playerInfo.additionalInfo.isInternalPlayer === true)
            || false;

        return {
            playerId: playerInfo.playerId,
            code: playerInfo.playerId,
            status: "normal",
            firstName: undefined,
            lastName: undefined,
            email: undefined,
            currency: playerInfo.currencyCode,
            country: playerInfo.countryCode,
            language: undefined,
            isTest: isTestPlayer,
            vipLevel: playerInfo.vipLevel || "",
            additionalInfo: playerInfo.additionalInfo,
            externalResellerPath: playerInfo?.additionalInfo?.["KAA/KioskXPath"]
        } as POPPlayerInfo;
    }

    public async getUrl(merchant: MerchantInfo): Promise<string> {
        const params = merchant.params as POPParams;
        const url = params.serverUrl;

        return url ? url : Promise.reject(new AdapterErrors.MerchantMisconfiguration());
    }

    @measure({ name: "POPAdapter.performRegulatoryAction", isAsync: true })
    public async performRegulatoryAction(merchant: MerchantInfo,
                                         authToken: POPAuthTokenData,
                                         request: PlayerRegulatoryActionRequest): Promise<any> {

        if (request.action === PlayerRegulatoryActionsAtServer.customAction) {
            const response = await this.submitInsightAction(merchant, authToken, request);
            return this.addAAMSDataToExtraDataIfPresent(response);
        }

        const popUserAction = request.action === PlayerRegulatoryActionsAtServer.closeSession ?
                              "stopgaming" : "reset";
        const regulation = request.params && request.params.regulation || "uk";

        const postData = {
            _meta: this.getMeta(authToken),
            secureToken: authToken.secureToken,
            playerId: authToken.playerCode,
            skinId: authToken.skinId,
            localeCode: authToken.localeCode,
            accountId: authToken.accountId || "",
            userAction: popUserAction
        };

        await this.post<POPRealityCheckActionResponse>(
            await this.getUrl(merchant),
            `/gamesession/regulation/${regulation}/realitycheckdialogresponse`,
            postData,
            merchant?.proxy?.url
        );
    }

    private async submitInsightAction(merchant: MerchantInfo,
                                      authToken: POPAuthTokenData,
                                      request: PlayerRegulatoryActionRequest): Promise<POPInsightResponse> {
        const inSight = request.params.inSight;

        const postData = {
            _meta: this.getMeta(authToken),
            secureToken: authToken.secureToken,
            playerId: authToken.playerCode,
            skinId: authToken.skinId,
            inSight
        };

        return this.post<POPInsightResponse>(
            await this.getUrl(merchant),
            "/insight",
            postData,
        );
    }

    private addAAMSDataToExtraDataIfPresent(popResponse: POPInsightResponse): POPInsightResponse {
        const aamsSessionCode = popResponse?.inSight?.data?.aamsSessionCode;
        const participationStartDate = popResponse?.inSight?.data?.participationStartDate;
        const ropCode = popResponse?.inSight?.data?.participationCode;
        const regulatoryData: AAMSTokenData = { regulatoryData: { aamsSessionCode, participationStartDate, ropCode } };
        if (aamsSessionCode) {
            popResponse["extraData"] = regulatoryData;

        }
        return popResponse;
    }

    @measure({ name: "POPAdapter.loginGame", isAsync: true })
    public async loginGame(merchant: MerchantInfo,
                           gameTokenData: POPAuthTokenData,
                           request: GameLoginRequest): Promise<void> {
        const params = merchant.params as POPParams;

        const resumeBrokenGameRequest: POPResumeBrokenGame = {
            skinId: gameTokenData.skinId,
            playerId: gameTokenData.playerCode,
            secureToken: gameTokenData.secureToken,
            brokenGameId: request.logoutId,
            gameCycleId: request.roundPID || request.roundId.toString(),
            _meta: this.getMeta(gameTokenData)
        };
        try {
            await this.post(params.serverUrl, "/gamesession/resumebrokengame",
                resumeBrokenGameRequest, merchant?.proxy?.url);
        } catch (e) {
            return Promise.reject(e);
        }
    }

    @measure({ name: "POPAdapter.logoutGame", isAsync: true })
    public async logoutGame(merchant: MerchantInfo,
                            gameTokenData: POPAuthTokenData,
                            request: GameLogoutRequest): Promise<GameLogoutResponse> {
        const params = merchant.params as POPParams;
        if (!params.gameLogoutOptions || request.state !== GameLogoutGameState.UNFINISHED) {
            return { requireLogin: false };
        } else {
            const createBrokenGameRequest = this.makeCreateBrokenGameRequest(gameTokenData, request);
            try {
                await this.post(params.serverUrl, "/gamesession/createbrokengame",
                    createBrokenGameRequest, merchant?.proxy?.url);
            } catch (e) {
                // in case of non recoverable error we can only skip logout
                if (e instanceof POPErrors.POPError || e instanceof POPErrors.IncompleteRequest) {
                    log.warn(e, createBrokenGameRequest, "Skip 'createBrokenGame' because of non-recoverable error");
                    return { requireLogin: false };
                }

                return Promise.reject(e);
            }
            return { requireLogin: true };
        }
    }

    public makeCreateBrokenGameRequest(gameTokenData: POPAuthTokenData,
                                       request: GameLogoutRequest): POPCreateBrokenGame {
        return {
            skinId: gameTokenData.skinId,
            playerId: gameTokenData.playerCode,
            secureToken: gameTokenData.secureToken,
            brokenGameId: request.logoutId,
            gameCycleId: request.roundPID || request.roundId.toString(),
            _meta: this.getMeta(gameTokenData)
        };
    }

    @measure({ name: "POPAdapter.getFreeBetInfo", isAsync: true })
    public async getFreeBetInfo(merchant: MerchantInfo,
                                authToken: POPAuthTokenData,
                                freeBetRequest: FreeBetInfoRequest) {
        const balances = await this.getBalances(merchant, authToken);
        const balance = balances[authToken.currency];
        const configuration = await this.getGameConfiguration(merchant, authToken);
        const coin = configuration?.data?.configData?.bonus?.freeSpinCoinSize || freeBetRequest.stakeAll[0];
        return {
            amount: balance && balance.freeBets && balance.freeBets.amount || 0,
            coin
        };
    }

    private async postDebit(merchant: MerchantInfo,
                            authToken: POPAuthTokenData,
                            request: PaymentRequest,
                            bet: number,
                            isCycleStarted?: boolean): Promise<POPTrxIOObject> {

        const gameCycleStarted = isCycleStarted !== undefined ? isCycleStarted : this.getGameType(request) === "normal";

        const isJpWinAndZeroBet = request.bet === 0 && request.isJPWin;
        const isFreespin = (bet === 0 && gameCycleStarted === false); // For free spins we do not send 0 bet to POP

        if (isFreespin || isJpWinAndZeroBet) {
            return {
                request: undefined,
                response: undefined
            };
        }

        const trxId = request.transactionId;

        const betData: POPTransactionRequest = {
            ...this.getDataForTransactionRequest(authToken, request),
            moneyTransArray: [] as any,
            gameCycleStarted,
            gameCycleFinished: false
        };

        // POP do not allow to send 0 bet. But allow to send empty moneyTransArray for gameCycleStarted = true request
        if (bet > 0) {
            betData.moneyTransArray.push(this.makePOPTransactionRequestDetails(merchant,
                authToken,
                request,
                bet,
                true));
        }

        if (betData.gameCycleStarted) {
            betData.gameCycleStartDateTime = new Date(trxId.timestamp).toISOString();
        }

        const result = await this.post<POPTransactionResponse>(merchant.params.serverUrl,
            "/gamesession/moneytransactions",
            betData,
            merchant?.proxy?.url);

        return {
            request: betData,
            response: result
        };
    }

    private async postCredit(merchant: MerchantInfo,
                             authToken: POPAuthTokenData,
                             request: PaymentRequest,
                             win: number,
                             isCycleFinished: boolean = null): Promise<POPTrxIOObject> {

        const gameCycleFinished = isCycleFinished !== null
                                  ? isCycleFinished
                                  : this.getGameStatus(request) === "settled";

        const winData: POPTransactionRequest = {
            ...this.getDataForTransactionRequest(authToken, request),
            gameCycleStarted: false,
            gameCycleFinished,
            moneyTransArray: [] as any
        };

        if (win > 0) { // Send win transaction only if we have win > 0
            winData.moneyTransArray.push(this.makePOPTransactionRequestDetails(merchant,
                authToken,
                request,
                win,
                false));
        }

        if (winData.gameCycleFinished) {
            // Requirement from POP to get different start game cycle date and finish game cycle date
            // which matches on regular round with one spin
            winData.gameCycleFinishDateTime = this.makeIncreasedDateForCredit(request);

            const currency = Currencies.get(authToken.currency);
            this.addJPinfo(request, winData, currency);
        }

        let result: POPTransactionResponse;

        if (winData.gameCycleFinished || win > 0) { // Sent win payment only on round-ended or if win not zero
            result = await this.post<POPTransactionResponse>(
                merchant.params.serverUrl,
                "/gamesession/moneytransactions",
                winData,
                merchant?.proxy?.url, request.offlineRetry);
        }

        return {
            request: winData,
            response: result
        };
    }

    private addJPinfo(request: PaymentRequest, winData: POPTransactionRequest, currency: Currency): void {
        // jp contribution is required field for POP, so first check it.
        // But in some cases we have jp win without contribution (OMQ-JP,  GRC games)
        const AMOUNT_PRECISION = 3;
        const jackpotArray: POPJackpotInfo[] = [];
        if (request.totalJpContribution || request.totalJpWin) {
            const details = request.jackpotDetails;
            const jackpots = details && details.jackpots || {};
            for (const jackpotId of Object.keys(jackpots)) {

                const pools: JackpotIdDetails = jackpots[jackpotId];
                for (const poolId of Object.keys(pools)) {
                    const pool: JackpotPoolDetails = pools[poolId];
                    const progressiveCntr = pool?.contribution?.progressive || 0;
                    const seed = pool?.contribution?.seed || 0;
                    const poolWin = pool.win || 0;
                    const normalizedContributionInMinorUnits =
                        calculation.normalizeAmountByPrecision(AMOUNT_PRECISION,
                            (seed + progressiveCntr) * currency.multiplier);
                    jackpotArray.push({
                        pjsId: jackpotId,
                        controllerId: poolId,
                        sharedJackpot: !pool.isLocal,
                        jackpotContribAmt: normalizedContributionInMinorUnits,
                        jackpotAwardAmt: poolWin > 0 ? currency.toMinorUnits(poolWin) : undefined
                    });
                }
            }
        }
        if (jackpotArray.length !== 0) {
            winData.jackpotArray = jackpotArray;
        }
    }

    private makeIncreasedDateForCredit(request: PaymentRequest): string {
        const tsDate = new Date(request.transactionId.timestamp);
        tsDate.setMilliseconds(tsDate.getMilliseconds() + POPAdapter.TIME_TO_INCREASE_END_ROUND_DATE_MS);

        return tsDate.toISOString();
    }

    private makePOPTransactionRequestDetails(merchant: MerchantInfo,
                                             authToken: POPAuthTokenData,
                                             request: PaymentRequest,
                                             amount: number,
                                             isBet): POPTransactionRequestDetails {
        const transIdPostfix = isBet ? OPERATION_ID.BET.toString() : OPERATION_ID.WIN.toString();
        const currency = Currencies.get(authToken.currency);
        const trxId = request.transactionId;

        const moneyTransObj: POPTransactionRequestDetails = {
            transSeq: typeof request.eventId === "number" ? request.eventId : 1,
            transId: trxId.serialId.toString() + transIdPostfix,
            transAmt: currency.toMinorUnits(amount),
            transType: isBet ? POPAdapter.TRANS_TYPE_DEBIT : POPAdapter.TRANS_TYPE_CREDIT,
            transDateTime: new Date(trxId.timestamp).toISOString(),
            // freeBet VS freeGame
            transCategory: this.getTransCategory(merchant,
                request,
                isBet ? POP_WALLET_OPERATION.DEBIT : POP_WALLET_OPERATION.CREDIT),
            transDesc: this.getGameType(request) + (isBet ? " bet" : " win")
        };

        if (isFreeBet(request) && merchant.params.isPromoInternal) {
            const promoId = request?.freeBetBalance?.activePromoId || request.roundId;
            moneyTransObj.bonusData = {
                bonusReferenceId: promoId.toString()
            };
            if (isBet) {
                moneyTransObj.bonusData.remainingSpins = request?.freeBetBalance?.amount || 0;
            }
        }

        return moneyTransObj;
    }

    private getTransCategory(merchant: MerchantInfo, request: PaymentRequest, walletOperation: POP_WALLET_OPERATION) {
        if (walletOperation === POP_WALLET_OPERATION.DEBIT) {
            if (isFreeBet(request)) {
                return merchant.params.isPromoInternal
                       ? POPAdapter.TRANS_CATEGORY_INTERNAL_FREE_SPIN_BET
                       : POPAdapter.TRANS_CATEGORY_EXTERNAL_FREE_SPIN_BET;

            }
            return POPAdapter.TRANS_CATEGORY_WAGER;
        } else if (walletOperation === POP_WALLET_OPERATION.CREDIT) {
            if (isFreeBet(request)) {
                return merchant.params.isPromoInternal
                       ? POPAdapter.TRANS_CATEGORY_INTERNAL_FREE_SPIN_WIN
                       : POPAdapter.TRANS_CATEGORY_EXTERNAL_FREE_SPIN_WIN;
            }
            return POPAdapter.TRANS_CATEGORY_WIN;
        }
    }

    private async parseBalance(response: POPPlayerBalance,
                               authToken: POPAuthTokenData,
                               params: MerchantParams,
                               checkForPOPNotifications: boolean = true): Promise<Balance> {
        if (!response?.accountBalance?.balanceArray?.length) {
            return this.getFakeBalance();
        }

        const currency = Currencies.get(response.accountBalance.currencyCode);

        const mainBalance = response.accountBalance.balanceArray.find(e =>
            e.balanceType === POPAdapter.BALANCE_NAME_MAIN);

        if (!mainBalance) {
            log.error("POP: Failed to find balance in payment response");
            return Promise.reject(
                new CannotCompletePayment("Failed to find balance in payment response"));
        }

        const balance: Balance = {
            main: currency.toMajorUnits(mainBalance.balanceAmt)
        };

        this.decorateBalanceWithFreebets(response, balance, authToken);
        this.decorateBalanceWithCMAMessage(response, balance, params);
        this.decorateBalanceWithSessionTimer(balance, authToken);
        this.decorateBalanceWithPOPRoundId(response, balance);

        if (checkForPOPNotifications) {
            try {
                await this.decorateBalanceWithPOPNotifications(balance, authToken, params);
            } catch (err) {
                log.warn(err, "Failed to add pop notifications to balance extraData.");
            }
        }

        return balance;
    }

    private decorateBalanceWithPOPRoundId(response: POPPlayerBalance, balance: Balance) {
        const popRoundId = response?.extraData?.igpGameCycleId;
        if (popRoundId) {
            balance.extraData = balance.extraData || {};
            balance.extraData.extRoundId = popRoundId;
        }
    }

    private async decorateBalanceWithPOPNotifications(balance: Balance,
                                                      authToken: POPAuthTokenData,
                                                      params: MerchantParams): Promise<void> {
        const popNotifications = await getPOPNotificationHelper().getPOPNotifications(authToken, params);

        if (popNotifications.length) {
            if (!balance.extraData) {
                const popCMAExtraData: MrchExtraDataImpl = MrchExtraDataImpl.create();
                popNotifications.forEach(item => popCMAExtraData.addExtraMessage(item));
                balance.extraData = popCMAExtraData;
                return;
            }

            if (!balance?.extraData?.messageArray) {
                balance.extraData.messageArray = popNotifications;
                return;
            }

            popNotifications.forEach(item => balance.extraData.messageArray.push(item));
        }
    }

    private decorateBalanceWithFreebets(response: POPPlayerBalance,
                                        balance: Balance,
                                        authToken?: POPAuthTokenData): void {
        const freeBets = response.accountBalance.balanceArray.find(e =>
            e.balanceType === POPAdapter.BALANCE_NAME_FREEBET
        );
        if (freeBets && freeBets.balanceAmt) {
            if (authToken.isPromoInternal) {
                log.warn("Merchant free bets are processed in internal wallet and will be ignored. " +
                    "Token: %j, response: %j", authToken, response);
            } else {
                balance.freeBets = {
                    amount: freeBets.balanceAmt
                };
            }
        }
    }

    /**
     * Populates extraData field of balance with CMA message data if its present in POP response
     */
    private decorateBalanceWithCMAMessage(response: POPPlayerBalance, balance: Balance, params: MerchantParams): void {
        const extraDataWithCMAMessages: MrchExtraDataImpl = POPExtraData.getExtraDataFromResponse(response,
            undefined,
            params) as any;
        if (extraDataWithCMAMessages) {
            // ensure that we do not overwrite existing values
            balance.extraData = balance.extraData || {};
            balance.extraData.messageArray =
                (balance.extraData.messageArray || []).concat(extraDataWithCMAMessages.messageArray);
        }
    }

    public getMeta(clentParams?: POPClientParams): POPMetaObject {
        return {
            gpId: this.gpId,
            gsId: this.gsId,
            clientPlatform: clentParams.clientPlatform || "web",
            clientType: clentParams.clientType,
            apiFeatures: ["returnImsGameCode"]
        };
    }

    @measure({ name: "POPAdapter.logoutPlayer", isAsync: true })
    public async logoutPlayer(merchant: MerchantInfo,
                              authToken: POPAuthTokenData): Promise<boolean> {
        const params = merchant.params as POPParams;

        const postData: POPLogoutRequest = {
            skinId: authToken.skinId,
            playerId: authToken.playerCode,
            secureToken: authToken.secureToken,
            _meta: this.getMeta(authToken)
        };

        await this.post<POPLogoutResponse>(
            params.serverUrl,
            "/player/logout",
            postData,
            merchant?.proxy?.url);

        return true;
    }

    public async reportCriticalFiles(merchant: MerchantInfo,
                                     req: ReportCriticalFilesToMerchantRequest): Promise<void> {
        if (req.games && req.games.length) {
            return this.reportGameCriticalFiles(merchant, req);
        } else if (req.modules && req.modules.length) {
            return this.reportPlatformCriticalFiles(merchant, req);
        }
    }

    private getRegulationPath(merchant: MerchantInfo) {
        const regulation = merchant?.params?.regulatorySettings?.merchantRegulation;
        return regulation === "brazilian" ? "generic" : "it";
    }

    private async reportGameCriticalFiles(merchant: MerchantInfo,
                                          req: ReportCriticalFilesToMerchantRequest): Promise<void> {

        const regulation = this.getRegulationPath(merchant);

        for (const gameWithHashes of req.games) {
            try {
                const request: POPSetItalianCriticalFilesRequest = {
                    _meta: {
                        gpId: this.gpId,
                        gsId: this.gsId
                    },
                    skinId: merchant.params.skinId,
                    gameId: gameWithHashes.code,
                    files: [] as any
                };

                for (const filenameAndHash of gameWithHashes.list) {
                    const filePath = Object.keys(filenameAndHash)[0];
                    request.files.push({
                        name: filePath,
                        hash: filenameAndHash[filePath]
                    });
                }

                await this.post<{}>(
                    merchant.params.serverUrl,
                    `/gamesession/regulation/${regulation}/setGameCriticalFiles`,
                    request,
                    merchant?.proxy?.url);
            } catch (err) {
                log.error(err, "Failed to report game critical file for POP");
            }
        }
    }

    private async reportPlatformCriticalFiles(merchant: MerchantInfo,
                                              req: ReportCriticalFilesToMerchantRequest): Promise<void> {

        const regulation = this.getRegulationPath(merchant);

        const request: POPSetItalianCriticalFilesRequest = {
            _meta: {
                gpId: this.gpId,
                gsId: this.gsId
            },
            skinId: merchant.params.skinId,
            files: [] as any
        };

        for (const module of req.modules) {
            if (module.list && module.list.length) {
                for (const filenameAndHash of module.list) {
                    const filePath = Object.keys(filenameAndHash)[0];
                    request.files.push({
                        name: filePath,
                        hash: filenameAndHash[filePath]
                    });
                }
            }
        }

        await this.post<{}>(
            merchant.params.serverUrl,
            `/gamesession/regulation/${regulation}/setPlatformCriticalFiles`,
            request,
            merchant?.proxy?.url);
    }

    public async refundBetPayment(merchant: MerchantInfo,
                                  authToken: POPAuthTokenData, request: RefundBetRequest): Promise<Balance> {
        if (!request.bet) {
            return this.getFakeBalance();
        }

        const now = (new Date()).toISOString();
        const cancelByBet: POPCancelTransactionRequest = {
            _meta: this.getMeta(authToken),
            secureToken: authToken.secureToken,
            playerId: authToken.playerCode,
            gameId: authToken.gameCode,
            gameCycleId: request.roundPID || request.roundId.toString(),
            skinId: authToken.skinId,
            cancelTransArray: [
                {
                    transSeq: 1,
                    transId: request.transactionId.serialId.toString() + OPERATION_ID.ROLLBACK.toString(),
                    referenceId: request.transactionId.serialId.toString() + OPERATION_ID.BET.toString(),
                    originalTransDate: new Date(request.transactionId.timestamp).toISOString(),
                    transDateTime: now
                }
            ],
            gameCycleFinished: true,
            gameCycleFinishDateTime: now
        };

        const isFreeSpin = isFreeBet(request) && !merchant.params.isPromoInternal;

        if (isFreeSpin) {
            cancelByBet.cancelTransArray[0].transCategory = POPAdapter.TRANS_CATEGORY_EXTERNAL_FREE_SPIN_CANCEL;
        }

        let cancelResponse;
        try {
            cancelResponse = await this.post<POPCancelTransactionResponse>(
                merchant.params.serverUrl,
                "/gamesession/canceltransactions",
                cancelByBet,
                merchant?.proxy?.url,
                request.offlineRetry
            );
        } catch (err) {
            if (err instanceof POPErrors.CancelTransactionNotExist ||
                err instanceof POPErrors.BetTransactionAlreadyCanceled) {
                return this.getFakeBalance();
            }
            throw err;
        }

        if (!cancelResponse ||
            !cancelResponse.accountBalance ||
            !cancelResponse.accountBalance.balanceArray ||
            !cancelResponse.accountBalance.balanceArray.length) {
            return this.getFakeBalance();
        }

        const currency = Currencies.get(cancelResponse.accountBalance.currencyCode);

        const balance: Balance = {
            main: currency.toMajorUnits(cancelResponse.accountBalance.balanceArray.find(e =>
                e.balanceType === POPAdapter.BALANCE_NAME_MAIN).balanceAmt)
        };
        return balance;
    }

    private async transferIn(merchant: MerchantInfo,
                             authToken: POPAuthTokenData,
                             request: MerchantTransferRequest): Promise<Balance> {

        log.debug(`Transfer in for token: ${authToken.secureToken}, roundPID: ${request.roundPID} `);

        const { response } = await this.wrapDebit(
            request,
            this.postDebit(merchant, authToken, request, request.amount, true),
            true);

        const currency = Currencies.get(authToken.currency);

        const balance = await this.parseBalance(response, authToken, merchant.params);
        balance.previousValue = currency.toFixedByExponent(balance.main + request.amount);

        return balance;
    }

    private async transferOut(merchant: MerchantInfo,
                              authToken: POPAuthTokenData,
                              request: MerchantTransferRequest): Promise<Balance> {
        let cancelResponse: POPTransactionResponse;
        let winResponse: POPTransactionResponse;
        const currency = Currencies.get(authToken.currency);

        if (this.isEmptyTranferRequest(request)) {
            return this.getFakeBalance();
        }

        try {
            const { totalWin = 0, amount = 0, previousAmount = 0 } = request;
            const amountToCancel = previousAmount - totalWin; // unused bets
            const leftAmount = currency.toFixedByExponent(previousAmount - amount);
            let amountToTransferOut = totalWin;

            log.debug(`Transfer out amountToCancel: ${amountToCancel}, leftAmount: ${leftAmount}, ` +
                `balanceBefore: ${previousAmount}`);

            if (amountToCancel > 0) {
                cancelResponse = await this.cancelPartialAmount(merchant, authToken, request, amountToCancel);
            } else {
                amountToTransferOut = previousAmount;
            }

            const { response } = await this.postCredit(merchant, authToken, request as PaymentRequest,
                amountToTransferOut, request.roundEnded);
            winResponse = response;

            let resultBalance: Balance = this.getFakeBalance();
            for (const popResponse of [cancelResponse, winResponse]) {
                const balance = await this.parseBalance(popResponse, authToken, merchant.params);
                resultBalance = { ...resultBalance, ...balance };
            }

            const previousValue = resultBalance.main - request.amount;
            resultBalance.previousValue = previousValue > 0 ? previousValue : 0;

            return resultBalance;
        } catch (err) {
            if (cancelResponse && !winResponse) {
                // throw 500 error to have broken game
                log.error(err, "Win request on transfer out is failed");
                return Promise.reject(new AdapterErrors.MerchantInternalError());
            }
            return Promise.reject(err);
        }

    }

    private isEmptyTranferRequest(request: MerchantTransferRequest) {
        return !request.amount && !request.totalBet && !request.totalWin;
    }

    private async cancelPartialAmount(merchant: MerchantInfo,
                                      authToken: POPAuthTokenData,
                                      request: MerchantTransferRequest,
                                      amountToCancel): Promise<POPTransactionResponse> {
        const params = merchant.params as POPParams;
        const currency = Currencies.get(authToken.currency);
        const trxId = request.transactionId;

        const transferData: POPTransactionRequest = {
            ...this.getDataForTransactionRequest(authToken, request),
            gameCycleStarted: false,
            gameCycleFinished: false,
            moneyTransArray: [
                {
                    transSeq: typeof request.eventId === "number" ? request.eventId : 1,
                    transId: trxId.serialId.toString() + OPERATION_ID.TRANSFER.toString(),
                    transAmt: currency.toMinorUnits(amountToCancel),
                    transType: POPAdapter.TRANS_TYPE_CREDIT,
                    transDateTime: new Date(trxId.timestamp).toISOString(),
                    transCategory: POPAdapter.TRANS_CATEGORY_CANCEL,
                    transDesc: "transfer-out partial cancel",
                }
            ]
        };

        return this.post<POPTransactionResponse>(
            params.serverUrl,
            "/gamesession/moneytransactions",
            transferData,
            merchant?.proxy?.url
        );
    }

    private getDataForTransactionRequest(authToken: POPAuthTokenData,
                                         request: MerchantTransferRequest | PaymentRequest):
        CommonForPOPTransactionRequest {

        const data: CommonForPOPTransactionRequest = {
            _meta: this.getMeta(authToken),
            secureToken: authToken.secureToken,
            playerId: authToken.playerCode,
            localeCode: authToken.localeCode,
            gameId: authToken.gameCode,
            gameCycleId: request.roundPID || request.roundId.toString(),
            skinId: authToken.skinId,
            currencyCode: authToken.currency
        };

        if (authToken.accountId) {
            data.accountId = authToken.accountId;
        }

        return data;
    }

    public async post<T>(baseUrl: string,
                         url: string,
                         data: any,
                         proxy = config.pop.proxy,
                         isOfflineRetry?: boolean): Promise<T> {
        url += `?t=${new Date().getTime()}`;
        return new Promise<T>((resolve, reject) => this.postWithRetries(baseUrl,
            url, data, resolve, reject, 1, this.retryPolicy.delay, proxy, isOfflineRetry));
    }

    private postWithRetries<T>(baseUrl: string,
                               url: string, data: any, resolve, reject, nRetry: number, retryDelay: number,
                               proxy?: string, isOfflineRetry?: boolean) {
        // Requested by Playtech to keep requests idempotency with requestId
        data._meta.requestId = uuid.v4();

        const reqLogData = {
            baseUrl: baseUrl,
            url: url,
            proxy,
            data: { ...data },
            nRetry
        };

        log.info({ request: reqLogData }, "POST %s", url);

        const postOptions: any = {
            baseUrl: baseUrl,
            url: url,
            timeout: 30000,
            body: data,
            json: true,
            agent: baseUrl.startsWith("https") ? agentHttps : agentHttp,
        };

        if (this.certSettings.useCert) {
            postOptions.cert = this.certSettings.settings.cert;
            postOptions.key = this.certSettings.settings.key;
            postOptions.passphrase = this.certSettings.settings.password;
        }

        if (proxy) {
            postOptions.proxy = proxy;
        }

        const ts = new Date().getTime();
        nodeRequest.post(postOptions as any, (err: any, incomingMessage: IncomingMessage, body: any) => {
            const logData = {
                request: reqLogData,
                responseCode: incomingMessage ? incomingMessage.statusCode : 0,
                responseBody: body,
                requestTime: this.getRequestTime(ts)
            } as any;
            log.info(logData, "Unparsed response");
            if (err) {
                log.warn(err, logData, "Error sending request");
                return reject(new AdapterErrors.MerchantInternalError(err.message));
            }

            const statusCode: number = incomingMessage.statusCode;
            if (statusCode === 200) {
                const response = body;
                logData.response = response;
                return resolve(response as T);
                // Logical AND has more priority rather than OR any way
            } else if (statusCode >= 400 && statusCode <= 599 || statusCode === 207) {
                log.warn(logData, "Error response: code=%s", statusCode);

                const response = body;

                const errorCode = response.errorCode;
                const errorMsg = response.errorMsg;
                log.warn(logData, `POPError: ${errorCode} ${errorMsg}`);

                const popError = nonRetriablePopErrorToSWError(errorCode, errorMsg, response);
                if (popError) {
                    if (popError instanceof POPErrors.CancelTransactionNotExist
                        || popError instanceof POPErrors.BetTransactionAlreadyCanceled
                        || !isOfflineRetry) {
                        return reject(popError);
                    }
                    return reject(new CannotCompletePayment(`Offline retry failed with non-retriable error 
                        from POP: ${errorCode}: ${errorMsg}`));
                }
                if (isRetriablePOPError(errorCode)) {
                    if (data.moneyTransArray
                        && data.moneyTransArray[0]
                        && data.moneyTransArray[0].transCategory === POPAdapter.TRANS_CATEGORY_WAGER
                        && data.moneyTransArray[0].transAmt > 0
                        && data.gameCycleStarted
                        && !data.gameCycleFinished) {
                        return reject(new RequireRefundBetError());
                    }
                    if ((!data.cancelTransArray && nRetry === this.retryPolicy.maxAttempts) ||
                        (data.cancelTransArray && nRetry === this.retryPolicy.cancelMaxAttempts)) {
                        log.info(logData, "POST %s aborted after %d retries due to: code=%s",
                            url, nRetry, statusCode);
                        return reject(new AdapterErrors.MerchantInternalError(errorMsg));
                    }
                    log.info(logData, "Retrying %s due to: code=%s, errCode=%s", url, statusCode, errorCode);
                    setTimeout(this.postWithRetries.bind(this),
                        retryDelay,
                        baseUrl,
                        url,
                        data,
                        resolve,
                        reject,
                        nRetry + 1,
                        retryDelay * 2,
                        proxy);
                } else {
                    return reject(new POPErrors.POPError(500, errorMsg));
                }
            } else {
                return reject(new POPErrors.POPError(500, "Unexpected behaviour"));
            }
        });
    }

    private async getBalanceFromPopResponseOrFetch(response: POPTransactionResponse,
                                                   merchant: MerchantInfo,
                                                   authToken: POPAuthTokenData,
                                                   balanceChange: number = 0): Promise<Balance> {
        if (response && response.accountBalance) {
            const balance = await this.parseBalance(response, authToken, merchant.params);
            if (!balance.previousValue) {
                balance.previousValue = balance.main + balanceChange;
            }
            return balance;
        } else {
            return this.fetchAndParseBalance(merchant, authToken, balanceChange);
        }
    }

    private async fetchAndParseBalance(merchant: MerchantInfo, authToken: POPAuthTokenData, balanceChange: number = 0) {
        const balances = await this.getBalances(merchant, authToken);
        if (!balances) {
            log.error("Cant receive balance from POP");
            // If we do not receive balance from payment response and
            // from getBalance response it looks like invalid ticket
            return Promise.reject(new POPErrors.InvalidPOPValidateTicketResponse());
        }

        const currencies = Object.keys(balances);
        if (currencies.length === 0) {
            log.error("POP returns empty currency list");
            return Promise.reject(new AdapterErrors.MerchantInternalError("Currency list is empty"));
        }

        const currentBalance = balances[currencies[0]]; // Take first balance.
        currentBalance.previousValue = currentBalance.main + balanceChange;

        return currentBalance;
    }

    private async validateInitRequest(initRequest) {
        if (initRequest.previousStartTokenData) {
            return;
        }

        if (initRequest.playmode !== "fun") {
            if (!initRequest.ticket || !initRequest.username) {
                return Promise.reject(new AdapterErrors.ValidationError("missing ticket or username parameter"));
            }
        }
    }

    public async finalizeGame(merchant: MerchantInfo,
                              gameTokenData: POPAuthTokenData,
                              request: FinalizeGameRequest): Promise<Balance> {
        if (merchant.params.walletType === WALLET_TYPE.UPDATE_WIN_ON_ROUND_FINISHED) {
            try {
                const win = 0;
                const { response } = await this.postCredit(merchant, gameTokenData, request as any, win, true);

                if (response?.accountBalance?.balanceArray?.length) {
                    const mainBalance = response.accountBalance.balanceArray.find(e =>
                        e.balanceType === POPAdapter.BALANCE_NAME_MAIN);

                    if (!mainBalance) {
                        log.warn("POP: no balance in close round request");
                        return this.getFakeBalance();
                    }

                    const currency = Currencies.get(response.accountBalance.currencyCode);

                    return {
                        main: currency.toMajorUnits(mainBalance.balanceAmt)
                    };
                }
                return this.getFakeBalance();
            } catch (err) {
                if (err instanceof CannotCompletePayment) {
                    return this.getFakeBalance();
                }

                throw err;
            }
        }
    }

    protected decorateBalanceWithSessionTimer(balance: Balance, authToken: POPAuthTokenData) {
        if (authToken.timeInLoginSession !== undefined) {
            const extraData: ExtraData = balance.extraData || {};
            balance.extraData = extraData;
            const msDifference: number = new Date().getTime() - authToken.startedAt;
            extraData.timeInLoginSession = authToken.timeInLoginSession + Math.round(msDifference / 1000);
        }
    }

    @measure({ name: "POPAdapter.commitBonusPayment", isAsync: true })
    public async commitBonusPayment(merchant: MerchantInfo,
                                    authToken: POPAuthTokenData,
                                    request: PaymentRequest): Promise<Balance> {
        if (request.finalizationType) {
            return this.getBalanceForFinalize(merchant, authToken);
        }

        const params = merchant.params as POPParams;
        const currency = Currencies.get(authToken.currency);

        const trxId = request.transactionId;

        const winData: POPOfflineTransactionRequest = {
            ...this.getDataForTransactionRequest(authToken, request),
            moneyTransArray: [
                {
                    transSeq: typeof request.eventId === "number" ? request.eventId : 1,
                    transId: trxId.serialId.toString() + OPERATION_ID.WIN.toString(),
                    transAmt: currency.toMinorUnits(request.win),
                    transType: POPAdapter.TRANS_TYPE_BONUS_CREDIT,
                    transDateTime: new Date(trxId.timestamp).toISOString(),

                    transCategory: POPAdapter.TRANS_CATEGORY_OFFLINE_WIN,
                    transDesc: this.getGameType(request) + " win",
                }
            ]
        };

        await this.post<POPTransactionResponse>(
            params.serverUrl,
            "/gamesession/moneytransactions",
            winData,
            merchant?.proxy?.url);

        const resultBalance: Balance = await this.getBalanceForFinalize(merchant, authToken);

        const previousValue = resultBalance.main - request.win;
        resultBalance.previousValue = previousValue > 0 ?
                                      Currencies.get(authToken.currency).toFixedByExponent(previousValue) :
                                      0;

        return resultBalance;
    }

    private async getGameConfiguration(merchant: MerchantInfo,
                                       gameToken: POPAuthTokenData): Promise<POPGameConfigurationResponse> {
        const data: POPGameConfigurationRequest = {
            _meta: this.getMeta(gameToken),
            playerId: gameToken.playerCode,
            gameDocumentId: `SKW_${gameToken.gameCode}`,
            gameId: gameToken.gameCode,
            secureToken: gameToken.secureToken,
            skinId: gameToken.skinId,
            currency: gameToken.currency,
        };

        return this.post<POPGameConfigurationResponse>(merchant.params.serverUrl,
            "/gamesession/getgameconfiguration",
            data,
            merchant?.proxy?.url);
    }
}

export class POPExtraData {
    private static getMsgType(message: POPMessage): number {
        if ("nonIntrusive" in message) {
            if (message.nonIntrusive) {
                return ClientMessageType.toaster;
            } else {
                return ClientMessageType.info;
            }
        }
    }

    public static getExtraDataFromResponse(
        response: POPPlayerBalance,
        gameAction: PopupButtonGameAction = PopupButtonGameActions.close,
        params?: MerchantParams): ExtraData {

        if (!response || !response.accountBalance) {
            return;
        }
        const okButton = PopupButtonImpl.create()
            .setLabel("OK")
            .setGameAction(gameAction)
            .setTranslate(true);

        const closePopupButton = PopupButtonImpl.create()
            .setLabel("Close")
            .setGameAction(gameAction)
            .setTranslate(true);

        const messageArray = response.accountBalance.messageArray;

        if (messageArray && messageArray.length) {
            const messages = messageArray.filter(msg => msg.msgType.toLowerCase() === "message");
            const popCMAExtraData: MrchExtraDataImpl = MrchExtraDataImpl.create();

            for (const message of messages) {
                const msgType = POPExtraData.getMsgType(message);
                const popCMAMessage = ExtraMessageImpl.create()
                    .setButtons(msgType === ClientMessageType.toaster ? [closePopupButton] : [okButton])
                    .setTranslate(false)
                    .setMessage(message.accountMsg)
                    .setMessageTitle(POPNotificationsHelper.getMessageTitle(params))
                    .setTranslateTitle(true)
                    .setMessageType(msgType);
                popCMAExtraData.addExtraMessage(popCMAMessage);
            }

            if (messages.length) {
                return popCMAExtraData;
            }
        }
    }
}
