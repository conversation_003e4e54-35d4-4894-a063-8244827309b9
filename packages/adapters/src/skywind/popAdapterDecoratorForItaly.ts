import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>er,
    POPAuthTokenData,
    POPCreateBrokenGame,
    POPGameInitRequest,
    POPLogoutRequest,
    POPStartGameTokenData
} from "./popadapter";
import { logging, measures } from "@skywind-group/sw-utils";
import {
    GameLogoutGameState,
    GameLogoutRequest,
    GameLogoutResponse,
    MerchantAdapterDecorator,
    MerchantInfo
} from "@skywind-group/sw-wallet-adapter-core";
import { POPErrors } from "./errors/popErrors";
import measure = measures.measure;

const log = logging.logger("sw-management-adapters:pop-adapter");

export class POPAdapterDecoratorForItaly
    extends MerchantAdapterDecorator<POPGameInitRequest, POPStartGameTokenData, POPAuthTokenData, POPAdapter> {

    @measure({ name: "POPAdapter.logoutGame", isAsync: true })
    public async logoutGame(merchant: MerchantInfo,
                            gameTokenData: POPAuthTokenData,
                            request: GameLogoutRequest): Promise<GameLogoutResponse> {
        await this.adapter.logoutPlayer(merchant, gameTokenData);
        const result = await this.doLogoutGameForItaly(merchant, gameTokenData, request);
        await this.finalizeSession(merchant, gameTokenData);
        return result;
    }

    // overriding popadapter.logoutGame() to add expirationDateTime to request
    public async doLogoutGameForItaly(merchant: MerchantInfo,
                                      gameTokenData: POPAuthTokenData,
                                      request: GameLogoutRequest): Promise<GameLogoutResponse> {
        const params = merchant.params;
        if (!params.gameLogoutOptions || request.state !== GameLogoutGameState.UNFINISHED) {
            return { requireLogin: false };
        } else {
            const createBrokenGameRequest = this.makeCreateBrokenGameRequest(gameTokenData, request);
            try {
                await this.adapter.post(params.serverUrl, "/gamesession/createbrokengame",
                    createBrokenGameRequest, merchant?.proxy?.url);
            } catch (e) {
                // in case of non recoverable error we can only skip logout
                if (e instanceof POPErrors.POPError) {
                    log.warn(e, createBrokenGameRequest, "Skip 'createBrokenGame' because of non-recoverable error");
                    return { requireLogin: false };
                }

                return Promise.reject(e);
            }
            return { requireLogin: true };
        }
    }

    private makeCreateBrokenGameRequest(gameTokenData: POPAuthTokenData,
                                        request: GameLogoutRequest): POPCreateBrokenGame {
        const createBrokenGameRequest = this.adapter.makeCreateBrokenGameRequest(gameTokenData, request);
        const expirationDateTime = new Date();
        // 6h is the broken game expiration time for Italy
        expirationDateTime.setHours(expirationDateTime.getHours() + 6);
        createBrokenGameRequest.expirationDateTime = expirationDateTime.toISOString();
        return createBrokenGameRequest;
    }

    @measure({ name: "POPAdapter.finalizeSession", isAsync: true })
    protected async finalizeSession(merchant: MerchantInfo,
                                    gameTokenData: POPAuthTokenData): Promise<void> {
        const finalizeGameRequest: POPLogoutRequest = {
            skinId: gameTokenData.skinId,
            playerId: gameTokenData.playerCode,
            secureToken: gameTokenData.secureToken,
            _meta: this.adapter.getMeta(gameTokenData)
        };
        const serverUrl: string = await this.adapter.getUrl(merchant);
        /*
        From PT spec: Response is HTTP200 with no body.
        From prod log:Response is HTTP400 ERR004 Incomplete or Malformed Request: Could not find window session with
        given windowsessionId
         */
        try {
            await this.adapter.post(serverUrl,
                "/gamesession/finalize",
                finalizeGameRequest,
                merchant?.proxy?.url);
        } catch (e) {
            if (e instanceof POPErrors.IncompleteRequest) {
                log.warn(e, "Non-documented error from pop, ignore");
                return;
            }
            throw e;
        }
    }
}
