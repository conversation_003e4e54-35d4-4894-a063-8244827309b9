import { RegulationSessionStorage } from "@skywind-group/sw-adapter-regulation-support";
import { logging } from "@skywind-group/sw-utils";
import { AdapterErrors } from "./errors/errors";
import { POPGameInitRequest, POPStartGameTokenData } from "./popadapter";
import { MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";

const log = logging.logger("sw-management-adapters:pop-adapter:relaunch-helper");

// tslint:disable-next-line:no-empty-interface
interface POPGameRelaunchInfo {}

/**
 * Reusing RegulationSessionStorage for redis access.
 * Creates key in redis with ttl that serves as an indicator that game was already launched with same token.
 *
 * !NB, this mechanism will not help with refresh of game client when verifyPlayerOnGameStart=false (because
 * verifyplayersession call is triggered only during getGameUrl process in this case), it will help only when our game
 * is relaunched with the same data via POP Launcher or in other words, when refresh triggers verifyplayersession call.
 *
 * Intended usage:
 * 1) Upon successful verifyplayersession response, if crosslaunchurl was present in launch request, we store entry in
 * redis.
 * 2) During relaunch, when we get TokenExpiredError, we check if redis entry exists. If it does, then we throw
 * RedirectionError that should cause redirection on game client, if not - throw initial TokenExpiredError.
 */
export class POPGameRelaunchHelper extends RegulationSessionStorage<POPGameRelaunchInfo> {

    private ttlSecs: number = 3600; // 60 mins

    constructor() {
        super("");
    }

    public buildSessionKey(brandId, playerCode, token) {
        return `pop-game-relaunch:${brandId}:${playerCode}:${token}`;
    }

    public async storePopLaunchTicketAfterFirstUse(merchant: MerchantInfo,
                                                   initRequest: POPGameInitRequest): Promise<void> {
        await this.createRedisEntry(merchant.brandId, initRequest.username, initRequest.ticket);
    }

    public async storePopTokenAfterFirstGameStart(merchant: MerchantInfo,
                                                  startToken: POPStartGameTokenData): Promise<void> {
        await this.createRedisEntry(merchant.brandId, startToken.playerCode, startToken.secureToken);
    }

    private async createRedisEntry(brandId: number,
                                   playerCode: string,
                                   token: string): Promise<void> {
        try {
            await super.setSessionWithExpiry(this.buildSessionKey(brandId, playerCode, token), {}, this.ttlSecs);
        } catch (err) { // we suppress errors because without this session it should function as it used to
            log.warn(err, "Failed to create redis relaunch key of POP");
        }
    }

    public async findPopToken(merchant: MerchantInfo, startToken: POPStartGameTokenData): Promise<POPGameRelaunchInfo> {
        return this.get(merchant.brandId, startToken.playerCode, startToken.secureToken);
    }

    private async get(brandId: number, playerCode: string, token: string): Promise<POPGameRelaunchInfo> {
        try {
            const key = this.buildSessionKey(brandId, playerCode, token);
            return await super.getSession(key);
        } catch (err) { // we suppress errors because without this session it should function as it used to
            log.warn(err, "Failed to get redis relaunch key of POP");
        }
    }

    public async resetTtl(brandId: number, playerCode: string, token: string): Promise<void> {
        try {
            const key = this.buildSessionKey(brandId, playerCode, token);
            await super.setSessionWithExpiry(key, {}, this.ttlSecs);
        } catch (err) { // we suppress errors because without this session it should function as it used to
            log.warn(err, "Failed to reset ttl for relaunch key of POP");
        }
    }

    /**
     *
     */
    public async useCrossLaunchUrlIfTicketWasOnceUsed(merchant: MerchantInfo,
                                                      initRequest: POPGameInitRequest): Promise<any> {
        const info = await this.getAndDeleteIfExists(merchant.brandId, initRequest.username, initRequest.ticket);
        if (info && initRequest.crosslaunchurl) {
            return Promise.reject(new AdapterErrors.GameClientRedirectRequiredError(initRequest.crosslaunchurl));
        }
        return Promise.reject(new AdapterErrors.GameTokenExpired());
    }

    /**
     * If redis entry exists - get it, delete it from redis, and throw error with redirection link.
     */
    private async getAndDeleteIfExists(brandId: number,
                                       playerCode: string,
                                       token: string): Promise<POPGameRelaunchInfo> {
        try {
            const key = this.buildSessionKey(brandId, playerCode, token);
            const info = await super.getSession(key);
            if (info) {
                await super.delSession(key);
            }
            return info;
        } catch (err) { // we suppress errors because without this session it should function as it used to
            log.warn(err, "Failed to get and remove redis relaunch key of POP");
        }
    }
}

const instance = new POPGameRelaunchHelper();

export function getPOPGameRelaunchHelper() {
    return instance;
}
