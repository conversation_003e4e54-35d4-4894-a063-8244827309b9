import {
    escapeSomeHtmlChars,
    ExtraMessageImpl,
    MrchExtraData,
    MrchExtraDataImpl,
    PlayerActionServerCallImpl,
    PlayerRegulatoryActionsAtServer,
    PopupButtonGameActions,
    PopupButtonImpl,
    SWError
} from "@skywind-group/sw-wallet-adapter-core";
import { PromoWalletErrors } from "@skywind-group/sw-management-promo-wallet";
import { WalletErrors } from "@skywind-group/sw-management-wallet";
import { AdapterErrors } from "./errors";

export namespace IPMErrors {
    // tslint:disable:variable-name
    export class IPMError extends AdapterErrors.MerchantIntegrationError {
        public error_code: number;
        public error_msg: string;

        constructor(error_code: number, error_msg: string) {
            super(`code=${error_code}`);
            this.error_code = error_code;
            this.error_msg = error_msg;
        }
    }

    export class RepeatingSelfExclusionError extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 1506, "Can't execute operation. Player is permanently blocked.");
        }
    }

    export class PlayerIsSuspended extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 712, "Player is suspended");
        }
    }

    export class PlayerNotFoundError extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(404, 102, "Player not found");
        }
    }
}

export function isDuplicateTrxError(impErrorCode: number): boolean {
    return impErrorCode === 1;
}

export function IPMErrorToSWError(errorCode: number, errorMsg?: string, rci?: number): SWError {
    switch (errorCode) {
        case 5:
            return new AdapterErrors.RequestHashCodeError();
        case -1:
            return new AdapterErrors.MerchantInternalError(`code=${errorCode}`);
        case -2:
            return new IPMErrors.PlayerNotFoundError();
        case -3:
            return new AdapterErrors.GameTokenExpired();
        case -301:
            return new IPMErrors.PlayerIsSuspended();
        case -302:
            // TODO: need to add more errors for regulations
            return new AdapterErrors.ExceedBetLimit();
        case -4:
            return new WalletErrors.InsufficientBalanceError();
        case -5:
            return new PromoWalletErrors.InsufficientFreebet();
        case -6:
            return new PromoWalletErrors.InvalidFreebet();
        case -7:
            return new AdapterErrors.TransactionNotFound();

        // responsible gaming errors
        case -1500:
            return new AdapterErrors.PlayerOnTimeoutError();
        case -1501:
            return new AdapterErrors.PlayerIsSelfExcludedError();
        case -1502:
            return new AdapterErrors.RGPlayerDepositLimitReachedError();
        case -1503:
            return new AdapterErrors.RGSessionTimeLimitReachedError();
        case -1504:
            return new AdapterErrors.RGDailyTimeLimitReachedError();
        case -1505:
            return new AdapterErrors.RGRealityCheckError(
                escapeSomeHtmlChars(errorMsg), ipmErrorExtraDataStore.getRealityCheckExtraData());
        case -1506:
            return new IPMErrors.RepeatingSelfExclusionError();
        case -1507:
            return new AdapterErrors.RGDailyLossLimitExceedWarningError();
        case -1508:
            return new AdapterErrors.RGDailyLossLimitReachedError();
        case -1509:
            return new AdapterErrors.RGWeeklyLossLimitExceedWarningError();
        case -1510:
            return new AdapterErrors.RGWeeklyLossLimitReachedError();
        case -1511:
            return new AdapterErrors.RGMonthlyLossLimitExceedWarningError();
        case -1512:
            return new AdapterErrors.RGMonthlyLossLimitReachedError();
        case -1513:
            return new AdapterErrors.RGSessionLossLimitReachedError();
        case -1514:
            return getRegulatoryCustomError(errorMsg);
        case -1515:
            return new AdapterErrors.RGSessionBetLimitReachedError();
        case -1516:
            return new AdapterErrors.RGDailyBetLimitReachedError();
        case -1517:
            return new AdapterErrors.RGWeeklyBetLimitReachedError();
        case -1518:
            return new AdapterErrors.RGMonthlyBetLimitReachedError();
        case -1519:
            return new AdapterErrors.RGWithdrawLimitReachedError();
        case -1520:
            return new AdapterErrors.RGMandatoryLimitMissingError();
        case -1521:
            return new AdapterErrors.RGSessionGameGroupLossLimitReachedError();
        case -1522:
            return new AdapterErrors.RGDailyGameGroupLossLimitReachedError();
        case -1523:
            return new AdapterErrors.RGWeeklyGameGroupLossLimitReachedError();
        case -1524:
            return new AdapterErrors.RGMonthlyGameGroupLossLimitReachedError();
        case -1525:
            return new AdapterErrors.RGMaxBetExceededForBonusMoneyError();
        case -1526:
            return new AdapterErrors.RGDailyTimeLimitExceeded();
        case -1527:
            return new AdapterErrors.RGWeeklyTimeLimitExceeded();
        case -1528:
            return new AdapterErrors.RGMonthlyTimeLimitExceeded();
        case -1529:
            return new AdapterErrors.RGRealityCheckWithTimeError(rci);
        case -1530:
            return new AdapterErrors.RGRealityCheckLimitReachedError();
        case -1531:
            return new AdapterErrors.RGRealityCheckLossLimitReachedError();
        // end of responsible gaming errors
        case -1601:
            return new AdapterErrors.BetRejectedError();
        default:
            return new IPMErrors.IPMError(errorCode, errorMsg);
    }
}

function getRegulatoryCustomError(errorMsg: string): SWError {
    const regulatoryCustomError = new AdapterErrors.RGRegulatoryCustomError(escapeSomeHtmlChars(errorMsg),
        ipmErrorExtraDataStore.getRegulatoryCustomErrorExtraData());
    if (errorMsg && typeof errorMsg === "string") {
        regulatoryCustomError.dontTranslate();
    }
    return regulatoryCustomError;
}

class IPMErrorExtraDataStore {

    private realityCheckExtraData: MrchExtraData;
    private regulatoryCustomErrorExtraData: MrchExtraData;

    public getRealityCheckExtraData(): MrchExtraData {
        if (!this.realityCheckExtraData) {
            this.initRealityCheckExtraData();
        }
        return this.realityCheckExtraData;
    }

    public getRegulatoryCustomErrorExtraData(): MrchExtraData {
        if (!this.regulatoryCustomErrorExtraData) {
            this.initRegulatoryCustomErrorExtraData();
        }
        return this.regulatoryCustomErrorExtraData;
    }

    private initRealityCheckExtraData() {
        const okButtonServerCall = PlayerActionServerCallImpl.create()
            .setRegulatoryAction(PlayerRegulatoryActionsAtServer.resetRealityCheck);

        const stopButtonServerCall = PlayerActionServerCallImpl.create()
            .setRegulatoryAction(PlayerRegulatoryActionsAtServer.closeSession);

        const okButton = PopupButtonImpl.create()
            .setLabel("Keep Playing")
            .setGameAction(PopupButtonGameActions.continue)
            .setServerCall(okButtonServerCall)
            .setTranslate(true);

        const stopButton = PopupButtonImpl.create()
            .setLabel("Quit")
            .setGameAction(PopupButtonGameActions.lobby)
            .setServerCall(stopButtonServerCall)
            .setTranslate(true);

        const realityCheckExtraMessage = ExtraMessageImpl.create()
            .setButtons([okButton, stopButton])
            .setTranslate(true);

        this.realityCheckExtraData = MrchExtraDataImpl.create().addExtraMessage(realityCheckExtraMessage);
    }

    private initRegulatoryCustomErrorExtraData() {
        const okButton = PopupButtonImpl.create()
            .setLabel("OK")
            .setGameAction(PopupButtonGameActions.refresh)
            .setTranslate(true);
        const extraMessage = ExtraMessageImpl.create().setButtons([okButton]);
        this.regulatoryCustomErrorExtraData = MrchExtraDataImpl
            .create()
            .setUseServerMessage(true)
            .addExtraMessage(extraMessage);
    }
}

const ipmErrorExtraDataStore = new IPMErrorExtraDataStore();
