import { escapeSomeHtmlChars, SWError } from "@skywind-group/sw-wallet-adapter-core";
import { errors } from "@skywind-group/sw-utils";

export type ExtraData = any;

export namespace AdapterErrors {

    export class SWBaseAdapterError extends SWError {
        public translateMessage: boolean = true;

        public dontTranslate(): SWBaseAdapterError {
            this.translateMessage = false;
            return this;
        }

        public decorateResponseWithData(errorResponse) {
            return errorResponse;
        }

        public setExtraData(extra: ExtraData): AdapterErrors.SWBaseAdapterError {
            this.extraData = extra;
            return this;
        }
    }

    export class RequestHashCodeError extends SWBaseAdapterError {
        constructor() {
            super(403, 5, "Invalid hash code of the request");
        }
    }

    export class MerchantIntegrationError extends SWBaseAdapterError {
        constructor(reason?: string) {
            super(500,
                507,
                "Error during integration with merchant" + (reason ? `: ${escapeSomeHtmlChars(reason)}` : ""));
            this.data.reason = reason || "";
        }
    }

    export class MerchantInternalError extends SWBaseAdapterError {
        constructor(reason?: string) {
            super(500, 506, "Merchant internal error" + (reason ? `: ${escapeSomeHtmlChars(reason)}` : ""));
            this.data.reason = reason || "";
        }
    }

    export class MerchantNonRetriableError extends SWBaseAdapterError {
        constructor(reason?: string, extraData?: ExtraData) {
            super(400,
                762,
                "Merchant non-retriable error" + (reason ? `: ${escapeSomeHtmlChars(reason)}` : ""),
                undefined,
                extraData);
            this.data.reason = reason || "";
        }
    }

    export class ExceedBetLimit extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 752, "Bet limit was exceeded");
        }
    }

    export class TransactionNotFound extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(404, 600, "Transaction not found");
        }
    }

    export class GameTokenExpired extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 323, "Game token expired", errors.ERROR_LEVEL.INFO);
        }
    }

    export class MerchantMisconfiguration extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(500, 505, "Merchant should have serverUrl in params");
        }
    }

    export class OperationForbidden extends SWBaseAdapterError {
        constructor(reason: string = "") {
            super(403, 206, reason ? "Forbidden" : `Forbidden. ${reason}`);
            this.data.reason = reason;
        }
    }

    export class ValidationError extends SWBaseAdapterError {
        constructor(message: string) {
            super(400, 40, `Validation error: ${message}`, errors.ERROR_LEVEL.WARN);
            this.data.messages = message;
        }
    }

    export class InsufficientBalanceError extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 91, "Player does not have sufficient balance to perform an operation");
        }
    }

    export class GameClientRedirectRequiredError extends SWBaseAdapterError {
        constructor(redirectionURL: string) {
            const extraData = { redirectionURL };
            super(400, 799, "Redirection of game client is required", undefined, extraData);
        }
    }

    /******************************************************************************************************************
     *************************************** Responsible gaming errors ************************************************
     ******************************************************************************************************************/

    export class PlayerOnTimeoutError extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 1500, "Can't execute operation. Player is on timeout.");
        }
    }

    export class PlayerIsSelfExcludedError extends AdapterErrors.SWBaseAdapterError {
        constructor(message: string = "Can't execute operation. Player is self-excluded.") {
            super(403, 1501, message);
        }
    }

    export class RGPlayerDepositLimitReachedError extends SWBaseAdapterError {
        constructor() {
            super(403, 1502, "Can't execute operation. Player has reached his deposit limit.");
        }
    }

    // generic error for RG session limit time exceeding
    export class RGSessionTimeLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403, 1503, message ? message : "You have reached your session time limit.", undefined, extraData);
        }
    }

    export class RGDailyTimeLimitReachedError extends SWBaseAdapterError {
        constructor() {
            super(403, 1504,
                "You have reached your daily time limit you previously set. Please come back and play again tomorrow.");
        }
    }

    export class RGRealityCheckError extends SWBaseAdapterError {

        constructor(message?: string,
                    extraData?: ExtraData) {
            super(403, 1505,
                message ? message :
                "We would like to notify you that you've already played for your reality check interval.",
                undefined,
                extraData);
        }
    }

    export class RGDailyLossLimitExceedWarningError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403,
                1507,
                message ? message : "The bet amount you selected exceeds your loss limit for today.",
                undefined,
                extraData);
        }
    }

    export class RGDailyLossLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403, 1508, message ? message : "You've reached your loss limit for today.", undefined, extraData);
        }
    }

    export class RGWeeklyLossLimitExceedWarningError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403,
                1509,
                message ? message : "The bet amount you selected exceeds your loss limit for this week.",
                undefined,
                extraData);
        }
    }

    export class RGWeeklyLossLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403, 1510, message ? message : "You've reached your loss limit for this week.", undefined, extraData);
        }
    }

    export class RGMonthlyLossLimitExceedWarningError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403,
                1511,
                message ? message : "The bet amount you selected exceeds your loss limit for this month.",
                undefined,
                extraData);
        }
    }

    export class RGMonthlyLossLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403,
                1512,
                message ? message : "You've reached your loss limit for this month.",
                undefined,
                extraData);
        }
    }

    export class RGSessionLossLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403, 1513, message ? message : "You've reached your session loss limit.", undefined, extraData);
        }
    }

    export class RGRegulatoryCustomError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403, 1514, message ? message : "Player has exceeded regulatory limits.", undefined, extraData);
        }
    }

    export class RGSessionBetLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403, 1515, message ? message : "You've reached your session bet limit.", undefined, extraData);
        }
    }

    export class RGDailyBetLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403, 1516, message ? message : "You've reached your bet limit for today.", undefined, extraData);
        }
    }

    export class RGWeeklyBetLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403, 1517, message ? message : "You've reached your bet limit for this week.", undefined, extraData);
        }
    }

    export class RGMonthlyBetLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403, 1518, message ? message : "You've reached your bet limit for this month.", undefined, extraData);
        }
    }

    export class RGWithdrawLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403, 1519, message ? message : "You've reached your withdraw limit.", undefined, extraData);
        }
    }

    export class RGMandatoryLimitMissingError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403, 1520, message ? message : "Mandatory limit missing.", undefined, extraData);
        }
    }

    export class RGSessionGameGroupLossLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403,
                1521,
                message ? message : "You've reached your session game group loss limit.",
                undefined,
                extraData);
        }
    }

    export class RGDailyGameGroupLossLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403,
                1522,
                message ? message : "You've reached your game group loss limit for today.",
                undefined,
                extraData);
        }
    }

    export class RGWeeklyGameGroupLossLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403,
                1523,
                message ? message : "You've reached your game group loss limit for this week.",
                undefined,
                extraData);
        }
    }

    export class RGMonthlyGameGroupLossLimitReachedError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403,
                1524,
                message ? message : "You've reached your game group loss limit for this month.",
                undefined,
                extraData);
        }
    }

    export class RGMaxBetExceededForBonusMoneyError extends SWBaseAdapterError {
        constructor(message?: string, extraData?: ExtraData) {
            super(403, 1525, message ? message : "You have exceeded max bet for bonus funds.", undefined, extraData);
        }
    }

    export class RGDailyTimeLimitExceeded extends SWError {
        constructor(message: string = "You have exceeded the daily time limit.", extraData?: ExtraData) {
            super(403, 1526, message, undefined, extraData);
        }
    }

    export class RGWeeklyTimeLimitExceeded extends SWError {
        constructor(message: string = "You have exceeded the weekly time limit.", extraData?: ExtraData) {
            super(403, 1527, message, undefined, extraData);
        }
    }

    export class RGMonthlyTimeLimitExceeded extends SWError {
        constructor(message: string = "You have exceeded the monthly time limit.", extraData?: ExtraData) {
            super(403, 1528, message, undefined, extraData);
        }
    }

    export class RGRealityCheckWithTimeError extends RGRealityCheckError {
        constructor(realityCheckInterval: number, message?: string, extraData?: ExtraData) {
            super(
                message ? message :
                `Your session has now exceeded ${realityCheckInterval} minutes.` +
                    "We would like to notify you that you've already played for your reality check interval.",
                extraData
            );
        }
    }

    export class RGRealityCheckLimitReachedError extends SWError {
        constructor(message: string = "You have reached your limit.", extraData?: ExtraData) {
            super(403, 1530, message, undefined, extraData);
        }
    }

    export class RGRealityCheckLossLimitReachedError extends SWError {
        constructor(message: string = "You have reached your loss limit.", extraData?: ExtraData) {
            super(403, 1531, message, undefined, extraData);
        }
    }

    export class BetRejectedError extends SWError {
        constructor(message: string = "Your bet was not accepted and your account has not been charged.") {
            super(403, 1601, message);
        }
    }
}
