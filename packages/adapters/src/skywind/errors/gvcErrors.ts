import {
    escapeSomeHtmlChars,
    ExtraData,
    ExtraMessageImpl,
    MrchExtraDataImpl,
    PopupButtonGameAction,
    PopupButtonGameActions,
    PopupButtonImpl,
    MrchExtraData
} from "@skywind-group/sw-wallet-adapter-core";
import { AdapterErrors } from "./errors";

export namespace GVCErrors {

    export class GameTokenError extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 322, "Game token error");
        }
    }

    export class CountryIsRestricted extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 701, "Country is restricted");
        }
    }

    export class GameNotFoundError extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(404, 240, "Game not found");
        }
    }

    export class CurrencyMismatch extends AdapterErrors.SWBaseAdapterError {
        constructor(message: string = "Player default currency doesn't match with requested") {
            super(400, 755, message);
        }
    }

    export class IpLocationError extends AdapterErrors.SWBaseAdapterError {
        constructor(message: string) {
            super(500, 700, `Ip location lookup error: ${message}`);
        }
    }

    export class CurrencyNotFoundError extends AdapterErrors.SWBaseAdapterError {
        constructor(message: string = "Currency not found") {
            super(404, 85, message);
        }
    }

    export class MerchantDuplicateTransactionError extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            // 500 error code as we don't want our game server to do a rollback on such response
            super(500, 757, "Merchant has already received transaction request");
        }
    }

    export class PlayerSessionNotFoundError extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(404, 105, "Session not found");
        }
    }

    export class GVCUnknownError extends AdapterErrors.MerchantNonRetriableError {
        private errorMsg: string;

        constructor(errorCode: number, errorMsg: string) {
            super(`code=${errorCode}`);
            this.errorMsg = errorMsg;
        }
    }
}

interface GVCExtendedParam {
    key: string;
    value: string;
}

export function GVCErrorToSWError(gvcErrorCode: number, gvcErrorMsg?: string,
                                  extendedParams?: GVCExtendedParam[]): AdapterErrors.SWBaseAdapterError {
    gvcErrorMsg = cleanGVCMessage(gvcErrorMsg);
    switch (gvcErrorCode) {
        case -2: // "statusCode": "-2", "statusMessage": "No data found for txn id : ahNIumrEC24AAALxahNIuzLayeo=_bet"
            return new AdapterErrors.TransactionNotFound();
        case -1:
            return new AdapterErrors.MerchantInternalError(); // GVCTechnicalError();
        case 101:
            return new GVCErrors.GameTokenError();
        case 102:
            return new GVCErrors.CountryIsRestricted();
        case 103:
            return new GVCErrors.GameNotFoundError();
        case 104:
            return new AdapterErrors.InsufficientBalanceError();
        case 105:
            return new GVCErrors.CurrencyMismatch();
        case 106:
            return new AdapterErrors.RGDailyTimeLimitReachedError();
        case 107:
            return new AdapterErrors.MerchantNonRetriableError(); // GVCBetNotFound();
        case 108:
            // GVCFraudUserEncountered();
            return new AdapterErrors.MerchantNonRetriableError();
        case 109:
            return new AdapterErrors.PlayerIsSelfExcludedError();
        case 110:
            return new AdapterErrors.PlayerOnTimeoutError(); // GVCServiceClosureByPlayer();
        case 111:
            return new AdapterErrors.PlayerOnTimeoutError(); // GVCServiceClosureByAgent();
        case 112:
            return new AdapterErrors.PlayerOnTimeoutError(); // GVCServiceClosureCoolOff();
        case 113:
            return new GVCErrors.IpLocationError(
                "Sorry, we could not validate your location due to technical issues. Please retry later.");
        case 114:
            return new GVCErrors.IpLocationError(
                "Our system has indicated that you are in a region that does not allow " +
                "this operation. Due to regulatory restrictions we cannot allow you to proceed.");
        case 115:
            return new GVCErrors.CurrencyNotFoundError();
        case 116:
            return new AdapterErrors.MerchantNonRetriableError(); // GVCInvalidSkinCode();
        case 117:
            return new AdapterErrors.MerchantNonRetriableError(); // GVCNoGameInProgress();
        case 118:
            return new AdapterErrors.MerchantNonRetriableError(); // GVCBetAlreadySettled();
        case 119:
            return new AdapterErrors.ExceedBetLimit(); // GVCInvalidBet();
        case 120:
            return new GVCErrors.MerchantDuplicateTransactionError();
        case 121:
            return new GVCErrors.PlayerSessionNotFoundError();
        case 122:
            return new AdapterErrors.MerchantNonRetriableError(); // GVCInvalidAccountId();
        case 123:
            return new AdapterErrors.MerchantNonRetriableError(); // GVCBetCancelFailure();
        case 124:
            return new AdapterErrors.RGSessionTimeLimitReachedError();
        case 125:
            return new AdapterErrors.TransactionNotFound();
        case 126:
            // "Transaction to cancel has win sub transactions"
            return new AdapterErrors.MerchantNonRetriableError();
        case 127:
            // Transaction already cancelled but we use existing error as it gives same result
            return new AdapterErrors.TransactionNotFound();
        case 128:
            return new AdapterErrors.MerchantNonRetriableError();
        case 129:
            return new AdapterErrors.MerchantNonRetriableError();
        case 130:
            return new AdapterErrors.MerchantNonRetriableError();
        case 131: // Daily spending limit reached
            return new AdapterErrors.RGDailyLossLimitReachedError(gvcErrorMsg, makeExtraDataForClosingGame());
        case 132:
            return new AdapterErrors.RGDailyLossLimitExceedWarningError();
        case 133: // Weekly spending limit reached
            return new AdapterErrors.RGWeeklyLossLimitReachedError(gvcErrorMsg, makeExtraDataForClosingGame());
        case 134:
            return new AdapterErrors.RGWeeklyLossLimitExceedWarningError();
        case 135: // Monthly spending limit reached
            return new AdapterErrors.RGMonthlyLossLimitReachedError(gvcErrorMsg, makeExtraDataForClosingGame());
        case 136:
            return new AdapterErrors.RGMonthlyLossLimitExceedWarningError();
        case 137:
            return new AdapterErrors.RGDailyTimeLimitReachedError();
        case 200:
        case 201:
        case 211:
        case 212:
        case 213:
        case 214:
        case 215:
        case 216:
        case 217:
            return makeNonRetriableErrorWithGVCMessage(gvcErrorMsg);
        case 303: // Max bet limit reached
            return new AdapterErrors.RGDailyBetLimitReachedError(gvcErrorMsg, makeExtraDataForContinuingGame());
        case 2222:
            return makeErrorFromResponseExtParams(gvcErrorMsg, extendedParams);
        case 3333:
            return new AdapterErrors.RGMaxBetExceededForBonusMoneyError(
                escapeSomeHtmlChars(gvcErrorMsg),
                MrchExtraDataImpl.create().setUseServerMessage(true)
            ).dontTranslate();
        default:
            return new GVCErrors.GVCUnknownError(gvcErrorCode, gvcErrorMsg);
    }
}

function cleanGVCMessage(gvcMessage: string): string {
    if (!gvcMessage) {
        return gvcMessage;
    }
    return gvcMessage.replace("\\\\n", "\\n");
}

function makeNonRetriableErrorWithGVCMessage(gvcErrorMsg: string): AdapterErrors.SWBaseAdapterError {
    const error = new AdapterErrors.MerchantNonRetriableError(gvcErrorMsg,
        MrchExtraDataImpl.create().setUseServerMessage(true));
    error.message = gvcErrorMsg;
    return error;
}

function makeExtraDataForClosingGame(): MrchExtraData {
    const closeGameButton = PopupButtonImpl.create()
        .setLabel("Close")
        .setGameAction(PopupButtonGameActions.closeWindow)
        .setTranslate(true);
    const realityCheckExtraMessage = ExtraMessageImpl.create();
    realityCheckExtraMessage.addButton(closeGameButton);
    return MrchExtraDataImpl.create().addExtraMessage(realityCheckExtraMessage).setUseServerMessage(true);
}

// must stop reels so that player could adjust his bet
function makeExtraDataForContinuingGame(): MrchExtraData {
    const continueGameButton = PopupButtonImpl.create()
        .setLabel("Ok")
        .setGameAction(PopupButtonGameActions.stopPositions)
        .setTranslate(true);
    const realityCheckExtraMessage = ExtraMessageImpl.create();
    realityCheckExtraMessage.addButton(continueGameButton);
    return MrchExtraDataImpl.create().addExtraMessage(realityCheckExtraMessage).setUseServerMessage(true);
}

const GVC_BET_LIMIT_EXCEEDED_FEATURE_ERROR = "10207";
const GVC_SESSION_LIMIT_EXCEEDED_FEATURE_ERROR = "10206";

function makeErrorFromResponseExtParams(gvcErrorMsg?: string,
                                        extendedParams?: GVCExtendedParam[]): AdapterErrors.SWBaseAdapterError {

    const featureId = getValueFromExtendedParams(extendedParams, "FEATURE_ID");
    const gvcMessage = cleanGVCMessage(getValueFromExtendedParams(extendedParams, "MESSAGE_FOR_PLAYER")) || gvcErrorMsg;
    if (featureId === GVC_BET_LIMIT_EXCEEDED_FEATURE_ERROR) {
        return new AdapterErrors.RGSessionBetLimitReachedError(gvcMessage,
            makeExtraDataForExtMessage(PopupButtonGameActions.stopPositions));
    } else if (featureId === GVC_SESSION_LIMIT_EXCEEDED_FEATURE_ERROR) {
        return new AdapterErrors.RGSessionTimeLimitReachedError(gvcMessage,
            makeExtraDataForExtMessage(PopupButtonGameActions.lobby));
    }
    return new AdapterErrors.RGRealityCheckError(gvcMessage, MrchExtraDataImpl.create().setErrorVisibility(1));
}

function makeExtraDataForExtMessage(buttonAction: PopupButtonGameAction): ExtraData {
    const okButton = PopupButtonImpl.create()
        .setLabel("Ok")
        .setGameAction(buttonAction) // close popup to let player bet less amount
        .setTranslate(false);
    const realityCheckExtraMessage = ExtraMessageImpl.create();
    realityCheckExtraMessage.setButtons([okButton]);
    return MrchExtraDataImpl.create().addExtraMessage(realityCheckExtraMessage).setUseServerMessage(true);
}

function getValueFromExtendedParams(extendedParams: GVCExtendedParam[], keyToFind: string): string {
    let result;
    if (extendedParams && extendedParams.length) {
        result = extendedParams.find(item => item.key === keyToFind);
    }
    return result ? result.value : undefined;
}

export function isNonRetriableInResponse(response: any): boolean {
    if (response.extendedParams &&
        response.extendedParams.length &&
        response.extendedParams.some(extParam =>
            extParam["key"] && extParam["key"].toUpperCase() === "IS_RETRIABLE" && extParam["value"] === "false"
        )) {
        return true;
    }
    return false;
}

/**
 * Returns true if game should be closed on GVC errorCode as per GVC integration guide
 */
export function shouldCloseGameOnStatusCode(gvcErrorCode: number): boolean {
    switch (gvcErrorCode) {
        case 101:
        case 102:
        case 103:
        // case 104: - Insufficient balance
        case 105:
        case 106:
        case 107:
        case 108:
        case 109:
        case 110:
        case 111:
        case 112:
        case 113:
        case 114:
        case 115:
        case 116:
        case 117:
        // case 118: - bet already settled
        case 119:
        // case 120: - duplicate transaction
        case 121:
        case 122:
        // case 123: - Failed to cancel one or more bets
        case 124:
        // case 125: - Transaction to cancel not found
        // case 126: - Transaction to cancel has a WIN sub transactions
        // case 127: - Transaction already cancelled
        case 128:
        case 129:
        case 130:
        case 131:
        case 132:
        case 133:
        case 134:
        case 135:
        case 136:
        case 137:
        case 303:
            return true;
        default:
            return false;
    }
}

/**
 * Returns true if there is no sense to repeat same request due to nature of a returned error code
 */
export function isNonRetriableStatusCode(errorCode: number): boolean {
    return shouldCloseGameOnStatusCode(errorCode) || errorCode === -2 || errorCode === 107 || errorCode === 108
        || errorCode === 116 || errorCode === 117 || errorCode === 118 || errorCode === 120 || errorCode === 122
        || errorCode === 123 || errorCode === 126 || errorCode === 127 || errorCode === 128 || errorCode === 129
        || errorCode === 130 || errorCode === 2222 || errorCode === 3333;
}

export function isDuplicateTrxResponse(gvcStatusCode: number, gvcStatusMsg: string): boolean {
    return gvcStatusCode === 120 ||
        (gvcStatusCode === -1 &&
            gvcStatusMsg && gvcStatusMsg.includes("Specific jp transaction is already processed"));
}
