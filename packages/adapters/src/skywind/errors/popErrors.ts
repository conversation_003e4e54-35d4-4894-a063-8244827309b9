import { POP<PERSON>xtraData, POPPlayerBalance } from "../popadapter";
import {
    escapeSomeHtmlChars,
    ExtraMessageImpl,
    MrchExtraData,
    MrchExtraDataImpl,
    PlayerActionServerCallImpl,
    PlayerRegulatoryActionsAtServer,
    PopupButtonGameActions,
    PopupButtonImpl,
    SWError
} from "@skywind-group/sw-wallet-adapter-core";
import { AdapterErrors } from "./errors";

export namespace POPErrors {
    export class POPError extends AdapterErrors.MerchantIntegrationError {
        public errorCode: number;
        public errorMsg: string;

        constructor(errorCode: number, errorMsg: string) {
            super(`code=${errorCode}, msg=${escapeSomeHtmlChars(errorMsg)}`);
            this.errorCode = errorCode;
            this.errorMsg = errorMsg;
        }
    }

    export class InvalidRemoveService extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10001, "Invalid remote service identifier.");
        }
    }

    export class InvalidGamingPlatform extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10002, "Invalid gaming platform identifier.");
        }
    }

    export class IncompleteRequest extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(501, 10004, "Invalid remote service identifier.");
        }
    }

    export class UnknownRequest extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10007, "Unknown request.");
        }
    }

    export class ServiceUnavailable extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10008, "Invalid remote service identifier.");
        }
    }

    export class InsufficientBalance extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10025, "Invalid remote service identifier.");
        }
    }

    export class PlayerAccountBlocked extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10026, "Player account locked.");
        }
    }

    export class WagetLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10027, "Wager limit exceeded.");
        }
    }

    export class TransactionFailed extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10028, "Transaction failed.");
        }
    }

    export class UnsupportedGame extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10029, "Unsupported gameid.");
        }
    }

    export class GameCycleNotExist extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10034, "Game cycle does not exist.");
        }
    }

    export class InvalidPlayerSessionParams extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10035, "Incorrect parameters for a player session.");
        }
    }

    export class IncorrectPlayerIdOrToken extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10036, "Incorrect player identifier for secure token.");
        }
    }

    export class CancelTransactionNotExist extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10043, "Cancel request is sent for transaction that does not exist on POP side.");
        }
    }

    export class BetTransactionAlreadyCanceled extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 10044, "Bet request is sent with remote transaction code that has already been cancelled.");
        }
    }

    export class PermissionDenied extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 11001, "Insufficient API permissions for the action. Contact the POP team in" +
                " order to have more information.");
        }
    }

    export class SessionExpired extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 11004, "Session timer has expired.");
        }
    }

    export class TableSessionNotFound extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 11006, "Table session not found.");
        }
    }

    export class CantOpenRetailGame extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 11007, "Cannot open game session with retail client type because one already exists.");
        }
    }

    export class CantEndSessionWithNoBets extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 11008, "Cannot end game session which has no bet, and is " +
                "the first game session in a game table session.");
        }
    }

    export class InvalidUserNameOrPassword extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 11009, "Invalid username and password combination.");
        }
    }

    export class PlayerAlreadyLoggedIn extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 11010, "Player is already logged in. Duplicate login tried.");
        }
    }

    export class TooManyLoginAttempts extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 11011, "Player account is blocked (too many log in attempts).");
        }
    }

    export class InvalidPin extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 11012, "Player inserted invalid pin.");
        }
    }

    export class GamePlayBlockedByWallet extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 11016, "Game play blocked by wallet");
        }
    }

    export class BetNotAllowed extends AdapterErrors.SWBaseAdapterError {
        constructor(message?: string) {
            super(403, 11029, message || "Bet not allowed");
        }
    }

    export class GameCycleFinished extends AdapterErrors.MerchantInternalError {
        constructor(reason?: string) {
            super(reason);
        }
    }

    export class InactiveConfiguration extends AdapterErrors.MerchantInternalError {
        constructor(reason?: string) {
            super(reason);
        }
    }

    export class SessionLossLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12001, "Session loss limit has been reached.");
        }
    }

    export class DailyLossLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12002, "Daily loss limit has been reached.");
        }
    }

    export class WeeklyLossLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12003, "Weekly loss limit has been reached.");
        }
    }

    export class MonthlyLossLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12004, "Monthly loss limit has been reached.");
        }
    }

    export class SessionBetLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12005, "Session bet limit has been reached.");
        }
    }

    export class DaylyBetLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12006, "Daily bet limit has been reached.");
        }
    }

    export class WeeklyBetLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12007, "Weekly bet limit has been reached.");
        }
    }

    export class MonthlyBetLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12008, "Monthly bet limit has been reached.");
        }
    }

    export class WithdrawLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12009, "Withdraw limit has been reached.");
        }
    }

    export class MandatoryLimitMissing extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 12010, "Mandatory limit missing.");
        }
    }

    export class SessionGameGroupLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12011, "Session game group loss limit has been reached.");
        }
    }

    export class DailyGameGroupLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12012, "Daily game group loss limit has been reached.");
        }
    }

    export class WeeklyGameGroupLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12013, "Weekly game group loss limit has been reached.");
        }
    }

    export class MonthlyGameGroupLimitExceeded extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(403, 12014, "Monthly game group loss limit has been reached.");
        }
    }

    export class UKRealityCheck extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 12210, "UK Reality check");
        }
    }

    export class MaltaRealityCheck extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 12211, "Malta Reality check");
        }
    }

    // initially we thrown it on ERR2212, but as per POPs statement, it will come as a result of player self-exclusion
    export class ConcurrentSessionAccess extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 12212, "Previous session have not been closed.");
        }
    }

    export class CoolDownTimerActive extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 12213, "Cool-down timer is active.");
        }
    }

    export class RegulatorDeclinedOperation extends AdapterErrors.SWBaseAdapterError {
        constructor(message?: string) {
            const extraData = MrchExtraDataImpl.create().setUseServerMessage(true);
            super(403, 12214, message || "Regulator declined the operation", undefined, extraData);
        }
    }

    export class NetworkBlocked extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 13001, "Network blocked.");
        }
    }

    export class HeavyLoad extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 13001, "Server is under heavy load, internal error.");
        }
    }

    export class NoMessage extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 13001, "Error without message.");
        }
    }

    export class InvalidPOPValidateTicketResponse extends AdapterErrors.SWBaseAdapterError {
        constructor() {
            super(400, 15001, "Invalid validate ticket response object");
        }
    }

    export class AnotherGameIsOpenError extends AdapterErrors.SWBaseAdapterError {
        constructor(message?: string, extraData?: any) {
            super(400,
                1526,
                message ?
                message :
                "It is forbidden to play more than one game at the same time. Probably, you have a session with another game open.",
                undefined,
                extraData);
        }
    }
}

export function isRetriablePOPError(popErrorCode: string): boolean {
    return popErrorCode === "ERR008" || popErrorCode === "ERR3001"
        || popErrorCode === "ERR3002" || popErrorCode === "ERR3003";
}

interface MaltaRealityAndSessionTimerResponse {
    data: {
        message: string,
        logout?: boolean
    };
}

interface ErrorDataContainer {
    errorCode?: string;
    errorMsg?: string;
    logout?: boolean;
}

export function nonRetriablePopErrorToSWError(
    popErrorCode: string,
    popErrorMessage?: string,
    response?: POPPlayerBalance & ErrorDataContainer & MaltaRealityAndSessionTimerResponse): SWError {
    const messageArray = response?.accountBalance?.messageArray;
    // Basic errors
    switch (popErrorCode) {
        // Basic errors
        case "ERR001":
            return new POPErrors.InvalidRemoveService();
        case "ERR002":
            return new POPErrors.InvalidGamingPlatform();
        case "ERR004":
            return new POPErrors.IncompleteRequest();
        case "ERR007":
            return new POPErrors.UnknownRequest();
        case "ERR022":
            return new AdapterErrors.GameTokenExpired();
        case "ERR025":
            return new POPErrors.InsufficientBalance();
        case "ERR026":
            return new POPErrors.PlayerAccountBlocked();
        case "ERR027":
            return new POPErrors.WagetLimitExceeded();
        case "ERR028":
            return new POPErrors.TransactionFailed();
        case "ERR029":
            return new POPErrors.UnsupportedGame();
        case "ERR034":
            return new POPErrors.GameCycleNotExist();
        case "ERR035":
            return new POPErrors.InvalidPlayerSessionParams();
        case "ERR036":
            return new POPErrors.IncorrectPlayerIdOrToken();
        case "ERR037":
            return new AdapterErrors.InsufficientBalanceError();
        case "ERR1022":
            return new POPErrors.CancelTransactionNotExist();
        case "ERR1023":
            return new POPErrors.BetTransactionAlreadyCanceled();
        case "ERR1001":
            return new POPErrors.PermissionDenied();
        case "ERR1003":
            return new AdapterErrors.RGMaxBetExceededForBonusMoneyError(
                popErrorMessage,
                POPExtraData.getExtraDataFromResponse(response, PopupButtonGameActions.refresh)
            ).dontTranslate();
        case "ERR1004":
            return new POPErrors.SessionExpired();
        case "ERR1005":
            return new AdapterErrors.PlayerIsSelfExcludedError(popErrorMessage)
                .setExtraData(popErrorExtraDataStore.getPlayerIsSelfExcludedExtraData()).dontTranslate();
        case "ERR1006":
            return new POPErrors.TableSessionNotFound();
        case "ERR1007":
            return new POPErrors.CantOpenRetailGame();
        case "ERR1008":
            return new POPErrors.CantEndSessionWithNoBets();
        case "ERR1009":
            return new POPErrors.InvalidUserNameOrPassword();
        case "ERR1010":
            return new POPErrors.PlayerAlreadyLoggedIn();
        case "ERR1011":
            return new POPErrors.TooManyLoginAttempts();
        case "ERR1012":
            return new POPErrors.InvalidPin();
        case "ERR1016":
            return new POPErrors.GamePlayBlockedByWallet();
        case "ERR1029":
            return new POPErrors.BetNotAllowed(messageArray && messageArray.length
                                               ? messageArray[0].accountMsg
                                               : popErrorMessage);
        case "ERR1031":
            return new POPErrors.GameCycleFinished();
        case "ERR1035":
            return new POPErrors.InactiveConfiguration(response?.data?.message);
        case "ERR2001":
            return new AdapterErrors.RGSessionLossLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2002":
            return new AdapterErrors.RGDailyLossLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2003":
            return new AdapterErrors.RGWeeklyLossLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2004":
            return new AdapterErrors.RGMonthlyLossLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2005":
            return new AdapterErrors.RGSessionBetLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2006":
            return new AdapterErrors.RGDailyBetLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2007":
            return new AdapterErrors.RGWeeklyBetLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2008":
            return new AdapterErrors.RGMonthlyBetLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2009":
            return new AdapterErrors.RGWithdrawLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2010":
            return new AdapterErrors.RGMandatoryLimitMissingError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2011":
            return new AdapterErrors.RGSessionGameGroupLossLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2012":
            return new AdapterErrors.RGDailyGameGroupLossLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2013":
            return new AdapterErrors.RGWeeklyGameGroupLossLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2014":
            return new AdapterErrors.RGMonthlyGameGroupLossLimitReachedError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2015":
            return new AdapterErrors.RGRegulatoryCustomError(popErrorMessage,
                getBetLossErrorExtraData(response)).dontTranslate();
        case "ERR2210": // UK
            return new AdapterErrors.RGRealityCheckError(popErrorMessage,
                popErrorExtraDataStore.getUKRealityCheckExtraData(
                    noContinuePlayButtonForRealityCheck(response)))
                .dontTranslate();
        case "ERR2211": // Malta - can come as reality check or as expired session timer
            return new AdapterErrors.RGRealityCheckError(response?.data?.message || popErrorMessage,
                popErrorExtraDataStore.getMaltaRealityCheckExtraData(
                    noContinuePlayButtonForRealityCheck(response),
                    isSessionTimerMessage(popErrorMessage)))
                .dontTranslate();
        case "ERR2212":
            return new POPErrors.AnotherGameIsOpenError(popErrorMessage,
                popErrorMessage ? MrchExtraDataImpl.create().setUseServerMessage(true) : undefined);
        case "ERR2213":
            return new POPErrors.CoolDownTimerActive();
        case "ERR2214":
            return new POPErrors.RegulatorDeclinedOperation(messageArray && messageArray.length
                                                            ? messageArray[0].accountMsg
                                                            : popErrorMessage
            );
        default:
            return undefined;
    }
}

function noContinuePlayButtonForRealityCheck(response?: MaltaRealityAndSessionTimerResponse): boolean {
    return !!response?.data?.logout;
}

function noContinuePlayButton(response?: ErrorDataContainer): boolean {
    return !!response?.logout;
}

function isSessionTimerMessage(popErrorMessage?: string): boolean {
    return popErrorMessage && popErrorMessage.toLowerCase().startsWith("expired session");
}

function getBetLossErrorExtraData(response?: ErrorDataContainer): MrchExtraData {
    if (noContinuePlayButton(response)) {
        return popErrorExtraDataStore.getRGErrorExtraDataWithQuitButton();
    }
    return popErrorExtraDataStore.getRGErrorExtraDataWithContinueAndQuitButtons();
}

class POPErrorExtraDataStore {

    private rgErrorExtraDataWithContinueAndQuitButtons: MrchExtraData;
    private rgErrorExtraDataWithQuitButton: MrchExtraData;
    private playerIsSelfExcludedErrorExtraData: MrchExtraData;
    private readonly realityCheckTitle: string = "Reality check";
    private readonly sessionLimitTitle: string = "Session limit";

    public getUKRealityCheckExtraData(noExtendSessionButton: boolean): MrchExtraData {
        return this.initRealityCheckAction("uk", noExtendSessionButton, true);
    }

    public getMaltaRealityCheckExtraData(noExtendSessionButton: boolean,
                                         isSessionTimer: boolean): MrchExtraData {
        return this.initRealityCheckAction("mt", noExtendSessionButton, false, isSessionTimer);
    }

    public getRGErrorExtraDataWithContinueAndQuitButtons(): MrchExtraData {
        if (!this.rgErrorExtraDataWithContinueAndQuitButtons) {
            this.rgErrorExtraDataWithContinueAndQuitButtons = this.initRGErrorExtraDataWithContinueAndQuitButtons();
        }
        return this.rgErrorExtraDataWithContinueAndQuitButtons;
    }

    public getRGErrorExtraDataWithQuitButton(): MrchExtraData {
        if (!this.rgErrorExtraDataWithQuitButton) {
            this.rgErrorExtraDataWithQuitButton = this.initRGErrorExtraDataWithContinueAndQuitButtons(false);
        }
        return this.rgErrorExtraDataWithQuitButton;
    }

    public getPlayerIsSelfExcludedExtraData(): MrchExtraData {
        if (!this.playerIsSelfExcludedErrorExtraData) {
            this.initPlayerIsSelfExcludedExtraData();
        }
        return this.playerIsSelfExcludedErrorExtraData;
    }

    private initRealityCheckAction(regulatorValueForUrlPath: string,
                                   noExtendSessionButton: boolean,
                                   addHistoryButton: boolean,
                                   isSessionTimer: boolean = false): MrchExtraData {
        const okButtonServerCall = PlayerActionServerCallImpl.create()
            .setRegulatoryAction(PlayerRegulatoryActionsAtServer.resetRealityCheck)
            .setParams({ regulation: regulatorValueForUrlPath });

        const stopButtonServerCall = PlayerActionServerCallImpl.create()
            .setRegulatoryAction(PlayerRegulatoryActionsAtServer.closeSession)
            .setParams({ regulation: regulatorValueForUrlPath });

        const okButton = PopupButtonImpl.create()
            .setLabel("Keep Playing")
            .setGameAction(PopupButtonGameActions.continue)
            .setServerCall(okButtonServerCall)
            .setTranslate(true);
        const stopButton = PopupButtonImpl.create()
            .setLabel("Quit")
            .setGameAction(PopupButtonGameActions.lobby)
            .setServerCall(stopButtonServerCall)
            .setTranslate(true);
        const historyButton = PopupButtonImpl.create()
            .setLabel("Game History")
            .setGameAction(PopupButtonGameActions.gameHistory)
            .setTranslate(true)
            .setNoClose(true);

        const realityCheckExtraMessage = ExtraMessageImpl.create()
            .setTranslate(false)
            .setTranslateTitle(true);

        const buttons = [];

        // https://pop-playtech.readme.io/docs/session-timer
        if (!noExtendSessionButton) {
            // single close button is a sign of Session limit error
            buttons.push(okButton);
        }

        buttons.push(stopButton);
        if (addHistoryButton) {
            buttons.push(historyButton);
        }
        realityCheckExtraMessage.setButtons(buttons);

        if (isSessionTimer) {
            realityCheckExtraMessage.setMessageTitle(this.sessionLimitTitle);
        } else {
            realityCheckExtraMessage.setMessageTitle(this.realityCheckTitle);
        }

        return MrchExtraDataImpl.create()
            .addExtraMessage(realityCheckExtraMessage)
            .setUseServerMessage(true); // we assume that we shall use POP's message
    }

    private initRGErrorExtraDataWithContinueAndQuitButtons(addOkButton: boolean = true): MrchExtraData {
        const okButton = PopupButtonImpl.create()
            .setLabel("OK")
            .setGameAction(PopupButtonGameActions.stopPositions)
            .setTranslate(true);
        const quitButton = PopupButtonImpl.create()
            .setLabel("Quit")
            .setGameAction(PopupButtonGameActions.lobby)
            .setTranslate(true);

        const buttons = [];
        if (addOkButton) {
            buttons.push(okButton);
        }
        buttons.push(quitButton);

        const extraMessage = ExtraMessageImpl.create().setButtons(buttons).setTranslate(false);
        return MrchExtraDataImpl.create()
            .setUseServerMessage(true)
            .addExtraMessage(extraMessage);
    }

    private initPlayerIsSelfExcludedExtraData() {
        const okButton = PopupButtonImpl.create()
            .setLabel("OK")
            .setGameAction(PopupButtonGameActions.lobby)
            .setTranslate(true);
        const extraMessage = ExtraMessageImpl.create().setButtons([okButton]);
        this.playerIsSelfExcludedErrorExtraData = MrchExtraDataImpl.create()
            .setUseServerMessage(true)
            .addExtraMessage(extraMessage);
    }
}

const popErrorExtraDataStore = new POPErrorExtraDataStore();
