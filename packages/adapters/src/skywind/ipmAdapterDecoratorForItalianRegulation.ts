import { IPMAdapter, IPMAuthTokenData, IPMGameInitRequest, IPMStartGameTokenData } from "./ipmadapter";
import {
    MerchantAdapterDecorator,
    MerchantGameTokenInfo,
    MerchantGameURLInfo,
    MerchantInfo
} from "@skywind-group/sw-wallet-adapter-core";

export interface IPMGameInitRequestWithAAMS extends IPMGameInitRequest {
    aamsParticipationCode?: string;
    aamsSessionId?: string;
}

/*
 Extension of IPM adapter for italian regulation.
 The main purpose of it is to check get game url params for AAMS data and add those to token
 so that it will be stored in history.
 */
export class IPMAdapterDecoratorForItalianRegulation
    extends MerchantAdapterDecorator<IPMGameInitRequestWithAAMS, IPMStartGameTokenData, IPMAuthTokenData> {

    public constructor(ipmAdapter: IPMAdapter) {
        super(ipmAdapter);
    }

    public async createGameUrl(merchant: MerchantInfo,
                               gameCode: string,
                               providerCode: string,
                               providerGameCode: string,
                               initRequest: IPMGameInitRequestWithAAMS): Promise<MerchantGameURLInfo> {
        const result = await this.adapter.createGameUrl(merchant, gameCode, providerCode,
            providerGameCode, initRequest);

        if (initRequest.aamsSessionId || initRequest.aamsParticipationCode) {
            result.tokenData.regulatoryData = {
                aamsSessionCode: initRequest.aamsSessionId,
                ropCode: initRequest.aamsParticipationCode
            };
        }

        return result;
    }

    public async getGameTokenInfo(merchant: MerchantInfo,
                                  tokenData: IPMStartGameTokenData,
                                  currency: string,
                                  transferEnabled: boolean): Promise<MerchantGameTokenInfo<IPMAuthTokenData>> {
        const result = await this.adapter.getGameTokenInfo(merchant, tokenData, currency, transferEnabled);

        if (tokenData.regulatoryData) {
            result.gameTokenData.regulatoryData = tokenData.regulatoryData;
        }

        return result;
    }
}
