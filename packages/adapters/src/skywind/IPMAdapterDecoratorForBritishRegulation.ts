import { IPMAdapter, IPMAuthTokenData, IPMGameInitRequest, IPMStartGameTokenData } from "./ipmadapter";
import {
    Balance,
    Balances,
    GameLogoutRequest,
    GameLogoutResponse,
    MerchantAdapterDecorator,
    MerchantGameTokenInfo,
    MerchantInfo,
    PaymentRequest,
    RefundBetRequest
} from "@skywind-group/sw-wallet-adapter-core";
import { RegulationSessionStorage } from "@skywind-group/sw-adapter-regulation-support";
import { isFreeBet } from "./utils";

const BRITISH_REGULATION_KEY = "british";
const BRITISH_SESSION_TTL = 1800; // 30 mins

interface BritishPlayerSessionLimitInfo {
    totalBets: number;
    totalWins: number;
    sessionStartedDate: number; // msec
}

/*
 Extension of IPM adapter for British regulation.
 The main purpose of it is to store player's session total bet and win and session start time to provide it to client.
 */
export class IPMAdapterDecoratorForBritishRegulation
    extends MerchantAdapterDecorator<IPMGameInitRequest, IPMStartGameTokenData, IPMAuthTokenData> {

    private sessionService = new RegulationSessionStorage<BritishPlayerSessionLimitInfo>(BRITISH_REGULATION_KEY);

    public constructor(ipmAdapter: IPMAdapter) {
        super(ipmAdapter);
    }

    public async getGameTokenInfo(merchant: MerchantInfo,
                                  tokenData: IPMStartGameTokenData,
                                  currency: string,
                                  transferEnabled: boolean): Promise<MerchantGameTokenInfo<IPMAuthTokenData>> {
        const result = await this.adapter.getGameTokenInfo(merchant, tokenData, currency, transferEnabled);

        await this.initSession(merchant, tokenData.playerCode);

        return result;
    }

    public async commitPayment(merchant: MerchantInfo,
                               authToken: IPMAuthTokenData,
                               request: PaymentRequest): Promise<Balance> {
        const balance = await this.adapter.commitPayment(merchant, authToken, request);

        const sessionData = await this.getSession(authToken);
        if (!sessionData) {
            return balance;
        }

        sessionData.totalBets += isFreeBet(request) ? 0 : request.bet;
        sessionData.totalWins += request.win;
        await this.sessionService.setSessionWithExpiry(this.toSessionKey(authToken), sessionData, BRITISH_SESSION_TTL);

        return this.decorateBalanceWithSessionDataInfo(balance, sessionData);
    }

    public async commitBetPayment(merchant: MerchantInfo, authToken: IPMAuthTokenData,
                                  request: PaymentRequest): Promise<Balance> {
        const balance = await this.adapter.commitBetPayment(merchant, authToken, request);

        const sessionData = await this.getSession(authToken);
        if (!sessionData) {
            return balance;
        }

        sessionData.totalBets += isFreeBet(request) ? 0 : request.bet;
        await this.sessionService.setSessionWithExpiry(this.toSessionKey(authToken), sessionData, BRITISH_SESSION_TTL);

        return this.decorateBalanceWithSessionDataInfo(balance, sessionData);
    }

    public async commitWinPayment(merchant: MerchantInfo, authToken: IPMAuthTokenData,
                                  request: PaymentRequest): Promise<Balance> {
        const balance = await this.adapter.commitWinPayment(merchant, authToken, request);

        const sessionData = await this.getSession(authToken);
        if (!sessionData) {
            return balance;
        }

        sessionData.totalWins += request.win;
        await this.sessionService.setSessionWithExpiry(this.toSessionKey(authToken), sessionData, BRITISH_SESSION_TTL);

        return this.decorateBalanceWithSessionDataInfo(balance, sessionData);
    }

    public async refundBetPayment(merchant: MerchantInfo,
                                  authToken: IPMAuthTokenData,
                                  request: RefundBetRequest): Promise<Balance> {
        const refundResult = await this.adapter.refundBetPayment(merchant, authToken, request);

        const sessionData = await this.getSession(authToken);
        if (!sessionData) {
            return refundResult;
        }

        sessionData.totalBets -= request.bet;
        await this.sessionService.setSessionWithExpiry(this.toSessionKey(authToken), sessionData, BRITISH_SESSION_TTL);

        return this.decorateBalanceWithSessionDataInfo(refundResult, sessionData);
    }

    public async commitBonusPayment(merchant: MerchantInfo, authToken: IPMAuthTokenData,
                                    request: PaymentRequest): Promise<Balance> {
        const balance = await this.adapter.commitBonusPayment(merchant, authToken, request);
        const sessionData = await this.getSession(authToken);
        if (sessionData) {
            return this.decorateBalanceWithSessionDataInfo(balance, sessionData);
        }
        return balance;
    }

    public async getBalances(merchant: MerchantInfo, authToken: IPMAuthTokenData): Promise<Balances> {
        const balances = await this.adapter.getBalances(merchant, authToken);
        const sessionData = await this.getSession(authToken);
        if (sessionData) {
            this.decorateBalanceWithSessionDataInfo(balances[authToken.currency], sessionData);
        }
        return balances;
    }

    public async logoutGame(merchant: MerchantInfo,
                            gameTokenData: IPMAuthTokenData,
                            request: GameLogoutRequest): Promise<GameLogoutResponse> {
        const logoutResult = await this.adapter.logoutGame(merchant, gameTokenData, request);

        await this.sessionService.delSession(this.toSessionKey(gameTokenData));

        return logoutResult;
    }

    private decorateBalanceWithSessionDataInfo(balance: Balance, sessionData: BritishPlayerSessionLimitInfo): Balance {
        if (!balance.extraData) {
            balance.extraData = {};
        }

        balance.extraData.sessionData = sessionData;

        return balance;
    }

    /**
     *
     */
    private async initSession(merchant: MerchantInfo, playerCode: string): Promise<void> {
        // in case of session already exists - we leave existing session as is, without updating ttl
        const key = this.sessionService.buildKey(merchant.brandId, playerCode);
        await this.sessionService.setSessionWithExpiry(key, {
            totalBets: 0,
            totalWins: 0,
            sessionStartedDate: Date.now()
        }, BRITISH_SESSION_TTL);
    }

    /**
     * If value is missing it was whether never created whether its timer has already expired
     */
    private async getSession(authToken: IPMAuthTokenData): Promise<BritishPlayerSessionLimitInfo> {
        return this.sessionService.getSession(this.toSessionKey(authToken));
    }

    private toSessionKey(gameTokenData: IPMAuthTokenData): string {
        return this.sessionService.buildKey(gameTokenData.brandId, gameTokenData.playerCode);
    }
}
