import {
    Balance,
    BalanceRequestWithoutToken,
    Balances,
    FinalizeGameRequest,
    FreeBetInfo,
    FreeBetInfoRequest,
    GameLoginRequest,
    GameLogoutRequest,
    GameLogoutResponse,
    GameState,
    GameStatus,
    GameType,
    LoginTerminalRequest,
    LoginTerminalResponse,
    MerchantAdapter,
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantGameTokenInfo,
    MerchantGameURLInfo,
    MerchantInfo,
    MerchantPageRequest,
    MerchantPageResponse,
    MerchantStartGameTokenData,
    MerchantTransferRequest,
    OfflineBonusInfo,
    OfflineBonusPaymentRequest,
    PaymentRequest,
    PaymentRequestWithoutToken,
    PlayerInfo,
    PlayerRegulatoryActionRequest,
    RefundBetRequest,
    ReportCriticalFilesToMerchantRequest,
    RollbackBetRequest,
    RollbackWithoutTokenRequest
} from "@skywind-group/sw-wallet-adapter-core";
import { AdapterErrors } from "./errors/errors";

export interface MerchantGameHistoryRequest {
    /**
     * Type of merchant integration adapter (IPM, Playthech, etc)
     */

    merchantType: string;
    /**
     *  Merchant identifier. Should be unique for specified integration type
     */
    merchantCode: string;

    roundId: string;
    finishDate: string;
    language?: string;
    ttl?: number;
    timezone?: string;
    showRoundInfo?: boolean;
}

export interface MerchantGameHistoryDetailsImageRequest {
    merchantType: string;
    merchantCode: string;
    roundId: string;
    spinNumber: number;
    gameCode?: string;
    language?: string;
    timezone?: string;
}

export interface MerchantPlayerKillSessionInternalRequest {
    merchantType: string;
    merchantCode: string;
    playerCode: string;
    reason?: string;
}

export interface MerchantResolveRoundRequest {
    merchantType: string;
    merchantCode: string;
    roundId: string;
    playerCode?: string;
    gameProviderCode?: string;
}

export interface MerchantGameFinalizeRequest {
    merchantType: string;
    merchantCode: string;
    merchantSessionId: string;
    playerCode: string;
    gameCode: string;
    currency?: string;
}

export class MerchantAdapterImpl<GI extends MerchantGameInitRequest,
    SGT extends MerchantStartGameTokenData,
    AUTHT extends MerchantGameTokenData> implements MerchantAdapter<GI, SGT, AUTHT> {

    public commitBetPayment(merchant: MerchantInfo, authToken: AUTHT, request: PaymentRequest): Promise<Balance> {
        return undefined;
    }

    public commitPayment(merchant: MerchantInfo, authToken: AUTHT, request: PaymentRequest): Promise<Balance> {
        return undefined;
    }

    public commitWinPayment(merchant: MerchantInfo, authToken: AUTHT, request: PaymentRequest): Promise<Balance> {
        return undefined;
    }

    public commitWinWithoutToken(merchant: MerchantInfo, request: PaymentRequestWithoutToken): Promise<Balance> {
        return undefined;
    }

    public createGameUrl(merchant: MerchantInfo,
                         gameCode: string,
                         providerCode: string,
                         providerGameCode: string,
                         initRequest: MerchantGameInitRequest,
                         player?: PlayerInfo): Promise<MerchantGameURLInfo> {
        return undefined;
    }

    public getStartGameTokenData(merchant: MerchantInfo,
                                 gameCode: string,
                                 providerCode: string,
                                 providerGameCode: string,
                                 initRequest: GI): Promise<SGT> {
        return undefined;
    }

    public getBalances(merchant: MerchantInfo, authToken: AUTHT): Promise<Balances> {
        return undefined;
    }

    public getFreeBetInfo(merchant: MerchantInfo,
                          authToken: AUTHT,
                          freeBetRequest: FreeBetInfoRequest): Promise<FreeBetInfo> {
        return undefined;
    }

    public getPage(merchant: MerchantInfo, request: MerchantPageRequest): Promise<MerchantPageResponse> {
        return undefined;
    }

    public getBalanceWithoutToken(merchant: MerchantInfo, req: BalanceRequestWithoutToken): Promise<Balance> {
        return undefined;
    }

    public getPlayerInfo(merchant: MerchantInfo, authToken: AUTHT): Promise<PlayerInfo> {
        return undefined;
    }

    public performRegulatoryAction(merchant: MerchantInfo,
                                   gameTokenData: MerchantGameTokenData,
                                   request: PlayerRegulatoryActionRequest): Promise<any> {
        return undefined;
    }

    public rollbackBetPayment(merchant: MerchantInfo, authToken: AUTHT, request: RollbackBetRequest): Promise<Balance> {
        return undefined;
    }

    public async refundBetPayment(merchant: MerchantInfo,
                                  authToken: AUTHT, request: RefundBetRequest): Promise<Balance> {
        return Promise.reject(new AdapterErrors.OperationForbidden());
    }

    public rollbackBetPaymentWithoutToken(merchant: MerchantInfo,
                                          request: RollbackWithoutTokenRequest): Promise<Balance> {
        return undefined;
    }

    public transfer(merchant: MerchantInfo, authToken: AUTHT, request: MerchantTransferRequest): Promise<Balance> {
        return undefined;
    }

    public getGameTokenInfo(merchant: MerchantInfo,
                            startToken: MerchantStartGameTokenData,
                            currency: string,
                            transferEnabled: boolean): Promise<MerchantGameTokenInfo<AUTHT>> {
        return undefined;
    }

    public async keepAlive(merchant: MerchantInfo,
                           gameTokenData: MerchantGameTokenData): Promise<void> {
        throw new AdapterErrors.OperationForbidden();
    }

    public async loginGame(merchant: MerchantInfo,
                           gameTokenData: MerchantGameTokenData,
                           request: GameLoginRequest): Promise<void> {
        return Promise.reject(new AdapterErrors.OperationForbidden());
    }

    public async logoutGame(merchant: MerchantInfo,
                            gameTokenData: MerchantGameTokenData,
                            request: GameLogoutRequest): Promise<GameLogoutResponse> {
        return Promise.reject(new AdapterErrors.OperationForbidden());
    }

    public async finalizeGame(merchant: MerchantInfo,
                              gameTokenData: MerchantGameTokenData,
                              request: FinalizeGameRequest): Promise<Balance | void> {
        return;
    }

    public async reportCriticalFiles(merchant: MerchantInfo,
                                     req: ReportCriticalFilesToMerchantRequest): Promise<void> {
        return;
    }

    public async commitBonusPayment(merchant: MerchantInfo,
                                    authToken: AUTHT, request: PaymentRequest): Promise<Balance> {
        return Promise.reject(new AdapterErrors.OperationForbidden());
    }

    public async commitOfflineBonusPayment(merchant: MerchantInfo,
                                           bonusRequest: OfflineBonusPaymentRequest): Promise<OfflineBonusInfo> {
        return undefined;
    }

    protected getPlatform(deviceId: string): string {
        // In our system we have "mobile" and "Web"
        if (deviceId && deviceId.toLowerCase() === "mobile") {
            return "mobile";
        } else {
            return "web";
        }
    }

    protected getGameType(request: GameState): GameType {
        if (request.gameType) {
            return request.gameType;
        }
        if (request.spinType) {
            switch (request.spinType) {
                case "freeGame":
                case "reSpin":
                    return "freegame";
                case "bonus":
                    return "bonusgame";
                case "main":
                default:
                    return "normal";
            }
        }
        if (request.currentScene) {
            switch (request.currentScene) {
                case "freeSpins":
                    return "freegame";
                case "bonusGame":
                    return "bonusgame";
                case "main":
                default:
                    return "normal";
            }
        }

        return "normal";
    }

    protected getGameStatus(request: GameState): GameStatus {
        if (request.roundEnded) {
            return "settled";
        }
        if (request.gameStatus) {
            return request.gameStatus;
        }
        if (request.spinType) {
            if (request.spinType === "bonus") {
                return "bonusgame";
            }
            return "freegame";
        }
        if (request.currentScene && request.currentScene === "bonusGame") {
            return "bonusgame";
        }
        return "freegame";
    }

    protected getRequestTime(ts) {
        return (new Date().getTime() - ts) / 1000;
    }

    public loginTerminalPlayer(merchant: MerchantInfo,
                               initRequest: LoginTerminalRequest): Promise<LoginTerminalResponse<SGT>> {
        return undefined;
    }
}
