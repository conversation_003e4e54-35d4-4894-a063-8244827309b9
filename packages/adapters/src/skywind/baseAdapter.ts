import {
    Balance,
    BalanceRequestWithoutToken,
    Balances,
    FinalizeGameRequest,
    FreeBetInfo,
    FreeBetInfoRequest,
    GameLoginRequest,
    GameLogoutRequest,
    GameLogoutResponse,
    GameState,
    GameStatus,
    GameType,
    MerchantAdapter,
    MerchantBalanceService,
    MerchantBrokenGameService,
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantGameTokenInfo,
    MerchantGameURLInfo,
    MerchantInfo,
    MerchantInfoService,
    MerchantPageRequest,
    MerchantPageResponse,
    MerchantPaymentService,
    MerchantRegulationsService,
    MerchantStartGameTokenData,
    MerchantTransferRequest,
    PaymentRequest,
    PaymentRequestWithoutToken,
    PlayerInfo,
    PlayerRegulatoryActionRequest,
    RefundBetRequest,
    ReportCriticalFilesToMerchantRequest,
    RollbackBetRequest,
    RollbackWithoutTokenRequest,
    StartGameService,
    OfflineBonusPaymentRequest,
    OfflineBonusInfo, LoginTerminalRequest, LoginTerminalResponse, AdditionalInfo,
    RegisterRoundRequest, RegisterRoundResponse
} from "@skywind-group/sw-wallet-adapter-core";
import { WALLET_OPERATION_NAME } from "@skywind-group/sw-management-playservice";

export class BaseAdapter implements MerchantAdapter {

    constructor(private startGameService: StartGameService,
                private balanceService: MerchantBalanceService,
                private paymentService: MerchantPaymentService,
                private infoService: MerchantInfoService,
                private regulationService: MerchantRegulationsService,
                private brokenGameService: MerchantBrokenGameService) {

    }

    public async createGameUrl(merchant: MerchantInfo,
                               gameCode: string,
                               providerCode: string,
                               providerGameCode: string,
                               initRequest: MerchantGameInitRequest): Promise<MerchantGameURLInfo> {

        return this.startGameService.createGameUrl(
            merchant,
            gameCode,
            providerCode,
            providerGameCode,
            initRequest
        );
    }

    public async getStartGameTokenData(merchant: MerchantInfo,
                                       gameCode: string,
                                       providerCode: string,
                                       providerGameCode: string,
                                       initRequest: MerchantGameInitRequest): Promise<MerchantStartGameTokenData> {

        return this.startGameService.getStartGameTokenData(
            merchant,
            gameCode,
            providerCode,
            providerGameCode,
            initRequest
        );
    }

    public async keepAlive(merchant: MerchantInfo,
                           gameTokenData: MerchantGameTokenData): Promise<void> {
        return this.startGameService.keepAlive(merchant, gameTokenData);
    }

    public async getGameTokenInfo(merchant: MerchantInfo,
                                  startToken: MerchantStartGameTokenData,
                                  currency: string,
                                  transferEnabled: boolean,
                                  additionalInfo?: AdditionalInfo
    ): Promise<MerchantGameTokenInfo<MerchantGameTokenData>> {
        const result: MerchantGameTokenInfo<MerchantGameTokenData> = await this.startGameService
            .getGameTokenInfo(merchant, startToken, currency, transferEnabled, additionalInfo);

        // backward compatibility
        const gameTokenData = result.gameTokenData || result as any;
        if (gameTokenData.test === undefined && startToken.test !== undefined) {
            gameTokenData.test = !!startToken.test;
        }

        return result;
    }

    public async getBalances(merchant: MerchantInfo, authToken: MerchantGameTokenData): Promise<Balances> {
        return this.balanceService.getBalances(merchant, authToken);
    }

    public async getBalanceWithoutToken(merchant: MerchantInfo, req: BalanceRequestWithoutToken): Promise<Balance> {
        return this.balanceService.getBalanceWithoutToken(merchant, req);
    }

    public async commitBetPayment(merchant: MerchantInfo,
                                  authToken: MerchantGameTokenData,
                                  request: PaymentRequest): Promise<Balance> {
        return this.paymentService.commitBetPayment(merchant, authToken, request);
    }

    public async commitPayment(merchant: MerchantInfo,
                               authToken: MerchantGameTokenData,
                               request: PaymentRequest): Promise<Balance> {
        return this.paymentService.commitPayment(merchant, authToken, request);
    }

    public async commitWinPayment(merchant: MerchantInfo,
                                  authToken: MerchantGameTokenData,
                                  request: PaymentRequest): Promise<Balance> {
        return this.paymentService.commitWinPayment(merchant, authToken, request);
    }

    public async commitWinWithoutToken(merchant: MerchantInfo,
                                       request: PaymentRequestWithoutToken): Promise<Balance> {
        return this.paymentService.commitWinWithoutToken(merchant, request);
    }

    public async transfer(merchant: MerchantInfo,
                          authToken: MerchantGameTokenData,
                          request: MerchantTransferRequest): Promise<Balance> {
        return this.paymentService.transfer(merchant, authToken, request);
    }

    public async rollbackBetPayment(merchant: MerchantInfo,
                                    authToken: MerchantGameTokenData,
                                    request: RollbackBetRequest): Promise<Balance> {
        const refundRequest = this.convertRollbackRequestToRefundRequest(request);
        return this.paymentService.refundBetPayment(merchant, authToken, refundRequest);
    }

    public async refundBetPayment(merchant: MerchantInfo,
                                  authToken: MerchantGameTokenData,
                                  request: RefundBetRequest): Promise<Balance> {
        return this.paymentService.refundBetPayment(merchant, authToken, request);
    }

    public async rollbackBetPaymentWithoutToken(merchant: MerchantInfo,
                                                request: RollbackWithoutTokenRequest): Promise<Balance> {
        return this.paymentService.rollbackBetPaymentWithoutToken(merchant, request);
    }

    public async registerRound(merchant: MerchantInfo,
                               request: RegisterRoundRequest): Promise<RegisterRoundResponse> {
        return this.paymentService.registerRound(merchant, request);
    }

    public async getPlayerInfo(merchant: MerchantInfo, authToken: MerchantGameTokenData): Promise<PlayerInfo> {
        return this.infoService.getPlayerInfo(merchant, authToken);
    }

    public async getFreeBetInfo(merchant: MerchantInfo,
                                authToken: MerchantGameTokenData,
                                freeBetRequest: FreeBetInfoRequest): Promise<FreeBetInfo> {
        return this.infoService.getFreeBetInfo(merchant, authToken, freeBetRequest);
    }

    public async performRegulatoryAction(merchant: MerchantInfo,
                                         gameTokenData: MerchantGameTokenData,
                                         request: PlayerRegulatoryActionRequest): Promise<void> {
        return this.regulationService.performRegulatoryAction(merchant, gameTokenData, request);
    }

    public async loginGame(merchant: MerchantInfo,
                           gameTokenData: MerchantGameTokenData,
                           request: GameLoginRequest): Promise<void> {
        return this.brokenGameService.loginGame(merchant, gameTokenData, request);
    }

    public async logoutGame(merchant: MerchantInfo,
                            gameTokenData: MerchantGameTokenData,
                            request: GameLogoutRequest): Promise<GameLogoutResponse> {
        return this.brokenGameService.logoutGame(merchant, gameTokenData, request);
    }

    public async finalizeGame(merchant: MerchantInfo,
                              gameTokenData: MerchantGameTokenData,
                              request: FinalizeGameRequest): Promise<Balance | void> {
        return this.brokenGameService.finalizeGame(merchant, gameTokenData, request);
    }

    public getPage(merchant: MerchantInfo, request: MerchantPageRequest): Promise<MerchantPageResponse> {
        return this.infoService.getPage(merchant, request);
    }

    public async reportCriticalFiles(merchant: MerchantInfo,
                                     req: ReportCriticalFilesToMerchantRequest): Promise<void> {
        return this.regulationService.reportCriticalFiles(merchant, req);
    }

    protected getPlatform(deviceId: string): string {
        // In our system we have "mobile" and "Web"
        if (deviceId && deviceId.toLowerCase() === "mobile") {
            return "mobile";
        } else {
            return "web";
        }
    }

    protected getGameType(request: GameState): GameType {
        if (request.gameType) {
            return request.gameType;
        }
        if (request.spinType) {
            switch (request.spinType) {
                case "freeGame":
                case "reSpin":
                    return "freegame";
                case "bonus":
                    return "bonusgame";
                case "main":
                default:
                    return "normal";
            }
        }
        if (request.currentScene) {
            switch (request.currentScene) {
                case "freeSpins":
                    return "freegame";
                case "bonusGame":
                    return "bonusgame";
                case "main":
                default:
                    return "normal";
            }
        }

        return "normal";
    }

    protected getGameStatus(request: GameState): GameStatus {
        if (request.roundEnded) {
            return "settled";
        }
        if (request.gameStatus) {
            return request.gameStatus;
        }
        if (request.spinType) {
            if (request.spinType === "bonus") {
                return "bonusgame";
            }
            return "freegame";
        }
        if (request.currentScene && request.currentScene === "bonusGame") {
            return "bonusgame";
        }
        return "freegame";
    }

    public commitBonusPayment(merchant: MerchantInfo,
                              gameToken: MerchantGameTokenData,
                              request: PaymentRequest): Promise<Balance> {
        return this.paymentService.commitBonusPayment(merchant, gameToken, request);
    }

    public async commitOfflineBonusPayment(merchant: MerchantInfo,
                                           request: OfflineBonusPaymentRequest): Promise<OfflineBonusInfo> {
        return this.paymentService.commitOfflineBonusPayment(merchant, request);

    }

    public loginTerminalPlayer(
        merchant: MerchantInfo,
        initRequest: LoginTerminalRequest): Promise<LoginTerminalResponse<MerchantStartGameTokenData>> {
        return this.startGameService.loginTerminalPlayer(merchant, initRequest);
    }

    private convertRollbackRequestToRefundRequest(request: RollbackBetRequest): RefundBetRequest {
        return {
            bet: request.amount,
            roundId: request.roundId.toString(),
            roundPID: request.roundPID,
            transactionId: request.originalTransactionId,
            extTransactionId: request.extTransactionId,
            gameToken: request.gameToken,
            ts: request.originalTransactionId.timestamp.toString(),
            operation: WALLET_OPERATION_NAME.REFUND_BET,
            roundEnded: request.roundEnded,
            gameStatus: request.gameStatus,
            gameType: request.gameType,
            spinType: request.spinType
        };
    }
}
