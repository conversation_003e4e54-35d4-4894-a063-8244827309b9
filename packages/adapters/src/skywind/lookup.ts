import { IPMAdapter } from "./ipmadapter";
import { POPAdapter } from "./popadapter";
import { GVCAdapter } from "./gvcAdapter";
import { BaseAdapter } from "./baseAdapter";
import {
    HTTPMerchantBalanceService,
    HTTPMerchantBrokenGameService,
    HTTPMerchantInfoService,
    HTTPMerchantPaymentService,
    HTTPMerchantRegulationsService,
    HTTPMerchantStartGameService,
    MERCHANT_REGULATION,
    MerchantBalanceService,
    MerchantBrokenGameService,
    MerchantInfo,
    MerchantInfoService,
    MerchantPaymentService,
    MerchantRegulationsService,
    StartGameService,
} from "@skywind-group/sw-wallet-adapter-core";
import { POPAdapterDecoratorForItaly } from "./popAdapterDecoratorForItaly";
import { POPAdapterDecoratorForSpain } from "./popAdapterDecoratorForSpain";
import { IPMAdapterDecoratorForBritishRegulation } from "./IPMAdapterDecoratorForBritishRegulation";
import { IPMAdapterDecoratorForItalianRegulation } from "./ipmAdapterDecoratorForItalianRegulation";

const properties = require("properties");

export const MERCHANT_POP_TYPES = ["pop", "pop_moorgate", "pop_eu", "pop_asia", "pop_asia_moorgate"];

export type AnyMerchantAdapter = StartGameService &
    MerchantBrokenGameService &
    MerchantBalanceService &
    MerchantInfoService &
    MerchantPaymentService &
    MerchantRegulationsService;

type CreateAdapter = (merchantInfo?: MerchantInfo) => AnyMerchantAdapter;

interface LookupTable {
    [field: string]: CreateAdapter;
}

function mrchJSONParser(body: any) {
    if (typeof body === "object") {
        return body;
    }
    return JSON.parse(body);
}

const httpAdapter = (adapterUrl: string) => (merchantInfo: MerchantInfo) => {
    const merchantRegulation: MERCHANT_REGULATION =
        merchantInfo?.params?.regulatorySettings?.merchantRegulation;
    return new BaseAdapter(
        new HTTPMerchantStartGameService(adapterUrl, merchantInfo, merchantRegulation),
        new HTTPMerchantBalanceService(adapterUrl, merchantInfo, merchantRegulation),
        new HTTPMerchantPaymentService(adapterUrl, merchantInfo, merchantRegulation),
        new HTTPMerchantInfoService(adapterUrl, merchantInfo, merchantRegulation),
        new HTTPMerchantRegulationsService(adapterUrl, merchantInfo, merchantRegulation),
        new HTTPMerchantBrokenGameService(adapterUrl, merchantInfo, merchantRegulation)
    );
};

export const internalAdapters: LookupTable = {
    ipm: (merchantInfo: MerchantInfo) => IPMFactory.create(properties.parse, merchantInfo),
    mrch: (merchantInfo: MerchantInfo) => IPMFactory.create(JSON.parse, merchantInfo, true),
    mrch_json: (merchantInfo: MerchantInfo) => IPMFactory.create(mrchJSONParser, merchantInfo, true, true),
    gvc: (merchantInfo: MerchantInfo) => new GVCAdapter(merchantInfo.params),
    pop: (merchantInfo: MerchantInfo) => POPFactory.create(merchantInfo),
    pop_moorgate: (merchantInfo: MerchantInfo) => POPFactory.create(merchantInfo)
};

export interface LookupRequest {
    typeDetails?: {
        url?: string;
        mainDomainUrl?: string;
        isInternalAdapter: boolean;
    };
    type: string;
    info?: MerchantInfo;
    useMainDomainUrl?: boolean;
}

export async function lookupAdapter({
    type,
    typeDetails,
    info,
    useMainDomainUrl
}: LookupRequest): Promise<AnyMerchantAdapter> {
    if (typeDetails) {
        return typeDetails.isInternalAdapter
               ? internalAdapters[type] && internalAdapters[type](info)
               : httpAdapter(useMainDomainUrl ? typeDetails.mainDomainUrl || typeDetails.url : typeDetails.url)(info);
    }
    return internalAdapters[type] && internalAdapters[type](info);
}

export function registerAdapter(type: string, adapter: AnyMerchantAdapter) {
    internalAdapters[type] = () => adapter;
}

export class IPMFactory {
    public static create(responseParser: (body: any) => any,
                         merchantInfo: MerchantInfo,
                         responseIsJson: boolean = false,
                         requestIsJson: boolean = false): IPMAdapter {
        const ipmAdapter = new IPMAdapter(responseParser, responseIsJson, requestIsJson);

        if (MerchantInfoHelper.isBritishRegulation(merchantInfo)) {
            return new IPMAdapterDecoratorForBritishRegulation(ipmAdapter) as any;
        }
        if (MerchantInfoHelper.isItalianRegulation(merchantInfo)) {
            return new IPMAdapterDecoratorForItalianRegulation(ipmAdapter) as any;
        }
        return ipmAdapter;
    }
}

export class POPFactory {
    public static create(merchantInfo: MerchantInfo): POPAdapter {
        const popAdapter = new POPAdapter(merchantInfo.params);

        if (MerchantInfoHelper.isItalianRegulation(merchantInfo)) {
            return new POPAdapterDecoratorForItaly(popAdapter) as any;
        } else if (MerchantInfoHelper.isSpanishRegulation(merchantInfo)) {
            return new POPAdapterDecoratorForSpain(popAdapter) as any;
        }
        return popAdapter;
    }
}

export class MerchantInfoHelper {
    public static isRomanianRegulation(merchantInfo: MerchantInfo): boolean {
        const merchantRegulation = merchantInfo?.params?.regulatorySettings?.merchantRegulation;
        return merchantRegulation === MERCHANT_REGULATION.romanian;
    }

    public static isItalianRegulation(merchantInfo: MerchantInfo): boolean {
        const merchantRegulation = merchantInfo?.params?.regulatorySettings?.merchantRegulation;
        return merchantRegulation === MERCHANT_REGULATION.italian;
    }

    public static isSpanishRegulation(merchantInfo: MerchantInfo): boolean {
        const merchantRegulation = merchantInfo?.params?.regulatorySettings?.merchantRegulation;
        return merchantRegulation === MERCHANT_REGULATION.spanish;
    }

    public static isBritishRegulation(merchantInfo: MerchantInfo): boolean {
        const merchantRegulation = merchantInfo?.params?.regulatorySettings?.merchantRegulation;
        return merchantRegulation === MERCHANT_REGULATION.british;
    }

    public static shouldVerifyPlayerOnGameStart(merchantInfo: MerchantInfo): boolean {
        return merchantInfo?.params?.verifyPlayerOnGameStart;
    }
}
