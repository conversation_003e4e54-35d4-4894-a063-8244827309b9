{"/entities": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:create"]}], "tags": ["Entity"], "summary": "Creates a new entity under a specific parent", "parameters": [{"$ref": "#/parameters/createEntity"}], "responses": {"201": {"description": "Created entity", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 107: Parent is brand\n- 780: Merchant types are not supported by parent\n- 781: Merchant types is not array\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}}, "/merchantentities": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:create", "keyentity:merchant", "keyentity:merchant:create"]}], "tags": ["Entity"], "summary": "Creates a new merchant entity under a current key entity", "parameters": [{"$ref": "#/parameters/createMerchantEntity"}], "responses": {"201": {"description": "Created entity", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 501: Merchant type is not supported\n- 780: Merchant types are not supported by parent\n- 781: Merchant types is not array\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n- 500: Merchant already exists\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit", "merchant", "merchant:edit"]}], "tags": ["Entity"], "summary": "Updates merchant entity", "parameters": [{"$ref": "#/parameters/updateMerchantEntity"}], "responses": {"201": {"description": "Update merchant entity", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 101: Not a brand\n- 504: Not merchant brand\n- 780: Merchant types are not supported by parent\n- 781: Merchant types is not array\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view", "keyentity:merchant", "keyentity:merchant:view"]}], "tags": ["Entity"], "summary": "Gets merchant entity info", "description": "Gets current entity info if it is merchant", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "- 504: Not a merchant brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/merchantentities/search": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view", "keyentity:merchant", "keyentity:merchant:view"]}], "tags": ["Entity"], "parameters": [{"$ref": "#/parameters/merchantCode__in"}], "summary": "Gets merchants entity info", "description": "Gets merchants info under entity", "responses": {"200": {"description": "Entity information", "schema": {"type": "array", "items": {"$ref": "#/definitions/MerchantEntityWithBalances"}}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/bulk-operation": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:bulk-operation"]}], "tags": ["Entity"], "summary": "Updates static/dynamic domains, update merchant proxies.", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/EntityBulkOperations"}}], "responses": {"201": {"description": "Bulk operation results - array of Merchants/Entities", "schema": {"type": "array", "items": {"$ref": "#/definitions/Merchant"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/merchantentities/{path}": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view", "merchant", "merchant:view"]}], "tags": ["Entity"], "summary": "Gets merchant entity info by path", "description": "Gets merchant entity info under given path, including all balances", "responses": {"200": {"description": "Merchant entity information", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "- 504: Not a merchant brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit", "merchant", "merchant:edit"]}], "tags": ["Entity"], "summary": "Updates merchant entity by path", "parameters": [{"$ref": "#/parameters/updateMerchantEntity"}], "responses": {"201": {"description": "Update merchant entity", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 101: Not a brand\n- 504: Not merchant brand\n- 780: Merchant types are not supported by parent\n- 781: Merchant types is not array\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:delete", "merchant", "merchant:edit"]}], "tags": ["Entity"], "summary": "Deletes merchant entity by path", "description": "Deletes empty merchant entity", "responses": {"200": {"description": "Deleted entity info", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 64: Entity is not empty!\n- 101: Not a brand\n- 504: Not merchant brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:create", "merchant", "merchant:create"]}], "tags": ["Entity"], "summary": "Creates a new merchant entity under a specific parent path", "parameters": [{"$ref": "#/parameters/createMerchantEntity"}], "responses": {"201": {"description": "Created entity", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 107: Parent is brand\n- 501: Merchant type is not supported\n- 780: Merchant types are not supported by parent\n- 781: Merchant types is not array\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n- 500: Merchant already exists\n"}}}}, "/brief": {"get": {"security": [{"apiKey": []}], "tags": ["Entity"], "summary": "Gets brief info of current entity with settings only of this entity", "description": "Gets brief information information of user's entity (only type, key, name and description) and settings only of this users", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/BriefEntity"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:create"]}], "tags": ["Entity"], "summary": "Creates a new entity under a specific parent path", "parameters": [{"$ref": "#/parameters/createEntity"}], "responses": {"201": {"description": "Created entity", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 107: Parent is brand\n- 780: Merchant types are not supported by parent\n- 781: Merchant types is not array\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Updates entity by path", "parameters": [{"$ref": "#/parameters/updateEntity"}], "responses": {"200": {"description": "Updated entity", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 780: Merchant types are not supported by parent\n- 781: Merchant types is not array\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entity info by path", "description": "Gets specific entity information, no child entities and including all balances", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:delete"]}], "tags": ["Entity"], "summary": "Deletes entity by path", "description": "Deletes empty owning entity", "responses": {"200": {"description": "Deleted entity info", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 64: Enti<PERSON> is not empty!\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/brandentities": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:create"]}], "tags": ["Entity"], "summary": "Creates a new brand under a specific parent", "description": "Create new entity with type \"brand\" under a current parent", "parameters": [{"$ref": "#/parameters/createBrandEntity"}], "responses": {"201": {"description": "Created brand", "schema": {"$ref": "#/definitions/BrandOrMerchantWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 107: Parent is brand\n- 780: Merchant types are not supported by parent\n- 781: Merchant types is not array\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}}, "/brandentities/{path}": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:create"]}], "tags": ["Entity"], "summary": "Creates a new brand under a specific parent by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/createBrandEntity"}], "responses": {"201": {"description": "Created brand", "schema": {"$ref": "#/definitions/BrandOrMerchantWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 107: Parent is brand\n- 780: Merchant types are not supported by parent\n- 781: Merchant types is not array\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}}, "/entities/{path}/settings": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entity settings by path", "parameters": [{"$ref": "#/parameters/onlyOwnSettings"}, {"$ref": "#/parameters/extendSettings"}], "responses": {"200": {"description": "Entity settings", "schema": {"$ref": "#/definitions/EntitySettings"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Patch entity settings by path", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/EntitySettings"}}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Updated entity settings", "schema": {"$ref": "#/definitions/EntitySettings"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 64: En<PERSON><PERSON> is not empty!\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "409": {"description": "- 112: <PERSON><PERSON><PERSON> has duplicates\n", "schema": {"$ref": "#/definitions/Error"}}}}, "put": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Update entity settings (full rewrite of existing settings) by path", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/EntitySettings"}}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Updated entity settings", "schema": {"$ref": "#/definitions/EntitySettings"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 64: En<PERSON><PERSON> is not empty!\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "409": {"description": "- 112: <PERSON><PERSON><PERSON> has duplicates\n", "schema": {"$ref": "#/definitions/Error"}}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Resets entity settings to defaults by path", "parameters": [{"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Entity settings", "schema": {"$ref": "#/definitions/EntitySettings"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/check-website-whitelisted": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets value to check whether website is whitelisted by path", "responses": {"200": {"description": "Flag to check website whitelisted", "schema": {"$ref": "#/definitions/CheckWebSiteWhitelisted"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Adds the check of website whether it is whitelisted by path", "parameters": [{"$ref": "#/parameters/CheckWebSiteWhitelisted"}], "responses": {"200": {"description": "Added flag to check website whitelisted"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 64: En<PERSON><PERSON> is not empty!\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Resets the check of website whether it is whitelisted by path", "responses": {"204": {"description": "The flag has been reset"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/settings/bowhitelist": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"deprecated": true, "security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entity back office whitelisted IPs by path", "description": "Method returns list of IP addresses whitelisted for an entity's back office", "responses": {"200": {"description": "List of whitelisted back office IPs for own and parent", "schema": {"$ref": "#/definitions/EntityWhitelistDetailed"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access Token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 110: Whitelist not found\n"}}}, "post": {"deprecated": true, "security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Creates entity back office whitelisted IPs by path", "description": "Method creates list of IP addresses whitelisted for an entity's back office", "parameters": [{"$ref": "#/parameters/whitelist"}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Updated entity settings", "schema": {"$ref": "#/definitions/Entity<PERSON><PERSON>elist"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 111: Whitelist already exists. You can patch it\n"}}}, "patch": {"deprecated": true, "security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Adds IPs to entity's back office whitelisted IPs by path", "description": "Method appends IP addresses to entity's back office whitelisted IPs", "parameters": [{"$ref": "#/parameters/whitelist"}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Updated entity settings", "schema": {"$ref": "#/definitions/Entity<PERSON><PERSON>elist"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"deprecated": true, "security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Deletes IPs from entity's back office whitelisted IPs by path", "description": "Method removes IP addresses from entity's back office whitelisted IPs", "parameters": [{"$ref": "#/parameters/whitelist"}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Updated entity settings", "schema": {"$ref": "#/definitions/Entity<PERSON><PERSON>elist"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n- 776: Cannot delete IP from parent entity"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/settings/ip-whitelist/{type}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/whitelistType"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entity back office whitelisted IPs by path", "description": "Method returns list of IP addresses whitelisted for an entity's back office", "responses": {"200": {"description": "List of whitelisted back office IPs for own and parent", "schema": {"$ref": "#/definitions/EntityWhitelistDetailed"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access Token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 110: Whitelist not found\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Creates entity back office whitelisted IPs by path", "description": "Method creates list of IP addresses whitelisted for an entity's back office", "parameters": [{"$ref": "#/parameters/whitelist"}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Updated entity settings", "schema": {"$ref": "#/definitions/Entity<PERSON><PERSON>elist"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 111: Whitelist already exists. You can patch it\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Adds IPs to entity's back office whitelisted IPs by path", "description": "Method appends IP addresses to entity's back office whitelisted IPs", "parameters": [{"$ref": "#/parameters/whitelist"}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Updated entity settings", "schema": {"$ref": "#/definitions/Entity<PERSON><PERSON>elist"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Deletes IPs from entity's back office whitelisted IPs by path", "description": "Method removes IP addresses from entity's back office whitelisted IPs", "parameters": [{"$ref": "#/parameters/whitelist"}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Updated entity settings", "schema": {"$ref": "#/definitions/Entity<PERSON><PERSON>elist"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n- 776: Cannot delete IP from parent entity"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/settings": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets key entity settings", "parameters": [{"$ref": "#/parameters/onlyOwnSettings"}, {"$ref": "#/parameters/extendSettings"}], "responses": {"200": {"description": "Entity settings", "schema": {"$ref": "#/definitions/EntitySettings"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Updates key entity settings", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/EntitySettings"}}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Updated entity settings", "schema": {"$ref": "#/definitions/EntitySettings"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 64: En<PERSON><PERSON> is not empty!\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "409": {"description": "- 112: <PERSON><PERSON><PERSON> has duplicates\n", "schema": {"$ref": "#/definitions/Error"}}}}, "put": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Updates key entity settings (full update with cleaning of existing settings)", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/EntitySettings"}}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Updated entity settings", "schema": {"$ref": "#/definitions/EntitySettings"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 64: En<PERSON><PERSON> is not empty!\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "409": {"description": "- 112: <PERSON><PERSON><PERSON> has duplicates\n", "schema": {"$ref": "#/definitions/Error"}}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Resets key entity settings to defaults", "parameters": [{"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Entity settings", "schema": {"$ref": "#/definitions/EntitySettings"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/check-website-whitelisted": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets value to check whether website is whitelisted", "responses": {"200": {"description": "Flag to check website whitelisted", "schema": {"$ref": "#/definitions/CheckWebSiteWhitelisted"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Adds the check of website whether it is whitelisted", "parameters": [{"$ref": "#/parameters/CheckWebSiteWhitelisted"}], "responses": {"200": {"description": "Added flag to check website whitelisted"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 64: En<PERSON><PERSON> is not empty!\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Resets the check of website whether it is whitelisted", "responses": {"204": {"description": "The flag has been reset"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/suspended": {"parameters": [{"$ref": "#/parameters/path"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:change-state"]}], "tags": ["Entity"], "summary": "Suspends entity by path", "description": "Changes the status of the entity to suspended. Change operations of sub entities will fail", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:change-state"]}], "tags": ["Entity"], "summary": "Restores entity by path", "description": "Changes the status of the entity to normal", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "-734: Entity status not match\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/maintenance": {"parameters": [{"$ref": "#/parameters/path"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:change-state"]}], "tags": ["Entity"], "summary": "Turns maintanance mode for entity by path", "description": "Changes the status of the entity to maintenance. All sub entities will be considered under maintenance", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:change-state"]}], "tags": ["Entity"], "summary": "Restores entity by path", "description": "Changes the status of the entity to normal", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "-734: Entity status not match\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/test": {"parameters": [{"$ref": "#/parameters/forceFlagInQuery"}, {"$ref": "#/parameters/path"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:change-state-test"]}], "tags": ["Entity"], "summary": "Set 'test' status for entity by path", "description": "Changes the status of the entity to test", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "- 40: Can't change entity status from normal to test without force flag\n- 40: Entity should be not a test\n- 40: Entity should be brand or merchant\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:change-state-test"]}], "tags": ["Entity"], "summary": "Restores entity by path", "description": "Change the status of entity from 'test' to 'normal'\"", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "-734: Entity status not match\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/status": {"parameters": [{"$ref": "#/parameters/path"}], "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:change-state"]}], "tags": ["Entity"], "parameters": [{"$ref": "#/parameters/entityStatus"}], "summary": "Update status for entity by path (deprecated)", "description": "Change status for the selected entity. Change status can only master entity. Use new methods to change entity status(ex. PUT /entities/{path}/test)", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 50: Not master entity\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/domain/{domain}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/domain"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Find entity by domain by path", "description": "Gets specific entity information, no child entities and including all balances", "responses": {"200": {"description": "Entities information", "schema": {"type": "array", "items": {"$ref": "#/definitions/EntityWithPath"}}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/domain/{domain}": {"parameters": [{"$ref": "#/parameters/domain"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Find entity by domain", "description": "Gets specific entity information, no child entities and including all balances", "responses": {"200": {"description": "Entity information", "schema": {"type": "array", "items": {"$ref": "#/definitions/EntityWithPath"}}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/search-by-key": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "parameters": [{"in": "body", "name": "search", "required": true, "schema": {"$ref": "#/definitions/SearchByKey"}}], "tags": ["Entity"], "summary": "Find entity by domain by path", "description": "Gets specific entity information, no child entities and including all balances", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithPath"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/search-by-key": {"parameters": [], "post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "parameters": [{"in": "body", "name": "search", "required": true, "schema": {"$ref": "#/definitions/SearchByKey"}}], "tags": ["Entity"], "summary": "Find entity by domain", "description": "Gets specific entity information, no child entities and including all balances", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithPath"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/structure": {"get": {"parameters": [{"$ref": "#/parameters/includeProxy"}, {"$ref": "#/parameters/includeJurisdiction"}, {"$ref": "#/parameters/includeMerchantCode"}], "security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entities structure of the related key used", "responses": {"200": {"description": "Entity full hierarchy with proxy if includeProxy flag is present", "schema": {"$ref": "#/definitions/EntityWithProxy"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/short-structure": {"get": {"parameters": [{"$ref": "#/parameters/includeJurisdiction"}, {"$ref": "#/parameters/additionalFields"}], "security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entities short structure of the related key used", "responses": {"200": {"description": "Entity full hierarchy with short info", "schema": {"$ref": "#/definitions/EntityShortInfo"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/short-structure/search": {"parameters": [{"$ref": "#/parameters/url__equal"}, {"$ref": "#/parameters/url__contains"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Search in the entities short structure", "responses": {"200": {"description": "Entity hierarchy with short info", "schema": {"$ref": "#/definitions/EntityShortInfo"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/structure/move-entity": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:move"]}], "tags": ["Entity"], "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/MoveEntity"}}], "summary": "Move entity from one to another", "responses": {"400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 83: Country not exist in parent\n- 40: Master can not be moved\n- 40: Child is parent of parent\n- 40: This parent already has child with name\n- 40: <PERSON><PERSON><PERSON> and newParent<PERSON><PERSON> should be different\n- 62: One of the parents is suspended\n- 731: Parent is brand\n- 737: <PERSON><PERSON><PERSON> has conflicts with new parent\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/structure": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/includeProxy"}, {"$ref": "#/parameters/includeJurisdiction"}, {"$ref": "#/parameters/includeMerchantCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entity structure including all sub entities by path", "responses": {"200": {"description": "Entity full hierarchy", "schema": {"$ref": "#/definitions/EntityWithProxy"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/short-structure": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/includeJurisdiction"}, {"$ref": "#/parameters/additionalFields"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entities short structure of the related key used by path", "responses": {"200": {"description": "Entity full hierarchy with short info", "schema": {"$ref": "#/definitions/EntityShortInfo"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/balances": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:balance", "entity"]}], "tags": ["Entity"], "summary": "Gets key entity list of currencies balances", "responses": {"200": {"description": "List of currencies balances", "schema": {"$ref": "#/definitions/Balances"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/balances": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity:balance", "entity"]}], "tags": ["Entity"], "summary": "Gets entity list of currencies balances by path", "responses": {"200": {"description": "List of currencies balances", "schema": {"$ref": "#/definitions/Balances"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/audits": {"get": {"security": [{"apiKey": []}, {"Permissions": ["audit"]}], "tags": ["Entity"], "summary": "Gets list of audits by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/auditOperation__contains"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/auditId"}, {"$ref": "#/parameters/auditId__in"}, {"$ref": "#/parameters/initiatorType"}, {"$ref": "#/parameters/initiatorServiceName"}, {"$ref": "#/parameters/initiatorName"}, {"$ref": "#/parameters/initiatorName__in"}, {"$ref": "#/parameters/initiatorName__contains"}, {"$ref": "#/parameters/initiatorName__contains!"}, {"$ref": "#/parameters/ip"}, {"$ref": "#/parameters/ip__in"}, {"$ref": "#/parameters/ip__contains"}, {"$ref": "#/parameters/ip__contains!"}, {"$ref": "#/parameters/userAgent"}, {"$ref": "#/parameters/userAgent__in"}, {"$ref": "#/parameters/userAgent__contains"}, {"$ref": "#/parameters/userAgent__contains!"}, {"$ref": "#/parameters/initiatorIssueId"}, {"$ref": "#/parameters/initiatorIssueId__in"}, {"$ref": "#/parameters/initiatorIssueId__contains"}, {"$ref": "#/parameters/initiatorIssueId__contains!"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/auditsSessionId"}, {"$ref": "#/parameters/includeSubEntities"}, {"$ref": "#/parameters/auditsSummaryId"}, {"$ref": "#/parameters/auditsSummaryId__in"}, {"$ref": "#/parameters/auditsSummaryId__in!"}, {"$ref": "#/parameters/auditsSummaryEventName"}, {"$ref": "#/parameters/auditsSummaryMethod"}, {"$ref": "#/parameters/auditsSummaryMethod__in"}, {"$ref": "#/parameters/auditsSummaryMethod__in!"}, {"$ref": "#/parameters/auditsSummaryPath"}, {"$ref": "#/parameters/auditsSummaryPath__in"}, {"$ref": "#/parameters/auditsSummaryPath__in!"}, {"$ref": "#/parameters/auditsSummarySummary"}, {"$ref": "#/parameters/auditsSummarySummary__in"}, {"$ref": "#/parameters/auditsSummarySummary__in!"}], "responses": {"200": {"description": "List of audits", "schema": {"type": "array", "items": {"$ref": "#/definitions/AuditSchema"}}}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n- 907: Your request took too long time, please change your request\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n"}}}}, "/audits": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:audit"]}], "tags": ["Entity"], "summary": "Gets list of audits under the key entity", "parameters": [{"$ref": "#/parameters/auditOperation__contains"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/auditId"}, {"$ref": "#/parameters/auditId__in"}, {"$ref": "#/parameters/initiatorType"}, {"$ref": "#/parameters/initiatorServiceName"}, {"$ref": "#/parameters/initiatorName"}, {"$ref": "#/parameters/initiatorName__in"}, {"$ref": "#/parameters/initiatorName__contains"}, {"$ref": "#/parameters/initiatorName__contains!"}, {"$ref": "#/parameters/initiatorIssueId"}, {"$ref": "#/parameters/initiatorIssueId__in"}, {"$ref": "#/parameters/initiatorIssueId__contains"}, {"$ref": "#/parameters/initiatorIssueId__contains!"}, {"$ref": "#/parameters/ip"}, {"$ref": "#/parameters/ip__in"}, {"$ref": "#/parameters/ip__contains"}, {"$ref": "#/parameters/ip__contains!"}, {"$ref": "#/parameters/userAgent"}, {"$ref": "#/parameters/userAgent__in"}, {"$ref": "#/parameters/userAgent__contains"}, {"$ref": "#/parameters/userAgent__contains!"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/auditsSessionId"}, {"$ref": "#/parameters/includeSubEntities"}, {"$ref": "#/parameters/auditsSummaryId"}, {"$ref": "#/parameters/auditsSummaryId__in"}, {"$ref": "#/parameters/auditsSummaryId__in!"}, {"$ref": "#/parameters/auditsSummaryEventName"}, {"$ref": "#/parameters/auditsSummaryMethod"}, {"$ref": "#/parameters/auditsSummaryMethod__in"}, {"$ref": "#/parameters/auditsSummaryMethod__in!"}, {"$ref": "#/parameters/auditsSummaryPath"}, {"$ref": "#/parameters/auditsSummaryPath__in"}, {"$ref": "#/parameters/auditsSummaryPath__in!"}, {"$ref": "#/parameters/auditsSummarySummary"}, {"$ref": "#/parameters/auditsSummarySummary__in"}, {"$ref": "#/parameters/auditsSummarySummary__in!"}], "responses": {"200": {"description": "List of audits", "schema": {"type": "array", "items": {"$ref": "#/definitions/AuditSchema"}}}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n"}}}}, "/audits-summary": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:audit"]}], "tags": ["Entity"], "summary": "Gets list of audit summary under the key entity", "parameters": [{"$ref": "#/parameters/eventName"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "List of audit summary", "schema": {"type": "array", "items": {"$ref": "#/definitions/AuditSummarySchema"}}}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find summary\n"}}}}, "/entities/{path}/audits-sessions": {"get": {"security": [{"apiKey": []}, {"Permissions": ["audit"]}], "tags": ["Entity"], "summary": "Gets list of audit sessions", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/sessionId"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/initiatorName"}, {"$ref": "#/parameters/initiatorName__in"}, {"$ref": "#/parameters/initiatorName__contains"}, {"$ref": "#/parameters/initiatorName__contains!"}, {"$ref": "#/parameters/startedAt"}, {"$ref": "#/parameters/startedAt__gt"}, {"$ref": "#/parameters/startedAt__lt"}, {"$ref": "#/parameters/finishedAt"}, {"$ref": "#/parameters/finishedAt__gt"}, {"$ref": "#/parameters/finishedAt__lt"}], "responses": {"200": {"description": "List of audit sessions", "schema": {"type": "array", "items": {"$ref": "#/definitions/AuditSessionSchema"}}}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find audit session\n"}}}}, "/audits-sessions": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:audit"]}], "tags": ["Entity"], "summary": "Gets list of audit sessions under the key entity", "parameters": [{"$ref": "#/parameters/sessionId"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/initiatorName"}, {"$ref": "#/parameters/initiatorName__in"}, {"$ref": "#/parameters/initiatorName__contains"}, {"$ref": "#/parameters/initiatorName__contains!"}, {"$ref": "#/parameters/startedAt"}, {"$ref": "#/parameters/startedAt__gt"}, {"$ref": "#/parameters/startedAt__lt"}, {"$ref": "#/parameters/finishedAt"}, {"$ref": "#/parameters/finishedAt__gt"}, {"$ref": "#/parameters/finishedAt__lt"}], "responses": {"200": {"description": "List of audit sessions", "schema": {"type": "array", "items": {"$ref": "#/definitions/AuditSessionSchema"}}}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find audit session\n"}}}}, "/info/{type}": {"parameters": [{"$ref": "#/parameters/entityInfoType"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity:view", "entity"]}], "tags": ["Entity"], "summary": "Gets additional info for keyentity", "description": "Gets additional information for an entity be type. Additional information could be JSON-object.", "responses": {"200": {"description": "Additional info for an entity", "schema": {"$ref": "#/definitions/EntityAdditionalInfoRecord"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["entity:edit", "entity"]}], "tags": ["Entity"], "summary": "Creates additional info for keyentity", "description": "Method creates additional information for an entity. Type parameter is required. Additional information could be JSON-object.", "parameters": [{"$ref": "#/parameters/EntityAdditionalInfoCreateRecord"}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Additional info for an entity", "schema": {"$ref": "#/definitions/EntityAdditionalInfoRecord"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 763: Additional info for an entity already stored\n"}}}, "put": {"security": [{"apiKey": []}, {"Permissions": ["entity:edit", "entity"]}], "tags": ["Entity"], "summary": "Updates additional info for keyentity or create if it's not exist", "description": "Method creates or updatesadditional information for an entity. Type parameter is required. Additional information could be JSON-object.", "parameters": [{"$ref": "#/parameters/EntityAdditionalInfoCreateRecord"}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Additional info for an entity", "schema": {"$ref": "#/definitions/EntityAdditionalInfoRecord"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity:edit", "entity"]}], "tags": ["Entity"], "summary": "Updates additional info for keyentity", "description": "Method updates additional information for an entity. Type parameter is required. Additional information could be JSON-object. Method throws an error if info not created.", "parameters": [{"$ref": "#/parameters/EntityAdditionalInfoCreateRecord"}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Additional info for an entity", "schema": {"$ref": "#/definitions/EntityAdditionalInfoRecord"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n- 765: Bad query for updating additional info of entity\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 764: Additional info for an entity not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity:edit", "entity"]}], "tags": ["Entity"], "summary": "Removes additional info for keyentity", "description": "Method deletes additional information for an entity. Type parameter is required. Method throws an error if info not created.", "parameters": [{"$ref": "#/parameters/issueCode"}], "responses": {"204": {"description": "Additional information record has been removed"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 764: Additional info for an entity not found\n"}}}}, "/entities/{path}/info/{type}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/entityInfoType"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity:view", "entity"]}], "tags": ["Entity"], "summary": "Gets additional info for entity by path", "description": "Gets additional information for an entity be type. Additional information could be JSON-object.", "responses": {"200": {"description": "Additional info for an entity", "schema": {"$ref": "#/definitions/EntityAdditionalInfoRecord"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 764: Additional info for an entity not found\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["entity:edit", "entity"]}], "tags": ["Entity"], "summary": "Creates additional info for entity by path", "description": "Method creates additional information for an entity. Type parameter is required. Additional information could be JSON-object.", "parameters": [{"$ref": "#/parameters/EntityAdditionalInfoCreateRecord"}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Additional info for an entity", "schema": {"$ref": "#/definitions/EntityAdditionalInfoRecord"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}, "409": {"description": "- 763: Additional info for an entity already stored\n"}}}, "put": {"security": [{"apiKey": []}, {"Permissions": ["entity:edit", "entity"]}], "tags": ["Entity"], "summary": "Updates additional info for keyentity or create if it's not exist by path", "description": "Method creates or updatesadditional information for an entity. Type parameter is required. Additional information could be JSON-object.", "parameters": [{"$ref": "#/parameters/EntityAdditionalInfoCreateRecord"}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Additional info for an entity", "schema": {"$ref": "#/definitions/EntityAdditionalInfoRecord"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity:edit", "entity"]}], "tags": ["Entity"], "summary": "Updates additional info for entity by path", "description": "Method updates additional information for an entity. Type parameter is required. Additional information could be JSON-object. Method throws an error if info not created.", "parameters": [{"$ref": "#/parameters/EntityAdditionalInfoCreateRecord"}, {"$ref": "#/parameters/issueCode"}], "responses": {"200": {"description": "Additional info for an entity", "schema": {"$ref": "#/definitions/EntityAdditionalInfoRecord"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n- 765: Bad query for updating additional info of entity\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 764: Additional info for an entity not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity:edit", "entity"]}], "tags": ["Entity"], "summary": "Removes additional info for entity by path", "description": "Method deletes additional information for an entity. Type parameter is required. Method throws an error if info not created.", "parameters": [{"$ref": "#/parameters/issueCode"}], "responses": {"204": {"description": "Additional information record has been removed"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 764: Additional info for an entity not found\n"}}}}, "/entities/{path}/restricted-countries-solution": {"parameters": [{"$ref": "#/parameters/path"}], "patch": {"security": [{"apiKey": []}, {"Permissions": ["restricted-countries-solution"]}], "tags": ["Entity"], "summary": "Patch entity settings by useCountriesFromJurisdiction: true/false", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/RestrictedCountriesSolution"}}], "responses": {"200": {"description": "Updated entity settings with useCountriesFromJurisdiction parameter", "schema": {"$ref": "#/definitions/RestrictedCountriesSolution"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 64: En<PERSON><PERSON> is not empty!\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "409": {"description": "- 112: <PERSON><PERSON><PERSON> has duplicates\n", "schema": {"$ref": "#/definitions/Error"}}}}}}