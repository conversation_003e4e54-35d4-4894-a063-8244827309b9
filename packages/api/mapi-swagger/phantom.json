{"/entities/{path}/phantom/jackpots": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/jpCurrencyRequiredInQuery"}, {"$ref": "#/parameters/requiredPlayerCodeStrictEquality"}, {"$ref": "#/parameters/requiredGameCodeStrictEquality"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:instance", "jackpot:instance:view"]}], "tags": ["Phantom"], "summary": "Get phantom Jackpots", "responses": {"200": {"description": "Phantom jackpots", "schema": {"$ref": "#/definitions/PhantomJackpots"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/phantom/jackpots": {"parameters": [{"$ref": "#/parameters/jpCurrencyRequiredInQuery"}, {"$ref": "#/parameters/requiredPlayerCodeStrictEquality"}, {"$ref": "#/parameters/requiredGameCodeStrictEquality"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:jackpot", "keyentity:jackpot:instance", "keyentity:jackpot:instance:view"]}], "tags": ["Phantom"], "summary": "Get phantom Jackpots", "responses": {"200": {"description": "Phantom jackpots", "schema": {"$ref": "#/definitions/PhantomJackpots"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}