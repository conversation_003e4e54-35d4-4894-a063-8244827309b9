{"/terminals": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:terminal", "keyentity:terminal:view"]}], "tags": ["Terminal"], "summary": "Gets list of terminals", "description": "Method gets all terminals for a brand filtered by statuses, dates and titles", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/status"}, {"$ref": "#/parameters/searchTitleCode"}, {"$ref": "#/parameters/titleStrictEquality"}, {"$ref": "#/parameters/titleContains"}, {"$ref": "#/parameters/idStrictEquality"}, {"$ref": "#/parameters/idIn"}, {"$ref": "#/parameters/lobbyPublicId"}, {"$ref": "#/parameters/lobbyPublicIdIn"}, {"$ref": "#/parameters/createdAt"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__gte"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/createdAt__lte"}, {"$ref": "#/parameters/updatedAt"}, {"$ref": "#/parameters/updatedAt__gt"}, {"$ref": "#/parameters/updatedAt__gte"}, {"$ref": "#/parameters/updatedAt__lt"}, {"$ref": "#/parameters/updatedAt__lte"}], "responses": {"200": {"description": "List of terminals", "schema": {"type": "array", "items": {"$ref": "#/definitions/TerminalData"}}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:terminal", "keyentity:terminal:create"]}], "tags": ["Terminal"], "summary": "Creates terminal under keyentity", "description": "Method creates new terminal for a brand by title and lobby id and returns its info with public id", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/TerminalCreateData"}}], "responses": {"201": {"description": "Created terminal", "schema": {"$ref": "#/definitions/TerminalData"}}, "400": {"description": "- 101: Not a brand\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 324: <PERSON><PERSON> is not found\n"}, "409": {"description": "- 326: Terminal already exists\n"}}}}, "/terminals/{terminalId}": {"parameters": [{"$ref": "#/parameters/terminalId"}], "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:terminal", "keyentity:terminal:edit"]}], "tags": ["Terminal"], "summary": "Updates terminal", "description": "Method updates terminal info (title, status or lobbyId) by its public id. All fields are optional.", "parameters": [{"name": "description", "in": "body", "schema": {"$ref": "#/definitions/TerminalCreateData"}}], "responses": {"200": {"description": "Terminal info", "schema": {"$ref": "#/definitions/TerminalData"}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n- 328: Bad request for terminal updating\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 327: Terminal is not found\n"}, "409": {"description": "- 326: Terminal already exists\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:terminal", "keyentity:terminal:delete"]}], "tags": ["Terminal"], "summary": "Deletes terminal", "description": "Method deletes terminal by its public id", "responses": {"204": {"description": "Terminal has been deleted"}, "400": {"description": "- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 327: Terminal is not found\n"}}}}, "/terminals/players/logout": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:terminal", "keyentity:terminal:edit"]}], "tags": ["Terminal"], "summary": "Logs player out", "description": "Method logs player out from terminal. It works under Access Token.", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/LogoutTerminalPlayerDataInfo"}}], "responses": {"204": {"description": "Player has been logged out"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n"}, "404": {"description": "- 102: Player not found\n- 344: Terminal Session is not found\n"}}}}, "/terminals/token/generate": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:terminal", "keyentity:terminal:token"]}], "tags": ["Terminal"], "summary": "Generates lobby-wrapper token", "description": "Method generetes a lobby-wrapper token for access to manage terminals", "parameters": [{"name": "params", "in": "body", "schema": {"$ref": "#/definitions/TerminalTokenCreateData"}}], "responses": {"200": {"description": "Terminal info", "schema": {"$ref": "#/definitions/TerminalTokenData"}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/terminals/token/generate": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:terminal", "keyentity:terminal:token"]}], "tags": ["Terminal"], "summary": "Generates lobby-wrapper token by path", "description": "Method generetes a lobby-wrapper token for access to manage terminals", "parameters": [{"$ref": "#/parameters/path"}, {"name": "params", "in": "body", "schema": {"$ref": "#/definitions/TerminalTokenCreateData"}}], "responses": {"200": {"description": "Terminal info", "schema": {"$ref": "#/definitions/TerminalTokenData"}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{entityId}/terminal-token/lock": {"post": {"security": [{"apiKey": []}, {"Permissions": ["terminal:token:add"]}], "tags": ["Terminal"], "summary": "Lock terminal token", "description": "Lock terminal token", "parameters": [{"$ref": "#/parameters/entityId"}, {"name": "params", "in": "body", "schema": {"$ref": "#/definitions/TerminalTokenLock"}}], "responses": {"201": {"description": "Terminal token has been added to blacklist"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["terminal:token:delete"]}], "tags": ["Terminal"], "summary": "Deletes terminal token", "description": "Deletes terminal token", "parameters": [{"$ref": "#/parameters/entityId"}], "responses": {"204": {"description": "Terminal token has been deleted"}, "400": {"description": "- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}}