{"/stake-ranges": {"get": {"security": [{"apiKey": []}, {"Permissions": ["stake-range"]}], "tags": ["Stake Ranges"], "summary": "Return stake ranges per currency", "responses": {"200": {"description": "Returns stake ranges per currency", "schema": {"$ref": "#/definitions/StakeRanges"}}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["stake-range-update"]}], "tags": ["Stake Ranges"], "parameters": [{"$ref": "#/parameters/updateStakeRange"}], "summary": "Update stake ranges per currency", "responses": {"200": {"description": "Returns stake ranges per currency", "schema": {"$ref": "#/definitions/StakeRange"}}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["stake-range-create"]}], "tags": ["Stake Ranges"], "parameters": [{"$ref": "#/parameters/updateStakeRange"}], "summary": "Create stake ranges per currency", "responses": {"200": {"description": "Returns stake ranges per currency", "schema": {"$ref": "#/definitions/StakeRange"}}}}}, "/stake-ranges/{currency}": {"delete": {"security": [{"apiKey": []}, {"Permissions": ["stake-range-delete"]}], "tags": ["Stake Ranges"], "parameters": [{"$ref": "#/parameters/currency"}], "summary": "Drop stake ranges per currency", "responses": {"204": {"description": "Successful deleted"}}}}}