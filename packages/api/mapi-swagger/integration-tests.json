{"/test": {"get": {"tags": ["Integration tests"], "security": [{"apiKey": []}, {"Permissions": ["integrationtests", "integrationtests:view"]}], "summary": "Get reports", "description": "Get reports", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/integrationTestSortBy"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "list of tests", "schema": {"type": "array", "items": {"$ref": "#/definitions/TestReport"}}}, "400": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n- 205: Access Token is expired\n- 206: User doesn't have permission to execute operation\n", "schema": {"$ref": "#/definitions/IntegrationTestsError"}}}}}, "/test/{id}": {"parameters": [{"$ref": "#/parameters/integrationTestId"}, {"$ref": "#/parameters/integrationTestReportFormat"}], "get": {"tags": ["Integration tests"], "security": [{"apiKey": []}, {"Permissions": ["integrationtests", "integrationtests:view"]}], "summary": "Get integration tests report", "responses": {"200": {"description": "Test run info", "schema": {"$ref": "#/definitions/TestReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n- 205: Access Token is expired\n- 206: User doesn't have permission to execute operation\n", "schema": {"$ref": "#/definitions/IntegrationTestsError"}}}}}, "/test/run": {"post": {"tags": ["Integration tests"], "security": [{"apiKey": []}, {"Permissions": ["integrationtests", "integrationtests:run"]}], "summary": "Run integration tests", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/TestInfo"}}], "responses": {"200": {"description": "Test run info", "schema": {"$ref": "#/definitions/TestRunInfo"}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n- 504: Not a merchant brand\n- 62: One of the parents is suspended", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 743: Games not available\n- 51: Could not find entity\n- 502: Merchant not found", "schema": {"$ref": "#/definitions/Error"}}}}}, "/test/history/{merchantCode}": {"parameters": [{"$ref": "#/parameters/merchantCode"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortHistoryBy"}, {"$ref": "#/parameters/sortOrder"}], "get": {"tags": ["Integration tests"], "security": [{"apiKey": []}, {"Permissions": ["integrationtests", "integrationtests:view"]}], "summary": "Get executions list of tests for the merchant", "responses": {"200": {"description": "List of executed tests for the merchant", "schema": {"$ref": "#/definitions/TestHistoryReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n- 205: Access Token is expired\n- 206: User doesn't have permission to execute operation\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/merchants/{path}/test": {"get": {"tags": ["Integration tests"], "security": [{"apiKey": []}, {"Permissions": ["integrationtests", "integrationtests:view"]}], "summary": "Get reports", "description": "Get reports", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/integrationTestSortBy"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "list of tests", "schema": {"type": "array", "items": {"$ref": "#/definitions/TestReport"}}}, "400": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n- 205: Access Token is expired\n- 206: User doesn't have permission to execute operation\n", "schema": {"$ref": "#/definitions/IntegrationTestsError"}}}}}, "/merchants/{path}/test/run": {"post": {"tags": ["Integration tests"], "security": [{"apiKey": []}, {"Permissions": ["integrationtests", "integrationtests:run"]}], "summary": "Run integration tests", "parameters": [{"$ref": "#/parameters/path"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/TestInfo"}}], "responses": {"200": {"description": "Test run info", "schema": {"$ref": "#/definitions/TestRunInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n- 205: Access Token is expired\n- 206: User doesn't have permission to execute operation\n", "schema": {"$ref": "#/definitions/IntegrationTestsError"}}}}}, "/merchants/{path}/test/{id}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/integrationTestId"}, {"$ref": "#/parameters/integrationTestReportFormat"}], "get": {"tags": ["Integration tests"], "security": [{"apiKey": []}, {"Permissions": ["integrationtests", "integrationtests:view"]}], "summary": "Get integration tests report", "responses": {"200": {"description": "Test run info", "schema": {"$ref": "#/definitions/TestReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n- 205: Access Token is expired\n- 206: User doesn't have permission to execute operation\n", "schema": {"$ref": "#/definitions/IntegrationTestsError"}}}}}, "/merchants/{path}/test/history/{merchantCode}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/merchantCode"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortHistoryBy"}, {"$ref": "#/parameters/sortOrder"}], "get": {"tags": ["Integration tests"], "security": [{"apiKey": []}, {"Permissions": ["integrationtests", "integrationtests:view"]}], "summary": "Get executions list of tests for the merchant", "responses": {"200": {"description": "List of executed tests for the merchant", "schema": {"$ref": "#/definitions/TestHistoryReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n- 205: Access Token is expired\n- 206: User doesn't have permission to execute operation\n", "schema": {"$ref": "#/definitions/Error"}}}}}}