{"/entities/group/{path}/games": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:change-state", "entity:game:add-game-cascade"]}], "tags": ["Game"], "summary": "Add games to all entities in hierarchy by path", "parameters": [{"$ref": "#/parameters/gameCodes"}], "responses": {"200": {"description": "Number of added games", "schema": {"$ref": "#/definitions/BulkAddEntityGamesResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 743: Games not available\n"}, "409": {"description": "- 241: Game already exists\n"}}}}, "/entities/{path}/games": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:view"]}], "tags": ["Game"], "summary": "Gets list of entity's game by path", "description": "Gets games for entity, filtered by labels or category. Case sortBy == categoryList returns games from included list of category before others.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/codeStrictEquality"}, {"$ref": "#/parameters/titleStrictEquality"}, {"$ref": "#/parameters/titleContains"}, {"$ref": "#/parameters/titleNotContains"}, {"$ref": "#/parameters/typeStrictEquality"}, {"$ref": "#/parameters/jackpots"}, {"$ref": "#/parameters/includeLive"}, {"$ref": "#/parameters/gameCategoryId"}, {"$ref": "#/parameters/gameProviderId"}, {"$ref": "#/parameters/gameProviderCodeEquality"}, {"$ref": "#/parameters/gameProviderCode__in"}, {"$ref": "#/parameters/gameProviderTitleEquality"}, {"$ref": "#/parameters/gameProviderTitleContains"}, {"$ref": "#/parameters/gameProviderTitleNotContains"}, {"$ref": "#/parameters/labelsIdIn"}, {"$ref": "#/parameters/isFreebetSupported"}, {"$ref": "#/parameters/isBonusCoinsSupported"}, {"$ref": "#/parameters/isMarketplaceSupported"}, {"$ref": "#/parameters/isCustomLimitsSupported"}, {"$ref": "#/parameters/decreaseMaxBetSupported"}, {"$ref": "#/parameters/increaseMinBetSupported"}, {"$ref": "#/parameters/limitFiltersWillBeApplied"}, {"$ref": "#/parameters/transferEnabled"}, {"$ref": "#/parameters/isGRCGame"}, {"$ref": "#/parameters/jackpotTypes"}, {"$ref": "#/parameters/live"}, {"$ref": "#/parameters/features"}, {"$ref": "#/parameters/shortInfo"}, {"$ref": "#/parameters/limitsCurrencyInQuery"}, {"$ref": "#/parameters/schemaDefinitionIdInQuery"}, {"$ref": "#/parameters/physicalTableIdStrictEquality"}, {"$ref": "#/parameters/physicalTableIdIn"}, {"$ref": "#/parameters/excludeInactiveGames"}], "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:change-state"]}], "tags": ["Game"], "summary": "Add games to entity by path", "parameters": [{"$ref": "#/parameters/gameCodes"}], "responses": {"200": {"description": "List of games code", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameShortInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 867: Parent game type not match with child game type\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}, "409": {"description": "- 241: Game already exists\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:change-state"]}], "tags": ["Game"], "summary": "Remove games from entity by path", "parameters": [{"$ref": "#/parameters/gameCodes"}], "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameShortInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}, "409": {"description": "- 303: Failed to delete entity game with child entity games\n"}}}}, "/games": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:view"]}], "tags": ["Game"], "summary": "Gets list of key entity's game", "description": "Gets games for entity, filtered by labels or category. Case sortBy == categoryList returns games from included list of category before others.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/codeStrictEquality"}, {"$ref": "#/parameters/code__in"}, {"$ref": "#/parameters/titleStrictEquality"}, {"$ref": "#/parameters/titleContains"}, {"$ref": "#/parameters/titleNotContains"}, {"$ref": "#/parameters/jackpots"}, {"$ref": "#/parameters/includeLive"}, {"$ref": "#/parameters/gameCategoryId"}, {"$ref": "#/parameters/gameProviderId"}, {"$ref": "#/parameters/gameProviderCodeEquality"}, {"$ref": "#/parameters/gameProviderCode__in"}, {"$ref": "#/parameters/gameProviderTitleEquality"}, {"$ref": "#/parameters/gameProviderTitleContains"}, {"$ref": "#/parameters/gameProviderTitleNotContains"}, {"$ref": "#/parameters/labelsIdIn"}, {"$ref": "#/parameters/isFreebetSupported"}, {"$ref": "#/parameters/isBonusCoinsSupported"}, {"$ref": "#/parameters/isMarketplaceSupported"}, {"$ref": "#/parameters/isCustomLimitsSupported"}, {"$ref": "#/parameters/decreaseMaxBetSupported"}, {"$ref": "#/parameters/increaseMinBetSupported"}, {"$ref": "#/parameters/limitFiltersWillBeApplied"}, {"$ref": "#/parameters/transferEnabled"}, {"$ref": "#/parameters/isGRCGame"}, {"$ref": "#/parameters/jackpotTypes"}, {"$ref": "#/parameters/live"}, {"$ref": "#/parameters/features"}, {"$ref": "#/parameters/shortInfo"}, {"$ref": "#/parameters/limitsCurrencyInQuery"}, {"$ref": "#/parameters/schemaDefinitionIdInQuery"}, {"$ref": "#/parameters/physicalTableIdStrictEquality"}, {"$ref": "#/parameters/physicalTableIdIn"}, {"$ref": "#/parameters/typeStrictEquality"}, {"$ref": "#/parameters/excludeInactiveGames"}], "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/games/rtp-deduction": {"parameters": [{"$ref": "#/parameters/path"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:change-state"]}], "tags": ["Game"], "summary": "Update rtp deduction for entity game by path", "description": "Update rtp deduction for entity game by path", "parameters": [{"in": "body", "name": "info", "required": true, "description": "rtp deduction bulk update parameters", "schema": {"$ref": "#/definitions/rtpDeductionBulkUpdate"}}], "responses": {"204": {"description": "Successful update games code rtp deduction"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}}}}, "/entities/{path}/games/{gameCode}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/gameCode"}], "get": {"parameters": [{"$ref": "#/parameters/addAggregatedFinalLimits"}, {"$ref": "#/parameters/skipJurisdictionFiltering"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/gameGroupNameInQuery"}, {"$ref": "#/parameters/segmentIdInQuery"}], "security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:view"]}], "tags": ["Game"], "summary": "Get entity's game info by path", "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:change-state"]}], "tags": ["Game"], "summary": "Add game to entity by path", "parameters": [{"$ref": "#/parameters/changeEntityGame"}], "responses": {"200": {"description": "Game code", "schema": {"$ref": "#/definitions/GameShortInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}, "409": {"description": "- 241: Game already exists\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:change-state"]}], "tags": ["Game"], "summary": "Updates game for entity by path", "description": "Updates settings and status of game for entity by code", "parameters": [{"$ref": "#/parameters/changeEntityGame"}, {"$ref": "#/parameters/allowToFinishCurrentSession"}], "responses": {"200": {"description": "Game code", "schema": {"$ref": "#/definitions/GameShortInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:change-state", "entity:game:delete-cascade"]}], "tags": ["Game"], "summary": "Remove game from entity by path", "parameters": [{"name": "force", "in": "query", "description": "Force to delete with all child entity games. Required 'entity:game:delete-cascade' permission", "required": false, "type": "boolean"}], "responses": {"200": {"description": "List of games code", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameShortInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}, "409": {"description": "- 302: Failed to delete entity game with child entity games. Need force flag\n"}}}}, "/games/rtp-deduction": {"put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:change-state"]}], "tags": ["Game"], "summary": "Update rtp deduction for games", "description": "Update rtp deduction for games", "parameters": [{"in": "body", "name": "info", "required": true, "description": "rtp deduction bulk update parameters", "schema": {"$ref": "#/definitions/rtpDeductionBulkUpdate"}}], "responses": {"204": {"description": "Successful update games code rtp deduction"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 213: Game doesn't available for entity\n- 240: Game not found\n"}}}}, "/games/{gameCode}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:view"]}], "tags": ["Game"], "summary": "Gets key entity's game info", "parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/addAggregatedFinalLimits"}, {"$ref": "#/parameters/skipJurisdictionFiltering"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/gameGroupNameInQuery"}, {"$ref": "#/parameters/segmentIdInQuery"}], "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 306: Game is suspended\n- 690: Bonus coins not available\n- 703: IP address cannot be resolved\n- 708: It is forbidden to start game from unauthorized site\n- 712: Player is suspended\n- 736: Entity is under maintenance, but maintenance url is not defined\n- 751: Referrer is missing\n- 902: Static domain is not defined\n- 903: Dynamic domain is not defined\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n"}, "403": {"description": "- 701: Country of IP is restricted\n"}, "404": {"description": "- 102: Player not found\n- 300: Game not found\n- 502: Merchant not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:change-state"]}], "tags": ["Game"], "summary": "Updates game for key entity", "description": "Updates settings and status of game for key entity by code", "parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/changeEntityGame"}, {"$ref": "#/parameters/allowToFinishCurrentSession"}], "responses": {"200": {"description": "Game code", "schema": {"$ref": "#/definitions/GameShortInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}}}}, "/games/{gameCode}/info": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:view"]}], "tags": ["Game"], "summary": "Gets key entity's game base info", "description": "Method returns base game information by code (title, type, labels, provider information and so on)", "parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/jpCurrencyOptionalInQuery"}, {"$ref": "#/parameters/addAggregatedFinalLimits"}, {"$ref": "#/parameters/skipJurisdictionFiltering"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/gameGroupNameInQuery"}, {"$ref": "#/parameters/segmentIdInQuery"}], "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameBriefInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 240: Game not found\n"}}}}, "/entities/{path}/games/{gameCode}/info": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:view"]}], "tags": ["Game"], "summary": "Gets key entity's game base info", "description": "Method returns base game information by code (title, type, labels, provider information and so on)", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/jpCurrencyOptionalInQuery"}, {"$ref": "#/parameters/addAggregatedFinalLimits"}, {"$ref": "#/parameters/skipJurisdictionFiltering"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/gameGroupNameInQuery"}, {"$ref": "#/parameters/segmentIdInQuery"}], "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameBriefInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 240: Game not found\n"}}}}, "/games/info/search": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:view"]}], "tags": ["Game"], "summary": "Search key entity's games and returns base games info", "description": "Search games for key entity by labels, codes, titles, providers or category and return array of base games' information", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/codeStrictEquality"}, {"$ref": "#/parameters/code__in"}, {"$ref": "#/parameters/jackpots"}, {"$ref": "#/parameters/includeLive"}, {"$ref": "#/parameters/jpCurrencyOptionalInQuery"}, {"$ref": "#/parameters/titleStrictEquality"}, {"$ref": "#/parameters/titleContains"}, {"$ref": "#/parameters/titleNotContains"}, {"$ref": "#/parameters/gameCategoryId"}, {"$ref": "#/parameters/gameProviderId"}, {"$ref": "#/parameters/gameProviderCodeEquality"}, {"$ref": "#/parameters/gameProviderCode__in"}, {"$ref": "#/parameters/gameProviderTitleEquality"}, {"$ref": "#/parameters/gameProviderTitleContains"}, {"$ref": "#/parameters/gameProviderTitleNotContains"}, {"$ref": "#/parameters/labelsIdIn"}, {"$ref": "#/parameters/isFreebetSupported"}, {"$ref": "#/parameters/isBonusCoinsSupported"}, {"$ref": "#/parameters/isMarketplaceSupported"}, {"$ref": "#/parameters/isCustomLimitsSupported"}, {"$ref": "#/parameters/decreaseMaxBetSupported"}, {"$ref": "#/parameters/increaseMinBetSupported"}, {"$ref": "#/parameters/limitFiltersWillBeApplied"}, {"$ref": "#/parameters/transferEnabled"}, {"$ref": "#/parameters/isGRCGame"}, {"$ref": "#/parameters/jackpotTypes"}, {"$ref": "#/parameters/live"}, {"$ref": "#/parameters/features"}, {"$ref": "#/parameters/limitsCurrencyInQuery"}, {"$ref": "#/parameters/excludeInactiveGames"}], "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameBriefInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/games/{gameCode}/jackpot": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:view"]}], "tags": ["Game"], "summary": "Gets key entity's game info with jackpot", "description": "Method returns game information with jackpots, game setting should contain array or string 'jackpotId'", "parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/jpCurrencyOptionalInQuery"}], "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 240: Game not found\n"}}}}, "/games/live/{provider}/live-info": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:view"]}], "tags": ["Game"], "summary": "Gets key entity's Live Casino game (table) info", "description": "Method returns list of live table games info", "parameters": [{"name": "provider", "in": "path", "type": "string", "description": "Live game provider", "required": true}, {"$ref": "#/parameters/tableId__in"}], "responses": {"200": {"description": "Live game info for tables", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameLive"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 240: Game not found\n"}}}}, "/entities/{path}/games/{gameCode}/suspended": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/gameCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:change-state", "entity:game:change-state:disabled"]}], "tags": ["Game"], "parameters": [{"$ref": "#/parameters/reason"}, {"$ref": "#/parameters/allowToFinishCurrentSession"}], "summary": "Suspends game for entity by path", "description": "Changes the status of a game to suspended", "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameShortInfo"}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:change-state", "entity:game:change-state:enabled"]}], "tags": ["Game"], "summary": "Restores game for entity by path", "description": "Changes the status of a game to normal", "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameShortInfo"}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/games/{gameCode}/suspended": {"parameters": [{"$ref": "#/parameters/gameCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:change-state", "keyentity:game:change-state:disabled"]}], "tags": ["Game"], "parameters": [{"$ref": "#/parameters/reason"}, {"$ref": "#/parameters/allowToFinishCurrentSession"}], "summary": "Suspends key entity game", "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameShortInfo"}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:change-state", "keyentity:game:change-state:enabled"]}], "tags": ["Game"], "summary": "Restores key entity game", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/GameShortInfo"}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/games/group/status": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:change-state"]}], "tags": ["Game"], "summary": "Sets status for group of games under a specific brand by path", "parameters": [{"$ref": "#/parameters/gameGroupStatus"}, {"$ref": "#/parameters/reason"}], "responses": {"204": {"description": "Statuses changed"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 101: Not a brand\n- 151: Too many items for group action\n- 152: Incorrect action query\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/games/group/status": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:change-state"]}], "tags": ["Game"], "summary": "Sets status for group of games under key entity", "parameters": [{"$ref": "#/parameters/gameGroupStatus"}, {"$ref": "#/parameters/reason"}], "responses": {"204": {"description": "Statuses changed"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 101: Not a brand\n- 151: Too many items for group action\n- 152: Incorrect action query\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/games/group/limits": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:limits"]}], "tags": ["Game"], "summary": "Sets limitFilters for group of games under a specific brand by path", "parameters": [{"$ref": "#/parameters/gameGroupLimitFilters"}], "responses": {"204": {"description": "Filter limits was changed"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 101: Not a brand\n- 151: Too many items for group action\n- 152: Incorrect action query\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/games/group/limits": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:limits"]}], "tags": ["Game"], "summary": "Sets limitFilters for group of games for whole structure", "parameters": [{"$ref": "#/parameters/gameGroupLimitFilters"}], "responses": {"204": {"description": "Filter limits was changed"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 101: Not a brand\n- 151: Too many items for group action\n- 152: Incorrect action query\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/players/{playerCode}/games/{gameCode}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:url"]}], "tags": ["Game"], "summary": "Gets player game URL for entity by path", "parameters": [{"$ref": "#/parameters/ip"}, {"$ref": "#/parameters/ip_header"}, {"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/ticket"}, {"$ref": "#/parameters/playmode"}, {"$ref": "#/parameters/languageInQuery"}, {"$ref": "#/parameters/cashier"}, {"$ref": "#/parameters/lobby"}], "responses": {"200": {"description": "Game URL for player", "schema": {"$ref": "#/definitions/PlayerGameURLInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 323: Game token expired\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 240: Game not found\n"}, "500": {"description": "- 505: Merchant should have serverUrl in params\n- 506: Merchant internal error\n- 507: Error during integration with merchant\n"}}}}, "/players/{playerCode}/games/{gameCode}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:url"]}], "tags": ["Game"], "summary": "Gets player game URL", "parameters": [{"$ref": "#/parameters/ip"}, {"$ref": "#/parameters/ip_header"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/ticket"}, {"$ref": "#/parameters/playmode"}, {"$ref": "#/parameters/languageInQuery"}, {"$ref": "#/parameters/cashier"}, {"$ref": "#/parameters/lobby"}], "responses": {"200": {"description": "Game URL for player", "schema": {"$ref": "#/definitions/PlayerGameURLInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 323: Game token expired\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 240: Game not found\n"}, "500": {"description": "- 505: Merchant should have serverUrl in params\n- 506: Merchant internal error\n- 507: Error during integration with merchant\n"}}}}, "/entities/{path}/fun/games/{gameCode}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:url"]}], "tags": ["Game"], "summary": "Gets game URL for entity by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/ticket"}, {"$ref": "#/parameters/languageInQuery"}, {"$ref": "#/parameters/merchantLoginUrl"}, {"$ref": "#/parameters/cashier"}, {"$ref": "#/parameters/lobby"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/gameGroupStrictEquality"}], "responses": {"200": {"description": "Game URL for anonymous player", "schema": {"$ref": "#/definitions/PlayerGameURLInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n"}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n- 102: Player not found\n- 240: Game not found\n"}}}}, "/fun/games/{gameCode}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:url"]}], "tags": ["Game"], "summary": "Gets game URL", "parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/ticket"}, {"$ref": "#/parameters/languageInQuery"}, {"$ref": "#/parameters/merchantLoginUrl"}, {"$ref": "#/parameters/cashier"}, {"$ref": "#/parameters/lobby"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/gameGroupStrictEquality"}], "responses": {"200": {"description": "Game URL for anonymous player", "schema": {"$ref": "#/definitions/PlayerGameURLInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 240: Game not found\n"}}}}, "/entities/{path}/live-games": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["entity:live-game", "entity:live-game:add-live-game"]}], "tags": ["Game"], "summary": "Add live games to entity by path", "parameters": [{"$ref": "#/parameters/gameCodes"}], "responses": {"200": {"description": "List of games code", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameShortInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}, "409": {"description": "- 241: Game already exists\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity:live-game", "entity:live-game:remove-live-game"]}], "tags": ["Game"], "summary": "Remove live games from entity by path", "parameters": [{"$ref": "#/parameters/gameCodes"}], "responses": {"200": {"description": "List of games code", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameShortInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}, "409": {"description": "- 303: Failed to delete entity game with child entity games\n"}}}}, "/entities/{path}/live-games/{gameCode}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/gameCode"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["entity:live-game", "entity:live-game:add-live-game"]}], "tags": ["Game"], "summary": "Add live game to entity by path", "parameters": [{"$ref": "#/parameters/changeEntityGame"}], "responses": {"200": {"description": "Game code", "schema": {"$ref": "#/definitions/GameShortInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 867: Parent game type not match with child game type\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}, "409": {"description": "- 241: Game already exists\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity:live-game", "entity:live-game:remove-live-game"]}], "tags": ["Game"], "summary": "Remove live game from entity by path", "parameters": [{"name": "force", "in": "query", "description": "Force to delete with all child entity games", "required": false, "type": "boolean"}], "responses": {"200": {"description": "List of live games code", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameShortInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}, "409": {"description": "- 302: Failed to delete entity game with child entity games. Need force flag\n"}}}}, "/live-games/{gameCode}/suspended": {"parameters": [{"$ref": "#/parameters/gameCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:live-game", "keyentity:live-game:change-state", "keyentity:live-game:change-state:disabled"]}], "tags": ["Game"], "parameters": [{"$ref": "#/parameters/reason"}, {"$ref": "#/parameters/allowToFinishCurrentSession"}], "summary": "Suspends key entity live game", "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameShortInfo"}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:live-game", "keyentity:live-game:change-state", "keyentity:live-game:change-state:enabled"]}], "tags": ["Game"], "summary": "Restores key entity live game", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/GameShortInfo"}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/live-games/{gameCode}/suspended": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/gameCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["entity:live-game", "entity:live-game:change-state", "entity:live-game:change-state:disabled"]}], "tags": ["Game"], "parameters": [{"$ref": "#/parameters/reason"}, {"$ref": "#/parameters/allowToFinishCurrentSession"}], "summary": "Suspends live game for entity by path", "description": "Changes the status of a live game to suspended", "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameShortInfo"}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity:live-game", "entity:live-game:change-state", "entity:live-game:change-state:enabled"]}], "tags": ["Game"], "summary": "Restores live game for entity by path", "description": "Changes the status of a live game to normal", "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameShortInfo"}}, "400": {"description": "- 40: Validation error\n- 101: Not a brand\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}