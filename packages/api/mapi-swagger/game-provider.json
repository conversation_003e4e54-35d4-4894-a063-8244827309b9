{"/gameproviders": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game<PERSON><PERSON><PERSON>", "keyentity:gameprovider:view"]}], "tags": ["Game Provider"], "summary": "Get list of game providers", "parameters": [{"$ref": "#/parameters/isTest"}], "responses": {"200": {"description": "List of game providers", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameProviderInfo"}}}, "400": {"description": "- 62: One of the parents is suspended\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game<PERSON><PERSON><PERSON>", "keyentity:gameprovider:create"]}], "tags": ["Game Provider"], "summary": "Creates game provider for key entity", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"type": "object", "required": ["user", "code", "title", "secret"], "properties": {"user": {"type": "string", "description": "game provider user", "example": "provider1"}, "code": {"type": "string", "description": "game provider code", "example": "PR"}, "title": {"type": "string", "description": "game provider title", "example": "Provider 1"}, "secret": {"type": "string", "description": "game provider secret", "example": "secret1"}, "isTest": {"type": "boolean", "description": "is game provider created only for testing", "example": true}, "mustStoreExtHistory": {"type": "string", "description": "Set to true for external game providers that need game history to be saved on our side", "example": "false"}}}}], "responses": {"201": {"description": "Created game provider", "schema": {"$ref": "#/definitions/GameProviderInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}, "409": {"description": "- 310: Game provider already exists\n"}}}}, "/gameproviders/{providerId}/secret": {"put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game<PERSON><PERSON><PERSON>", "keyentity:gameprovider:change-secret"]}], "tags": ["Game Provider"], "summary": "Changes game provider secret", "parameters": [{"$ref": "#/parameters/providerId"}, {"in": "body", "name": "info", "required": true, "schema": {"type": "object", "required": ["secret"], "properties": {"secret": {"type": "string", "description": "secret", "example": "secret2"}}}}], "responses": {"200": {"description": "Game provider information", "schema": {"$ref": "#/definitions/GameProviderInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 311: Game provider is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 312: Game provider not found\n"}, "409": {"description": "- 310: Game provider already exists\n"}}}}, "/gameproviders/{providerId}/suspended": {"parameters": [{"$ref": "#/parameters/providerId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game<PERSON><PERSON><PERSON>", "keyentity:gameprovider:change-state"]}], "tags": ["Game Provider"], "summary": "Suspends game provider", "description": "Changes the status of the game provider to suspended", "responses": {"200": {"description": "Game provider information", "schema": {"$ref": "#/definitions/GameProviderInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 312: Game provider not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game<PERSON><PERSON><PERSON>", "keyentity:gameprovider:change-state"]}], "tags": ["Game Provider"], "summary": "Restores game provider", "description": "Changes the status of the game provider to normal", "responses": {"200": {"description": "Game provider information", "schema": {"$ref": "#/definitions/GameProviderInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 312: Game provider not found\n"}}}}, "/game": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:create"]}], "tags": ["Game Provider"], "summary": "Registers new game.", "parameters": [{"$ref": "#/parameters/gameInformation"}], "responses": {"201": {"description": "New game code", "schema": {"$ref": "#/definitions/GameCodeInfo"}}, "400": {"description": "- 40: Validation error\n- 871: Live games can be created through Live Admin only\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 793: Object not found by ID\n"}}}}, "/game/slot": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:create"]}], "tags": ["Game Provider"], "summary": "Registers new slot game", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/SlotGameRegistration"}}], "responses": {"201": {"description": "New slot game code", "schema": {"$ref": "#/definitions/GameCodeInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/game/action": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:create"]}], "tags": ["Game Provider"], "summary": "Registers new action game", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/ActionGameRegistration"}}], "responses": {"201": {"description": "New action game code", "schema": {"$ref": "#/definitions/GameCodeInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/game/table": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:create"]}], "tags": ["Game Provider"], "summary": "Registers new table game", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/TableGameRegistration"}}], "responses": {"201": {"description": "New table game code", "schema": {"$ref": "#/definitions/GameCodeInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/game/external": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:create"]}], "tags": ["Game Provider"], "summary": "Registers new external game", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/ExternalGameRegistration"}}], "responses": {"201": {"description": "New external game code", "schema": {"$ref": "#/definitions/GameCodeInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/game/{gameCode}": {"parameters": [{"$ref": "#/parameters/gameCode"}], "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:edit"]}], "tags": ["Game Provider"], "summary": "Updates game information", "parameters": [{"$ref": "#/parameters/gameUpdateInformation"}], "responses": {"200": {"description": "The game has been updated", "schema": {"$ref": "#/definitions/GameCodeInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 240: Game not found\n- 793: Schema Definition not found by ID (edqrvq7E)\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:view"]}], "tags": ["Game Provider"], "summary": "Gets game information", "responses": {"200": {"description": "The game's information has been returned", "schema": {"$ref": "#/definitions/GameCodeInfo"}}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:delete"]}], "tags": ["Game Provider"], "summary": "Disables game for a specific brand", "parameters": [{"name": "removeEntityGames", "in": "query", "description": "Delete all related entity games", "required": false, "type": "boolean"}], "responses": {"200": {"description": "The game has been disabled", "schema": {"$ref": "#/definitions/GameCodeInfo"}}}}}, "/gameproviders/available": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:external-game-provider:availability"]}], "tags": ["Game Provider"], "summary": "Get game providers that are currently in use by key entity", "parameters": [], "responses": {"200": {"description": "List of game providers that are in use by entity  ", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameProviderInfo"}}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> is expired\n"}}}}, "/entities/{path}/gameproviders/available": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:external-game-provider:availability"]}], "tags": ["Game Provider"], "summary": "Get game providers that are currently in use by key entity by path", "parameters": [{"$ref": "#/parameters/path"}], "responses": {"200": {"description": "List of game providers that are in use by entity  ", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameProviderInfo"}}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> is expired\n"}}}}, "/game/{gameCode}/client-features": {"parameters": [{"$ref": "#/parameters/gameCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:view"]}], "tags": ["Game Provider"], "summary": "Gets game client features", "responses": {"200": {"description": "Client features", "schema": {"$ref": "#/definitions/ClientFeatures"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: Token is expired\n- 311: Game provider (providerCode) is suspended"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired"}, "403": {"description": "- 206: Forbidden"}, "404": {"description": "- 240: Game not found"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:edit"]}], "tags": ["Game Provider"], "summary": "Sets game client features", "parameters": [{"$ref": "#/parameters/setGameClientFeatures"}], "responses": {"200": {"description": "Client features", "schema": {"$ref": "#/definitions/ClientFeatures"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON>ken is expired\n- 40: Validation error\n- 311: Game provider (providerCode) is suspended"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n- 793: Schema Definition not found by ID (schemaDefinitionId)"}, "403": {"description": "- 103: Limits for game type (type) and currency (currency) incorrect.\n- 206: Forbidden"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON><PERSON> not found\n- 240: Game not found"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:delete"]}], "tags": ["Game Provider"], "summary": "Deletes game client features", "responses": {"204": {"description": "Client features have been removed"}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON>ken is expired- 40: Validation error\n- 311: Game provider (providerCode) is suspended"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n- 793: Schema Definition not found by ID (schemaDefinitionId)`"}, "403": {"description": "- 103: Limits for game type (type) and currency (currency) incorrect.\n- 206: Forbidden"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON><PERSON> not found\n- 240: Game not found"}}}}, "/game/{gameCode}/activate": {"parameters": [{"$ref": "#/parameters/gameCode"}], "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:edit"]}], "tags": ["Game Provider"], "summary": "Activate game", "responses": {"200": {"description": "The game has been updated", "schema": {"$ref": "#/definitions/GameCodeInfo"}}, "400": {"description": "- 311: Game provider (providerCode) is suspended"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n- 792: Access Session is expired for userId {userId} and sessionId {sessionId}"}, "403": {"description": "- 206: Forbidden"}, "404": {"description": "- 51: Could not find entity\n- 240: Game not found"}, "409": {"description": "- 63: <PERSON><PERSON><PERSON> is being edited now"}}}}, "/game/{gameCode}/deactivate": {"parameters": [{"$ref": "#/parameters/gameCode"}, {"name": "removeEntityGames", "in": "query", "description": "Delete all related entity games", "required": false, "type": "boolean"}], "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameprovider:game", "keyentity:gameprovider:game:edit"]}], "tags": ["Game Provider"], "summary": "Deactivate game", "responses": {"200": {"description": "The game has been updated", "schema": {"$ref": "#/definitions/GameCodeInfo"}}, "400": {"description": "- 311: Game provider (providerCode) is suspended"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n- 792: Access Session is expired for userId {userId} and sessionId {sessionId}"}, "403": {"description": "- 206: Forbidden"}, "404": {"description": "- 51: Could not find entity\n- 240: Game not found"}, "409": {"description": "- 63: <PERSON><PERSON><PERSON> is being edited now"}}}}}