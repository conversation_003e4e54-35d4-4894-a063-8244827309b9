{"/oauth/token": {"post": {"tags": ["OAuth"], "summary": "Issue access and refresh tokens", "description": "Issue an access and a refresh token for the user using an authorization code", "parameters": [{"in": "body", "name": "reqBody", "required": true, "schema": {"required": ["authorizationCode"], "properties": {"authorizationCode": {"type": "string", "description": "Authorization code issued by the OAuth application for a specific user.", "example": "authorization-code"}, "grantType": {"type": "string", "description": "Grant type. Can be one of two values: accessToken, refreshToken. Default: accessToken.", "enum": ["accessToken", "refreshToken"], "example": "accessToken"}}}}], "responses": {"200": {"description": "Token information", "schema": {"$ref": "#/definitions/OAuthTokenInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 998: <PERSON>gin Failed\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Unauthorized"}}}}, "/oauth/logout": {"post": {"tags": ["OAuth"], "summary": "Logs user out", "description": "Logs user out by revoking the refresh token.", "responses": {"204": {"description": "Log out successfully executed"}, "400": {"description": "Returned in case we have error on the server side", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Unauthorized"}}}}}