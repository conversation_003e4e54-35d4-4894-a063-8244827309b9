{"/lobbies": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:lobby", "keyentity:lobby:view"]}], "tags": ["Lobby"], "summary": "Gets list of lobbies", "parameters": [{"name": "fields", "in": "query", "description": "A comma-separated list of LobbyData property paths you want returned. Path can be in dot notation, like \"info.liveManagerUrl\". Omit it to return all the properties", "type": "string"}], "responses": {"200": {"description": "List of lobbies", "schema": {"type": "array", "items": {"$ref": "#/definitions/LobbyData"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:lobby", "keyentity:lobby:create"]}], "tags": ["Lobby"], "summary": "Creates lobby", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/UpdateLobbyData"}}], "responses": {"201": {"description": "Created lobby", "schema": {"$ref": "#/definitions/LobbyData"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 325: <PERSON>bby already exists\n"}}}}, "/lobbies/{lobbyId}": {"parameters": [{"$ref": "#/parameters/lobbyId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:lobby", "keyentity:lobby:view"]}], "tags": ["Lobby"], "summary": "Gets lobby by id", "parameters": [{"name": "fields", "in": "query", "description": "A comma-separated list of LobbyData property paths you want returned. Path can be in dot notation, like \"info.liveManagerUrl\". Omit it to return all the properties", "type": "string"}], "responses": {"200": {"description": "Lobby", "schema": {"$ref": "#/definitions/LobbyData"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 324: <PERSON><PERSON> is not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:lobby", "keyentity:lobby:edit"]}], "tags": ["Lobby"], "summary": "Updates lobby", "parameters": [{"name": "description", "in": "body", "schema": {"$ref": "#/definitions/UpdateLobbyData"}}], "responses": {"200": {"description": "Lobby information", "schema": {"$ref": "#/definitions/LobbyData"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 324: <PERSON><PERSON> is not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:lobby", "keyentity:lobby:delete"]}], "tags": ["Lobby"], "summary": "Deletes lobby", "responses": {"204": {"description": "Lobby has been deleted"}, "400": {"description": "- 331: <PERSON><PERSON> could not be deleted cos it have assigned terminals\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 324: <PERSON><PERSON> is not found\n"}}}}, "/entities/{path}/lobbies": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["lobby", "lobby:view"]}], "tags": ["Lobby"], "summary": "Gets list of lobbies for specified entity by path", "parameters": [{"name": "fields", "in": "query", "description": "A comma-separated list of LobbyData property paths you want returned. Path can be in dot notation, like \"info.liveManagerUrl\". Omit it to return all the properties", "type": "string"}], "responses": {"200": {"description": "List of lobbies", "schema": {"type": "array", "items": {"$ref": "#/definitions/LobbyData"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 51: Could not find entity\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["lobby", "lobby:create"]}], "tags": ["Lobby"], "summary": "Creates lobby for specified entity by path", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/UpdateLobbyData"}}], "responses": {"201": {"description": "Created lobby", "schema": {"$ref": "#/definitions/LobbyData"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}, "409": {"description": "- 325: <PERSON>bby already exists\n"}}}}, "/entities/{path}/lobbies/{lobbyId}": {"parameters": [{"$ref": "#/parameters/lobbyId"}, {"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["lobby", "lobby:view"]}], "tags": ["Lobby"], "summary": "Gets lobby by id for specified entity by path", "parameters": [{"name": "fields", "in": "query", "description": "A comma-separated list of LobbyData property paths you want returned. Path can be in dot notation, like \"info.liveManagerUrl\". Omit it to return all the properties", "type": "string"}], "responses": {"200": {"description": "Lobby", "schema": {"$ref": "#/definitions/LobbyData"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 324: <PERSON><PERSON> is not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["lobby", "lobby:edit"]}], "tags": ["Lobby"], "summary": "Updates lobby for specified entity by path", "parameters": [{"name": "description", "in": "body", "schema": {"$ref": "#/definitions/UpdateLobbyData"}}], "responses": {"200": {"description": "Lobby information", "schema": {"$ref": "#/definitions/LobbyData"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 324: <PERSON><PERSON> is not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["lobby", "lobby:delete"]}], "tags": ["Lobby"], "summary": "Deletes lobby for specified entity by path", "responses": {"204": {"description": "Lobby has been deleted"}, "400": {"description": "- 331: <PERSON><PERSON> could not be deleted cos it have assigned terminals\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 324: <PERSON><PERSON> is not found\n"}}}}}