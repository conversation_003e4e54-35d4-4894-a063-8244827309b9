{"/emails/send": {"post": {"security": [{"apiKey": []}, {"Permissions": ["email"]}], "tags": ["Email"], "summary": "Sends broadcast emails", "description": "Method sends email to defined mail list", "parameters": [{"in": "body", "description": "Email content", "required": true, "name": "content", "schema": {"type": "object", "required": ["subject", "body", "recipients"], "properties": {"subject": {"type": "string", "description": "Email subject", "example": "Email subject"}, "body": {"type": "string", "description": "Email body", "example": "Email body"}, "recipients": {"type": "array", "description": "Default recipients", "example": ["<EMAIL>"], "items": {"type": "string", "description": "Recipient email address"}}, "fromEmail": {"type": "string", "description": "Sender email (sender service email will be used by default.)", "example": "<EMAIL>"}, "fromName": {"type": "string", "description": "Sender name, fromEmail will be used by default", "example": "GRC team"}}}}], "responses": {"204": {"description": "Email have been sent"}, "400": {"description": "- 40: Validation error"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}}