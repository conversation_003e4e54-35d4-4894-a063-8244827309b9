{"/rtp-report": {"get": {"tags": ["Game"], "parameters": [{"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/rtpDeduction__gte"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/offset"}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:gamertp", "keyentity:gamertp:view"]}], "summary": "Get rtp report for key entity", "responses": {"200": {"description": "Returns proxies", "schema": {"items": {"$ref": "#/definitions/RTPReport"}, "type": "array"}}, "400": {"description": "- 40: Valida<PERSON>\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 798: Game codes not found or are not available for entity\n"}}}}, "/entities/{path}/rtp-report": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"tags": ["Game"], "parameters": [{"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/rtpDeduction__gte"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/offset"}], "security": [{"apiKey": []}, {"Permissions": ["gamertp", "gamertp:view"]}], "summary": "Get rtp report for key entity by path", "responses": {"200": {"description": "Returns proxies", "schema": {"items": {"$ref": "#/definitions/RTPReport"}, "type": "array"}, "headers": {"x-paging-total": {"type": "integer", "format": "int32", "description": "Total number of records without limitation"}, "x-paging-limit": {"type": "integer", "format": "int32", "description": "Number of records in the bunch"}, "x-paging-offset": {"type": "integer", "format": "int32", "description": "The bunch offset"}}}, "400": {"description": "- 40: Valida<PERSON>\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 798: Game codes not found or are not available for entity\n"}}}}}