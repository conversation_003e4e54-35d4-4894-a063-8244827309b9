{"/entities/{path}/history/game": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Gets game history for the brand by path", "description": "The method returns game history items for the brand by its path. Default output is limited to 20 entries per page. Max output is 100 entries per page. This method will return data for a limited time only (default = 3 months). This restriction does not work if you have 'report-without-limit' permission. Note: if round id is in query all other filters are ignored.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/roundIdStrictEquality"}, {"$ref": "#/parameters/roundIdIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/firstTs"}, {"$ref": "#/parameters/firstTs__gt"}, {"$ref": "#/parameters/firstTs__gte"}, {"$ref": "#/parameters/firstTs__lt"}, {"$ref": "#/parameters/firstTs__lte"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/queryFinished"}, {"$ref": "#/parameters/bet"}, {"$ref": "#/parameters/bet__lt"}, {"$ref": "#/parameters/bet__lte"}, {"$ref": "#/parameters/bet__gt"}, {"$ref": "#/parameters/bet__gte"}, {"$ref": "#/parameters/win"}, {"$ref": "#/parameters/win__lt"}, {"$ref": "#/parameters/win__lte"}, {"$ref": "#/parameters/win__gt"}, {"$ref": "#/parameters/win__gte"}, {"$ref": "#/parameters/revenue"}, {"$ref": "#/parameters/revenue__lt"}, {"$ref": "#/parameters/revenue__lte"}, {"$ref": "#/parameters/revenue__gt"}, {"$ref": "#/parameters/revenue__gte"}, {"$ref": "#/parameters/device"}, {"$ref": "#/parameters/balanceBefore"}, {"$ref": "#/parameters/balanceBefore__lt"}, {"$ref": "#/parameters/balanceBefore__lte"}, {"$ref": "#/parameters/balanceBefore__gt"}, {"$ref": "#/parameters/balanceBefore__gte"}, {"$ref": "#/parameters/balanceAfter"}, {"$ref": "#/parameters/balanceAfter__lt"}, {"$ref": "#/parameters/balanceAfter__lte"}, {"$ref": "#/parameters/balanceAfter__gt"}, {"$ref": "#/parameters/balanceAfter__gte"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/recoveryType"}, {"$ref": "#/parameters/recoveryType__in"}], "responses": {"200": {"description": "Game history\n####Searchable fields:\n- brandId: Number,\n- roundId: Number,\n- playerCode: String,\n- gameCode: String,\n- currency: String,\n- firstTs: Number,\n- ts: Number,\n- finished: Boolean,\n- bet: Number,\n- win: Number,\n- revenue: Number,\n- isTest: Boolean,\n- recoveryType: String\n", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/history/game/{roundId}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Gets round information for the brand by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/isPayment"}, {"$ref": "#/parameters/withDetails"}, {"name": "spinNumber", "in": "query", "description": "Spin number", "required": false, "type": "integer"}], "responses": {"200": {"description": "Spins of the round", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistorySpin"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/history/events/{roundId}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Get events history list by the game round Id for the selected entity by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/historyMode"}], "responses": {"200": {"description": "Events history list", "schema": {"type": "array", "items": {"$ref": "#/definitions/EventsHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/history/game/{roundId}/sm-result": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Get game events history encoded as sm_result", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lte"}], "responses": {"200": {"description": "Representation of sm_result", "schema": {"$ref": "#/definitions/GameHistorySmResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/history/game/{roundId}/image": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Get game event details visualisation link by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/languageQuery"}, {"$ref": "#/parameters/timezoneQuery"}], "responses": {"200": {"description": "Link to the round details visualisation", "schema": {"$ref": "#/definitions/GameHistoryVisualization"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Game history details not found\n- 901: Domain is used by entity\n"}, "409": {"description": "- 689: Game history URL not found\n"}}}}, "/entities/{path}/history/game/{roundId}/details/{spinNumber}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Get game event details by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"name": "spinNumber", "in": "path", "description": "Spin number", "required": true, "type": "integer"}], "responses": {"200": {"description": "Spins of the round", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistoryDetails"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Game history details not found\n"}}}}, "/entities/{path}/history/game/{roundId}/details/{spinNumber}/image": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Get game event details visualisation link by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/spinNumber"}, {"$ref": "#/parameters/languageQuery"}, {"$ref": "#/parameters/timezoneQuery"}], "responses": {"200": {"description": "Link to the spin details visualisation", "schema": {"$ref": "#/definitions/GameHistoryVisualization"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Game history details not found\n- 901: Domain is used by entity\n"}, "409": {"description": "- 689: Game history URL not found\n"}}}}, "/history/game": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Gets game history for the key entity", "description": "The method returns game history items for the key entity. Default output is limited to 20 entries per page. Max output is 100 entries per page. This method will return data for a limited time only (default = 3 months). This restriction does not work if you have 'report-without-limit' permission. Note: if round id is in query all other filters are ignored.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/roundIdStrictEquality"}, {"$ref": "#/parameters/roundIdIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/firstTs"}, {"$ref": "#/parameters/firstTs__gt"}, {"$ref": "#/parameters/firstTs__gte"}, {"$ref": "#/parameters/firstTs__lt"}, {"$ref": "#/parameters/firstTs__lte"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/queryFinished"}, {"$ref": "#/parameters/bet"}, {"$ref": "#/parameters/bet__lt"}, {"$ref": "#/parameters/bet__lte"}, {"$ref": "#/parameters/bet__gt"}, {"$ref": "#/parameters/bet__gte"}, {"$ref": "#/parameters/win"}, {"$ref": "#/parameters/win__lt"}, {"$ref": "#/parameters/win__lte"}, {"$ref": "#/parameters/win__gt"}, {"$ref": "#/parameters/win__gte"}, {"$ref": "#/parameters/revenue"}, {"$ref": "#/parameters/revenue__lt"}, {"$ref": "#/parameters/revenue__lte"}, {"$ref": "#/parameters/revenue__gt"}, {"$ref": "#/parameters/revenue__gte"}, {"$ref": "#/parameters/device"}, {"$ref": "#/parameters/balanceBefore"}, {"$ref": "#/parameters/balanceBefore__lt"}, {"$ref": "#/parameters/balanceBefore__lte"}, {"$ref": "#/parameters/balanceBefore__gt"}, {"$ref": "#/parameters/balanceBefore__gte"}, {"$ref": "#/parameters/balanceAfter"}, {"$ref": "#/parameters/balanceAfter__lt"}, {"$ref": "#/parameters/balanceAfter__lte"}, {"$ref": "#/parameters/balanceAfter__gt"}, {"$ref": "#/parameters/balanceAfter__gte"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/recoveryType"}, {"$ref": "#/parameters/recoveryType__in"}], "responses": {"200": {"description": "Game history\n####Searchable fields:\n- brandId: Number,\n- roundId: Number,\n- playerCode: String,\n- gameCode: String,\n- currency: String,\n- firstTs: Number,\n- ts: Number,\n- finished: Boolean,\n- bet: Number,\n- win: Number,\n- revenue: Number,\n- isTest: Boolean,\n- recoveryType: String\n", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/game/{roundId}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Gets round information for the key entity", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/isPayment"}, {"$ref": "#/parameters/withDetails"}, {"name": "spinNumber", "in": "query", "description": "Spin number", "required": false, "type": "integer"}], "responses": {"200": {"description": "Spins of the round", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistorySpin"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/events/{roundId}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Get events history list by the game round Id", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/historyMode"}], "responses": {"200": {"description": "Events history list", "schema": {"type": "array", "items": {"$ref": "#/definitions/EventsHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/game/{roundId}/sm-result": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Get game events history encoded as sm_result", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lte"}], "responses": {"200": {"description": "Representation of sm_result", "schema": {"$ref": "#/definitions/GameHistorySmResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/game/{roundId}/image": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Get game event details visualisation link", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/languageQuery"}, {"$ref": "#/parameters/timezoneQuery"}], "responses": {"200": {"description": "Link to the round details visualisation", "schema": {"$ref": "#/definitions/GameHistoryVisualization"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Game history details not found\n- 901: Domain is used by entity\n"}, "409": {"description": "- 689: Game history URL not found\n"}}}}, "/history/game/{roundId}/details/{spinNumber}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Get game event details", "parameters": [{"$ref": "#/parameters/roundId"}, {"name": "spinNumber", "in": "path", "description": "Spin number", "required": true, "type": "integer"}], "responses": {"200": {"description": "Spins of the round", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistoryDetails"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Game history details not found\n"}}}}, "/history/game/{roundId}/details/{spinNumber}/image": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Get game event details visualisation link", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/spinNumber"}, {"$ref": "#/parameters/languageQuery"}, {"$ref": "#/parameters/timezoneQuery"}], "responses": {"200": {"description": "Link to the spin details visualisation", "schema": {"$ref": "#/definitions/GameHistoryVisualization"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Game history details not found\n- 901: Domain is used by entity\n"}, "409": {"description": "- 689: Game history URL not found\n"}}}}, "/entities/{path}/history/game/{roundId}/forcefinish": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity:gameclose:forcefinish"]}], "tags": ["History"], "summary": "Attempts to finish problematic round by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameContextId"}, {"$ref": "#/parameters/forceFlagInQuery"}, {"$ref": "#/parameters/closeInSWWalletOnlyFlagInQuery"}, {"$ref": "#/parameters/ignoreMerchantParamsFlagInQuery"}], "responses": {"200": {"description": "Forced round finish result", "schema": {"$ref": "#/definitions/ForceFinishRoundResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n- 761: An error occurred while trying to finish round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Round whether not found whether already finished\n"}}}}, "/history/game/{roundId}/forcefinish": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameclose:forcefinish"]}], "tags": ["History"], "summary": "Attempts to finish problematic round", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameContextId"}, {"$ref": "#/parameters/forceFlagInQuery"}, {"$ref": "#/parameters/closeInSWWalletOnlyFlagInQuery"}, {"$ref": "#/parameters/ignoreMerchantParamsFlagInQuery"}], "responses": {"200": {"description": "Forced round finish result", "schema": {"$ref": "#/definitions/ForceFinishRoundResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n- 761: An error occurred while trying to finish round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Round whether not found whether already finished\n"}}}}, "/history/external/game/{roundId}/forcefinish": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:external-game-provider:gameclose:forcefinish"]}], "tags": ["History"], "summary": "Attempts to finish problematic round", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameCodeInQueryRequired"}, {"$ref": "#/parameters/ignoreMerchantParamsFlagInQuery"}], "responses": {"200": {"description": "Forced round finish result", "schema": {"$ref": "#/definitions/ForceFinishRoundResult"}}, "404": {"description": "Returned in case we can't find round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/history/external/game/{roundId}/forcefinish": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity:external-game-provider:gameclose:forcefinish"]}], "tags": ["History"], "summary": "Attempts to finish problematic round", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameCodeInQueryRequired"}, {"$ref": "#/parameters/ignoreMerchantParamsFlagInQuery"}], "responses": {"200": {"description": "Forced round finish result", "schema": {"$ref": "#/definitions/ForceFinishRoundResult"}}, "404": {"description": "Returned in case we can't find round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/history/game/{roundId}/revert": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity:gameclose:revert"]}], "tags": ["History"], "summary": "Attempts to revert problematic round by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameContextIdRequiredInQuery"}, {"$ref": "#/parameters/forceFlagInQuery"}, {"$ref": "#/parameters/closeInSWWalletOnlyFlagInQuery"}], "responses": {"200": {"description": "Round revert result", "schema": {"$ref": "#/definitions/RevertRoundResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n- 761: An error occurred while trying to finish round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Round whether not found whether already finished\n"}}}}, "/history/game/{roundId}/revert": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameclose:revert"]}], "tags": ["History"], "summary": "Attempts to revert problematic round", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameContextIdRequiredInQuery"}, {"$ref": "#/parameters/forceFlagInQuery"}, {"$ref": "#/parameters/closeInSWWalletOnlyFlagInQuery"}], "responses": {"200": {"description": "Round revert result", "schema": {"$ref": "#/definitions/RevertRoundResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n- 761: An error occurred while trying to finish round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Round whether not found whether already finished\n"}}}}, "/entities/{path}/history/game/{roundId}/retry-pending": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity:gameclose:retry"]}], "tags": ["History"], "summary": "Attempts to retry pending operation for round by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameContextIdRequiredInQuery"}], "responses": {"200": {"description": "Round retry result", "schema": {"$ref": "#/definitions/RetryRoundResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n- 761: An error occurred while trying to finish round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Round whether not found whether already finished\n"}}}}, "/history/game/{roundId}/retry-pending": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameclose:retry"]}], "tags": ["History"], "summary": "Attempts to retry pending operation for round", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameContextIdRequiredInQuery"}], "responses": {"200": {"description": "Round retry result", "schema": {"$ref": "#/definitions/RetryRoundResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n- 761: An error occurred while trying to finish round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Round whether not found whether already finished\n"}}}}, "/entities/{path}/history/game/{roundId}/transfer-out": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity:gameclose:transfer-out"]}], "tags": ["History"], "summary": "Attempts to transfer out for round by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameContextIdRequiredInQuery"}], "responses": {"200": {"description": "Result", "schema": {"$ref": "#/definitions/RetryRoundResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n- 761: An error occurred while trying to finish round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Round whether not found whether already finished\n"}}}}, "/history/game/{roundId}/transfer-out": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameclose:transfer-out"]}], "tags": ["History"], "summary": "Attempts to transfer out for round", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameContextIdRequiredInQuery"}], "responses": {"200": {"description": "Result", "schema": {"$ref": "#/definitions/RetryRoundResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n- 761: An error occurred while trying to finish round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Round whether not found whether already finished\n"}}}}, "/entities/{path}/history/unfinished/game": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:unfinished"]}], "tags": ["History"], "summary": "Gets unfinished game rounds by entity path", "description": "The method returns unfinished game rounds by entity path.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/firstTs__gt"}, {"$ref": "#/parameters/firstTs__lt"}, {"$ref": "#/parameters/firstTs__gte"}, {"$ref": "#/parameters/firstTs__lte"}, {"$ref": "#/parameters/roundIdInQuery"}, {"$ref": "#/parameters/unfinishedRoundStatus"}, {"$ref": "#/parameters/includeBrokenSpin"}, {"$ref": "#/parameters/withTrx"}, {"$ref": "#/parameters/gameContextId"}], "responses": {"200": {"description": "Return unfinished game history by path", "schema": {"type": "array", "items": {"$ref": "#/definitions/UnfinishedGameHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 900: Domain does not exist"}}}}, "/history/unfinished/game": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:unfinished"]}], "tags": ["History"], "summary": "Gets unfinished game rounds for the key entity", "description": "The method returns unfinished game rounds for the key entity.", "parameters": [{"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/firstTs__gt"}, {"$ref": "#/parameters/firstTs__lt"}, {"$ref": "#/parameters/firstTs__gte"}, {"$ref": "#/parameters/firstTs__lte"}, {"$ref": "#/parameters/roundIdInQuery"}, {"$ref": "#/parameters/unfinishedRoundStatus"}, {"$ref": "#/parameters/includeBrokenSpin"}, {"$ref": "#/parameters/withTrx"}, {"$ref": "#/parameters/gameContextId"}], "responses": {"200": {"description": "Return unfinished game history", "schema": {"type": "array", "items": {"$ref": "#/definitions/UnfinishedGameHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/external": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:external-game-provider:history"]}], "tags": ["History"], "summary": "Get external win/bet history", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'insertedAt' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/currencyInQuery"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/roundIdInQuery"}, {"$ref": "#/parameters/extTrxId"}, {"$ref": "#/parameters/gameProviderCode"}, {"$ref": "#/parameters/insertedAt"}, {"$ref": "#/parameters/insertedAt__gt"}, {"$ref": "#/parameters/insertedAt__lt"}, {"$ref": "#/parameters/playerCodeInQuery"}, {"$ref": "#/parameters/gameCodeInQuery"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/queryFinished"}, {"$ref": "#/parameters/extRecoveryType"}], "responses": {"200": {"description": "Return external history", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtWinBetHistoryEntry"}}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> is expired\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/history/external": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:external-game-provider:history"]}], "tags": ["History"], "summary": "Get external win/bet history by path", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'insertedAt' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/currencyInQuery"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/roundIdInQuery"}, {"$ref": "#/parameters/extTrxId"}, {"$ref": "#/parameters/gameProviderCode"}, {"$ref": "#/parameters/insertedAt"}, {"$ref": "#/parameters/insertedAt__gt"}, {"$ref": "#/parameters/insertedAt__lt"}, {"$ref": "#/parameters/playerCodeInQuery"}, {"$ref": "#/parameters/gameCodeInQuery"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/queryFinished"}, {"$ref": "#/parameters/extRecoveryType"}], "responses": {"200": {"description": "Return history", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtWinBetHistoryEntry"}}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> is expired\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/external/{roundId}/details": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:external-game-provider:history"]}], "tags": ["History"], "summary": "Get external win/bet history details", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameProviderCodeRequired"}, {"$ref": "#/parameters/extTrxIdRequired"}], "responses": {"200": {"description": "Return history", "schema": {"$ref": "#/definitions/ExtHistoryDetailsResponse"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> is expired\n"}, "404": {"description": "- 2000: History details url is not present\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/history/external/{roundId}/details": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:external-game-provider:history"]}], "tags": ["History"], "summary": "Get external win/bet history details by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameProviderCodeRequired"}, {"$ref": "#/parameters/extTrxIdRequired"}], "responses": {"200": {"description": "Return history", "schema": {"$ref": "#/definitions/ExtHistoryDetailsResponse"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> is expired\n"}, "404": {"description": "- 2000: History details url is not present\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/history/game/recovery/finalize": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity:gameclose:finalize"]}], "tags": ["History"], "summary": "Attempts to finalize problematic round by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/gameContextIdRequiredInQuery"}, {"$ref": "#/parameters/waitForCompletion"}, {"$ref": "#/parameters/closeInSWWalletOnly"}], "responses": {"200": {"description": "Forced round finish result", "schema": {"$ref": "#/definitions/FinalizeRoundResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n- 761: An error occurred while trying to finish round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/game/recovery/finalize": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameclose:finalize"]}], "tags": ["History"], "summary": "Attempts to finalize problematic round", "parameters": [{"$ref": "#/parameters/gameContextIdRequiredInQuery"}, {"$ref": "#/parameters/waitForCompletion"}, {"$ref": "#/parameters/closeInSWWalletOnly"}], "responses": {"200": {"description": "Forced round finish result", "schema": {"$ref": "#/definitions/FinalizeRoundResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n- 761: An error occurred while trying to finish round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/unfinished/get-contexts": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:view:game-contexts"]}], "tags": ["History"], "summary": "Search for game-contexts for brand's player", "parameters": [{"$ref": "#/parameters/getGameContextsRequest"}], "responses": {"200": {"description": "Return game-contexts", "schema": {"type": "array", "items": {"type": "object"}}}, "400": {"description": "Returned in case we have error on the server side\n- 760: An error occurred while querying gameserver\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/history/external/game/recovery/finalize": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity:gameclose:finalize"]}], "tags": ["History"], "summary": "Attempts to finalize problematic round by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCodeInQuery"}, {"$ref": "#/parameters/gameCodeInQuery"}, {"$ref": "#/parameters/currencyInQuery"}, {"$ref": "#/parameters/waitForCompletion"}], "responses": {"200": {"description": "Finalization result", "schema": {"$ref": "#/definitions/FinalizeRoundResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n- 761: An error occurred while trying to finish round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/external/game/recovery/finalize": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gameclose:finalize"]}], "tags": ["History"], "summary": "Attempts to finalize problematic round", "parameters": [{"$ref": "#/parameters/playerCodeInQuery"}, {"$ref": "#/parameters/gameCodeInQuery"}, {"$ref": "#/parameters/currencyInQuery"}, {"$ref": "#/parameters/waitForCompletion"}], "responses": {"200": {"description": "Finalization result", "schema": {"$ref": "#/definitions/FinalizeRoundResult"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n- 761: An error occurred while trying to finish round\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}