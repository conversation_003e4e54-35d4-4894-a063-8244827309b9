{"/jackpots": {"get": {"parameters": [{"in": "query", "name": "jackpotIds", "type": "string", "required": false, "description": "Comma separated jackpot ids to return. (All by default)"}, {"in": "query", "name": "gameCodes", "type": "string", "required": false, "description": "Comma separated game codes, that belong to entity, for which jackpot instances should be returned."}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:report:jackpot:instances"]}], "tags": ["JPN API"], "summary": "Return all jackpot instances", "responses": {"200": {"description": "Return all jackpot instances", "schema": {"type": "array", "items": {"$ref": "#/definitions/JPInfo"}}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}, "post": {"parameters": [{"$ref": "#/parameters/jackpotInstanceInformation"}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:jackpot:instance:create"]}], "tags": ["JPN API"], "summary": "Create jackpot instance", "responses": {"200": {"description": "Return created jackpot instance", "schema": {"$ref": "#/definitions/JackpotInstance"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/jackpots": {"get": {"parameters": [{"$ref": "#/parameters/path"}, {"in": "query", "name": "jackpotIds", "type": "string", "required": false, "description": "Comma separated jackpot ids to return. (All by default)"}, {"in": "query", "name": "gameCodes", "type": "string", "required": false, "description": "Comma separated game codes, that belong to entity, for which jackpot instances should be returned."}], "security": [{"apiKey": []}, {"Permissions": ["report:jackpot:instances"]}], "tags": ["JPN API"], "summary": "Return all jackpot instances", "responses": {"200": {"description": "Return all jackpot instances", "schema": {"type": "array", "items": {"$ref": "#/definitions/JPInfo"}}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}, "post": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/jackpotInstanceInformation"}], "security": [{"apiKey": []}, {"Permissions": ["jackpot:instance:create"]}], "tags": ["JPN API"], "summary": "Create jackpot instance", "responses": {"200": {"description": "Return created jackpot instance", "schema": {"$ref": "#/definitions/JackpotInstance"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/jackpots/{id}": {"patch": {"parameters": [{"$ref": "#/parameters/jackpotInstanceId"}, {"$ref": "#/parameters/jackpotInstanceInformation"}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:jackpot:instance:edit"]}], "tags": ["JPN API"], "summary": "Update jackpot instance", "responses": {"200": {"description": "Return updated jackpot instance", "schema": {"$ref": "#/definitions/JackpotInstance"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/jackpots/{id}": {"patch": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/jackpotInstanceId"}, {"$ref": "#/parameters/jackpotInstanceInformation"}], "security": [{"apiKey": []}, {"Permissions": ["jackpot:instance:edit"]}], "tags": ["JPN API"], "summary": "Update jackpot instance", "responses": {"200": {"description": "Return updated jackpot instance", "schema": {"$ref": "#/definitions/JackpotInstance"}}, "403": {"description": "- 2: Operation forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "500": {"description": "- 1: JPN internal server error\n", "schema": {"$ref": "#/definitions/Error"}}}}}}