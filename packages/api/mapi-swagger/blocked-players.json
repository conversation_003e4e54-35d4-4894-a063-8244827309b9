{"/suspendedplayers/{playerCode}": {"parameters": [{"$ref": "#/parameters/playerCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:view"]}], "tags": ["Blocked Players"], "summary": "Gets player information under the key entity (brand or merchant).", "parameters": [{"$ref": "#/parameters/with<PERSON><PERSON>t"}], "responses": {"200": {"description": "Gets player information under the key entity (brand or merchant). For merchant, player should have been blocked at least once in our system to be returned.", "schema": {"$ref": "#/definitions/BlockedPlayerInfoWithAudit"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/entities/{path}/suspendedplayers/{playerCode}": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:view"]}], "tags": ["Blocked Players"], "summary": "Gets player information under the entity (brand or merchant) by path", "parameters": [{"$ref": "#/parameters/with<PERSON><PERSON>t"}], "responses": {"200": {"description": "Gets player information under the entity (brand or merchant). For merchant, player should have been blocked at least once in our system to be returned.", "schema": {"$ref": "#/definitions/BlockedPlayerInfoWithAudit"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/entities/{path}/suspendedplayers": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:view"]}], "tags": ["Blocked Players"], "summary": "Finds blocked players under the entity (brand or merchant) by path", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/codeOfPlayerStrictEquality"}, {"$ref": "#/parameters/codeOfPlayerContains"}, {"$ref": "#/parameters/codeOfPlayerNotContains"}, {"$ref": "#/parameters/codeOfPlayerIn"}], "responses": {"200": {"description": "List of blocked players", "schema": {"type": "array", "items": {"$ref": "#/definitions/BlockedPlayerInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 101: Not a brand\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n"}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n"}}}}, "/suspendedplayers": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:view"]}], "tags": ["Blocked Players"], "summary": "Finds blocked players under the key entity (brand or merchant) by path", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}], "responses": {"200": {"description": "List of blocked players", "schema": {"type": "array", "items": {"$ref": "#/definitions/BlockedPlayerInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 101: Not a brand\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n"}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n"}}}}, "/entities/{path}/suspendedplayers/{playerCode}/suspended": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:change-state"]}], "tags": ["Blocked Players"], "parameters": [{"$ref": "#/parameters/reason"}], "summary": "Suspends player under entity by path", "responses": {"200": {"description": "Suspended player info. For merchants, player code is not verified - we just add it to block list.", "schema": {"$ref": "#/definitions/BlockedPlayerInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:change-state"]}], "tags": ["Blocked Players"], "summary": "Restores player under entity by path", "responses": {"200": {"description": "Unsuspended player info (works both for brand and merchant players).", "schema": {"$ref": "#/definitions/BlockedPlayerInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/suspendedplayers/{playerCode}/suspended": {"parameters": [{"$ref": "#/parameters/playerCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:change-state"]}], "parameters": [{"$ref": "#/parameters/reason"}], "tags": ["Blocked Players"], "summary": "Suspends player under the key entity.", "responses": {"200": {"description": "Suspended player info. For merchants, player code is not verified - we just add it to block list.", "schema": {"$ref": "#/definitions/BlockedPlayerInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:change-state"]}], "tags": ["Blocked Players"], "summary": "Restores player under the key entity", "responses": {"200": {"description": "Unsuspended player info (works both for brand and merchant players)", "schema": {"$ref": "#/definitions/BlockedPlayerInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}