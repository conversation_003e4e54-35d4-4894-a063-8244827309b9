{"/game-server/settings": {"post": {"security": [{"apiKey": []}, {"Permissions": ["gs:settings", "gs:settings:create"]}], "tags": ["GameServer"], "summary": "Creates new game server settings", "parameters": [{"$ref": "#/parameters/createGameServerSettings"}], "responses": {"201": {"description": "Created settings", "schema": {"$ref": "#/definitions/GameServerSettings"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error: name should be a non-empty string, roundIdRange should be an array of two big numbers, sessionIdRange should be an array of two big numbers\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 741: Game server settings not found\n"}, "409": {"description": "- 742: Game server settings already exists\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["gs:settings", "gs:settings:view"]}], "tags": ["GameServer"], "summary": "Get all game server settings", "responses": {"200": {"description": "View gs settings", "schema": {"$ref": "#/definitions/GameServerSettings"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/game-server/settings/{name}": {"put": {"security": [{"apiKey": []}, {"Permissions": ["gs:settings", "gs:settings:edit"]}], "tags": ["GameServer"], "summary": "Updates game server settings", "parameters": [{"$ref": "#/parameters/gsSettingsName"}, {"$ref": "#/parameters/updateGameServerSettings"}], "responses": {"200": {"description": "Update settings", "schema": {"$ref": "#/definitions/GameServerSettings"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error: name should be a non-empty string, roundIdRange should be an array of two big numbers, sessionIdRange should be an array of two big numbers\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 741: Game server settings not found\n"}, "409": {"description": "- 742: Game server settings already exists\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["gs:settings", "gs:settings:view"]}], "tags": ["GameServer"], "summary": "Get specific game server settings", "parameters": [{"$ref": "#/parameters/gsSettingsName"}], "responses": {"200": {"description": "View gs settings", "schema": {"$ref": "#/definitions/GameServerSettings"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["gs:settings", "gs:settings:remove"]}], "tags": ["GameServer"], "summary": "Remove specific game server settings", "parameters": [{"$ref": "#/parameters/gsSettingsName"}], "responses": {"401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}}