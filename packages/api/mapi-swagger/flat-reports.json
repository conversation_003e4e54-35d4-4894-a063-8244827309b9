{"/flat-reports": {"get": {"tags": ["Flat Reports"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:flat-reports", "keyentity:flat-reports:view"]}], "summary": "Gets reports", "description": "Method gets list of reports available for user for keyentity", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "List of reports", "schema": {"type": "array", "items": {"$ref": "#/definitions/FlatReportInfo"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/flat-reports/{reportType}": {"parameters": [{"$ref": "#/parameters/reportType"}], "get": {"tags": ["Flat Reports"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:flat-reports", "keyentity:flat-reports:view"]}], "summary": "Gets report by its type", "description": "Method gets existing report by its type", "responses": {"200": {"description": "Updated Report", "schema": {"$ref": "#/definitions/FlatReportInfo"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/flat-reports": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"tags": ["Flat Reports"], "security": [{"apiKey": []}, {"Permissions": ["flat-reports", "flat-reports:view"]}], "summary": "Gets reports for specific entity by path", "description": "Method gets list of reports available for user for entity from path", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "List of reports", "schema": {"type": "array", "items": {"$ref": "#/definitions/FlatReportInfo"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/flat-reports/{reportType}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/reportType"}], "get": {"tags": ["Flat Reports"], "security": [{"apiKey": []}, {"Permissions": ["flat-reports", "flat-reports:view"]}], "summary": "Gets report for specific entity by path", "description": "Method gets existing report by its type and for entity from path", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "List of reports", "schema": {"type": "array", "items": {"$ref": "#/definitions/FlatReportInfo"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}}