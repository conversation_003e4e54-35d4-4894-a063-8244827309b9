{"/entities/{path}/game-limits": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["gamelimits", "gamelimits:view"]}], "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/gameCodeInQuery"}, {"$ref": "#/parameters/schemaDefinitionIdInQuery"}, {"$ref": "#/parameters/gameGroupIdInQuery"}], "tags": ["New Game Limits System"], "summary": "Get list of game limits configurations by path", "description": "Get game limits configuration. Sortable fields are \"game_code\", \"created_at\"", "responses": {"200": {"description": "Game limits configuration", "schema": {"$ref": "#/definitions/GameLimitsConfiguration"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side:- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["gamelimits", "gamelimits:create"]}], "tags": ["New Game Limits System"], "parameters": [{"$ref": "#/parameters/createGameLimitsConfiguration"}], "summary": "Create game limits configuration by path", "description": "Create game limits configuration", "responses": {"201": {"description": "Created game limits configuration", "schema": {"$ref": "#/definitions/GameLimitsConfiguration"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 793: Object not found by ID\n"}, "409": {"description": "- 790: Game limits configuration already exists\n"}}}}, "/entities/{path}/game-limits-extended/{gameCode}": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["gamelimits", "gamelimits:view"]}], "parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/gameGroupNameInQuery"}, {"$ref": "#/parameters/currencyStrictEquality"}], "tags": ["New Game Limits System"], "summary": "Get list of game limits configurations by path", "description": "Get game limits configuration", "responses": {"200": {"description": "Updated game limits configuration", "schema": {"$ref": "#/definitions/ExtendedGameLimitsInfo"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side:- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game is not available for entity\n- 240: Game not found\n"}}}}, "/entities/{path}/game-limits/{gameLimitsConfigurationId}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/gameLimitsConfigurationId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["gamelimits", "keyentity:gamelimits:view"]}], "tags": ["New Game Limits System"], "summary": "Get game limits configuration by id and path", "description": "Get game limits configuration by id", "responses": {"200": {"description": "Game limits configuration", "schema": {"$ref": "#/definitions/GameLimitsConfiguration"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side:- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 793: Object not found by ID\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["gamelimits", "gamelimits:edit"]}], "tags": ["New Game Limits System"], "parameters": [{"$ref": "#/parameters/updateGameLimitsConfiguration"}], "summary": "Update game limits configuration by path", "description": "Update game limits configuration. You can set gameLimits|filters|levels as null for removing from object.", "responses": {"200": {"description": "Updated game limits configuration", "schema": {"$ref": "#/definitions/GameLimitsConfiguration"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 793: Object not found by ID\n"}, "409": {"description": "- 790: Game limits configuration already exists\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["gamelimits", "gamelimits:remove"]}], "tags": ["New Game Limits System"], "summary": "Delete game limits configuration by path", "description": "Delete game limits configuration", "responses": {"204": {"description": "Game limits configuration was deleted"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 793: Object not found by ID\n"}}}}, "/game-limits": {"parameters": [], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamelimits", "keyentity:gamelimits:view"]}], "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/gameCodeInQuery"}, {"$ref": "#/parameters/schemaDefinitionIdInQuery"}, {"$ref": "#/parameters/gameGroupIdInQuery"}], "tags": ["New Game Limits System"], "summary": "Get list of game limits configurations", "description": "Get game limits configuration. Sortable fields are \"gameCode\", \"createdAt\"", "responses": {"200": {"description": "Game limits configuration", "schema": {"$ref": "#/definitions/GameLimitsConfiguration"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side:- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamelimits", "keyentity:gamelimits:create"]}], "tags": ["New Game Limits System"], "parameters": [{"$ref": "#/parameters/createGameLimitsConfiguration"}], "summary": "Create game limits configuration", "description": "Create game limits configuration", "responses": {"201": {"description": "Created game limits configuration", "schema": {"$ref": "#/definitions/GameLimitsConfiguration"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 793: Object not found by ID\n"}, "409": {"description": "- 790: Game limits configuration already exists\n"}}}}, "/game-limits/built/{gameCode}": {"parameters": [], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamelimits", "keyentity:gamelimits:view"]}], "parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/gameGroupNameInQuery"}, {"$ref": "#/parameters/currencyStrictEquality"}], "tags": ["New Game Limits System"], "summary": "Get built game limits", "description": "Get built game limits, game code or schema definition are required", "responses": {"200": {"description": "Game limits configuration", "schema": {"$ref": "#/definitions/SlotGameLimitsByCurrencyCode"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side:- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 213: Game is not available for entity\n- 240: Game not found\n"}}}}, "/entities/{path}/game-limits/built/{gameCode}": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["gamelimits", "gamelimits:view"]}], "parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/gameGroupNameInQuery"}, {"$ref": "#/parameters/currencyStrictEquality"}], "tags": ["New Game Limits System"], "summary": "Get built game limits by path", "description": "Get built game limits, game code or schema definition are required", "responses": {"200": {"description": "Game limits configuration", "schema": {"$ref": "#/definitions/SlotGameLimitsByCurrencyCode"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side:- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game is not available for entity\n- 240: Game not found\n"}}}}, "/game-limits-extended/{gameCode}": {"parameters": [], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamelimits", "keyentity:gamelimits:view"]}], "parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/gameGroupNameInQuery"}, {"$ref": "#/parameters/currencyStrictEquality"}], "tags": ["New Game Limits System"], "summary": "Get list of game limits configurations by path", "description": "Get game limits configuration", "responses": {"200": {"description": "Game limits configuration", "schema": {"$ref": "#/definitions/ExtendedGameLimitsInfo"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side:- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 213: Game is not available for entity\n- 240: Game not found\n"}}}}, "/game-limits/{gameLimitsConfigurationId}": {"parameters": [{"$ref": "#/parameters/gameLimitsConfigurationId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamelimits", "keyentity:gamelimits:view"]}], "tags": ["New Game Limits System"], "summary": "Get game limits configuration by id", "description": "Get game limits configuration by id", "responses": {"200": {"description": "Game limits configuration", "schema": {"$ref": "#/definitions/GameLimitsConfiguration"}}, "401": {"description": "Returned in case we have error on the server side:- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 793: Object not found by ID\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamelimits", "keyentity:gamelimits:edit"]}], "tags": ["New Game Limits System"], "parameters": [{"$ref": "#/parameters/updateGameLimitsConfiguration"}], "summary": "Update game limits configuration", "description": "Update game limits configuration. You can set gameLimits|filters|levels as null for removing from object.", "responses": {"200": {"description": "Updated game limits configuration", "schema": {"$ref": "#/definitions/GameLimitsConfiguration"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 793: Object not found by ID\n"}, "409": {"description": "- 790: Game limits configuration already exists\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamelimits", "keyentity:gamelimits:remove"]}], "tags": ["New Game Limits System"], "summary": "Delete game limits configuration", "description": "Delete game limits configuration", "responses": {"204": {"description": "Game limits configuration was deleted"}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "404": {"description": "- 793: Object not found by ID\n"}, "403": {"description": "- 206: Forbidden\n"}}}}}