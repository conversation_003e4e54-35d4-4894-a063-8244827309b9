{"/deployment-groups": {"post": {"deprecated": true, "security": [{"apiKey": []}, {"Permissions": ["deployment"]}], "tags": ["Deployment Group"], "summary": "Add new deployment group (You should not use it)", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/DeploymentGroupPost"}}], "responses": {"200": {"description": "Deployment group Id"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["deployment"]}], "tags": ["Deployment Group"], "summary": "Get list of deployment groups", "responses": {"200": {"description": "List of deployment groups", "schema": {"type": "array", "items": {"$ref": "#/definitions/DeploymentGroup"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/deployment-groups/{deploymentGroupRoute}": {"parameters": [{"$ref": "#/parameters/deploymentGroupRoute"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["deployment"]}], "tags": ["Deployment Group"], "summary": "Get deployment groups by route", "responses": {"200": {"description": "deployment groups", "schema": {"type": "array", "items": {"$ref": "#/definitions/DeploymentGroup"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/games/{gameCode}/deployment-groups/{deploymentGroupRoute}": {"parameters": [{"$ref": "#/parameters/deploymentGroupRoute"}, {"$ref": "#/parameters/gameCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["deployment"]}], "tags": ["Deployment Group"], "summary": "Assign deployment groups to the game", "responses": {"201": {"description": "deployment groups"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/deployment-groups/{deploymentGroupRoute}": {"parameters": [{"$ref": "#/parameters/deploymentGroupRoute"}, {"$ref": "#/parameters/path"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["deployment"]}], "tags": ["Deployment Group"], "summary": "Assign deployment groups to the entity by path", "responses": {"201": {"description": "deployment groups"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/games/{gameCode}/deployment-groups": {"parameters": [{"$ref": "#/parameters/gameCode"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["deployment"]}], "tags": ["Deployment Group"], "summary": "Detach deployment groups from the game", "responses": {"201": {"description": "Successfully detached from game"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/deployment-groups": {"parameters": [{"$ref": "#/parameters/path"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["deployment"]}], "tags": ["Deployment Group"], "summary": "Detach deployment groups from the entity by path", "responses": {"201": {"description": "Successfully detached from entity"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/deployment-groups/{deploymentGroupRoute}/games": {"parameters": [{"$ref": "#/parameters/deploymentGroupRoute"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["deployment"]}], "tags": ["Deployment Group"], "summary": "Get list of game codes by specified game group", "responses": {"200": {"description": "Game codes list", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameCodeByDeploymentGroup"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/deployment-groups/{deploymentGroupRoute}/entities": {"parameters": [{"$ref": "#/parameters/deploymentGroupRoute"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["deployment"]}], "tags": ["Deployment Group"], "summary": "Get list of entity path by specified game group", "responses": {"200": {"description": "Entity path list", "schema": {"type": "array", "items": {"$ref": "#/definitions/EntityPathByDeploymentGroup"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/deployment-groups/games/{gameCode}/versions": {"parameters": [{"$ref": "#/parameters/gameCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["deployment"]}], "tags": ["Deployment Group Game Client Version"], "summary": "Get list of game clients versions per deployment group routes", "responses": {"200": {"description": "Game versions", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameClientVersionsPerRoute"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["deployment"]}], "tags": ["Deployment Group Game Client Version"], "summary": "Update list of game clients versions per deployment group routes", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/GameClientVersionsPerRoute"}}], "responses": {"200": {"description": "Updated game client versions", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameClientVersionsPerRoute"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/deployment-groups/{deploymentGroupRoute}/games/{gameCode}/versions": {"parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/deploymentGroupRoute"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["deployment"]}], "tags": ["Deployment Group Game Client Version"], "summary": "Remove game client version from deployment group", "responses": {"200": {"description": "Game versions", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameClientVersionsPerRoute"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}}