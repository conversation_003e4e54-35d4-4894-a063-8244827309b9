{"/domains/dynamic/{domainId}": {"parameters": [{"$ref": "#/parameters/domainId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:dynamic", "domain:dynamic:view"]}], "tags": ["Domain"], "summary": "Gets dynamic domain by id", "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:dynamic", "domain:dynamic:edit"]}], "tags": ["Domain"], "summary": "Updates dynamic domain by id", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/DynamicDomainData"}}], "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/Domain"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:dynamic", "domain:dynamic:remove"]}], "tags": ["Domain"], "summary": "Deletes dynamic domain", "responses": {"204": {"description": "Successfully deleted domain"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 900: Domain not found\n"}, "409": {"description": "- 901: Domain is used by entity\n"}}}}, "/domains/dynamic": {"post": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:dynamic", "domain:dynamic:create"]}], "tags": ["Domain"], "summary": "Creates dynamic domain (game server domain)", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/DynamicDomainData"}}], "responses": {"201": {"description": "Created domain info", "schema": {"$ref": "#/definitions/Domain"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:dynamic", "domain:dynamic:view"]}], "tags": ["Domain"], "summary": "Gets list of dynamic domains", "responses": {"200": {"description": "List of domains", "schema": {"type": "array", "items": {"$ref": "#/definitions/Domain"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domains/static/{domainId}": {"parameters": [{"$ref": "#/parameters/domainId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:view"]}], "tags": ["Domain"], "summary": "Gets static domain by id", "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:edit"]}], "tags": ["Domain"], "summary": "Updates static domain by id", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/StaticDomainData"}}], "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/Domain"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:remove"]}], "tags": ["Domain"], "summary": "Deletes static domain", "responses": {"204": {"description": "Successfully deleted domain"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 900: Domain not found\n"}, "409": {"description": "- 901: Domain is used by entity\n"}}}}, "/domains/static": {"post": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:create"]}], "tags": ["Domain"], "summary": "Creates static domain (game client domain)", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/StaticDomainData"}}], "responses": {"201": {"description": "Created domain info", "schema": {"$ref": "#/definitions/Domain"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "get": {"parameters": [{"$ref": "#/parameters/pathInQuery"}], "security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:view"]}], "tags": ["Domain"], "summary": "Gets list of static domains", "responses": {"200": {"description": "List of domains", "schema": {"type": "array", "items": {"$ref": "#/definitions/StaticDomain"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domains/lobby/{domainId}": {"parameters": [{"$ref": "#/parameters/domainId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:view"]}], "tags": ["Domain"], "summary": "Gets lobby domain by id", "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/LobbyDomain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:edit"]}], "tags": ["Domain"], "summary": "Updates lobby domain by id", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/LobbyDomainData"}}], "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/LobbyDomain"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:remove"]}], "tags": ["Domain"], "summary": "Deletes lobby domain", "responses": {"204": {"description": "Successfully deleted domain"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 900: Domain not found\n"}, "409": {"description": "- 901: Domain is used by entity\n"}}}}, "/domains/lobby": {"post": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:create"]}], "tags": ["Domain"], "summary": "Creates domain (lobby client domain)", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/LobbyDomainData"}}], "responses": {"201": {"description": "Created domain info", "schema": {"$ref": "#/definitions/LobbyDomain"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:view"]}], "tags": ["Domain"], "summary": "Gets list of lobby domains", "responses": {"200": {"description": "List of domains", "schema": {"type": "array", "items": {"$ref": "#/definitions/LobbyDomain"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/{path}/entitydomain/dynamic/{domainId}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/domainId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:dynamic", "entitydomain:dynamic:edit"]}], "tags": ["Domain"], "summary": "Set entity dynamic domain. By default domains are inherited by children by path", "responses": {"200": {"description": "Domain found and set for entity", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 771: Migration is in progress for one of the entity\n- 900: Domain not found\n"}}}}, "/{path}/entitydomain/dynamic": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:dynamic", "entitydomain:dynamic:view"]}], "tags": ["Domain"], "summary": "Gets entity dynamic domain by path", "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:dynamic", "entitydomain:dynamic:remove"]}], "tags": ["Domain"], "summary": "Reset entity dynamic domain to parent value by path", "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}}, "/entitydomain/dynamic/{domainId}": {"parameters": [{"$ref": "#/parameters/domainId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entitydomain", "keyentity:entitydomain:dynamic", "keyentity:entitydomain:dynamic:edit"]}], "tags": ["Domain"], "summary": "Set key entity dynamic domain", "responses": {"200": {"description": "Domain found and set for entity", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 771: Migration is in progress for one of the entity\n- 900: Domain not found\n"}}}}, "/entitydomain/dynamic": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entitydomain", "keyentity:entitydomain:dynamic", "keyentity:entitydomain:dynamic:view"]}], "tags": ["Domain"], "summary": "Gets key entity dynamic domain", "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entitydomain", "keyentity:entitydomain:dynamic", "keyentity:entitydomain:dynamic:remove"]}], "tags": ["Domain"], "summary": "Reset key entity dynamic domain to parent value", "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}}, "/{path}/entitydomain/static/tags": {"put": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/setDomainTags"}], "security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:static", "entitydomain:static:tags", "entitydomain:static:tags:set"]}], "tags": ["Domain"], "summary": "Set static domain tags by path", "responses": {"200": {"description": "Entity info", "schema": {"$ref": "#/definitions/EntityDomainInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"parameters": [{"$ref": "#/parameters/path"}], "security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:static", "entitydomain:static:tags", "entitydomain:static:tags:reset"]}], "tags": ["Domain"], "summary": "Reset static domain tags by path", "responses": {"200": {"description": "Entity info", "schema": {"$ref": "#/definitions/EntityDomainInfo"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/{path}/entitydomain/static/{domainId}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/domainId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:static", "entitydomain:static:edit"]}], "tags": ["Domain"], "summary": "Set entity static domain by path. By default domains are inherited by children.", "responses": {"200": {"description": "Domain found and set for entity", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}}, "/{path}/entitydomain/static": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:static", "entitydomain:static:view"]}], "tags": ["Domain"], "summary": "Gets entity static domain by path", "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:static", "entitydomain:static:remove"]}], "tags": ["Domain"], "summary": "Reset entity static domain to parent value by path", "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}}, "/entitydomain/static/{domainId}": {"parameters": [{"$ref": "#/parameters/domainId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entitydomain", "keyentity:entitydomain:static", "keyentity:entitydomain:static:edit"]}], "tags": ["Domain"], "summary": "Set key entity static domain", "responses": {"200": {"description": "Domain found and set for entity", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}}, "/entitydomain/static": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entitydomain", "keyentity:entitydomain:static", "keyentity:entitydomain:static:view"]}], "tags": ["Domain"], "summary": "Gets key entity static domain", "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entitydomain", "keyentity:entitydomain:static", "keyentity:entitydomain:static:remove"]}], "tags": ["Domain"], "summary": "Reset key entity static domain to parent value", "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}}}