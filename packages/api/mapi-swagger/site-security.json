{"/entities/{path}/site/tokens": {"post": {"security": [{"apiKey": []}, {"Permissions": ["site"]}], "tags": ["Site Security"], "summary": "Generates site token by path", "description": "Method generates and returns token for working with customers in site", "parameters": [{"$ref": "#/parameters/path"}], "responses": {"200": {"description": "Token information", "schema": {"$ref": "#/definitions/SiteToken"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["site"]}], "tags": ["Site Security"], "summary": "Gets all site tokens by path", "description": "Method gets all tokens available for a brand", "parameters": [{"$ref": "#/parameters/path"}], "responses": {"200": {"description": "List of token information", "schema": {"type": "array", "items": {"$ref": "#/definitions/SiteToken"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/site/tokens/{tokenId}/suspended": {"put": {"security": [{"apiKey": []}, {"Permissions": ["site"]}], "tags": ["Site Security"], "summary": "Suspends site token by path", "description": "Method suspends site token for a brand", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/tokenId"}], "responses": {"204": {"description": "Token suspended"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 226: Bad query for updating token status\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 227: No record for this token\n"}}}}, "/site/tokens": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:site", "site"]}], "tags": ["Site Security"], "summary": "Generates site token", "description": "Method generates and returns token for working with customers in site", "responses": {"200": {"description": "Token information", "schema": {"$ref": "#/definitions/SiteToken"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:site", "site"]}], "tags": ["Site Security"], "summary": "Gets all site tokens", "description": "Method gets all tokens available for a brand", "responses": {"200": {"description": "List of token information", "schema": {"type": "array", "items": {"$ref": "#/definitions/SiteToken"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/site/tokens/{tokenId}/suspended": {"put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:site", "site"]}], "tags": ["Site Security"], "summary": "Suspends site token", "description": "Method suspends site token for a brand", "parameters": [{"$ref": "#/parameters/tokenId"}], "responses": {"204": {"description": "Token suspended"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 226: Bad query for updating token status\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 227: No record for this token\n"}}}}}