{"/entities/{path}/testplayers": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:view"]}], "tags": ["Test Players"], "summary": "Finds merchant test players under the entity by path", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/codeOfPlayerStrictEquality"}, {"$ref": "#/parameters/codeOfPlayerContains"}, {"$ref": "#/parameters/codeOfPlayerNotContains"}, {"$ref": "#/parameters/codeOfPlayerIn"}], "responses": {"200": {"description": "List of merchant test players", "schema": {"type": "array", "items": {"$ref": "#/definitions/MerchantTestPlayerInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 403: Key is not valid for sort by\n- 504: Not a merchant brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n"}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:view"]}], "tags": ["Test Players"], "summary": "Create merchant test player", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/CreateMerchantTestPlayer"}}], "responses": {"201": {"description": "operation results - merchant test player", "schema": {"type": "object", "items": {"$ref": "#/definitions/MerchantTestPlayerInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "put": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:view"]}], "tags": ["Test Players"], "summary": "Update merchant test player", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/UpdateMerchantTestPlayer"}}], "responses": {"201": {"description": "operation results - merchant test player", "schema": {"type": "object", "items": {"$ref": "#/definitions/MerchantTestPlayerInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/testplayers": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:view"]}], "tags": ["Test Players"], "summary": "Finds merchant test players under the key entity", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}], "responses": {"200": {"description": "List of merchant test players", "schema": {"type": "array", "items": {"$ref": "#/definitions/MerchantTestPlayerInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 403: Key is not valid for sort by\n- 504: Not a merchant brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n"}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:view"]}], "tags": ["Test Players"], "summary": "Create merchant test player", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/CreateMerchantTestPlayer"}}], "responses": {"201": {"description": "operation results - merchant test player", "schema": {"type": "object", "items": {"$ref": "#/definitions/MerchantTestPlayerInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "put": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:view"]}], "tags": ["Test Players"], "summary": "Update merchant test player", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/UpdateMerchantTestPlayer"}}], "responses": {"201": {"description": "operation results - merchant test player", "schema": {"type": "object", "items": {"$ref": "#/definitions/MerchantTestPlayerInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/testplayers/{playerCode}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:change-state"]}], "tags": ["Test Players"], "summary": "Removes merchant player from test players by path", "responses": {"200": {"description": "Merchant test player info", "schema": {"$ref": "#/definitions/MerchantTestPlayerInfo"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n- 504: Not a merchant brand\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/testplayers/{playerCode}": {"parameters": [{"$ref": "#/parameters/playerCode"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:change-state"]}], "tags": ["Test Players"], "summary": "Removes merchant player from test players under the key entity", "responses": {"200": {"description": "Merchant test player info", "schema": {"$ref": "#/definitions/MerchantTestPlayerInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 504: Not a merchant brand\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}