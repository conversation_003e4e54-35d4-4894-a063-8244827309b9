{"/entities/{path}/report/currency": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:currency"]}], "tags": ["Reports"], "summary": "Gets currencies report by path", "description": "The method returns currencies report for key entity. This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/from"}, {"$ref": "#/parameters/to"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "Currencies report", "schema": {"$ref": "#/definitions/CurrencyReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/currency": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:currency"]}], "tags": ["Reports"], "summary": "Gets currencies report for key entity", "description": "The method returns currencies report for key entity. This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/from"}, {"$ref": "#/parameters/to"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "Currencies report", "schema": {"$ref": "#/definitions/CurrencyReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/report/wallet/currency": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:wallet-currency"]}], "tags": ["Reports"], "summary": "Gets currencies report by path", "description": "The method returns currencies report for the brand by its path. This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.\nRange between ts__gte and ts__lte dates can't be more, than 1 month, and ts__gte date in ts__gte and ts__lte range can't be older, than 3 months (if there is no 'report-without-limit' permission).\nIf only ts__gte specified and it's older, than 1 month it will be automatically set to 1 month ago from current date.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/includeSubBrands"}, {"$ref": "#/parameters/from"}, {"$ref": "#/parameters/to"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "Currencies report", "schema": {"$ref": "#/definitions/CurrencyReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/wallet/currency": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:wallet-currency"]}], "tags": ["Reports"], "summary": "Gets currencies report for key entity", "description": "The method returns currencies report for key entity. This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.\nRange between ts__gte and ts__lte dates can't be more, than 1 month, and ts__gte date in ts__gte and ts__lte range can't be older, than 3 months (if there is no 'report-without-limit' permission).\nIf only ts__gte specified and it's older, than 1 month it will be automatically set to 1 month ago from current date.", "parameters": [{"$ref": "#/parameters/includeSubBrands"}, {"$ref": "#/parameters/from"}, {"$ref": "#/parameters/to"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/limit"}], "responses": {"200": {"description": "Currencies report", "schema": {"$ref": "#/definitions/CurrencyReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/report/players": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:players"]}], "tags": ["Reports"], "summary": "Gets players report by path", "description": "The method returns players report items for the brand by its path. This method will return data for a limited time only (default = 3 months). The limit works by 'paymentDate', 'paymentDateHour' fields. This restriction does not work if you have 'report-without-limit' permission.\nRange between paymentDateHour__gte and paymentDateHour__lte dates can’t be more, than 1 month, and paymentDateHour__gte date in paymentDateHour__gte and paymentDateHour__lte range can’t be older, than 3 months (if there is no ‘report-without-limit’ permission).\nIf only paymentDateHour__gte specified and it’s older, than 1 month it will be automatically set to 1 month ago from current date.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/playedGames__gt"}, {"$ref": "#/parameters/playedGames__lt"}, {"$ref": "#/parameters/playedGames__gte"}, {"$ref": "#/parameters/playedGames__lte"}, {"$ref": "#/parameters/playedGames"}, {"$ref": "#/parameters/totalBets__gt"}, {"$ref": "#/parameters/totalBets__lt"}, {"$ref": "#/parameters/totalBets__gte"}, {"$ref": "#/parameters/totalBets__lte"}, {"$ref": "#/parameters/totalBets"}, {"$ref": "#/parameters/totalWins__gt"}, {"$ref": "#/parameters/totalWins__lt"}, {"$ref": "#/parameters/totalWins__gte"}, {"$ref": "#/parameters/totalWins__lte"}, {"$ref": "#/parameters/totalWins"}, {"$ref": "#/parameters/paymentDate"}, {"$ref": "#/parameters/paymentDate__gt"}, {"$ref": "#/parameters/paymentDate__gte"}, {"$ref": "#/parameters/paymentDate__lt"}, {"$ref": "#/parameters/paymentDate__lte"}, {"$ref": "#/parameters/paymentDateHour"}, {"$ref": "#/parameters/paymentDateHour__gt"}, {"$ref": "#/parameters/paymentDateHour__lt"}, {"$ref": "#/parameters/paymentDateHour__gte"}, {"$ref": "#/parameters/paymentDateHour__lte"}], "responses": {"200": {"description": "Players report", "schema": {"$ref": "#/definitions/PlayersReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/players": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:players"]}], "tags": ["Reports"], "summary": "Gets players report for key entity", "description": "The method returns players report items for the brand by its path. This method will return data for a limited time only (default = 3 months). The limit works by 'paymentDate', 'paymentDateHour' fields. This restriction does not work if you have 'report-without-limit' permission.\nRange between paymentDateHour__gte and paymentDateHour__lte dates can’t be more, than 1 month, and paymentDateHour__gte date in paymentDateHour__gte and paymentDateHour__lte range can’t be older, than 3 months (if there is no ‘report-without-limit’ permission).\nIf only paymentDateHour__gte specified and it’s older, than 1 month it will be automatically set to 1 month ago from current date.", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/playedGames__gt"}, {"$ref": "#/parameters/playedGames__lt"}, {"$ref": "#/parameters/playedGames__gte"}, {"$ref": "#/parameters/playedGames__lte"}, {"$ref": "#/parameters/playedGames"}, {"$ref": "#/parameters/totalBets__gt"}, {"$ref": "#/parameters/totalBets__lt"}, {"$ref": "#/parameters/totalBets__gte"}, {"$ref": "#/parameters/totalBets__lte"}, {"$ref": "#/parameters/totalBets"}, {"$ref": "#/parameters/totalWins__gt"}, {"$ref": "#/parameters/totalWins__lt"}, {"$ref": "#/parameters/totalWins__gte"}, {"$ref": "#/parameters/totalWins__lte"}, {"$ref": "#/parameters/totalWins"}, {"$ref": "#/parameters/paymentDate"}, {"$ref": "#/parameters/paymentDate__gt"}, {"$ref": "#/parameters/paymentDate__gte"}, {"$ref": "#/parameters/paymentDate__lt"}, {"$ref": "#/parameters/paymentDate__lte"}, {"$ref": "#/parameters/paymentDateHour"}, {"$ref": "#/parameters/paymentDateHour__gt"}, {"$ref": "#/parameters/paymentDateHour__lt"}, {"$ref": "#/parameters/paymentDateHour__gte"}, {"$ref": "#/parameters/paymentDateHour__lte"}], "responses": {"200": {"description": "Players report", "schema": {"$ref": "#/definitions/PlayersReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/report/games/daily": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:games"]}], "tags": ["Reports"], "summary": "Gets games daily report by path", "description": "Provides daily basis games report with aggregate amount of Players and Rounds with GGR. This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/ts__lt"}], "responses": {"200": {"description": "Players report", "schema": {"$ref": "#/definitions/PlayersDailyReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/games/daily": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:games"]}], "tags": ["Reports"], "summary": "Gets games daily report for key entity", "description": "Provides daily basis games report with aggregate amount of Players and Rounds with GGR. This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/ts__lt"}], "responses": {"200": {"description": "Players report", "schema": {"$ref": "#/definitions/PlayersDailyReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/report/ggr": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:ggr"]}], "tags": ["Reports"], "summary": "Gets ggr report by currencies by path", "description": "Provides common Gross Gaming Revenue report by currencies﻿per Brand", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "GGR report", "schema": {"$ref": "#/definitions/ReportGGR"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/ggr": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:ggr"]}], "tags": ["Reports"], "summary": "Gets ggr report by currencies under the key entity", "description": "Provides common Gross Gaming Revenue report by currencies﻿under the key entity", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "GGR report", "schema": {"$ref": "#/definitions/ReportGGR"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}}