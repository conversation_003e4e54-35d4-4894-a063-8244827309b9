{"/entities/{path}/credits/{currency}/{amount}": {"post": {"security": [{"apiKey": []}, {"Permissions": ["finance", "finance:credit"]}], "tags": ["Finance"], "summary": "Credits entity by path", "description": "Debits parent entity and credits this entity", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/currency"}, {"$ref": "#/parameters/amount"}], "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 89: Currency not exist\n- 90: Amount is negative\n- 729: Entity does not have sufficient balance to perform an operation\n- 108: Max capacity reached\n- 735: <PERSON><PERSON><PERSON> is under maintenance\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/debits/{currency}/{amount}": {"post": {"security": [{"apiKey": []}, {"Permissions": ["finance", "finance:debit"]}], "tags": ["Finance"], "summary": "Debits entity by path", "description": "Debits entity and credits parent entity", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/currency"}, {"$ref": "#/parameters/amount"}], "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 89: Currency not exist\n- 90: Amount is negative\n- 729: Entity does not have sufficient balance to perform an operation\n- 735: Entity is under maintenance\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/credits": {"get": {"security": [{"apiKey": []}, {"Permissions": ["finance", "finance:credit", "finance:debit", "finance:view"]}], "tags": ["Finance"], "summary": "Get entity's credit operations by path", "description": "Gets credits that were made from entity", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/amount_strict"}, {"$ref": "#/parameters/amount__gt"}, {"$ref": "#/parameters/amount__lt"}, {"$ref": "#/parameters/amount__gte"}, {"$ref": "#/parameters/amount__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/toEntityId"}], "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityCredits"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 89: Currency not exist\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/debits": {"get": {"security": [{"apiKey": []}, {"Permissions": ["finance", "finance:credit", "finance:debit", "finance:view"]}], "tags": ["Finance"], "summary": "Get entity's debit operations by path", "description": "Gets debits that were made to entity", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/amount_strict"}, {"$ref": "#/parameters/amount__gt"}, {"$ref": "#/parameters/amount__lt"}, {"$ref": "#/parameters/amount__gte"}, {"$ref": "#/parameters/amount__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/fromEntityId"}], "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityDebits"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 89: Currency not exist\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/credits": {"get": {"security": [{"apiKey": []}, {"Permissions": ["finance", "finance:credit", "finance:debit", "finance:view"]}], "tags": ["Finance"], "summary": "Get key entity's credit operations by path", "description": "Gets credits that were made from key entity", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/amount_strict"}, {"$ref": "#/parameters/amount__gt"}, {"$ref": "#/parameters/amount__lt"}, {"$ref": "#/parameters/amount__gte"}, {"$ref": "#/parameters/amount__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/toEntityId"}], "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityCredits"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 89: Currency not exist\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/debits": {"get": {"security": [{"apiKey": []}, {"Permissions": ["finance", "finance:credit", "finance:debit", "finance:view"]}], "tags": ["Finance"], "summary": "Get key entity's debit operations", "description": "Gets debits that were made to key entity", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/amount_strict"}, {"$ref": "#/parameters/amount__gt"}, {"$ref": "#/parameters/amount__lt"}, {"$ref": "#/parameters/amount__gte"}, {"$ref": "#/parameters/amount__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/fromEntityId"}], "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityDebits"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 89: Currency not exist\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/finance": {"get": {"security": [{"apiKey": []}, {"Permissions": ["finance", "finance:credit", "finance:debit", "finance:view"]}], "tags": ["Finance"], "summary": "Get entity's debit and credit operations by path", "description": "Get entity's debit and credit operations", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/amount_strict"}, {"$ref": "#/parameters/amount__gt"}, {"$ref": "#/parameters/amount__lt"}, {"$ref": "#/parameters/amount__gte"}, {"$ref": "#/parameters/amount__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/toEntityId"}, {"$ref": "#/parameters/fromEntityId"}, {"$ref": "#/parameters/debitOrCredit"}], "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityCreditsDebits"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 89: Currency not exist\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/finance": {"get": {"security": [{"apiKey": []}, {"Permissions": ["finance", "finance:credit", "finance:debit", "finance:view"]}], "tags": ["Finance"], "summary": "Get entity's debit and credit operations", "description": "Get entity's debit and credit operations", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/amount_strict"}, {"$ref": "#/parameters/amount__gt"}, {"$ref": "#/parameters/amount__lt"}, {"$ref": "#/parameters/amount__gte"}, {"$ref": "#/parameters/amount__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/toEntityId"}, {"$ref": "#/parameters/fromEntityId"}, {"$ref": "#/parameters/debitOrCredit"}], "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityCreditsDebits"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 89: Currency not exist\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}