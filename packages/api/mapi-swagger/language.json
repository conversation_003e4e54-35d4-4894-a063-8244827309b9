{"/entities/{path}/languages": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Language"], "summary": "Get entity languages list by path", "description": "Get list of all available languages in a specific entity", "responses": {"200": {"description": "list of languages", "schema": {"$ref": "#/definitions/Languages"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: User doesn't have permission to execute operation\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "put": {"security": [{"apiKey": []}, {"Permissions": ["language", "language:add"]}], "tags": ["Language"], "summary": "Add languages by path", "description": "Add languages to the entity. Languages must be available in the parent entity.", "parameters": [{"$ref": "#/parameters/languages__body"}], "responses": {"204": {"description": "list of languages", "schema": {"$ref": "#/definitions/Languages"}}, "400": {"description": "Returned in case we have a validation error\n- 40: Validation error", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have an error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: User doesn't have permission to execute operation\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["language", "language:remove"]}], "tags": ["Language"], "summary": "Delete entity languages from the list by path", "description": "Delete the list of specified languages from the entity", "parameters": [{"$ref": "#/parameters/languages__body"}], "responses": {"200": {"description": "list of languages", "schema": {"$ref": "#/definitions/Languages"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 94: Language is default. You cannot remove default language\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: User doesn't have permission to execute operation\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/languages": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:view", "entity"]}], "tags": ["Language"], "summary": "Get key entity languages list", "description": "Get list of all available languages in key entity", "responses": {"200": {"description": "list of languages", "schema": {"$ref": "#/definitions/Languages"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/languages/{language}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/language"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["language", "language:add"]}], "tags": ["Language"], "summary": "Add language by path", "description": "Add a language to the entity. The Language should be available in the parent entity as well.", "responses": {"204": {"description": "Language added"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: User doesn't have permission to execute operation\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["language", "language:remove"]}], "tags": ["Language"], "summary": "Remove language by path", "description": "Remove a language from the entity. Only available if it's not default language", "parameters": [{"$ref": "#/parameters/forceFlagInQuery"}], "responses": {"204": {"description": "Language removed"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 94: Language is default. You cannot remove default language\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: User doesn't have permission to execute operation\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}