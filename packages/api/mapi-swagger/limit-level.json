{"/limit-levels": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:limit-level", "keyentity:limit-level:view"]}], "tags": ["Limit levels"], "summary": "Gets list of limit levels", "description": "Gets list of limit levels", "parameters": [{"$ref": "#/parameters/all"}, {"$ref": "#/parameters/titleStrictEquality"}, {"$ref": "#/parameters/titleContains"}], "responses": {"200": {"description": "List of limit levels", "schema": {"type": "array", "items": {"$ref": "#/definitions/LimitLevel"}}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:limit-level", "keyentity:limit-level:create"]}], "tags": ["Limit levels"], "summary": "Add limit level", "description": "Create limit level and return info with public id", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/CreateLimitLevel"}}], "responses": {"200": {"description": "Limit level info", "schema": {"$ref": "#/definitions/LimitLevel"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/limit-levels/{levelId}": {"patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:limit-level", "keyentity:limit-level:update"]}], "tags": ["Limit levels"], "summary": "Update limit level", "description": "Update limit level and return info with public id", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/CreateLimitLevel"}}, {"$ref": "#/parameters/levelId"}], "responses": {"200": {"description": "Limit level info", "schema": {"$ref": "#/definitions/LimitLevel"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:limit-level", "keyentity:limit-level:delete"]}], "tags": ["Limit levels"], "summary": "Delete limit level", "description": "Delete limit level and return status 203", "parameters": [{"$ref": "#/parameters/levelId"}], "responses": {"203": {"description": "Empty response"}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/limit-levels/{levelId}": {"patch": {"security": [{"apiKey": []}, {"Permissions": ["limit-level", "limit-level:update"]}], "tags": ["Limit levels"], "summary": "Update limit level", "description": "Update limit level and return info with public id", "parameters": [{"$ref": "#/parameters/path"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/CreateLimitLevel"}}, {"$ref": "#/parameters/levelId"}], "responses": {"200": {"description": "Limit level info", "schema": {"$ref": "#/definitions/LimitLevel"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["limit-level", "limit-level:delete"]}], "tags": ["Limit levels"], "summary": "Delete limit level", "description": "Delete limit level and return status 203", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/levelId"}], "responses": {"203": {"description": "Empty response"}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/limit-levels": {"get": {"security": [{"apiKey": []}, {"Permissions": ["limit-level", "limit-level:view"]}], "tags": ["Limit levels"], "summary": "Gets list of limit levels", "description": "Gets list of limit levels", "parameters": [{"$ref": "#/parameters/all"}, {"$ref": "#/parameters/path"}, {"$ref": "#/parameters/titleStrictEquality"}, {"$ref": "#/parameters/titleContains"}], "responses": {"200": {"description": "List of limit levels", "schema": {"type": "array", "items": {"$ref": "#/definitions/LimitLevel"}}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["limit-level", "limit-level:create"]}], "tags": ["Limit levels"], "summary": "Add limit level", "description": "Create limit level and return info with public id", "parameters": [{"$ref": "#/parameters/path"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/CreateLimitLevel"}}], "responses": {"200": {"description": "Limit level info", "schema": {"$ref": "#/definitions/LimitLevel"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/game-limit-levels": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity-game-limit-level", "entity-game-limit-level:view"]}], "tags": ["Limit levels"], "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/gameCodeStrictEquality"}], "summary": "Gets list of entity game limit levels", "description": "Gets list of entity game limit levels", "responses": {"200": {"description": "List of entity game limit levels", "schema": {"type": "array", "items": {"$ref": "#/definitions/EntityGameLimitLevel"}}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["entity-game-limit-level", "entity-game-limit-level:create"]}], "tags": ["Limit levels"], "summary": "Add entity game limit level", "description": "Create entity game limit level and return info with public id", "parameters": [{"$ref": "#/parameters/path"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/CreateEntityGameLimitLevel"}}], "responses": {"200": {"description": "Entity Limit level info", "schema": {"$ref": "#/definitions/EntityGameLimitLevel"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/game-limit-levels": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entity-game-limit-level", "keyentity:entity-game-limit-level:view"]}], "parameters": [{"$ref": "#/parameters/gameCodeStrictEquality"}], "tags": ["Limit levels"], "summary": "Gets list of entity game limit levels", "description": "Gets list of entity game limit levels", "responses": {"200": {"description": "List of entity game limit levels", "schema": {"type": "array", "items": {"$ref": "#/definitions/EntityGameLimitLevel"}}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entity-game-limit-level", "keyentity:entity-game-limit-level:create"]}], "tags": ["Limit levels"], "summary": "Add entity game limit level", "description": "Create entity game limit level and return info with public id", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/CreateEntityGameLimitLevel"}}], "responses": {"200": {"description": "Entity Limit level info", "schema": {"$ref": "#/definitions/EntityGameLimitLevel"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/game-limit-levels/{gameLimitId}": {"delete": {"security": [{"apiKey": []}, {"Permissions": ["entity-game-limit-level", "entity-game-limit-level:delete"]}], "tags": ["Limit levels"], "summary": "Drop entity game limit level", "description": "Drop entity game limit level", "parameters": [{"$ref": "#/parameters/path"}, {"in": "path", "name": "gameLimitId", "required": true, "type": "string", "description": "publicId"}], "responses": {"200": {"description": "Entity Limit level info", "schema": {"$ref": "#/definitions/EntityGameLimitLevel"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/game-limit-levels/{gameLimitId}": {"delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entity-game-limit-level", "keyentity:entity-game-limit-level:delete"]}], "tags": ["Limit levels"], "summary": "Drop entity game limit level", "description": "Drop entity game limit level", "parameters": [{"in": "path", "name": "gameLimitId", "required": true, "type": "string", "description": "publicId"}], "responses": {"200": {"description": "Entity Limit level info", "schema": {"$ref": "#/definitions/EntityGameLimitLevel"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}}