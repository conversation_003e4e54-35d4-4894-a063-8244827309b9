{"/schema-configurations": {"parameters": [], "post": {"security": [{"apiKey": []}, {"Permissions": ["schemaconfiguration", "schemaconfiguration:create"]}], "tags": ["New Game Limits System"], "parameters": [{"$ref": "#/parameters/createSchemaConfiguration"}], "summary": "Create schema configuration", "description": "Create schema configuration, allowed only for master entity", "responses": {"201": {"description": "Created schema configuration", "schema": {"$ref": "#/definitions/SchemaConfiguration"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 793: Object not found by ID (edqrvq7E)\n"}, "409": {"description": "- 789: Schema configuration for this definition already exists"}}}}, "/schema-configurations/{schemaConfigurationId}": {"parameters": [{"$ref": "#/parameters/schemaConfigurationId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["schemaconfiguration", "schemaconfiguration:view"]}], "tags": ["New Game Limits System"], "summary": "Get schema configuration", "description": "Get schema configuration", "responses": {"200": {"description": "Schema configuration", "schema": {"$ref": "#/definitions/SchemaConfiguration"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side:\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 793: Object not found by ID (edqrvq7E)\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["schemaconfiguration", "schemaconfiguration:edit"]}], "tags": ["New Game Limits System"], "parameters": [{"$ref": "#/parameters/updateSchemaConfiguration"}], "summary": "Edit schema configuraion", "description": "Edit schema configuraiont, allowed only for master entity", "responses": {"200": {"description": "Updated schema configuraion", "schema": {"$ref": "#/definitions/SchemaConfiguration"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 793: Object not found by ID (edqrvq7E)\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["schemaconfiguration", "schemaconfiguration:remove"]}], "tags": ["New Game Limits System"], "summary": "Remove schema configuraion", "description": "Remove schema configuration, allowed only for master entity", "responses": {"204": {"description": "Schema configuration was successfully removed"}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 793: Object not found by ID (edqrvq7E)\n"}}}}, "/schema-configuration-for-definition/{schemaDefinitionId}": {"parameters": [{"$ref": "#/parameters/schemaDefinitionId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["schemaconfiguration", "schemaconfiguration:view"]}], "tags": ["New Game Limits System"], "summary": "Get schema configuration for schema definition", "description": "Get schema configuration", "responses": {"200": {"description": "Schema configuration", "schema": {"$ref": "#/definitions/SchemaConfiguration"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side:\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 793: Object not found by ID (edqrvq7E)\n"}}}}, "/schema-configuration-for-definition/{schemaDefinitionId}/currency/{currency}": {"parameters": [{"$ref": "#/parameters/schemaDefinitionId"}, {"$ref": "#/parameters/currency"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["schemaconfiguration", "schemaconfiguration:view"]}], "tags": ["New Game Limits System"], "summary": "Get game limits values from schema configuration for currency using for schema definition id", "description": "Get game limits values from schema configuration for currency using for schema definition id", "responses": {"200": {"description": "Game limits", "schema": {"$ref": "#/definitions/Limits"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side:\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 793: Object not found by ID (edqrvq7E)\n"}}}}}