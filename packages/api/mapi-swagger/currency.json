{"/entities/{path}/currencies": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Gets entity currencies list by path", "description": "Gets list of all available currencies in a specific entity", "responses": {"200": {"description": "List of currencies", "schema": {"type": "array", "items": {"$ref": "#/definitions/Currency"}}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "put": {"security": [{"apiKey": []}, {"Permissions": ["currency", "currency:add"]}], "tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Adds currencies by path", "description": "Adds currencies to specific entity. Currencies must be available in parent entity.", "parameters": [{"$ref": "#/parameters/currencies__body"}], "responses": {"204": {"description": "Currencies added"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 83: Country not exist in parent\n- 85: Currency not found\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: User doesn't have permission to execute operation\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["currency", "currency:remove"]}], "tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Removes currencies by path", "description": "Removes currencies from entity. Default currency should not be put here.", "parameters": [{"$ref": "#/parameters/currencies__body"}], "responses": {"204": {"description": "Currencies removed"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 86: Currency is default. You cannot remove default currency\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: User doesn't have permission to execute operation\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/currencies": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Gets key entity currencies list", "description": "Gets list of all available currencies in the key entity", "responses": {"200": {"description": "List of currencies", "schema": {"type": "array", "items": {"$ref": "#/definitions/Currency"}}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "put": {"security": [{"apiKey": []}, {"Permissions": ["currency", "currency:add"]}], "tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Adds currencies", "description": "Adds currencies to specific entity. Currencies must be available in parent entity.", "parameters": [{"$ref": "#/parameters/currencies__body"}], "responses": {"204": {"description": "Currencies added"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 83: Country does not exist in parent\n- 85: Currency not found\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: User doesn't have permission to execute operation\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["currency", "currency:remove"]}], "tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Removes currencies", "description": "Removes currencies from entity. Default currency should not be put here.", "parameters": [{"$ref": "#/parameters/currencies__body"}], "responses": {"204": {"description": "Currencies removed"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 86: Currency is default. You cannot remove default currency\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: User doesn't have permission to execute operation\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/currencies/{currency}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/currency"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["currency", "currency:add"]}], "tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Adds currency by path", "description": "Adds currency to specific entity. Must be available in parent entity, too.", "responses": {"204": {"description": "<PERSON><PERSON><PERSON><PERSON> added"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 83: Country not exist in parent\n- 85: Currency not found\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["currency", "currency:remove"]}], "tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Removes currency by path", "description": "Removes currency from entity. Only available if it's not default currency.", "parameters": [{"$ref": "#/parameters/forceFlagInQuery"}], "responses": {"204": {"description": "Currency removed"}, "400": {"description": "Returned in case we have error on the server side\n- 86: Currency is default. You cannot remove default currency\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}