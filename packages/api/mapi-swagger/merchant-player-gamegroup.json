{"/merchant/players/gameGroup": {"get": {"security": [{"apiKey": []}, {"Permissions": ["merchant:player:gamegroup"]}], "tags": ["Merchant Game Group"], "summary": "This method allows the Operator to get a list of players along with the game group they’ve been assigned to", "responses": {"200": {"description": "List of players with game codes", "schema": {"$ref": "#/definitions/PlayerGameGroups"}}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/merchant/player/{playerCode}/gameGroup": {"parameters": [{"$ref": "#/parameters/playerCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["merchant:player:gamegroup"]}], "tags": ["Merchant Game Group"], "summary": "This method allows the Operator to get Game Group assigned to the player", "responses": {"200": {"description": "Game group", "schema": {"$ref": "#/definitions/PlayerGameGroup"}}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/merchant/player/{playerCode}/gameGroup/{gameGroup}": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/gameGroup"}], "patch": {"security": [{"apiKey": []}, {"Permissions": ["merchant:player:gamegroup"]}], "tags": ["Merchant Game Group"], "summary": "This method allows the Operator to assign a player to a particular game group", "responses": {"200": {"description": "Successfully executed"}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["merchant:player:gamegroup"]}], "tags": ["Merchant Game Group"], "summary": "This method allows the Operator to delete gameGroup assigned to the player", "responses": {"200": {"description": "Successfully executed"}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}}