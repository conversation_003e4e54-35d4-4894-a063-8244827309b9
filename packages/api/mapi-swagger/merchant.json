{"/merchants": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:merchant", "keyentity:merchant:view"]}], "tags": ["Merchant"], "summary": "Gets merchant of key entity", "responses": {"200": {"description": "List of merchants", "schema": {"type": "array", "items": {"$ref": "#/definitions/Merchant"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:merchant", "keyentity:merchant:edit"]}], "tags": ["Merchant"], "summary": "Updates merchant of key entity", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/UpdateMerchantData"}}], "responses": {"200": {"description": "Merchant information", "schema": {"$ref": "#/definitions/Merchant"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 500: Merchant already exists\n"}}}}, "/merchants/game/url": {"post": {"tags": ["Merchant"], "summary": "Gets game URL for third party integration", "parameters": [{"$ref": "#/parameters/ip"}, {"$ref": "#/parameters/ip_header"}, {"name": "request", "in": "body", "description": "Game Init request", "required": true, "schema": {"type": "object", "required": ["merchantType", "merchantCode", "gameCode"], "additionalProperties": {"type": "string"}, "properties": {"merchantType": {"type": "string", "description": "merchant integration type", "example": "type1"}, "merchantCode": {"type": "string", "description": "merchant integration code", "example": "code1"}, "gameCode": {"type": "string", "description": "game code", "example": "merchant-game"}, "token": {"type": "string", "description": "merchant's security token", "example": "oJqXX2tkAADKn3MpcM9kVbVk53neuIYI62dEkYdYubl+9lyXRECjQww3VsmEPfMoUkO6uqB56WDPhPGdS3aGnQ"}}}}], "responses": {"200": {"description": "Game URL for player", "schema": {"$ref": "#/definitions/PlayerGameURLInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 502: Merchant not found\n"}}}}, "/merchants/lobby/url": {"post": {"tags": ["Merchant"], "summary": "Gets lobby URL for third party integration", "parameters": [{"name": "request", "in": "body", "description": "Lobby url request", "required": true, "schema": {"type": "object", "required": ["merchantType", "merchantCode", "ticket"], "properties": {"merchantType": {"type": "string", "description": "merchant integration type", "example": "type1"}, "merchantCode": {"type": "string", "description": "merchant integration code", "example": "code1"}, "ticket": {"type": "string", "description": "merchant’s ticket, used for authentication and getting session ID", "example": "mrch0fwd"}, "language": {"type": "string", "description": "customer language code", "example": "en"}, "lobbyId": {"type": "string", "description": "Lobby public id", "example": "en"}}}}], "responses": {"200": {"description": "Lobby <PERSON> for player", "schema": {"type": "object", "required": ["url"], "properties": {"url": {"type": "string", "description": "lobby URL for specific player", "example": "http://super_lobby.com/"}}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 502: Merchant not found\n"}}}}, "/merchants/{path}": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["merchant", "merchant:view"]}], "tags": ["Merchant"], "summary": "Gets merchant data of entity by path", "responses": {"200": {"description": "List of merchants", "schema": {"type": "array", "items": {"$ref": "#/definitions/Merchant"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 504: Not merchant brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 502: Merchant not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["merchant", "merchant:edit"]}], "tags": ["Merchant"], "summary": "Updates merchant data of entity by path", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/UpdateMerchantData"}}], "responses": {"200": {"description": "Merchant information", "schema": {"$ref": "#/definitions/Merchant"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 502: Merchant not found\n"}, "409": {"description": "- 500: Merchant already exists\n"}}}}}