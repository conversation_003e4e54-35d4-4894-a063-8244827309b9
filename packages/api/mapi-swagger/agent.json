{"/entities/{path}/agents": {"get": {"security": [{"apiKey": []}, {"Permissions": ["agent", "agent:view"]}], "tags": ["Agent"], "summary": "Gets list of agents by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/affiliateCodeStyleStrictEquality"}, {"$ref": "#/parameters/affiliateCodeContains"}, {"$ref": "#/parameters/affiliateCodeNotContains"}, {"$ref": "#/parameters/affiliateCodeIn"}, {"$ref": "#/parameters/domainStrictEquality"}, {"$ref": "#/parameters/domainContains"}, {"$ref": "#/parameters/domainNotContains"}, {"$ref": "#/parameters/domainIn"}, {"$ref": "#/parameters/status"}], "responses": {"200": {"description": "Entity settings", "schema": {"type": "array", "items": {"$ref": "#/definitions/AgentSchema"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["agent"]}], "tags": ["Agent"], "summary": "Add agent for brand by path", "parameters": [{"$ref": "#/parameters/path"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/AddItemAgentData"}}], "responses": {"201": {"description": "Added item object", "schema": {"$ref": "#/definitions/AgentSchema"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}, "500": {"description": "- 216: Bad <PERSON>Id from list of agents or Domain is not unique\n"}}}}, "/entities/{path}/agents/{agentId}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/agentId"}], "patch": {"security": [{"apiKey": []}, {"Permissions": ["agent"]}], "tags": ["Agent"], "summary": "Updates agent under a specific brand by path", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/AddItemAgentData"}}], "responses": {"200": {"description": "Patched agent information", "schema": {"$ref": "#/definitions/AgentSchema"}}, "400": {"description": "Returned in case we have error on the server side\n- 218: Bad query for updating agent\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 215: No record for this Domain in agents\n"}, "500": {"description": "- 216: Bad <PERSON>Id from list of agents or Domain is not unique\n"}}}}, "/entities/{path}/agents/{agentId}/suspended": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/agentId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["agent"]}], "tags": ["Agent"], "summary": "Suspends domain for brand by path", "description": "Changes the status of an domain to suspended", "responses": {"200": {"description": "Agent information", "schema": {"$ref": "#/definitions/AgentSchema"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["agent"]}], "tags": ["Agent"], "summary": "Restores domain for brand by path", "description": "Changes the status of an brl to normal", "responses": {"200": {"description": "Agent information", "schema": {"$ref": "#/definitions/AgentSchema"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/agents": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:agent", "keyentity:agent:view"]}], "tags": ["Agent"], "summary": "Gets list of agents under the key entity", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/affiliateCodeStyleStrictEquality"}, {"$ref": "#/parameters/affiliateCodeContains"}, {"$ref": "#/parameters/affiliateCodeNotContains"}, {"$ref": "#/parameters/affiliateCodeIn"}, {"$ref": "#/parameters/domainStrictEquality"}, {"$ref": "#/parameters/domainContains"}, {"$ref": "#/parameters/domainNotContains"}, {"$ref": "#/parameters/domainIn"}, {"$ref": "#/parameters/status"}], "responses": {"200": {"description": "Entity settings", "schema": {"type": "array", "items": {"$ref": "#/definitions/AgentSchema"}}}, "401": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 204: Access token error\n- 205: Access Token has expired\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:agent"]}], "tags": ["Agent"], "summary": "Add agent for brand under the key entity", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/AddItemAgentData"}}], "responses": {"201": {"description": "Added item object", "schema": {"$ref": "#/definitions/AgentSchema"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/agents/{agentId}": {"patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:agent"]}], "tags": ["Agent"], "summary": "Updates agent under the key entity", "parameters": [{"$ref": "#/parameters/agentId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/AddItemAgentData"}}], "responses": {"200": {"description": "Patched agent information", "schema": {"$ref": "#/definitions/AgentSchema"}}, "400": {"description": "Returned in case we have error on the server side\n- 218: Bad query for updating agent\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 215: No record for this Domain in agents\n"}}}}, "/agents/{agentId}/suspended": {"parameters": [{"$ref": "#/parameters/agentId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:agent"]}], "tags": ["Agent"], "summary": "Suspends domain for brand under key entity", "description": "Changes the status of an domain to suspended", "responses": {"200": {"description": "Agent information", "schema": {"$ref": "#/definitions/AgentSchema"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 215: No record for this Domain in agents\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:agent"]}], "tags": ["Agent"], "summary": "Restores domain for brand under key entity", "description": "Changes the status of an agent to normal", "responses": {"200": {"description": "Agent information", "schema": {"$ref": "#/definitions/AgentSchema"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 215: No record for this Domain in agents\n"}}}}, "/entities/{path}/agents/group/status": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["agent"]}], "tags": ["Agent"], "summary": "Sets status for group of agents under a specific brand by path", "parameters": [{"$ref": "#/parameters/agentGroupStatus"}], "responses": {"204": {"description": "Statuses has been changed"}, "400": {"description": "Returned in case we have error on the server side\n- 101: Not a brand\n- 151: Too many items for group action\n- 152: Incorrect action query\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/agents/group/status": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:agent"]}], "tags": ["Agent"], "summary": "Sets status for group of agents under key entity", "parameters": [{"$ref": "#/parameters/agentGroupStatus"}], "responses": {"204": {"description": "Statuses changed"}, "400": {"description": "Returned in case we have error on the server side\n- 101: Not a brand\n- 151: Too many items for group action\n- 152: Incorrect action query\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}