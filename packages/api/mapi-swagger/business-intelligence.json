{"/bi/reports": {"get": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi:reports", "keyentity:bi:reports:view"]}], "summary": "Gets reports", "description": "Method gets list of reports available for user for keyentity", "parameters": [{"$ref": "#/parameters/AcceptLanguage"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "List of reports", "schema": {"type": "array", "items": {"$ref": "#/definitions/BiReportInfo"}}}, "400": {"description": "- 726: Reports list does not initialized\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi:reports"]}], "summary": "Creates new report", "description": "Method creates new report name with settings for all entities", "parameters": [{"$ref": "#/parameters/biReportCreateData"}], "responses": {"201": {"description": "Created report", "schema": {"$ref": "#/definitions/BiReportInfo"}}, "400": {"description": "- 40: Validation error\n- 725: Create Report request error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 724: Report already exists. You can patch it\n"}}}}, "/bi/reports/{reportId}": {"parameters": [{"$ref": "#/parameters/reportId"}], "patch": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi:reports"]}], "parameters": [{"$ref": "#/parameters/biReportUpdateData"}], "summary": "Updates report by its public id", "description": "Method updates existing report by its public id. Editable fields - caption, description, status, permission and settings", "responses": {"200": {"description": "Updated Report", "schema": {"$ref": "#/definitions/BiReportInfo"}}, "400": {"description": "- 40: Validation error\n- 725: Create Report request error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi:reports"]}], "summary": "Deletes report by its public id", "description": "Method deletes existing report by its public id. Report is deleted only from MAPI but it still exists in Business Intelligence Server Reports System", "responses": {"204": {"description": "Report has been deleted"}, "400": {"description": "- 40: Validation error\n- 727: Report not found\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/bi/reports/permissions": {"get": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi:reports"]}], "summary": "Gets list of report permissions", "description": "Method returns list of all permissions and its postfixes", "responses": {"200": {"description": "List of permissions", "schema": {"type": "array", "items": {"$ref": "#/definitions/BiPermissionInfo"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/bi/reports": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi:reports", "bi:reports:view"]}], "summary": "Gets reports for specific entity by path", "description": "Method gets list of reports available for user for entity from path", "parameters": [{"$ref": "#/parameters/AcceptLanguage"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "List of reports", "schema": {"type": "array", "items": {"$ref": "#/definitions/BiReportInfo"}}}, "400": {"description": "- 726: Reports list does not initialized\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/bi/reports/url": {"post": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi:reports", "keyentity:bi:reports:view"]}], "summary": "Create an URL of BI report", "description": "Method generates token for access to BI report by workbook and report name. Permissions and parameters should be posted before.", "parameters": [{"$ref": "#/parameters/biCreateUrlData"}, {"$ref": "#/parameters/AcceptLanguage"}], "responses": {"200": {"description": "Created report", "schema": {"$ref": "#/definitions/BiUrlInfo"}}, "400": {"description": "- 40: Validation error\n- 727: Report not found\n- 728: Report is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/bi/reports/url": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi:reports", "bi:reports:view"]}], "summary": "Create an URL of BI report for entity by path", "description": "Method generates token for access to BI report by workbook and report name. Permissions and parameters should be posted before.", "parameters": [{"$ref": "#/parameters/biCreateUrlData"}, {"$ref": "#/parameters/AcceptLanguage"}], "responses": {"200": {"description": "Created report", "schema": {"$ref": "#/definitions/BiUrlInfo"}}, "400": {"description": "- 40: Validation error\n- 727: Report not found\n- 728: Report is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/bi/reports/{reportId}/move": {"parameters": [{"$ref": "#/parameters/reportId"}], "put": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi:reports"]}], "parameters": [{"$ref": "#/parameters/biReportUpdateOrderingData"}], "summary": "Moves report by its public id", "description": "Method moves existing report by its public id", "responses": {"200": {"description": "Move Report", "schema": {"$ref": "#/definitions/BiReportInfo"}}, "400": {"description": "- 40: Validation error\n- 727: Report not found\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/bi/reports/domains/{reportDomainsId}": {"parameters": [{"$ref": "#/parameters/reportDomainsId"}], "get": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi-reports-domains", "keyentity:bi-reports-domains:view"]}], "summary": "Returns report domains", "description": "Returns report domains", "responses": {"200": {"description": "Report domains have been returned", "schema": {"$ref": "#/definitions/BiReportDomains"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 793: ResourceNotFoundError\n"}}}, "put": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi-reports-domains", "keyentity:bi-reports-domains:edit"]}], "parameters": [{"$ref": "#/parameters/biReportDomainsUpdateData"}], "summary": "Updates report domains", "description": "Updates report domains", "responses": {"200": {"description": "Report domains have been updated", "schema": {"$ref": "#/definitions/BiReportDomains"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 793: ResourceNotFoundError\n"}}}, "delete": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi-reports-domains", "keyentity:bi-reports-domains:delete"]}], "parameters": [{"name": "forceDelete", "in": "query", "description": "Delete report domains forcibly", "required": false, "type": "boolean"}], "summary": "Deletes report domains", "description": "Deletes report domains", "responses": {"204": {"description": "Report domains have been deleted"}, "400": {"description": "- 40: Validation error\n- 867: DeleteReportDomainsError\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 793: ResourceNotFoundError\n"}}}}, "/bi/reports/domains/{reportDomainsId}/select": {"parameters": [{"$ref": "#/parameters/reportDomainsId"}], "patch": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi-reports-domains", "keyentity:bi-reports-domains:select"]}], "summary": "Selects report domains", "description": "Selects report domains", "responses": {"200": {"description": "Report domains have been updated", "schema": {"$ref": "#/definitions/BiReportDomainsUpdateData"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 793: ResourceNotFoundError\n"}}}}, "/bi/reports/domains": {"get": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi-reports-domains", "keyentity:bi-reports-domains:view"]}], "summary": "Returns report domains", "description": "Returns report domains", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "Report domains have been returned", "schema": {"$ref": "#/definitions/BiReportDomains"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"tags": ["Business Intelligence"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:bi-reports-domains", "keyentity:bi-reports-domains:create"]}], "summary": "Creates report domains", "description": "Creates report domains", "parameters": [{"$ref": "#/parameters/biReportDomainsCreateData"}], "responses": {"200": {"description": "Report domains have been updated", "schema": {"$ref": "#/definitions/BiReportDomains"}}, "400": {"description": "- 868: CreateReportDomainsError\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}}