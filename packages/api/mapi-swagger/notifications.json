{"/notifications/send": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:notifications"]}], "tags": ["Notifications"], "summary": "Sends broadcast notification to users", "description": "Method sends notifications to users from list, all users from my entity (if allSiblings=true) or all users in my entity and subentities (recursively=true)", "parameters": [{"name": "message", "in": "body", "description": "Notification Request", "required": true, "schema": {"type": "object", "required": ["message"], "properties": {"message": {"type": "string", "description": "string broadcast message for users", "example": "New feature! Filtering and sorting active now players"}, "allSiblings": {"type": "boolean", "description": "send message to all users of current entity", "example": false}, "recursively": {"type": "boolean", "description": "send message to all users of all subentities", "example": true}, "receiversId": {"type": "array", "description": "list of users ids who receive this message", "items": {"type": "string"}, "example": ["7Lg8Z3d5", "7LgZZjXa"]}}}}], "responses": {"204": {"description": "Notifications have been sent"}, "400": {"description": "- 40: Validation error\n- 340: No receivers found for notification\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/notifications/list": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:notifications", "keyentity:notifications:view"]}], "tags": ["Notifications"], "summary": "Gets all notifications by filter", "description": "Method gets all received notifications. Notifications can be fitered by status, unread flag and sorted by status, unread and sended time (ts).", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/status"}, {"$ref": "#/parameters/unread"}], "responses": {"200": {"description": "List of notifications", "schema": {"type": "array", "items": {"$ref": "#/definitions/NotificationInfo"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/notifications/posted": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:notifications"]}], "tags": ["Notifications"], "summary": "Gets all posted notifications by filter", "description": "Method gets all posted notifications. Notifications can be fitered by status and sorted by status and sended time (ts).", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/status"}], "responses": {"200": {"description": "List of notifications", "schema": {"type": "array", "items": {"$ref": "#/definitions/NotificationShortInfo"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/notifications/group/mark": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:notifications", "keyentity:notifications:view"]}], "tags": ["Notifications"], "summary": "Marks group of messages as read/unread or normal/suspended", "description": "Method changes statuses and unread flags for group of recieved notifications.", "parameters": [{"name": "info", "in": "body", "description": "Notification's ids", "required": true, "schema": {"type": "object", "required": ["id"], "properties": {"status": {"type": "string", "description": "new status", "example": "normal", "enum": ["normal", "suspended"]}, "unread": {"type": "boolean", "description": "is message unread", "enum": [true, false]}, "id": {"type": "array", "description": "list of notifications's ids", "items": {"type": "string"}, "example": ["7Lg8Z3d5", "7LgZZjXa"]}}}}], "responses": {"204": {"description": "Notifications have been marked or unmarked as read or suspended"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/notifications/{notificationId}/suspended": {"parameters": [{"$ref": "#/parameters/notificationId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:notifications"]}], "tags": ["Notifications"], "summary": "Suspends notification", "description": "Method marks sended notification as suspended or normal", "responses": {"200": {"description": "Notification info after changing", "schema": {"$ref": "#/definitions/NotificationShortInfo"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}}