{"/gitbook/login": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:integration:view"]}], "tags": ["Gitbook"], "summary": "GitBook login data", "description": "Generate a token and url for work with Gitbook", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["space"], "properties": {"space": {"type": "string", "description": "Gitbook space name", "example": "skywind-api-documentation"}}}}], "responses": {"200": {"description": "GitBook login data", "schema": {"$ref": "#/definitions/GitbookLoginData"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n"}, "401": {"description": "- 10: <PERSON>ss token is missing\n"}, "404": {"description": "- 51: Could not find entity\n- 227: Site token does not exist"}}}}}