{"/players/login": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:login"]}], "tags": ["Player"], "summary": "Logs player in", "description": "Logs player in under a specific parent", "parameters": [{"in": "body", "name": "info", "required": true, "description": "Login data. Should contains player's code and password.", "schema": {"$ref": "#/definitions/LoginPlayerDataInfo"}}], "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginPlayerInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 860: Player has another session", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 223: Player created without password\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/entities/{path}/players/login": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:login"]}], "tags": ["Player"], "summary": "Logs player in by path", "description": "Logs player in under a specific parent", "parameters": [{"in": "body", "name": "info", "required": true, "description": "Login data. Should contains player's code and password.", "schema": {"$ref": "#/definitions/LoginPlayerDataInfo"}}], "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginPlayerInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 223: Player created without password\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/players/password/reset": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:login"]}], "tags": ["Player"], "summary": "Reset password via email", "description": "Sends confirmation email for reset player's password", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["email"], "properties": {"email": {"type": "string", "description": "player's email", "example": "<EMAIL>"}, "redirectTo": {"type": "string", "description": "url of site with \"new password form\" guid will added to the end of url", "example": "https://game-portal.fart88.com/confirm-password/"}}}}], "responses": {"204": {"description": "Confirmation email has been sent"}, "400": {"description": "- 40: Validation error\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/players/{playerCode}/password": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:login"]}], "tags": ["Player"], "summary": "Update player password by path", "description": "Update player password with new password", "parameters": [{"$ref": "#/parameters/updatePlayerPassword"}], "responses": {"200": {"description": "Player info", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"schema": {"$ref": "#/definitions/Error"}, "description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n"}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n- 223: Player created without password\n- 231: Player change password is blocked\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/players/{playerCode}/password": {"parameters": [{"$ref": "#/parameters/playerCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:login"]}], "tags": ["Player"], "summary": "Update player password", "description": "Update player password with new password", "parameters": [{"$ref": "#/parameters/updatePlayerPassword"}], "responses": {"200": {"description": "Player info", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n"}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n- 223: Player created without password\n- 231: Player change password is blocked\n"}, "404": {"schema": {"$ref": "#/definitions/Error"}, "description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/entities/{path}/players/password/reset": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:login"]}], "tags": ["Player"], "summary": "Reset password via email by path", "description": "Sends confirmation email for reset player's password", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["email"], "properties": {"email": {"type": "string", "description": "player's email", "example": "<EMAIL>"}, "redirectTo": {"type": "string", "description": "url of site with \"new password form\" guid will added to the end of url", "example": "https://game-portal.fart88.com/confirm-password/"}}}}], "responses": {"204": {"description": "Confirmation email has been sent"}, "400": {"description": "- 40: Validation error\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/players/password/confirm": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:login"]}], "tags": ["Player"], "summary": "Set new password", "description": "Set new password after email by token", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["password", "guid"], "properties": {"password": {"type": "string", "format": "password", "description": "new password is longer than or equal 8 letters and contains at least one letter, one uppercase letter and one digit", "example": "19Letters&4Numbers&3Signs!"}, "guid": {"type": "string", "description": "guid from confirmation email", "example": "263480bd-ca9b-4f5a-a713-f7a024c3c7db"}}}}], "responses": {"204": {"description": "Password has been changed"}, "400": {"description": "- 40: Validation error\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 221: Reset password link is expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/players/password/confirm": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:login"]}], "tags": ["Player"], "summary": "Set new password by path", "description": "Set new password after email by token", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["password", "guid"], "properties": {"password": {"type": "string", "format": "password", "description": "new password is longer than or equal 8 letters and contains at least one letter, one uppercase letter and one digit", "example": "19Letters&4Numbers&3Signs!"}, "guid": {"type": "string", "description": "guid from confirmation email", "example": "263480bd-ca9b-4f5a-a713-f7a024c3c7db"}}}}], "responses": {"204": {"description": "Password has been changed"}, "400": {"description": "- 40: Validation error\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 221: Reset password link is expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/players/password/{playerCode}/temporary": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:login"]}], "tags": ["Player"], "summary": "Set temporary password by path", "description": "Set temporary password and send it in response", "responses": {"200": {"description": "New temporary password has been set", "schema": {"$ref": "#/definitions/TemporaryPassword"}}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/players/password/{playerCode}/temporary": {"parameters": [{"$ref": "#/parameters/playerCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:login"]}], "tags": ["Player"], "summary": "Set temporary password", "description": "Set temporary password and send it in response", "responses": {"200": {"description": "New temporary password has been set", "schema": {"$ref": "#/definitions/TemporaryPassword"}}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/players/register": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:create"]}], "tags": ["Player"], "summary": "Creates new player under the key entity", "parameters": [{"$ref": "#/parameters/registerPlayer"}], "responses": {"201": {"description": "Created player", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 82: Country not in list\n- 87: Currency not in list\n- 95: Language not in list\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group is not found\n"}}}}, "/entities/{path}/players/register": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:create"]}], "tags": ["Player"], "summary": "Creates new player by path", "parameters": [{"$ref": "#/parameters/registerPlayer"}], "responses": {"201": {"description": "Created player", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 82: Country not in list\n- 87: Currency not in list\n- 95: Language not in list\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group is not found\n"}}}}, "/entities/{path}/players": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:create"]}], "tags": ["Player"], "summary": "Creates new player under a specific brand by path", "parameters": [{"$ref": "#/parameters/createPlayer"}], "responses": {"201": {"description": "Created player", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 82: Country not in list\n- 87: Currency not in list\n- 95: Language not in list\n- 101: Not a brand\n- 503: Merchant brand doesn't support this operation\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group is not found\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:view"]}], "tags": ["Player"], "summary": "Finds players under the key entity by path", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/firstNameStrictEquality"}, {"$ref": "#/parameters/firstNameContains"}, {"$ref": "#/parameters/firstNameNotContains"}, {"$ref": "#/parameters/firstNameIn"}, {"$ref": "#/parameters/lastNameStrictEquality"}, {"$ref": "#/parameters/lastNameContains"}, {"$ref": "#/parameters/lastNameNotContains"}, {"$ref": "#/parameters/lastNameIn"}, {"$ref": "#/parameters/emailStrictEquality"}, {"$ref": "#/parameters/emailContains"}, {"$ref": "#/parameters/emailNotContains"}, {"$ref": "#/parameters/emailIn"}, {"$ref": "#/parameters/gameGroupStrictEquality"}, {"$ref": "#/parameters/gameGroupIn"}, {"$ref": "#/parameters/countryStrictEquality"}, {"$ref": "#/parameters/countryIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/lastLogin"}, {"$ref": "#/parameters/lastLogin__gt"}, {"$ref": "#/parameters/lastLogin__lt"}, {"$ref": "#/parameters/createdAt"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/updatedAt"}, {"$ref": "#/parameters/updatedAt__gt"}, {"$ref": "#/parameters/updatedAt__lt"}, {"$ref": "#/parameters/status"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/withoutGameGroup"}, {"$ref": "#/parameters/withBalance"}], "responses": {"200": {"description": "List of players", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerInfoWithBalances"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 101: Not a brand\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n"}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n- 211: Game group is not found\n"}}}}, "/players/info": {"put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:create", "keyentity:player:edit"]}], "tags": ["Player"], "parameters": [{"$ref": "#/parameters/upsertPlayerInfo"}], "summary": "Create or update player info under the key entity", "description": "Method allow create or update player's info information.", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerChatInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 504: Not a merchant brand\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:view"]}], "tags": ["Player"], "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/createdAt"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/updatedAt"}, {"$ref": "#/parameters/updatedAt__gt"}, {"$ref": "#/parameters/updatedAt__lt"}], "summary": "Get player info under the entity", "description": "Method allow get player's info information.", "responses": {"200": {"description": "Player information", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerChatInfo"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 504: Not a merchant brand\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/players/info/{brandPID}/{playerCode}/change-nickname/attempts": {"parameters": [{"$ref": "#/parameters/brandPIDPath"}, {"$ref": "#/parameters/playerCode"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:reset-change-nickname-attempts"]}], "tags": ["Player"], "summary": "Reset nickname change attempts", "description": "Method allows to reset attempts to change player's nickname", "responses": {"204": {"description": "Operation succeeded"}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 504: Not a merchant brand\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found"}}}}, "/entities/{path}/players/info": {"parameters": [{"$ref": "#/parameters/path"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:create", "keyentity:player:edit"]}], "tags": ["Player"], "parameters": [{"$ref": "#/parameters/upsertPlayerInfo"}], "summary": "Create or update player info under the key entity", "description": "Method allow create or update player's info information.", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerChatInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 504: Not a merchant brand\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:view"]}], "tags": ["Player"], "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/createdAt"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/updatedAt"}, {"$ref": "#/parameters/updatedAt__gt"}, {"$ref": "#/parameters/updatedAt__lt"}], "summary": "Get player info under the entity", "description": "Method allow get player's info information.", "responses": {"200": {"description": "Player information", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerChatInfo"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 504: Not a merchant brand\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/players/{playerCode}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:view"]}], "tags": ["Player"], "summary": "Gets player information under a specific brand by path", "parameters": [{"$ref": "#/parameters/with<PERSON><PERSON>t"}, {"$ref": "#/parameters/withLastAction"}], "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalanceExtended"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 211: Game group is not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:edit"]}], "tags": ["Player"], "summary": "Updates player under a specific brand by path", "parameters": [{"$ref": "#/parameters/updatePlayer"}], "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 82: Country not in list\n- 87: Currency not in list\n- 95: Language not in list\n- 101: Not a brand\n- 113: Player personal information cannot be saved\n- 225: Player info has not changed\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group is not found\n"}}}}, "/entities/{path}/players/{playerCode}/session": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["playersession:find"]}], "tags": ["Player"], "summary": "Gets player information under a specific brand by path", "responses": {"200": {"description": "Player session information", "schema": {"$ref": "#/definitions/PlayerSessionInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["playersession:kill"]}], "tags": ["Player"], "parameters": [{"$ref": "#/parameters/reason"}], "summary": "Kill player session by path", "responses": {"202": {"description": "Player session is killed"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/entities/{path}/players/{playerCode}/gamegroups/{gameGroup}": {"put": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:edit"]}], "tags": ["Player"], "summary": "Updates player game group under a specific brand, to reset game group send null", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/gameGroup"}], "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group is not found\n"}}}}, "/players": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:create"]}], "tags": ["Player"], "summary": "Creates new player under the key entity", "parameters": [{"$ref": "#/parameters/createPlayer"}], "responses": {"201": {"description": "Created player", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 82: Country not in list\n- 87: Currency not in list\n- 95: Language not in list\n- 101: Not a brand\n- 503: Merchant brand doesn't support this operation\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group is not found\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:view"]}], "tags": ["Player"], "summary": "Finds players under the specified entity", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/firstNameStrictEquality"}, {"$ref": "#/parameters/firstNameContains"}, {"$ref": "#/parameters/firstNameNotContains"}, {"$ref": "#/parameters/firstNameIn"}, {"$ref": "#/parameters/lastNameStrictEquality"}, {"$ref": "#/parameters/lastNameContains"}, {"$ref": "#/parameters/lastNameNotContains"}, {"$ref": "#/parameters/lastNameIn"}, {"$ref": "#/parameters/emailStrictEquality"}, {"$ref": "#/parameters/emailContains"}, {"$ref": "#/parameters/emailNotContains"}, {"$ref": "#/parameters/emailIn"}, {"$ref": "#/parameters/gameGroupStrictEquality"}, {"$ref": "#/parameters/gameGroupIn"}, {"$ref": "#/parameters/countryStrictEquality"}, {"$ref": "#/parameters/countryIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/lastLogin"}, {"$ref": "#/parameters/lastLogin__gt"}, {"$ref": "#/parameters/lastLogin__lt"}, {"$ref": "#/parameters/createdAt"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/updatedAt"}, {"$ref": "#/parameters/updatedAt__gt"}, {"$ref": "#/parameters/updatedAt__lt"}, {"$ref": "#/parameters/status"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/withoutGameGroup"}, {"$ref": "#/parameters/withBalance"}], "responses": {"200": {"description": "List of players", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerInfoWithBalances"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 101: Not a brand\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group is not found\n"}}}}, "/players/bulk-operation": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player:bulk-operation"]}], "tags": ["Player"], "summary": "Update game group for players", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayerBulkOperations"}}], "responses": {"201": {"description": "Bulk operation results - array of players", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/players/{playerCode}": {"parameters": [{"$ref": "#/parameters/playerCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:view"]}], "tags": ["Player"], "summary": "Gets player information under the key entity", "parameters": [{"$ref": "#/parameters/with<PERSON><PERSON>t"}, {"$ref": "#/parameters/withLastAction"}], "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalanceExtended"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 211: Game group is not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:edit"]}], "tags": ["Player"], "summary": "Updates player under the key entity", "parameters": [{"$ref": "#/parameters/updatePlayer"}], "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 82: Country not in list\n- 87: Currency not in list\n- 95: Language not in list\n- 101: Not a brand\n- 113: Player personal information cannot be saved\n- 225: Player info has not changed\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 211: Game group is not found\n"}}}}, "/players/{playerCode}/session": {"parameters": [{"$ref": "#/parameters/playerCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:playersession:find"]}], "tags": ["Player"], "summary": "Gets player information under the key entity", "responses": {"200": {"description": "Player session information", "schema": {"$ref": "#/definitions/PlayerSessionInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:playersession:kill"]}], "parameters": [{"$ref": "#/parameters/reason"}], "tags": ["Player"], "summary": "Kill player session", "responses": {"202": {"description": "Player session is killed"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/players/{playerCode}/gamegroups/{gameGroup}": {"put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:edit"]}], "tags": ["Player"], "summary": "Updates player game group under a specific brand, to reset game group send null", "parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/gameGroup"}], "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 211: Game group is not found\n"}}}}, "/entities/{path}/players/{playerCode}/suspended": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:change-state"]}], "parameters": [{"$ref": "#/parameters/reason"}], "tags": ["Player"], "summary": "Suspends player by path", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalances"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:change-state"]}], "tags": ["Player"], "summary": "Restores player by path", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalances"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/players/{playerCode}/login-lock": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["player-extra:login-unlock"]}], "tags": ["Player"], "summary": "Unlock player by path", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalances"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/players/group/status": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:edit", "player:change-state"]}], "tags": ["Player"], "summary": "Sets status for group of players under a specific brand by path", "parameters": [{"$ref": "#/parameters/playerGroupStatus"}], "responses": {"204": {"description": "Statuses changed"}, "400": {"description": "Returned in case we have error on the server side\n- 101: Not a brand\n- 151: Too many items for group action\n- 152: Incorrect action query\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/players/group/status": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:edit", "keyentity:player:change-state"]}], "tags": ["Player"], "summary": "Sets status for group of players under key entity", "parameters": [{"$ref": "#/parameters/playerGroupStatus"}], "responses": {"204": {"description": "Statuses changed"}, "400": {"description": "Returned in case we have error on the server side\n- 101: Not a brand\n- 151: Too many items for group action\n- 152: Incorrect action query\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/players/{playerCode}/deposits/{currency}/{amount}": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:deposit"]}], "tags": ["Player"], "summary": "Deposits to key entity's player account", "parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/currency"}, {"$ref": "#/parameters/amount"}, {"$ref": "#/parameters/externalReference"}], "responses": {"200": {"description": "Deposit exists"}, "201": {"description": "Amount deposited"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 89: Currency not exist\n- 729: Entity does not have sufficient balance to perform an operation\n- 101: Not a brand\n- 108: Max capacity reached\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/players/{playerCode}/withdrawals/{currency}/{amount}": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:withdrawal"]}], "tags": ["Player"], "summary": "Withdraws from key entity's player account", "parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/currency"}, {"$ref": "#/parameters/amount"}, {"$ref": "#/parameters/externalReference"}], "responses": {"200": {"description": "Withdrawal exists"}, "201": {"description": "Amount withdrawn"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 89: Currency not exist\n- 91: Player does not have sufficient balance to perform an operation\n- 101: Not a brand\n- 108: Max capacity reached\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/players/{playerCode}/deposits/{currency}/{amount}": {"post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:deposit"]}], "tags": ["Player"], "summary": "Deposits to key entity's player account by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/currency"}, {"$ref": "#/parameters/amount"}, {"$ref": "#/parameters/externalReference"}], "responses": {"200": {"description": "Deposit exists"}, "201": {"description": "Amount deposited"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 89: Currency not exist\n- 729: En<PERSON><PERSON> does not have sufficient balance to perform an operation\n- 101: Not a brand\n- 108: Max capacity reached\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/players/{playerCode}/withdrawals/{currency}/{amount}": {"post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:withdrawal"]}], "tags": ["Player"], "summary": "Withdraws from key entity's player account by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/currency"}, {"$ref": "#/parameters/amount"}, {"$ref": "#/parameters/externalReference"}], "responses": {"200": {"description": "Withdrawal exists"}, "201": {"description": "Amount withdrawn"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 89: Currency not exist\n- 91: Player does not have sufficient balance to perform an operation\n- 101: Not a brand\n- 108: Max capacity reached\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/players/{playerCode}/suspended": {"parameters": [{"$ref": "#/parameters/playerCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:change-state"]}], "parameters": [{"$ref": "#/parameters/reason"}], "tags": ["Player"], "summary": "Suspends key entity player", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalances"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:change-state"]}], "tags": ["Player"], "summary": "Restores key entity player", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalances"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/players/{playerCode}/login-lock": {"parameters": [{"$ref": "#/parameters/playerCode"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player-extra:login-unlock"]}], "tags": ["Player"], "summary": "Unlock player", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalances"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/players/{playerCode}/password/set-for-unpassworded": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:login"]}], "tags": ["Player"], "summary": "Set player password by path", "description": "Set player password for player without password", "parameters": [{"$ref": "#/parameters/setPlayerPassword"}], "responses": {"200": {"description": "Player info", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"schema": {"$ref": "#/definitions/Error"}, "description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n"}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n- 775: Password already exists\n- 231: Player change password is blocked\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/players/{playerCode}/password/set-for-unpassworded": {"parameters": [{"$ref": "#/parameters/playerCode"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:login"]}], "tags": ["Player"], "summary": "Set player password", "description": "Set player password for player without password", "parameters": [{"$ref": "#/parameters/setPlayerPassword"}], "responses": {"200": {"description": "Player info", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"schema": {"$ref": "#/definitions/Error"}, "description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n"}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n- 775: Password already exists\n- 231: Player change password is blocked\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/players/validate-nickname/{playerNickname}": {"parameters": [{"$ref": "#/parameters/playerNickname"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:view"]}], "tags": ["Player"], "summary": "check valid player nickname", "responses": {"204": {"description": "is valid player nickname"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/player/change-nickname": {"patch": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:change-nickname"]}], "tags": ["Player"], "summary": "Change nickname", "parameters": [{"$ref": "#/parameters/ChangeNickname"}], "responses": {"204": {"description": "Nickname has been changed successfully"}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/player/change-nickname": {"patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:change-nickname"]}], "tags": ["Player"], "summary": "Change nickname", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/ChangeNickname"}], "responses": {"204": {"description": "Nickname has been changed successfully"}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}