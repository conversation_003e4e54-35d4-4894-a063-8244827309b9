{"/gamecategories": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamecategory", "keyentity:gamecategory:view"]}], "tags": ["Game Category"], "summary": "Gets list of game categories", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/includeGames"}, {"$ref": "#/parameters/includeGamesAmount"}, {"$ref": "#/parameters/gameCategoryType"}], "responses": {"200": {"description": "List of game categories", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameCategoryShortInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamecategory", "keyentity:gamecategory:create"]}], "tags": ["Game Category"], "summary": "Creates game category", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/GameCategoryCreateData"}}], "responses": {"201": {"description": "Created game group", "schema": {"$ref": "#/definitions/GameCategoryShortInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 210: Game category already exists\n"}}}}, "/entities/{path}/gamecategories": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["gamecategory", "gamecategory:view"]}], "tags": ["Game Category"], "summary": "Gets list of game categories by path", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/includeGames"}, {"$ref": "#/parameters/includeGamesAmount"}, {"$ref": "#/parameters/gameCategoryType"}], "responses": {"200": {"description": "List of game categories", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameCategoryShortInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 304: Game category not found\n- 51: Could not find entity\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["gamecategory", "gamecategory:create"]}], "tags": ["Game Category"], "summary": "Creates game category by path", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/GameCategoryCreateData"}}], "responses": {"201": {"description": "Created game group", "schema": {"$ref": "#/definitions/GameCategoryShortInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}, "409": {"description": "- 210: Game category already exists\n"}}}}, "/gamecategories/{gameCategoryId}/": {"parameters": [{"$ref": "#/parameters/gameCategoryInPath"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamecategory", "keyentity:gamecategory:view"]}], "tags": ["Game Category"], "summary": "Gets game category by public Id", "responses": {"200": {"description": "Game category info with list of games", "schema": {"$ref": "#/definitions/GameCategoryInfo"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 304: Game category not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamecategory", "keyentity:gamecategory:edit"]}], "tags": ["Game Category"], "summary": "Updates game category", "parameters": [{"name": "description", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GameCategoryUpdateData"}}], "responses": {"200": {"description": "Game group information", "schema": {"$ref": "#/definitions/GameCategoryInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 304: Game category not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamecategory", "keyentity:gamecategory:delete"]}], "tags": ["Game Category"], "summary": "Deletes game category", "responses": {"204": {"description": "No body"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 304: Game category not found\n"}}}}, "/entities/{path}/gamecategories/{gameCategoryId}/": {"parameters": [{"$ref": "#/parameters/gameCategoryInPath"}, {"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["gamecategory", "gamecategory:view"]}], "tags": ["Game Category"], "summary": "Gets game category by public Id and path", "responses": {"200": {"description": "Game category info with list of games", "schema": {"$ref": "#/definitions/GameCategoryInfo"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 304: Game category not found\n- 51: Could not find entity\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["gamecategory", "gamecategory:edit"]}], "tags": ["Game Category"], "summary": "Updates game category by path", "parameters": [{"name": "description", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GameCategoryUpdateData"}}], "responses": {"200": {"description": "Game group information", "schema": {"$ref": "#/definitions/GameCategoryInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 304: Game category not found\n- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["gamecategory", "gamecategory:delete"]}], "tags": ["Game Category"], "summary": "Deletes game category by path", "responses": {"204": {"description": "No body"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 304: Game category not found\n- 51: Could not find entity\n"}}}}, "/gamecategories/{gameCategoryId}/move": {"parameters": [{"$ref": "#/parameters/gameCategoryInPath"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamecategory", "keyentity:gamecategory:change-ordering"]}], "tags": ["Game Category"], "summary": "Update game category position", "parameters": [{"name": "description", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GameCategoryUpdateOrderingData"}}], "responses": {"200": {"description": "Game group information", "schema": {"$ref": "#/definitions/GameCategoryInfo"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 304: Game category not found\n"}}}}, "/entities/{path}/gamecategories/{gameCategoryId}/move": {"parameters": [{"$ref": "#/parameters/gameCategoryInPath"}, {"$ref": "#/parameters/path"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["gamecategory", "gamecategory:change-ordering"]}], "tags": ["Game Category"], "summary": "Update game category position by path", "parameters": [{"name": "description", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GameCategoryUpdateOrderingData"}}], "responses": {"200": {"description": "Game group information", "schema": {"$ref": "#/definitions/GameCategoryInfo"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 304: Game category not found\n- 51: Could not find entity\n"}}}}, "/gamecategories/{gameCategoryId}/games": {"parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/gameCategoryInPath"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamecategory", "keyentity:gamecategory:view"]}], "tags": ["Game Category"], "summary": "Gets list of games by game category public id", "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameInfo"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/gamecategories/{gameCategoryId}/games": {"parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/gameCategoryInPath"}, {"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamecategory", "keyentity:gamecategory:view"]}], "tags": ["Game Category"], "summary": "Gets list of games by game category public id and path", "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameInfo"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 304: Game category not found\n- 51: Could not find entity\n"}}}}, "/favoritegames/{playerCode}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:favoritegames"]}], "tags": ["Game"], "summary": "Gets list of favorite games", "parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/gameCodeOnly"}], "responses": {"200": {"description": "List of favorite games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GamesTerminalInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/recentlygames/{playerCode}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:recentlygames"]}], "tags": ["Game"], "summary": "Gets list of recently played games", "parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "List of recently played games", "schema": {"type": "array", "items": {"$ref": "#/definitions/RecentlyGameInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}}