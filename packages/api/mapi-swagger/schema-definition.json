{"/schema-definitions": {"parameters": [], "get": {"security": [{"apiKey": []}, {"Permissions": ["schemadefinition", "schemadefinition:view"]}], "tags": ["New Game Limits System"], "summary": "Get list of all schema definitions", "description": "Get list of all schema definitions", "responses": {"200": {"description": "List of schema definitions", "schema": {"$ref": "#/definitions/SchemaDefinitions"}}, "401": {"description": "Returned in case we have error on the server side:\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["schemadefinition", "schemadefinition:create"]}], "tags": ["New Game Limits System"], "parameters": [{"$ref": "#/parameters/createSchemaDefinition"}], "summary": "Create schema definition", "description": "Create schema definition, allowed only for master entity", "responses": {"201": {"description": "Created schema definitions", "schema": {"$ref": "#/definitions/SchemaDefinition"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "409": {"description": "- 788: Schema definition with name already exists"}}}}, "/schema-definitions/{schemaDefinitionId}": {"parameters": [{"$ref": "#/parameters/schemaDefinitionId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["schemadefinition", "schemadefinition:view"]}], "tags": ["New Game Limits System"], "summary": "Get schema definition", "description": "Get schema definition", "responses": {"200": {"description": "Schema definitions", "schema": {"$ref": "#/definitions/SchemaDefinition"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side:\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["schemadefinition", "schemadefinition:edit"]}], "tags": ["New Game Limits System"], "parameters": [{"$ref": "#/parameters/updateSchemaDefinition"}], "summary": "Edit schema definition", "description": "Edit schema definition, allowed only for master entity and if there's no schema configurations and game configurations", "responses": {"200": {"description": "Edited schema definitions", "schema": {"$ref": "#/definitions/SchemaDefinition"}}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["schemadefinition", "schemadefinition:remove"]}], "tags": ["New Game Limits System"], "summary": "Remove schema definition", "description": "Remove schema definition, allowed only for master entity and if there's no schema configurations and game configurations", "responses": {"204": {"description": "Schema definition was successfully removed"}, "400": {"description": "Returned in case we have error on the server side:\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}}}}}