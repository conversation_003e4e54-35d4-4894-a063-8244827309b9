-- keyPattern = "sw-management-api:entitySettings:*";
-- settingToFind = "rtpDeduction"
local settings = {};

local keyPattern = KEYS[1]
local settingToFind = ARGV[1]

local list = redis.call("KEYS", keyPattern);

for i = 1, #list do
    local key = list[i];
    local value = redis.call("GET", key);
    if string.find(value, settingToFind) then
        table.insert(settings, {key = key, value = value})
    end;
end;

if next(settings) ~= nil then
    return cjson.encode(settings)
end
