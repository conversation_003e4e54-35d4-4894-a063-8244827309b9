local AuthSessionManager = {}

-- Create user session and remove expired sessions
-- params:
--      sessionKey -> authSession:v2:user:${userId}
--      sessionId -> uuid v4
--      ts -> timestamp now
--      expireTime -> mapi env variable in sec (ACCESS_TOKEN_ISSUER_EXPIRES_IN)
function AuthSessionManager.createSession(sessionKey, sessionId, ts, expireTime)
    AuthSessionManager.removeExpiredSessions(sessionKey, ts, expireTime)

    redis.call('ZADD', sessionKey, ts, sessionId)
    redis.call('EXPIRE', sessionKey, expireTime)
end

-- Check user session and remove expired sessions
-- params:
--      sessionKey -> authSession:v2:user:${userId}
--      sessionId -> uuid v4
--      ts -> timestamp now
--      expireTime -> mapi env variable in sec (ACCESS_TOKEN_ISSUER_EXPIRES_IN)
function AuthSessionManager.checkSession(sessionKey, sessionId, ts, expireTime)
    AuthSessionManager.removeExpiredSessions(sessionKey, ts, expireTime)
    
    return redis.call('ZSCORE', sessionKey, sessionId)
end

-- Remove expired sessions
-- params:
--      sessionKey -> authSession:v2:user:${userId}
--      ts -> timestamp now
--      expireTime -> mapi env variable in sec (ACCESS_TOKEN_ISSUER_EXPIRES_IN)
function AuthSessionManager.removeExpiredSessions(sessionKey, ts, expireTime)
    local expireTimeMs = expireTime * 1000
    redis.call('ZREMRANGEBYSCORE', sessionKey, 0, ts - expireTimeMs)
end