local separator = ":"
local defaultKeyValue = "-1"
local globalKey = KEYS[1]
local activeGameLimitsListKey = KEYS[2]
local entityPath = ARGV[1]
local definitionId = ARGV[2]
local gameCode = ARGV[3]
local gameGroupId = ARGV[4]
local segmentId = ARGV[5]
local property = ARGV[6]

local currentPath = separator
local configurations = {}

-- in some cases no need to load all properties from game limits config
local function getMatchValue(key, currentPath)
    local configuration = {}

    if property ~= "" then
        local processing = redis.call('HGET', key, "processing")
        if processing == "true" then
            return {}
        end

        local value = redis.call('HGET', key, property)
        configuration[property] = value
    else
        local match = redis.call('HGETALL', key)
        if match then
            local nextkey
            for i, v in ipairs(match) do
                if i % 2 == 1 then
                    nextkey = v
                else
                    configuration[nextkey] = v
                end
            end

            if configuration.processing == "true" then
                return {}
            end
        end
    end

    configuration.entityPath = currentPath

    return configuration
end

local function getConfigurationMatch(key, currentPath)
    local isActive = redis.call('SISMEMBER', activeGameLimitsListKey, key);
    if isActive == 0 then
        return {}
    end

    local configuration = getMatchValue(key, currentPath)

    if next(configuration) ~= nil then
        table.insert(configurations, configuration)
    else
        redis.call('SREM', activeGameLimitsListKey, key);
    end

    return configuration
end

-- there's no need to find parent configuration and merge them for moorgate (segmentId exists)
if segmentId ~= "" and segmentId ~= nil then
    local key = globalKey .. entityPath .. definitionId .. separator .. gameCode .. separator .. segmentId
    getConfigurationMatch(key, entityPath)
    return cjson.encode(configurations)
end

local function getKey(currentPath, definitionId, gameCode, gameGroupId)
    return globalKey .. currentPath .. definitionId .. separator .. gameCode .. separator .. gameGroupId
end


local function getEntityConfigurationMatches(currentPath, definitionId, gameCode, gameGroupId)
    local match = {}

    -- schema + game + game group
    if gameGroupId ~= "-1" then
        match = getConfigurationMatch(
            getKey(currentPath, definitionId, gameCode, gameGroupId),
            currentPath)
    end

    -- schema + game
    if next(match) == nil then
        match = getConfigurationMatch(
            getKey(currentPath, definitionId, gameCode, defaultKeyValue),
            currentPath)
    end

    -- schema + game group
    if next(match) == nil and gameGroupId ~= "-1" then
        match = getConfigurationMatch(
            getKey(currentPath, definitionId, defaultKeyValue, gameGroupId),
            currentPath)
    end

    -- schema
    if next(match) == nil then
        match = getConfigurationMatch(
            getKey(currentPath, definitionId, defaultKeyValue, defaultKeyValue),
            currentPath)
    end
end


-- iterate over entity path to get all entity parents
for entityName in string.gmatch(entityPath, "([^"..separator.."]+)") do

    getEntityConfigurationMatches(currentPath, definitionId, gameCode, gameGroupId)
    currentPath = currentPath .. entityName .. separator
end


getEntityConfigurationMatches(entityPath, definitionId, gameCode, gameGroupId)


if next(configurations) ~= nil then
    return cjson.encode(configurations)
end