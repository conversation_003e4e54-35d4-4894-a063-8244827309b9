DROP FUNCTION IF EXISTS  fnc_list_game_rtp_history(p_entity_id integer, p_game_codes character varying[], p_ts_from timestamp without time zone, p_ts_till timestamp without time zone, p_limit integer, p_offset integer);
CREATE OR REPLACE FUNCTION fnc_list_game_rtp_history(p_entity_id integer, p_game_codes character varying[] DEFAULT NULL::character varying[], p_ts_from timestamp without time zone DEFAULT NULL::timestamp without time zone, p_ts_till timestamp without time zone DEFAULT NULL::timestamp without time zone, p_limit integer DEFAULT 100, p_offset integer DEFAULT 0, p_deduction_gte numeric DEFAULT NULL)
 RETURNS TABLE(id integer, entity_id integer, game_id integer, rtp_info jsonb, rtp_deduction jsonb, ts timestamp without time zone, game_code character varying, entity_title character varying, game_title character varying, total_rows bigint)
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
   Object Name: fnc_list_game_rtp_history
   Purpose    : List from the table game_rtp_history for given entity_id, list of games and period,
               with merged rtp_deduction from the entity ancestors
   Add records in entity_games base for given entity_id and its childs. Records based on parents entity_games record.
   History    :
      1.0.0
         Date    : Jul 09, 2020
         Authors : Ales
         Notes   : Release (DEVOPS-9481)
      1.0.1 - v.4.47
         Date    : Oct 21, 2020
         Authors : Ales
         Notes   : Added params for Limit, Offset, added 2 columns for titles of entity and game in result (DEVOPS-10766)
      1.0.2 - v.4.50
         Date    : Dec 03, 2020
         Authors : Timur Luchkin
         Notes   : SWS-23026: Change final records order
                   SWS-22952: Return total rows count
      1.0.3 - v.4.56
         Date    : Mar 18, 2021
         Authors : Sergey Malkov
         Notes   : SWS-24306: Ability to see history of theoretical RTP changes for entities without records in game_rtp_history
      1.0.4 - v.4.58
         Date    : Apr 6, 2021
         Authors : Sergey Malkov
         Notes   : 	SWS-24279: Add optional parameter - filter by rtpDeduction value

   Sample run:
      SELECT * FROM fnc_list_game_rtp_history (
                                      p_entity_id => 4837
                                     ,p_game_codes => '{qs_dragonshrine, sw_gs, sw_ksm, sw_jodobi}'::VARCHAR[]
                                     ,p_ts_from => '2020-11-01 15:42'
                                     ,p_ts_till => '2020-12-15 15:42'
                                     ,p_limit => 20
                                     ,p_offset => 0
                                     ,p_deduction_gte => 0.1
                                  );
********************************************************************************************************/
BEGIN

    /* Check mandatory params */
    IF (p_entity_id IS NULL) THEN
        RAISE EXCEPTION 'Parameter "p_entity_id" must be defined!';
    END IF;

    IF coalesce(p_limit, -1) < 0 THEN
      RAISE EXCEPTION 'Parameter "p_limit" must be positive!';
    END IF;

    IF coalesce(p_offset, -1) < 0 THEN
      RAISE EXCEPTION 'Parameter "p_offset" must be positive!';
    END IF;

    RETURN QUERY
        WITH RECURSIVE cte_entity_hier_up AS
    ( -- full hierarchy tree to root direction for given entity_id
        SELECT e.id, e.parent, e.name, e.type, 0 AS root_level, e.title, e.path, e.is_test
        FROM entities e
        WHERE e.id = p_entity_id
        UNION ALL
        SELECT e.id, e.parent, e.name, e.type, h.root_level + 1 AS root_level, e.title, e.path, e.is_test
        FROM cte_entity_hier_up h
        INNER JOIN entities e ON e.id = h.parent
    )
    , cte_game_rtp_hist AS
    ( -- history of the given entity, its ancients
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
            e.root_level, e.path
        FROM game_rtp_history h
        INNER JOIN cte_entity_hier_up e ON e.id = h.entity_id
        WHERE (p_game_codes IS NULL OR h.game_code = ANY(p_game_codes))
            AND (p_ts_till IS NULL OR h.ts <= p_ts_till)
    )
    ,
    p_entity_games AS
    ( -- select all entity games for p_entity_id with p_game_codes filter
        SELECT eg.game_id FROM entity_games eg
        INNER JOIN games g ON g.id = eg.game_id
        WHERE eg.entity_id = p_entity_id AND (p_game_codes IS NULL OR g.code = ANY(p_game_codes))
    )
    ,cte_game_rtp_hist_full AS
    ( -- add history of games which have links to the main entity
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
            root_level
        FROM cte_game_rtp_hist h
        UNION ALL
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
            999 AS root_level
        FROM game_rtp_history h
        WHERE h.entity_id IS NULL
            AND h.game_id IN (SELECT peg.game_id FROM p_entity_games peg)
            AND (p_ts_till IS NULL OR h.ts <= p_ts_till)
    )
    , cte_rtp_deduction_values AS
    ( -- all rtp_deduction key-value pairs */
        SELECT h.id, h.entity_id, h.game_id, h.ts,
            root_level,
            (jsonb_each(h.rtp_deduction))."key" AS rtp_key,
            (jsonb_each(h.rtp_deduction)).value AS rtp_val
        FROM cte_game_rtp_hist h
    )
    , cte_game_rtp_hist_ext AS
    (
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
                v.rtp_key,
                FIRST_VALUE(v.rtp_val) OVER (
                        PARTITION BY h.entity_id, h.game_id, h.ts, v.rtp_key
                        ORDER BY v.root_level, v.ts DESC
                ) AS rtp_val
        FROM cte_game_rtp_hist_full h
        LEFT JOIN cte_rtp_deduction_values v
            ON v.id = h.id OR (v.game_id = h.game_id AND v.ts <= h.ts AND v.root_level < h.root_level)
    )
    , cte_game_rtp_hist_processed AS
    (
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info,
            CASE WHEN max(rtp_key) IS NULL
                THEN '{}'::jsonb
                ELSE jsonb_object_agg(COALESCE (rtp_key, '-'), rtp_val )
            END AS rtp_deduction,
            h.ts, h.game_code
        FROM cte_game_rtp_hist_ext h
        WHERE (p_ts_from IS NULL OR h.ts >= p_ts_from)
        GROUP BY h.id, h.entity_id, h.game_id, h.rtp_info, h.ts, h.game_code
        HAVING p_deduction_gte IS NULL
            OR (jsonb_object_agg(COALESCE (rtp_key, '-'), rtp_val ) ->>'rtpDeduction')::numeric >= p_deduction_gte
    )
    SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
           e.title AS entity_title, g.title AS game_title,
           Cast(Sum(1) OVER () AS BIGINT) as total_rows
    FROM   cte_game_rtp_hist_processed h
           LEFT JOIN entities e   ON e.id = h.entity_id
           LEFT JOIN games g      ON g.id = h.game_id
    ORDER BY h.ts DESC
    LIMIT p_limit OFFSET p_offset;

END $function$
;
