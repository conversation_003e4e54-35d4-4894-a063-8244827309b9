CREATE OR REPLACE FUNCTION fnc_entity_jackpots(p_entity_id INTEGER)
 RETURNS TABLE(game_title character varying, game_code character varying, jp_type character varying,
    jp_id character varying, jp_is_inherited boolean, configured_on character varying, is_global boolean,
    is_owned boolean, is_local boolean, is_used_by_others boolean)
 LANGUAGE plpgsql
AS $function$

DECLARE
BEGIN

     /* Check mandatory params */
    IF p_entity_id IS NULL THEN
        RAISE EXCEPTION 'Parameters must be defined!';
    END IF;

    RETURN QUERY WITH RECURSIVE

        all_brands_hier AS (
                SELECT ent.id, ent.parent, 0 AS deep_level FROM entities ent
                    WHERE ent.id = p_entity_id
                UNION SELECT ent.id, ent.parent, hier.deep_level + 1 as deep_level FROM entities ent
                    JOIN all_brands_hier hier ON hier.parent = ent.id
        ),

        all_hierarchy_games AS (SELECT all_brands_hier.id AS entity_id, deep_level, eg.id AS eg_id, game_id, eg.settings
                FROM entity_games eg JOIN all_brands_hier ON eg.entity_id = all_brands_hier.id),

        -- all brand's games with jackpot
        all_jp_eg AS (SELECT eg_id, entity_id, eg.game_id, settings->'jackpotId' as jackpots, deep_level
            FROM all_hierarchy_games eg
            WHERE settings IS NOT NULL AND settings <> '{}'
                AND settings-> 'jackpotId' <> '{}'
                AND game_id IN (SELECT game_id FROM all_hierarchy_games WHERE deep_level = 0)),

        -- unnested brand's games with jackpots
        brand_jp_eg_plain AS (SELECT eg_id, entity_id, game_id, key::CHARACTER VARYING AS jp_type,
            value::CHARACTER VARYING AS jp_id, deep_level  FROM all_jp_eg, JSONB_EACH_TEXT(all_jp_eg.jackpots)),

        -- game uses jackpot from nearest parent or from brand configuration
        nearest_parent_entity AS (SELECT game_id, brand_jp_eg_plain.jp_type, MIN(deep_level) AS min_deep_level
            FROM brand_jp_eg_plain GROUP by game_id, brand_jp_eg_plain.jp_type),

        -- get effective game configuration with min deep level
        applied_configuration AS (SELECT brand_jp_eg_plain.entity_id AS configured_on_entity_id,
            brand_jp_eg_plain.game_id, brand_jp_eg_plain.jp_type, brand_jp_eg_plain.jp_id
                FROM nearest_parent_entity JOIN brand_jp_eg_plain
                    ON brand_jp_eg_plain.deep_level = nearest_parent_entity.min_deep_level
                        AND brand_jp_eg_plain.jp_type = nearest_parent_entity.jp_type
                        AND brand_jp_eg_plain.game_id = nearest_parent_entity.game_id),

        -- we need to get field is_used_by_others
        all_others_games_with_jackpot AS (SELECT settings->'jackpotId' as jackpots FROM entity_games
            WHERE  entity_id <> p_entity_id AND settings IS NOT NULL AND settings <> '{}' AND settings-> 'jackpotId' <> '{}'),
        -- unique jackpot ids of other games
        others_jackpots AS (SELECT DISTINCT value::CHARACTER VARYING AS jp_id FROM all_others_games_with_jackpot, JSONB_EACH_TEXT(jackpots))

        SELECT games.title, code, applied_configuration.jp_type, applied_configuration.jp_id,
            configured_on_entity_id <> p_entity_id AS jp_is_inherited, path AS configured_on, FALSE,
            FALSE, FALSE, applied_configuration.jp_id IN (SELECT j.jp_id FROM others_jackpots j)
        FROM applied_configuration
           JOIN games ON applied_configuration.game_id = games.id
           JOIN entities ON entities.id = configured_on_entity_id
        ORDER BY configured_on DESC, code, jp_id;
END $function$
;
