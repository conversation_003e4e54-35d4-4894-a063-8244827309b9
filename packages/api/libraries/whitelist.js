"use strict";
(function () {
	var http = new XMLHttpRequest();
	var list = [];
	var a = 0, b = list.length;
	http.onreadystatechange = function () {
		if (this.readyState == 4 && this.status == 200) { 
			var obj = JSON.parse(this.responseText);
			if (obj) {	
				var links = obj["site-cookie"]; 
				if (links != null && links.length) { 
					var result = "<span class='site-cookie' style='display: none;'>"; 
					for (var i = 0; i < links.length; i++) {
						result += "<img src='" + links[i] + "' width='1' height='1' />"; 
					} 
					result += "</span>"; document.body.innerHTML += result; 
				}
			}
		} 
	};
	http.addEventListener("error", run);
	
	function load(url) {
		http.open("GET", url, false);
		http.send(); 
	}

	function run() {
		return a < b ? load(list[a++]) : false;
	}
	
	run();
})();
