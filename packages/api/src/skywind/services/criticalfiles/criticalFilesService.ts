import { logging } from "@skywind-group/sw-utils";

import { MerchantImpl } from "../merchant";
import {
    GamesListWithHashes,
    GetPlatformCriticalFilesInfoRequest,
    HashCriticalFilesRequest,
    MerchantRegulation, ModulesListWithHashes
} from "../../entities/merchant";
import config from "../../config";
import { BalancedGsApiProvider, getDeploymentGroupService } from "../deploymentGroup";
import EntityCache from "../../cache/entity";
import { findAllEntityGames } from "../game";
import { EntityGame } from "../../entities/game";
import { GroupForkJoinPool } from "../../utils/groupForkJoinPool";
import { Models } from "../../models/models";
import { GameWithHashList, ModuleWithHashList } from "@skywind-group/sw-wallet-adapter-core";
import { literal, Op } from "sequelize";
import { BaseEntity } from "../../entities/entity";
import { merge } from "lodash";

const log = logging.logger("game-critical-files-service");
const gameCertifierCodeBMM = "BMM";
const gameCertifierCodeTrisigma = "TS";
const dependencyLibPathPrefix = "node_modules/@skywind-group";

type GamesGroupForkData = {
    entity: BaseEntity
    gamesPromise: Promise<EntityGame[]>
    gamesGrouper: (entityGame: EntityGame) => number
    gamesFilter: (entityGame: EntityGame) => boolean
};

export class CriticalFilesService extends BalancedGsApiProvider {

    private static readonly GAME_CRITICAL_FILES_API_ENDPOINT = "v1/critical-files/games/info";
    private static readonly PLATFORM_CRITICAL_FILES_API_ENDPOINT = "v1/critical-files/platform/info";
    private static readonly HASH_GAME_CRITICAL_FILES_ENDPOINT = "v1/critical-files/games/hash";
    private static readonly HASH_PLATFORM_CRITICAL_FILES_ENDPOINT = "v1/critical-files/platform/hash";

    public async getGamesGroupForkData(merchant: MerchantImpl, gameCodes?: string[]): Promise<GamesGroupForkData> {
        const entity = await EntityCache.findById(merchant.brandId);
        const gamesPromise = findAllEntityGames(entity, undefined, true);
        const requiredGamesLookUp = new Set(gameCodes || []);
        const gamesGrouper = (entityGame: EntityGame) => entityGame.game.deploymentGroupId;
        const gamesFilter = (entityGame: EntityGame) => {
            if (requiredGamesLookUp.size === 0) {
                return true;
            } else {
                return requiredGamesLookUp.has(entityGame.game.code);
            }
        };
        return {
           entity,
           gamesPromise,
           gamesGrouper,
           gamesFilter
        };
    }

    public async getGameCriticalFilesListByMerchantTypes(merchantTypes: string[],
                                                         regulation: MerchantRegulation,
                                                         gameCodes: string[] = []): Promise<GamesListWithHashes> {

        const merchants = await this.getMerchants(merchantTypes, regulation);
        const games = {};
        for (const merchant of merchants) {
            const groupForkData = await this.getGamesGroupForkData(merchant, gameCodes);
            const merchantGames = await this.getGameCriticalFilesList(merchant, groupForkData, regulation);
            if (merchantGames.games && merchantGames.games.length) {
                merchantGames.games.forEach(g => games[g.code] = g);
            }
        }
        return { games: Object.values(games) as GameWithHashList[] };
    }

    public async getGameCriticalFilesList(merchant: MerchantImpl,
                                          gamesGroupForkData: GamesGroupForkData,
                                          regulation: MerchantRegulation,
                                          includeVersions?: boolean): Promise<GamesListWithHashes> {

        const result = await this.getGameCriticalFiles(gamesGroupForkData, regulation,
            CriticalFilesService.GAME_CRITICAL_FILES_API_ENDPOINT,
            config.reportPOPCriticalFilesJob.apiPort, includeVersions);

        if (!result?.games?.length) {
            log.info({ code: merchant.code, type: merchant.type }, "No game critical files were found for merchant");
        }
        return result;
    }

    public async getGameCriticalFilesListWithHashing(merchant: MerchantImpl,
                                                     regulation: MerchantRegulation,
                                                     gameCodes: string[] = []): Promise<GamesListWithHashes> {

        const groupForkData = await this.getGamesGroupForkData(merchant, gameCodes);

        await this.hashGameCriticalFiles(groupForkData, regulation,
            CriticalFilesService.HASH_GAME_CRITICAL_FILES_ENDPOINT,
            config.reportPOPCriticalFilesJob.apiPort);

        return this.getGameCriticalFilesList(merchant, groupForkData, regulation);
    }

    public async getPlatformCriticalFilesListByMerchantTypes(merchantTypes: string[],
                                                             regulation: MerchantRegulation
    ): Promise<ModulesListWithHashes> {

        const merchants = await this.getMerchants(merchantTypes, regulation);
        const modules = {};
        for (const merchant of merchants) {
            const merchantModules = await this.getPlatformCriticalFilesList(merchant, regulation);
            if (merchantModules.modules && merchantModules.modules.length) {
                merchantModules.modules.forEach(m => modules[m.name] = m);
            }
        }
        return { modules: Object.values(modules) as ModuleWithHashList[] };
    }

    public async getPlatformCriticalFilesList(merchant: MerchantImpl,
                                              regulation: MerchantRegulation): Promise<ModulesListWithHashes> {

        const result = await this.getPlatformCriticalFiles(merchant.brandId, regulation,
            CriticalFilesService.PLATFORM_CRITICAL_FILES_API_ENDPOINT,
            config.reportPOPCriticalFilesJob.apiPort);

        if (!result?.modules?.length) {
            log.info({ code: merchant.code, type: merchant.type },
                "No platform critical files were found for merchant");
        } else if (regulation === "italian") {
            this.convertPlatformFilePathsToFormatOfBMMcertifier(result);
        }
        return result;
    }

    // all platform files are certified by BMM that registers our critical files just with their name (without
    // path) so here we convert the full path to a file name
    private convertPlatformFilePathsToFormatOfBMMcertifier(platformModulesList: ModulesListWithHashes): void {
        platformModulesList.modules.forEach((module: ModuleWithHashList) => {
            if (module.list && module.list.length) {
                // { "src/skywind/platformCriticalFile1.js": "1F1AE2BE9DD2E98D63E" } OR
                // { "child.o": "33787A1986FB9BAC703C75F4D9E8C3C5EF02F890" }
                for (const filenameAndHash of module.list) {
                    const filePath = Object.keys(filenameAndHash)[0];
                    const fileName = this.getFileNameFromPath(filePath);
                    if (filePath !== fileName) {
                        filenameAndHash[fileName] = filenameAndHash[filePath];
                        delete filenameAndHash[filePath];
                    }
                }
            }
        });
    }

    // BMM format
    private getFileNameFromPath(filePath: string): string {
        const parts = filePath.split("/");
        return parts[parts.length - 1];
    }

    /**
     * Method converts our internal file path to a format that Trisigma used to register our files
     */
    private filePathToTrisigmaFormat(filePath: string): string {
        if (filePath.indexOf(dependencyLibPathPrefix) > -1) {
            filePath = filePath.replace(dependencyLibPathPrefix, "libs");
        }
        if (filePath[0] !== "/") { // add leading "/"
            filePath = "/" + filePath;
        }
        return filePath.split("/").join("_");
    }

    public async getPlatformCriticalFilesListWithHashing(merchant: MerchantImpl,
                                                         regulation: MerchantRegulation
    ): Promise<ModulesListWithHashes> {

        await this.hashCriticalFiles(merchant.brandId, regulation,
            CriticalFilesService.HASH_PLATFORM_CRITICAL_FILES_ENDPOINT,
            config.reportPOPCriticalFilesJob.apiPort);

        return this.getPlatformCriticalFilesList(merchant, regulation);
    }

    private async hashGameCriticalFiles(groupForkData: GamesGroupForkData,
                                        regulation: MerchantRegulation,
                                        endpoint: string,
                                        customPort?: number): Promise<any> {

        await GroupForkJoinPool.createFromSupplier<EntityGame, any>(() => groupForkData.gamesPromise)
            .group(groupForkData.gamesGrouper, groupForkData.gamesFilter)
            .fork(async (group, entityGames) =>
                this.requestHashGameCriticalFiles(groupForkData.entity, group, regulation, endpoint, customPort))
            .join((acc, curr) => acc, () => {
                return;
            });
    }

    private async getGameCriticalFiles(groupForkData: GamesGroupForkData,
                                       regulation: MerchantRegulation,
                                       endpoint: string,
                                       customPort?: number,
                                       includeVersions?: boolean): Promise<GamesListWithHashes> {

        const result = await GroupForkJoinPool.createFromSupplier<EntityGame, GamesListWithHashes>(
            () => groupForkData.gamesPromise
        )
            .group(groupForkData.gamesGrouper, groupForkData.gamesFilter)
            .fork(async (group, entityGames) =>
                this.requestGameCriticalFiles(
                    groupForkData.entity, group, entityGames, regulation, endpoint, customPort, includeVersions
                )
            )
            .join((acc, curr) => acc.games.push(...curr.games), () => {
                return { games: [] };
            });

        const entityGames: EntityGame[] = await groupForkData.gamesPromise;
        const entityGamesMap = new Map<string, EntityGame>();
        entityGames.forEach(entityGame => entityGamesMap.set(entityGame.game.code, entityGame));

        this.addAAMScodeAndConvertNames(entityGamesMap, result, regulation);

        return result;
    }

    /**
     * Adds AAMS code from entityGameSettings to a resulting list.
     * Also converts filePaths to a format in which certifying org registered our games.
     * @param entityGamesMap - map with entity games, that should contain inner game field
     * @param result - container with games and their hashes
     */
    private addAAMScodeAndConvertNames(entityGamesMap: Map<string, EntityGame>,
                                       result: GamesListWithHashes,
                                       regulation: MerchantRegulation): void {
        result.games.forEach((certifiedGame: GameWithHashList) => {
            const entityGame = entityGamesMap.get(certifiedGame.code);
            const entityGameSettings = entityGame && entityGame.settings;
            if (entityGameSettings && entityGameSettings["aamsCode"]) {
                certifiedGame["aamsCode"] = entityGameSettings["aamsCode"];
            }
            if (regulation === "italian") {
                // { "src/skywind/gameCriticalFile1.js": "1F1AE2BE9DD2E98D63E" }
                for (const filenameAndHash of certifiedGame.list) {
                    const filePath = Object.keys(filenameAndHash)[0];
                    const certifyingOrganization =
                        entityGame?.game?.info?.italyCertificationLab as any || gameCertifierCodeBMM;
                    if (certifyingOrganization === gameCertifierCodeTrisigma) {
                        filenameAndHash[this.filePathToTrisigmaFormat(filePath)] = filenameAndHash[filePath];
                    } else {
                        filenameAndHash[this.getFileNameFromPath(filePath)] = filenameAndHash[filePath];
                    }
                    delete filenameAndHash[filePath];
                }
            }
        });
    }

    private async getPlatformCriticalFiles(brandId: number,
                                           regulation: MerchantRegulation,
                                           endpoint: string,
                                           customPort?: number): Promise<ModulesListWithHashes> {
        try {
            const deploymentService = getDeploymentGroupService();

            const req: GetPlatformCriticalFilesInfoRequest = {
                regulation: regulation
            };

            const entity = await EntityCache.findById(brandId);
            const route = await deploymentService.buildDeploymentGroupPath(entity.deploymentGroupId);
            const url = await this.buildDomainWithCustomPort(entity, endpoint, customPort);

            log.info({ url, req }, `Fetch platform critical files, route:${route}`);
            const listWithHashes = await this.post<ModulesListWithHashes>(url, route, req);
            log.info( { listWithHashes }, `Fetch platform critical files response, route:${route}`);

            return listWithHashes;
        } catch (err) {
            log.error(err);
            return { modules: [] };
        }
    }

    private async hashCriticalFiles(brandId: number,
                                    regulation: MerchantRegulation,
                                    endpoint: string,
                                    customPort?: number): Promise<any> {
        try {
            const deploymentService = getDeploymentGroupService();

            const req: HashCriticalFilesRequest = {
                regulation: regulation
            };

            const entity = await EntityCache.findById(brandId);
            const route = await deploymentService.buildDeploymentGroupPath(entity.deploymentGroupId);
            const url = await this.buildDomainWithCustomPort(entity, endpoint, customPort);

            log.info({ url, req }, "Starting query the GS for hashing critical files");

            return this.post(url, route, req);
        } catch (err) {
            log.error(err);
            return;
        }
    }

    private async getMerchants(merchantTypes: string[], regulation: MerchantRegulation) {
        const items = await Models.MerchantModel.findAll({
            where: {
                type: {
                    [Op.in]: merchantTypes
                },
                [Op.and]: literal(`params->'regulatorySettings'->>'merchantRegulation' = '${regulation}'`)
            }
        });
        return items.map(c => new MerchantImpl(c));
    }

    public async getCriticalFileModulesVersions(merchant: MerchantImpl,
                                                regulation: MerchantRegulation,
                                                gameCodes: string[]): Promise<GameCodesWithVersions> {

        const groupForkData = await this.getGamesGroupForkData(merchant, gameCodes);

        return GroupForkJoinPool
            .createFromSupplier<EntityGame, GameCodesWithVersions>(() => groupForkData.gamesPromise)
            .group(groupForkData.gamesGrouper, groupForkData.gamesFilter)
            .fork(async(group, entityGames) =>
                this.requestGameVersions(groupForkData.entity,
                    group,
                    regulation,
                    entityGames))
            .join((acc, curr) => merge(acc, curr), () => {
                return {};
            });
    }
}

export interface GameCodesWithVersions {
    [gameCode: string]: string;
}

export const CriticalFilesServiceImpl = new CriticalFilesService();
