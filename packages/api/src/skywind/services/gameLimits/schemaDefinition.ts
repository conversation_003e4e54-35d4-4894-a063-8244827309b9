import { CRUDServiceImpl } from "../crudService";
import { SchemaDefinitionInstance, SchemaDefinitionModel } from "../../models/schemaDefinition";
import { GameLimits } from "../../models/gameLimitsConfiguration";
import { lazy } from "@skywind-group/sw-utils";
import * as Errors from "../../errors";
import { ValidationError } from "../../errors";
import { UniqueConstraintError } from "sequelize";
import { Cache } from "../../cache/cache";
import { getLimitsCalculator } from "./limitsCalculator";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { Models } from "../../models/models";
import * as Ajv from "ajv";
import {
    Definition,
    GameLimitsPermissionType,
    LIMIT_CONFIGURATION_TYPE,
    PropertiesSchema,
    PropertySchema,
    Schema,
    SchemaDefinition,
    SchemaPermissions
} from "../../entities/schemaDefinition";

const schemaDefinitionService = lazy(() => new SchemaDefinitionService());

const GameLimitsConfigurationModel = Models.GameLimitsConfigurationModel;
const SchemaConfigurationModel = Models.SchemaConfigurationModel;
const SchemaDefinitionModel = Models.SchemaDefinitionModel;

export const getSchemaDefinitionService = (): SchemaDefinitionService => {
    return schemaDefinitionService.get();
};

export function isPropertyValueRequired(property: PropertySchema) {
    if (property.$ref) {
        return false;
    }
    const { type, limitConfigurationType } = property;
    return type !== "object" &&
        [LIMIT_CONFIGURATION_TYPE.CONFIGURABLE, LIMIT_CONFIGURATION_TYPE.CONST, LIMIT_CONFIGURATION_TYPE.FIXED]
            .includes(limitConfigurationType);
}

export class SchemaDefinitionImpl implements SchemaDefinition {
    public id: number;
    public name: string;
    public schema: Schema;
    public definitions: Definition;
    public permissions: SchemaPermissions;
    public createdAt: Date;
    public updatedAt: Date;

    public get betTypes(): string[] {
        if (!this.levelsSupported()) {
            return;
        }
        const bets = this.schema.properties?.bets?.properties;
        if (!bets) {
            return;
        }
        return Object.keys(bets);
    }

    public levelsSupported(): boolean {
        return !!this.schema.levels;
    }

    public getBetTypeSchema(betType: string): Schema {
        if (!this.levelsSupported()) {
            return this.schema;
        }

        const value = this.schema.properties?.bets?.properties?.[betType];

        if (value["$ref"]) {
            return this.definitions[this.getNameFromSchema(value["$ref"])];
        }

        return value as Schema;
    }

    public getAJVSchemaId(name: string): string {
        return "/" + name.replace(/\s/, "");
    }

    public getNameFromSchema(schemaId: string) {
        return schemaId.substring(1);
    }

    public getBetPropPermission(currency: string, betProp: string): GameLimitsPermissionType {
        const key = `bets.${betProp}`;
        return this.permissions?.[currency]?.[key] as GameLimitsPermissionType;
    }

    public getBetPropFromPermission(currency: string, gameLimitProperty: string): string {
        const [, betProp] = gameLimitProperty.split(".");

        return betProp;
    }
}

class SchemaDefinitionService extends CRUDServiceImpl
    <SchemaDefinitionInstance, SchemaDefinition, SchemaDefinitionModel> {

    private cache: Cache<number, any> = new Cache("schema-definitions", super.retrieve.bind(this));
    protected modelName = "Schema Definition";

    constructor(private model = SchemaDefinitionModel,
                private schemaValidation = new SchemaValidation()) {
        super();
    }

    public getModel(): SchemaDefinitionModel {
        return this.model;
    }

    public async create(data: SchemaDefinition): Promise<SchemaDefinitionInstance> {
        try {
            data.permissions = this.getSchemaPermissions(data.schema, data.permissions);

            return await super.create(data);
        } catch (err) {
            if (err instanceof UniqueConstraintError) {
                return Promise.reject(new Errors.SchemaDefinitionAlreadyExists(data.name));
            }
            return Promise.reject(err);
        }

    }

    public async update(id: number, data: SchemaDefinition): Promise<SchemaDefinitionInstance> {

        await this.checkDependencies(id);

        const dataToUpdate: SchemaDefinition = {};
        const schemaDefinition = (await this.getInstance(id)).toInfo();
        if (data.schema) {
            dataToUpdate.schema = data.schema;
        }
        if (data.definitions) {
            dataToUpdate.definitions = data.definitions;
        }

        const schema = dataToUpdate.schema || schemaDefinition.schema;
        if (data.permissions) {
            this.validatePermissions({ ...data, schema });
        }
        dataToUpdate.permissions = this.getSchemaPermissions(
            schema,
            data.permissions,
            schemaDefinition.permissions
        );

        const result = await super.update(id, dataToUpdate);
        this.cache.reset(id);
        return result;
    }

    public async destroy(id: number): Promise<void> {
        await this.checkDependencies(id);
        await super.destroy(id);
        this.cache.reset(id);
    }

    public async retrieve(id: number): Promise<SchemaDefinitionInstance> {
        return this.cache.find(id);
    }

    public resetCache() {
        this.cache.reset();
    }

    private async checkDependencies(schemaDefinitionId: number): Promise<void> {
        const schemaConfigurations = await SchemaConfigurationModel.findAll({
            where: { schemaDefinitionId: schemaDefinitionId }
        });
        if (schemaConfigurations && schemaConfigurations.length > 0) {
            throw new ValidationError("Schema definition linked with schema configuration(s)");
        }
        const gameLimitsConfigurations = await GameLimitsConfigurationModel.findAll({
            where: { schemaDefinitionId: schemaDefinitionId }
        });
        if (gameLimitsConfigurations && gameLimitsConfigurations.length > 0) {
            throw new ValidationError("Schema definition linked with game limits configuration(s)");
        }
    }

    protected validateCreateData(data: SchemaDefinition): SchemaDefinition {
        if (!data.name) {
            throw new ValidationError("Name is required");
        }

        this.validateSchemaUpdateData(data);
        this.validatePermissions(data);
        this.validateRequiredFields(data.schema);
        this.validateBets(data);
        data.schema.required = this.getRequired(data.schema);

        return data;
    }

    protected validateUpdateData(data: SchemaDefinition): SchemaDefinition {
        if (data.schema) {
            this.validateSchemaUpdateData(data);
            this.validateRequiredFields(data.schema);
            this.validateBets(data);

            data.schema.required = this.getRequired(data.schema);
        }

        return data;
    }

    private validatePermissionsCurrencies(permissions: SchemaPermissions) {
        const invalidCurrencies = Object.keys(permissions).filter(c => !Currencies.exists(c) && c !== "default");

        if (invalidCurrencies && invalidCurrencies.length) {
            throw new ValidationError(`Invalid currency value ${invalidCurrencies}`);
        }
    }

    private validatePermissions(data: SchemaDefinition): SchemaPermissions {
        const schemaImpl = Object.assign(new SchemaDefinitionImpl(), data);

        if (schemaImpl.permissions && typeof schemaImpl.permissions !== "object") {
            throw new ValidationError("SchemaPermissions must be an object");
        }

        this.validatePermissionsCurrencies(schemaImpl.permissions);

        if (schemaImpl.permissions) {
            for (const currency in schemaImpl.permissions) {
                if (!schemaImpl.permissions.hasOwnProperty(currency)) {
                    continue;
                }

                for (const gameLimitProperty in schemaImpl.permissions[currency]) {
                    if (!schemaImpl.permissions[currency].hasOwnProperty(gameLimitProperty)) {
                        continue;
                    }

                    // common permission is used for the same property in different bet types
                    if (gameLimitProperty.startsWith("bets.")) {
                        const betProp = schemaImpl.getBetPropFromPermission(currency, gameLimitProperty);
                        const betTypes = schemaImpl.betTypes;
                        if (!betTypes) {
                            throw new ValidationError(`Invalid  permission ${gameLimitProperty}. Bet types are missed`);
                        }
                        for (const betType of schemaImpl.betTypes) {
                            const betTypeSchema = schemaImpl.getBetTypeSchema(betType);

                            this.validatePermissionPropertySchema(
                                betTypeSchema?.properties?.[betProp],
                                gameLimitProperty,
                                schemaImpl.permissions?.[currency]?.[gameLimitProperty],
                                currency);
                        }

                    } else {
                        this.validatePermissionPropertySchema(
                            schemaImpl.schema?.properties?.[gameLimitProperty],
                            gameLimitProperty,
                            schemaImpl.permissions?.[currency]?.[gameLimitProperty],
                            currency);
                    }

                }

            }
        }
        return schemaImpl.permissions;
    }

    private validatePermissionPropertySchema(propertySchema: PropertySchema,
                                             gameLimitProperty: string,
                                             permission: string,
                                             currency: string) {

        if (!propertySchema) {
            throw new ValidationError(`${gameLimitProperty} - unknown property. ` +
                "Please add property to schema definition");
        }

        if (propertySchema.type !== "array") { // for stakeAll and coins permission cannot be specified
            if (propertySchema.type !== "object" && !isPropertyValueRequired(propertySchema)) {
                throw new ValidationError(`Property ${gameLimitProperty} is not configurable`);
            }

            if (!this.validatePermissionValue(permission)) {
                throw new ValidationError(`${currency}.${gameLimitProperty} invalid value`);
            }
        }
    }

    private validatePermissionValue(value: string) {
        return Object.values(GameLimitsPermissionType).includes(value as any);
    }

    private getRequired(schema: Schema): string[] {
        if (!schema.required || !schema.required.length) {
            return Object.keys(schema.properties)
                .filter(p =>
                    schema.properties[p].limitConfigurationType !== LIMIT_CONFIGURATION_TYPE.CALCULATED);
        }

        return schema.required;
    }

    private validateRequiredFields(schema: Schema) {
        if (!schema.required) {
            return;
        }

        if (!Array.isArray(schema.required)) {
            throw new ValidationError("schema.required is invalid value");
        }
        for (const property of schema.required) {
            if (!schema.properties[property]) {
                throw new ValidationError(`${property} can't be required, it's missed in schema`);
            }
            if (schema.properties[property].limitConfigurationType === LIMIT_CONFIGURATION_TYPE.CALCULATED) {
                throw new ValidationError("Calculated field can't be required");
            }
        }
    }

    private validateBets(data: SchemaDefinition) {
        const schemaImpl = Object.assign(new SchemaDefinitionImpl(), data);
        if (!schemaImpl.levelsSupported()) {
            return;
        }

        if (schemaImpl.schema.properties.bets) {
            if (schemaImpl.schema.properties.bets.type !== "object") {
                throw new ValidationError("bets must be an object");
            }

            if (!schemaImpl.betTypes || !schemaImpl.betTypes.length) {
                throw new ValidationError("bets object must include bet types");
            }

            for (const betType of schemaImpl.betTypes) {
                const betSchema = schemaImpl.getBetTypeSchema(betType);
                if (!betSchema) {
                    throw new ValidationError(`bets.${betType} schema is missed`);
                }

                if (!betSchema.properties.payout) {
                    throw new ValidationError(`bets.${betType}.payout is missed`);
                }
            }
        }
    }

    private validateSchemaUpdateData(data: SchemaDefinition): SchemaDefinition {
        const errors = [];
        if (typeof data.schema !== "object" || data.schema.type !== "object") {
            errors.push("Schema type must be an object");
        }
        if (!data.schema.properties ||
            (typeof data.schema.properties !== "object" || Array.isArray(data.schema.properties))) {
            errors.push("Invalid schema value");
        }

        if ("definitions" in data && typeof data.definitions !== "object") {
            errors.push("Definitions is invalid value");
        }

        this.schemaValidation.validateJSONSchema(data);
        this.validateStakeAllFields(data);
        const invalidCalculatedFields = this.validateCalculatedFields(data);
        if (invalidCalculatedFields.length) {
            errors.push(`Calculation of ${invalidCalculatedFields} is not supported`);
        }

        const invalidFields = this.validatePropertyFieldType(data.schema.properties);

        if (invalidFields.length) {
            errors.push(`Properties "${invalidFields}" must have limitConfigurationType`);
        }

        if (errors.length) {
            throw new Errors.ValidationError(errors);
        }

        return data;
    }

    private validatePropertyFieldType(properties: PropertiesSchema) {
        const invalidProperties = [];
        for (const field in properties) {
            if (properties.hasOwnProperty(field)) {
                const property = properties?.[field];
                if (property.type === "object") {
                    const invalidNestedProperties = this.validatePropertyFieldType(property.properties);
                    invalidProperties.push(...invalidNestedProperties);
                } else if (!Object.values(LIMIT_CONFIGURATION_TYPE).includes(property.limitConfigurationType)
                    && property.type) {
                    invalidProperties.push(field);
                }
            }
        }
        return invalidProperties;
    }

    private getSchemaPermissions(schema: Schema,
                                 newPermissions: SchemaPermissions = { default: {} },
                                 currentPermissions?: SchemaPermissions): SchemaPermissions {
        if (!newPermissions.default) {
            newPermissions.default = {};
        }

        for (const currency in newPermissions) {
            if (!newPermissions.hasOwnProperty(currency)) {
                continue;
            }

            for (const schemaDefinitionKey in schema.properties) {
                if (!schema.properties.hasOwnProperty(schemaDefinitionKey) ||
                    // tslint:disable-next-line:max-line-length
                    schema.properties[schemaDefinitionKey].limitConfigurationType === LIMIT_CONFIGURATION_TYPE.CALCULATED) {
                    continue;
                }

                let permission = newPermissions?.[currency]?.[schemaDefinitionKey] ||
                    currentPermissions?.[currency]?.[schemaDefinitionKey] ||
                    GameLimitsPermissionType.ADMIN;

                if (schema.properties[schemaDefinitionKey].type === "array") {
                    permission = this.getDefaultPermissionForStakeAll(schema, schemaDefinitionKey);
                }

                newPermissions[currency] = {
                    ...newPermissions[currency],
                    [schemaDefinitionKey]: permission
                };
            }
        }

        return newPermissions;
    }

    private getDefaultPermissionForStakeAll(schema: Schema, propertyName: string) {
        const isFixed = schema.properties[propertyName].limitConfigurationType === LIMIT_CONFIGURATION_TYPE.FIXED;
        return isFixed ? GameLimitsPermissionType.ADMIN : GameLimitsPermissionType.ENTITY;
    }

    private validateStakeAllFields(data: SchemaDefinition): void {
        if (data.schema) {
            this.checkStakeAllField(data.schema);
        }
        if (data.definitions) {
            for (const prop in data.definitions) {
                if (data.definitions.hasOwnProperty(prop)) {
                    this.checkStakeAllField(data.definitions[prop]);
                }
            }
        }
    }

    private checkStakeAllField(schema: Schema): void {
        const stakeAllDef: PropertySchema = schema.properties?.stakeAll;
        if (stakeAllDef) {
            if (stakeAllDef.type !== "array" || stakeAllDef?.items?.type !== "number") {
                throw new ValidationError("StakeAll is special field and can be only array of numbers");
            }
        }
    }

    private validateCalculatedFields(data: SchemaDefinition): string[] {
        const result: string[] = [];
        const schemaImpl = Object.assign(new SchemaDefinitionImpl(), data);
        const calculator = getLimitsCalculator(schemaImpl.levelsSupported());

        if (data.schema) {
            for (const prop in data.schema.properties) {
                if (data.schema.properties.hasOwnProperty(prop)) {
                    const description: PropertySchema = data.schema.properties[prop];

                    if (description.limitConfigurationType === LIMIT_CONFIGURATION_TYPE.CALCULATED
                        && !calculator.supportedFields().includes(prop)) {
                        result.push(prop);
                    }
                }
            }
        }
        if (data.definitions) {
            for (const prop in data.definitions) {
                if (data.definitions.hasOwnProperty(prop)) {
                    const schema: Schema = data.definitions[prop];
                    for (const nestedProp in schema.properties) {
                        if (schema.properties.hasOwnProperty(nestedProp)) {
                            const description: PropertySchema = schema.properties[nestedProp];
                            if (description.limitConfigurationType === LIMIT_CONFIGURATION_TYPE.CALCULATED
                                && !calculator.supportedFields().includes(nestedProp)) {
                                result.push(`${prop}.${nestedProp}`);
                            }
                        }
                    }
                }
            }
        }
        return result;
    }
}

export class SchemaValidation {
    public parseAJVErrors(errors: any[]) {
        const errorMessages: string[] = errors
            .map(({ dataPath, message = "- invalid value" }) => {
                return dataPath ? dataPath + " " + message : message;
            })
            .filter((message, i, messages) =>
                !messages.includes(message, i + 1)
            );

        if (errorMessages && errorMessages.length) {
            throw new Errors.ValidationError(errorMessages);
        }
    }

    public validateByDefinition(schemaDefinition: SchemaDefinitionInstance,
                                gameLimits: GameLimits,
                                isDefaultLimits: boolean,
                                isMaster: boolean = false) {
        const ajv = new Ajv();
        const { schema, definitions = {}, name } = schemaDefinition;

        const schemaKey = schemaDefinition.getAJVSchemaId(name);
        const ajvSchema = { ...schema };
        if (!isDefaultLimits) {
            ajvSchema.required = [];
        }
        const validate = ajv.addSchema(ajvSchema, schemaKey);
        for (const definitionKey in definitions) {
            if (!definitions.hasOwnProperty(definitionKey)) {
                continue;
            }

            const propSchema: Schema = { ...definitions[definitionKey] };
            if (propSchema.required && !isMaster) {
                propSchema.required = propSchema.required.filter(field => field !== "payout");
            }

            ajv.addSchema(propSchema, schemaDefinition.getAJVSchemaId(definitionKey));
        }

        const isValid = validate.validate(schemaKey, gameLimits);
        if (!isValid && ajv.errors) {
            this.parseAJVErrors(ajv.errors);
        }
    }

    public validateJSONSchema(data: SchemaDefinition) {
        const ajv = new Ajv();

        if (data.definitions) {
            for (const definitionKey in data.definitions) {
                if (!data.definitions.hasOwnProperty(definitionKey)) {
                    continue;
                }
                const definition = this.getAJVSchema("/" + definitionKey, data.definitions[definitionKey]);

                const isValidDefinition = ajv.validateSchema(definition);
                if (!isValidDefinition && ajv.errors) {
                    this.parseAJVErrors(ajv.errors);
                }
            }
        }

        const isValid = ajv.validateSchema(data.schema);
        if (!isValid && ajv.errors) {
            this.parseAJVErrors(ajv.errors);
        }
    }

    public getAJVSchema(id: string, schema: Schema): Schema {
        return {
            "$id": id,
            ...schema
        };
    }
}

export class SchemaDefinitionHelper {
    constructor(private schemaDefinition: SchemaDefinition,
                public currency?: string) {
    }

    public getConfigurableFields(currency?: string): string[] {
        const configurableFields = [];

        for (const property in this.schemaDefinition.schema.properties) {
            if (!this.schemaDefinition.schema.properties.hasOwnProperty(property)) {
                continue;
            }

            if (this.isPropertyConfiguredByEntity(property, currency)) {
                configurableFields.push(property);
            }
        }

        return configurableFields;
    }

    public isSchemaConfigurable(currency?: string): boolean {
        let configurable = false;

        for (const property in this.schemaDefinition.schema.properties) {
            if (!this.schemaDefinition.schema.properties.hasOwnProperty(property)) {
                continue;
            }

            if (this.isPropertyConfiguredByEntity(property)) {
                configurable = true;
                break;
            }
        }

        return configurable;
    }

    public isPropertyConfiguredByEntity(property: string, currency: string = "default") {
        const value = this.schemaDefinition.schema.properties[property];
        const schemaPermissions = this.schemaDefinition?.permissions?.[currency] ||
            this.schemaDefinition?.permissions?.default;

        return value.limitConfigurationType === LIMIT_CONFIGURATION_TYPE.CONFIGURABLE &&
            schemaPermissions[property] === GameLimitsPermissionType.ENTITY;
    }
}
