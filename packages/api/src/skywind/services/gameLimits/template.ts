import {
    LimitTemplate,
    LimitTemplateDB,
    LimitTemplateModel,
    LimitTemplateData,
} from "../../models/limitTemplate";
import { UniqueConstraintError } from "sequelize";
import { lazy } from "@skywind-group/sw-utils";
import * as Errors from "../../errors";
import { ValidationError } from "../../errors";
import { VARCHAR_DEFAULT_LENGTH } from "../../utils/common";
import { CRUDServiceImpl } from "../crudService";
import { Models } from "../../models/models";

const LimitTemplateDBModel = Models.LimitTemplateDBModel;
class LimitTemplateService extends CRUDServiceImpl<LimitTemplateDB, LimitTemplate, LimitTemplateModel> {

    public getModel(): LimitTemplateModel {
        return LimitTemplateDBModel;
    }

    public async create(data: LimitTemplate): Promise<LimitTemplateDB> {
        try {
            return await super.create(data);
        } catch (err) {
            if (err instanceof UniqueConstraintError) {
                throw new Errors.LimitTemplateAlreadyExistError(data.name);
            }

            throw err;
        }
    }

    protected validateUpdateData(data: LimitTemplateData): Partial<LimitTemplateData> {
        this.validateObject(data);

        this.validateObject(data.template);

        return {
            template: data.template
        };
    }

    protected validateCreateData(data: LimitTemplateData): LimitTemplateData {
        this.validateObject(data);

        const name = this.validateName(data.name);
        this.validateObject(data.template);

        return {
            name,
            template: data.template
        };
    }

    private validateObject(data: object, entity: string = "data") {
        if (typeof data !== "object" || Array.isArray(data)) {
            throw new ValidationError(`Invalid ${entity}`);
        }
    }

    private validateName(name: string): string {

        if (typeof name !== "string" || !name.trim()) {
            throw new ValidationError("Name must be not empty string");
        }

        if (name.length > VARCHAR_DEFAULT_LENGTH) {
            throw new ValidationError(`name is too long ${name}`);
        }

        return name.trim();
    }
}

const limitTemplateService = lazy(() => new LimitTemplateService());

export const getLimitTemplateService = (): LimitTemplateService => {
    return limitTemplateService.get();
};
