import { HIDDEN_ACCESS_TOKEN, HIDDEN_PASSWORD } from "../../utils/common";
import { AuditInfo, AuditRequestInfo } from "../../utils/auditHelper";
import { Audit } from "../../entities/audit";
import config from "../../config";
import { Transaction } from "sequelize";
import { sequelize } from "../../storage/db";
import { lazy } from "@skywind-group/sw-utils";
import { AuditDBInstance } from "../../models/audit";
import logger from "../../utils/logger";
import { AuditSummary, AuditSummaryInfo } from "../../entities/auditSummary";
import { AuditSummaryService, AuditSummaryServiceImpl } from "./auditSummary";
import { AuditSessionService, AuditSessionServiceImpl, LoginAuditSessionServiceImpl } from "./auditSession";
import { Models } from "../../models/models";

const AuditModel = Models.AuditModel;
const LoginAuditModel = Models.LoginAuditModel;

const log = logger("audit");

export class AuditHistory {
    private hideFields = {
        password: HIDDEN_PASSWORD,
        newPassword: HIDDEN_PASSWORD,
        accessToken: HIDDEN_ACCESS_TOKEN
    };

    constructor(protected operation: string,
                protected parameters: object,
                protected result: object = {}) {
    }

    public toInfo() {
        const result: any = this.result;
        result.body = this.replaceFields(result.body);
        result.params = this.replaceFields(result.params);
        return {
            operation: this.operation,
            parameters: this.replaceFields(this.parameters),
            result: this.replaceFields(result)
        };
    }

    private replaceFields(data: object) {
        if (data) {
            Object.keys(this.hideFields).forEach(field => {
                if (field in data) {
                    data[field] = this.hideFields[field];
                }
            });
        }
        return data;
    }
}

class AuditFacadeImpl {
    constructor(private readonly auditSessionService: AuditSessionService,
                private readonly auditSummaryService: AuditSummaryService = new AuditSummaryServiceImpl()) { }

    public async save(info: AuditInfo, history: AuditHistory): Promise<AuditDBInstance> {
        const reqInfo = info.getInfo();
        let dbInstance: any;
        const transaction = await sequelize.transaction();
        try {
            if (reqInfo.session) {
                await this.auditSessionService.create(reqInfo.session, transaction);
            }
            const auditSummary: AuditSummaryInfo = await this.getAuditSummary(reqInfo, transaction);
            const audit: Audit = AuditFacadeImpl.toAuditData(reqInfo, history, auditSummary);
            dbInstance = await this.writeToTable(audit, transaction);
        } catch (err) {
            await transaction.rollback();
            throw err;
        }
        await transaction.commit();
        return dbInstance;
    }

    private static toAuditData(info: AuditRequestInfo,
                               history: AuditHistory,
                               auditSummary: AuditSummaryInfo
    ): Audit {
        const entity = info.entity;
        return {
            entityId: entity.id,
            initiatorType: info.type,
            initiatorName: info.username,
            history: {
                key: entity.key,
                statusCode: info.statusCode,
                ...history.toInfo()
            },
            initiatorServiceName: config.server.getName() || "",
            initiatorIssueId: info.issueId || "",
            ts: new Date(),
            ip: info.requestInfo.ip,
            userAgent: info.requestInfo.userAgent,
            auditsSummaryId: auditSummary.id,
            auditsSessionId: info.sessionId
        };
    }

    public async getAuditSummary(info: AuditRequestInfo, transaction?: Transaction): Promise<AuditSummaryInfo> {
        const swagger = info.swagger;
        const auditSummary: AuditSummary = {
            eventName: swagger.operation.tags.join(","),
            summary: swagger.operation.summary,
            path: swagger.apiPath,
            method: info.method
        };
        const summary = await this.auditSummaryService.getOrCreate(auditSummary);
        return summary.toInfo();
    }

    private async writeToTable(record: Audit, transaction?: Transaction) {
        if (this.auditSessionService instanceof LoginAuditSessionServiceImpl) {
            return LoginAuditModel.create(record, { transaction }).catch(err => {
                log.error(err, "Fail to create login audit");
            });
        }
        return AuditModel.create(record, { transaction }).catch(err => {
            log.error(err, "Fail to create audit");
        });
    }
}

const lazyAuditFacade = lazy(() => init());
const lazyLoginAuditFacade = lazy(() => init(true));

function init(isLoginFacade = false) {
    return new AuditFacadeImpl(isLoginFacade ? new LoginAuditSessionServiceImpl() : new AuditSessionServiceImpl());
}

export function getAuditFacade(): AuditFacadeImpl {
    return lazyAuditFacade.get();
}

export function getLoginAuditFacade(): AuditFacadeImpl {
    return lazyLoginAuditFacade.get();
}
