import * as Errors from "../../errors";
import * as AuditSummaryCache from "../../cache/auditSummary";
import * as sequelize from "sequelize";
import { literal, Transaction, WhereOptions } from "sequelize";
import * as FilterService from "../filter";
import { PagingHelper } from "../../utils/paginghelper";
import { AuditSummary } from "../../entities/auditSummary";
import logger from "../../utils/logger";
import { Models } from "../../models/models";

const AuditSummaryModel = Models.AuditSummaryModel;
const log = logger("audit");

export interface AuditSummaryService {
    getOrCreate(auditSummary: AuditSummary, transaction?: Transaction);
    getFiltered(query: WhereOptions<any>);
    create(auditSummary: AuditSummary, transaction?: Transaction);
}

export class AuditSummaryServiceImpl implements AuditSummaryService {
    private sortableKeys = ["eventName", "summary"];
    private defaultSortKey = "eventName";

    public async getOrCreate(data: AuditSummary, transaction?: Transaction): Promise<AuditSummary> {
        try {
            let auditSummary: AuditSummary = await AuditSummaryCache.findOne(data);
            if (auditSummary) {
                return auditSummary;
            }
            auditSummary = await this.create(data, transaction);
            await AuditSummaryCache.saveById(auditSummary);
            return auditSummary;
        } catch (err) {
            if (err.code) {
                return Promise.reject(err);
            }
            return Promise.reject(new Errors.AuditSummaryGetOrCreateError());
        }
    }

    public async getFiltered(query: WhereOptions<any> = {}): Promise<AuditSummary[]> {
        const sortBy: string = FilterService.getSortKey(query, this.sortableKeys, this.defaultSortKey);
        const sortOrder: string = FilterService.valueFromQuery(query, "sortOrder") || "DESC";
        try {
            return PagingHelper.findAsyncAndCountAll(AuditSummaryModel, {
                    where: query,
                    offset: FilterService.valueFromQuery(query, "offset"),
                    limit: FilterService.valueFromQuery(query, "limit"),
                    order: literal(`"${sortBy}" ${sortOrder}`),
                }, async (item) => {
                    return Promise.resolve(item.toInfo());
                }
            );
        } catch (err) {
            log.error(err, "Fail to create get filtered list of AuditSummary");
            return Promise.reject(new Errors.AuditSummaryGetError());
        }
    }

    public async create(data: AuditSummary, transaction?: Transaction): Promise<AuditSummary> {
        try {
            return await AuditSummaryModel.create(data, { transaction, returning: true });
        } catch (err) {
            if (err instanceof sequelize.UniqueConstraintError) {
                return Promise.reject(
                    new Errors.AuditSummaryUniqueConstraintError((err as any).fields));
            } else if (err instanceof sequelize.ValidationError) {
                return Promise.reject(
                    new Errors.AuditSummaryValidationError((err as any).errors));
            } else {
                return Promise.reject(new Errors.AuditSummaryCreationError());
            }
        }
    }

    public getQueryParamsKey(): string[] {
        return [
            "eventName",
            "sortBy",
            "sortOrder",
            "offset",
            "limit",
            "eventName"
        ];
    }
}
