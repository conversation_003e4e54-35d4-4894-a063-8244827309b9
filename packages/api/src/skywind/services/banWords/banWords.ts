import { InternalServerError } from "../../errors";
import { logging } from "@skywind-group/sw-utils";
const fs = require("fs");
const path = require("path");

const log = logging.logger("ban-words-service");

export interface BanWordsService {
    addBanWords(words: string[]): any;
    isWordBanned(word: string): any;
    parseFile?(filePath?: string): string[];
}

export class BanWordsServiceImpl implements BanWordsService {
    private readonly filePath = path.join(process.cwd(), "resources/ban-words.json");
    private banWords: Set<string>;

    constructor() {
        this.initialize();
    }

    private initialize() {
        const banWords = this.parseFile();
        this.banWords = new Set<string>(banWords);
    }

    public parseFile(filePath?: string): string[] {
        filePath = filePath || this.filePath;

        if (!fs.existsSync(filePath)) {
            const err = new InternalServerError("Ban words file not found");
            log.error(err);
            return [] as string[];
        }
        try {
            const data = fs.readFileSync(filePath);
            return JSON.parse(data.toString());
        } catch (error) {
            const err = new InternalServerError(`Failed to load ban words: ${error.message}`);
            log.error(err);
            throw err;
        }
    }

    public addBanWords(banWords: string[]): void {
        this.banWords = new Set([...this.banWords, ...banWords]);
    }

    public isWordBanned(word: string): boolean {
        const lowerCaseWord = word.toLowerCase();

        for (const banWord of this.banWords.values()) {
            if (lowerCaseWord.includes(banWord)) {
                return true;
            }
        }
        return false;
    }
}

const banWordsService = new BanWordsServiceImpl();

export default function getBanWordsService() {
    return banWordsService;
}
