import getBanWordsService, { BanWordsService } from "./banWords";
import { GoogleBucket } from "../../utils/googleBucket";
import { logging } from "@skywind-group/sw-utils";
import config from "../../config";
import { <PERSON>ronJob } from "../../utils/cronJob";
import { StorageOptions } from "@google-cloud/storage";
import { join } from "node:path";

const log = logging.logger("ban-words-job");

export class BanWordsJob {
    private readonly filePath = join(process.cwd(), "resources/google-bucket-ban-words.json");
    private readonly fileName = config.banWords.googleBucket.fileName;

    private banWordsService: BanWordsService = getBanWordsService();
    private googleBucket = new GoogleBucket(config.banWords.googleBucket.bucketName, {
        credentials: config.banWords.googleBucket.credentials,
        projectId: config.banWords.googleBucket.projectId
    } as StorageOptions);

    public async fire() {
        log.info("Refresh ban words");
        try {
            await this.googleBucket.downloadFile(this.fileName, this.filePath);
            const banWords = this.banWordsService.parseFile(this.filePath);
            this.banWordsService.addBanWords(banWords);

        } catch (error) {
            log.error(error, "Failed to refresh ban words");
        }
    }
}

const banWordsJob = new BanWordsJob();
let banWordsCronJob: CronJob;

export async function initBanWordsJob() {
    banWordsCronJob = new CronJob({
        name: "ban-words",
        schedule: config.banWords.job.schedule,
        timeout: config.banWords.job.timeout
    }, banWordsJob.fire.bind(banWordsJob));

    if (config.banWords.job.runOnServerStart) {
        await banWordsCronJob.invoke();
    }
}
