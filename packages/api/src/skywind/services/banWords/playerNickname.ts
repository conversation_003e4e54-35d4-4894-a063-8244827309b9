import getBanWordsService, { BanWordsService } from "./banWords";
import * as Errors from "../../errors";
import { NICKNAME_MAX_LENGTH, NICKNAME_MIN_LENGTH } from "../../utils/common";
const competitorsNames = require("../../../../resources/competitors-names.json");
const competitorsNamesSet: Set<string> = new Set(competitorsNames);

export interface PlayerNicknameService {
    check(nickname: string): Promise<void>;
}

export class PlayerNicknameServiceImpl implements PlayerNicknameService {
    private banWordsService: BanWordsService = getBanWordsService();

    public async check(nickname: string): Promise<void> {
        if (typeof nickname !== "string") {
            throw new Errors.NicknameSymbolsError();
        }
        await this.checkNicknameLength(nickname);
        await this.checkNicknamePattern(nickname);
        await this.checkForRestrictedWords(nickname);
    }

    protected async checkNicknameLength(nickname: string) {
        if (nickname.length < NICKNAME_MIN_LENGTH) {
            throw new Errors.NicknameMinSymbolsError(NICKNAME_MIN_LENGTH);
        }
        if (nickname.length > NICKNAME_MAX_LENGTH) {
            throw new Errors.NicknameMaxSymbolsError(NICKNAME_MAX_LENGTH);
        }
    }

    protected async checkNicknamePattern(nickname: string) {
        const urlPattern = "^(http|https):\\/\\/|(www.)";
        const urlRegexp = new RegExp(urlPattern);
        if (urlRegexp.test(nickname)) {
            throw new Errors.NicknameSymbolsError();
        }

        const symbolPattern = "^[A-Za-z0-9\-\s!@#$%^&*()_+=-`~\\\]\[{}|';:/.,?><]*$";
        const symbolRegexp = new RegExp(symbolPattern);
        if (!symbolRegexp.test(nickname)) {
            throw new Errors.NicknameSymbolsError();
        }
    }

    protected async checkForRestrictedWords(nickname: string) {
        nickname = nickname.toLowerCase();

        for (const word of competitorsNamesSet.values()) {
            if (nickname.includes(word)) {
                throw new Errors.NicknameBadWordsError();
            }
        }

        if (this.banWordsService.isWordBanned(nickname)) {
            throw new Errors.NicknameBadWordsError();
        }
    }
}

export default function getPlayerNicknameService() {
    return new PlayerNicknameServiceImpl();
}
