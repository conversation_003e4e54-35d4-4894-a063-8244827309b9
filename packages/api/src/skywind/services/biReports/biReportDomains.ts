import { BiReportDomains, BiReportDomainsInfo } from "../../entities/biReportDomains";
import { BiReportDomainsDBInstance } from "../../models/biReportDomains";
import { encodeId } from "../../utils/publicid";
import { Transaction, UpdateOptions } from "sequelize";
import * as Errors from "../../errors";
import { Models } from "../../models/models";
const BiReportDomainsModel = Models.BiReportDomainsModel;

export class BiReportDomainsImpl implements BiReportDomains {
    public id: number;
    public trustServerUrl: string;
    public baseUrl: string;
    public isSelected: boolean;
    public version: number;
    public createdAt: Date;
    public updatedAt: Date;

    constructor(instance: BiReportDomainsDBInstance) {
        this.id = instance.get("id");
        this.trustServerUrl = instance.get("trustServerUrl");
        this.baseUrl = instance.get("baseUrl");
        this.isSelected = instance.get("isSelected");
        this.version = instance.get("version");
        this.createdAt = instance.get("createdAt");
        this.updatedAt = instance.get("updatedAt");
    }

    public toInfo(): BiReportDomainsInfo {
        return {
            pid: encodeId(this.id),
            trustServerUrl: this.trustServerUrl,
            baseUrl: this.baseUrl,
            isSelected: this.isSelected,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }

    public async save(transaction?: Transaction) {
        const oldVersion = this.version;
        this.version = oldVersion + 1;

        const updateOptions: UpdateOptions = {
            where: {
                id: this.id,
                version: oldVersion,
            }
        };

        if (transaction) {
            updateOptions["transaction"] = transaction;
        }

        try {
            const r = await BiReportDomainsModel.update(this, updateOptions);
            if (r[0] !== 1) {
                throw new Errors.OptimisticLockException();
            }
        } catch (error) {
            this.version = oldVersion;
            throw error;
        }

        return this;
    }
}
