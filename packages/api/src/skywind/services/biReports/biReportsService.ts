import logger from "../../utils/logger";
import { FindOptions, literal, Op, Transaction, UniqueConstraintError, WhereOptions } from "sequelize";
import * as Errors from "../../errors";
import {
    BiReport,
    BiReportUrl,
    BiUrlCreateData,
    BiSession,
    BiPermission,
    BiReportSettings
} from "../../entities/biReport";
import config from "../../config";
import { getPermissionsDescriptions, PermissionDescription } from "../permission";
import { BaseEntity, ChildEntity } from "../../entities/entity";
import request = require("request");
import { IncomingMessage } from "http";
import { sequelize as db } from "../../storage/db";
import * as FilterService from "../filter";
import { getBiReportDomainsService } from "./biReportDomainsService";
import { Models } from "../../models/models";
import { getEntitySettings } from "../settings";

const log = logger("audit");

const BiReportModel = Models.BiReportModel;
const BiSessionModel = Models.BiSessionModel;

const biReportDomainsService = getBiReportDomainsService();
const permissionKeyentityPrefix = config.bi.permissionPrefixKeyentity;
const permissionEntityPrefix = config.bi.permissionPrefixEntity;
const defaultLanguage = "en";
const DEFAULT_SORT_KEY = "ordering";
const sortableKeys = ["id", "name", "status", "permission"];
const validCurrencyArray = config.bi.validCurrencyArray;
export const queryParamsKeys = [
    "sortBy",
    "sortOrder",
    "offset",
    "limit"
];

export async function create(data: BiReport): Promise<BiReport> {
    await validatePermission(data.permission);
    try {
        if (typeof data.settings !== "object" || data.settings === null) {
            delete data.settings;
        }
        const orderingField = await BiReportModel.findOne({
            attributes: ["ordering"],
            limit: 1,
            order: [["ordering", "DESC"]]
        });

        data.settings = data.settings || {};
        data.settings.language = data.settings.language || defaultLanguage;
        data.ordering = orderingField ? orderingField.get("ordering") + 1 : 0;

        const reportInstance: BiReport = await BiReportModel.findOne({
            where: {
                "settings.language": { [Op.eq]: data.settings.language },
                "settings.reportId": { [Op.eq]: data.settings.reportId }
            },
        });

        if (reportInstance) {
            return Promise.reject(new Errors.ReportAlreadyExists());
        }
        const createdItem: BiReport = await BiReportModel.create(data);
        return getPublicInfo(createdItem);

    } catch (err) {
        if (err instanceof UniqueConstraintError) {
            return Promise.reject(new Errors.ReportAlreadyExists());
        }
        return Promise.reject(new Errors.CreateReportError());
    }
}

export async function update(data: BiReport, id: number): Promise<BiReport> {
    if (data.permission !== undefined) {
        await validatePermission(data.permission);
    }
    try {
        let report: BiReport = await BiReportModel.findOne({ where: { id: id } });
        if (!report) {
            return Promise.reject(new Errors.ReportNotFound());
        }
        report = getPublicInfo(report);
        report.caption = data.caption || report.caption;
        report.reportGroup = data.reportGroup || report.reportGroup;
        report.status = data.status || report.status;
        report.permission = data.permission || report.permission;
        report.settings = data.settings || report.settings;
        report.description = data.description === undefined ? report.description : data.description;
        await BiReportModel.update(report, { where: { id: id } });
        return report;
    } catch (err) {
        return Promise.reject(new Errors.CreateReportError());
    }
}

export async function remove(id: number): Promise<void> {
    try {
        const report: BiReport = await BiReportModel.findOne({ where: { id: id } });
        if (!report) {
            return Promise.reject(new Errors.ReportNotFound());
        }
        await BiReportModel.destroy({ where: { id: id } });
    } catch (err) {
        return Promise.reject(new Errors.ReportNotFound());
    }
}

export async function move(id: number, newPosition: number): Promise<BiReport> {
    if (newPosition < 0) {
        return Promise.reject(new Errors.ValidationError("New position is negative number"));
    }

    const count = await BiReportModel.count();

    if (newPosition > (count - 1)) {
        return Promise.reject(new Errors.ValidationError("New position is out of range"));
    }

    return db.transaction({ isolationLevel: Transaction.ISOLATION_LEVELS.REPEATABLE_READ },
        async (transaction) => {
            const reportInstance: BiReport = await BiReportModel.findOne({ where: { id: id } });
            if (!reportInstance) {
                return Promise.reject(new Errors.ReportNotFound());
            }
            const reportInfo = getPublicInfo(reportInstance);

            const currentPosition: number = reportInfo.ordering;
            let literalToShiftReport: string;
            const selectorForOrdering = {};

            if (currentPosition - newPosition > 0) { // MEMO: move to the up
                literalToShiftReport = "ordering + 1";
                selectorForOrdering[Op.lt] = currentPosition;
                selectorForOrdering[Op.gte] = newPosition;

            } else { // MEMO: move to the down
                literalToShiftReport = "ordering - 1";
                selectorForOrdering[Op.lte] = newPosition;
                selectorForOrdering[Op.gt] = currentPosition;
            }

            await BiReportModel.update({
                ordering: literal(literalToShiftReport)
            } as any, {
                where: {
                    ordering: selectorForOrdering
                },
                transaction
            });

            await BiReportModel.update({ ordering: newPosition } as any,
                { transaction, where: { id: id } });

            reportInfo.ordering = newPosition;
            return reportInfo;
        });
}

export async function getAvailableReports(userPermissions: string[],
                                          permissionPrefix: string,
                                          language: string = defaultLanguage,
                                          query: WhereOptions<any> = {}): Promise<BiReport[]> {

    const sortBy = FilterService.getSortKey(query, sortableKeys, DEFAULT_SORT_KEY);
    const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "ASC";
    const offset = FilterService.valueFromQuery(query, "offset");
    const limit = FilterService.valueFromQuery(query, "limit");
    let selector: any = {
        "settings.language": language,
    };

    if (userPermissions.indexOf("keyentity:bi:reports") === -1) {
        selector = {
            ...selector,
            permission: { [Op.in]: getReportsNamesFromPermissions(userPermissions, permissionPrefix) },
        };
    }

    return getAllReports({
        where: selector,
        order: literal(`"${sortBy}" ${sortOrder}`),
        offset,
        limit,
    });
}

export async function getAvailablePermissions(): Promise<BiPermission[]> {
    const permissionsList: BiPermission[] = [];
    const allPermissions: PermissionDescription[] = await getPermissionsDescriptions();
    for (const permission of allPermissions) {
        if (permission.code.startsWith(permissionKeyentityPrefix) ||
            permission.code.startsWith(permissionEntityPrefix)) {
            permissionsList.push({
                permission: permission.code,
                postfix: permission.code.substring(permission.code.lastIndexOf(":") + 1),
                description: permission.description,
            } as BiPermission);
        }
    }
    return permissionsList;
}

export async function getUrl(userPermissions: string[],
                             entity: BaseEntity,
                             createUrlData: BiUrlCreateData,
                             permissionPrefix: string,
                             language: string,
                             internalTrustTicket?: string): Promise<BiReportUrl> {

    const reportInstance: BiReport = await BiReportModel.findOne({
        where: { "settings.language": language, "settings.reportId": createUrlData.reportId },
    });
    if (!reportInstance) {
        return Promise.reject(new Errors.ReportNotFound());
    }
    if (reportInstance.status === "suspended") {
        return Promise.reject(new Errors.ReportSuspended());
    }

    const permission: string = permissionPrefix + reportInstance.permission;
    if (!userPermissions.includes(permission)) {
        return Promise.reject(new Errors.OperationForbidden());
    }

    const reportSettings = await getActualReportSettings(reportInstance, entity);
    const siteId = reportSettings.baseUrl.split("/site/")[1];
    const trustTicket: string = internalTrustTicket ||
        await getTableauTrustTicket(reportSettings.trustServerUrl, siteId);

    const token: string = require("uuid").v4();
    const expiredAt: Date = new Date();
    expiredAt.setSeconds(expiredAt.getSeconds() + reportSettings.tokenExpiresIn);

    const createdSession: BiSession = await BiSessionModel.create({
        token: token,
        entityId: entity.id,
        name: reportInstance.name,
        workbook: reportInstance.workbook,
        expiredAt: expiredAt,
    });
    if (!createdSession) {
        return Promise.reject(new Errors.CreateReportError());
    }
    const urlCurrency = (entity as ChildEntity).defaultCurrency;
    const trustURL = reportSettings.baseUrl.split("/#/")[0] + "/trusted";
    const url = `${trustURL}/${trustTicket}/t/${siteId}/views/`
        + `${createdSession.workbook}/${createdSession.name}?token=${token}`
        + "&:customViews=n"
        + `${(config.bi.showToolbar ? "" : "&:toolbar=no")}`
        + `${(createUrlData.tournamentId ? `&Tournament%20ID=${createUrlData.tournamentId}` : "")}`
        + `${(urlCurrency && validCurrencyArray.includes(urlCurrency) ? `&Currency=${urlCurrency}` : "")}`
        + "&:refresh";

    return {
        url: url,
        token: token,
        baseUrl: reportSettings.baseUrl,
        trustTicket: trustTicket,
        name: reportInstance.caption || createdSession.name,
        workbook: createdSession.workbook,
        settings: reportInstance.settings,
        expiredAt: createdSession.expiredAt,
    } as BiReportUrl;
}

async function getActualReportSettings(report: BiReport, entity: BaseEntity): Promise<BiReportSettings> {
    const settings = await getEntitySettings(entity.path);
    const [selectedDomains] = await biReportDomainsService.getMany({ isSelected: true });

    const trustServerUrl = report.settings.trustServerURL
        || settings.tableauTrustServerUrl
        || (selectedDomains && selectedDomains.trustServerUrl)
        || config.bi.trustServerURL;

    const baseUrl = report.settings.baseUrl
        || settings.tableauBaseUrl
        || (selectedDomains && selectedDomains.baseUrl)
        || config.bi.baseURL;

    return {
        trustServerUrl,
        baseUrl,
        tokenExpiresIn: +report.settings.tokenExpiresIn || config.bi.tokenExpiresIn
    };
}

async function getTableauTrustTicket(trustServerURL: string, siteId: string): Promise<string> {
    const body = [
        "username=" + config.bi.username,
        "target_site=" + siteId,
    ].join("&");
    const response = await post(trustServerURL, body);
    return response.toString();
}

async function post<T>(url: string, req): Promise<T> {
    return new Promise<T>((resolve, reject) => {
        request.post(url, {
            headers: { "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" },
            body: req,
        }, processResponse(resolve, reject));
    });
}

function processResponse(resolve, reject): (error: any, response: IncomingMessage, body: any) => Promise<any> {
    return function(error: Error, response: IncomingMessage, body: any): Promise<any> {
        if (error) {
            log.error(error, "Fail getting tableau ticket");
            return reject(new Errors.GetBiTicketError(""));
        } else if (response.statusCode !== 200) {
            log.error(response.statusCode, "Fail getting tableau ticket");
            return reject(new Errors.GetBiTicketError((body || "").toString()));
        } else {
            return resolve(body);
        }
    };
}

async function getAllReports(filter?: FindOptions<any>): Promise<BiReport[]> {
    try {
        const allReports: BiReport[] = await BiReportModel.findAll(filter);
        return allReports.map(getPublicInfo);
    } catch (err) {
        return Promise.reject(new Errors.GetReportError());
    }
}

const getReportsNamesFromPermissions = (permissions: string[], permissionPrefix: string): string[] => {
    const reportPermission: string[] = [];
    for (const permission of permissions) {
        if (permission.startsWith(permissionPrefix)) {
            reportPermission.push(permission.substring(permissionPrefix.length));
        }
    }
    return reportPermission;
};

const validatePermission = async (permission: string): Promise<void> => {
    const allPermissions: string[] = (await getPermissionsDescriptions()).map(x => x.code);
    if (allPermissions.indexOf(permissionKeyentityPrefix + permission) === -1
        && allPermissions.indexOf(permissionEntityPrefix + permission) === -1) {
        return Promise.reject(new Errors.PermissionNotExistInList(
            permissionKeyentityPrefix + permission + " or " + permissionEntityPrefix + permission
        ));
    }
};

const getPublicInfo = (item: BiReport): BiReport => {
    return {
        id: +item.id,
        reportId: item.settings.reportId,
        name: item.name,
        caption: item.caption,
        description: item.description,
        workbook: item.workbook,
        reportGroup: item.reportGroup,
        status: item.status,
        permission: item.permission,
        createdAt: item.createdAt,
        settings: item.settings,
        ordering: item.ordering
    } as BiReport;
};
