import {
    PLAYER_BULK_OPERATION_TYPE,
    PlayerBulkOperation,
    PlayerBulkOperationResult,
    PlayerExecutorData,
} from "../../entities/bulk";
import { BulkOperationExecutorFactory } from "./bulkOperationExecutorFactory";
import { BaseEntity } from "../../entities/entity";
import { Op, Transaction } from "sequelize";
import { BulkOperationExecutor, GameGroupOperationExecutor } from "./executors";
import { Player } from "../../entities/player";
import { isSetOperation } from "./utils";
import { PlayerImpl } from "../brandPlayer";
import { ValidationError } from "../../errors";
import { Models } from "../../models/models";
import { GameGroupAttributes } from "../../entities/gamegroup";

const playerModel = Models.PlayerModel;
const gameGroupModel = Models.GameGroupModel;

export class PlayerExecutorFactory
    implements BulkOperationExecutorFactory<PlayerBulkOperation, PlayerBulkOperationResult> {

    private typeNames: string[] = Object.values(PLAYER_BULK_OPERATION_TYPE);

    public isValidOperationType(type: string): boolean {
        return this.typeNames.includes(type);
    }

    public async createExecutors(
        entity: BaseEntity,
        operations: PlayerBulkOperation[],
        transaction: Transaction): Promise<BulkOperationExecutor<PlayerBulkOperation, PlayerBulkOperationResult>[]> {

        const data = await this.loadExecutorData(entity, operations);

        const initExecutors = (op: PlayerBulkOperation) => {
            switch (op.item.type) {
                case PLAYER_BULK_OPERATION_TYPE.GROUP:
                    return new GameGroupOperationExecutor(op, data);
                default:
                    throw new ValidationError(`Invalid operation item type - ${op.item.type}`);
            }
        };

        return operations.map(initExecutors);
    }

    private async loadExecutorData(
        entity: BaseEntity,
        operations: PlayerBulkOperation[]): Promise<PlayerExecutorData> {

        const players = await this.getPlayers(entity, operations);
        const gameGroups = await this.getGameGroups(entity, operations);

        return {
            players,
            gameGroups
        };
    }

    private async getPlayers(entity: BaseEntity, operations: PlayerBulkOperation[]): Promise<Player[]> {
        const playersInBrand: { [field: number]: string[] } = {};
        for (const op of operations) {
            const brand = entity.find({ key: op.entityKey });

            if (!brand.isBrand()) {
                return Promise.reject(new ValidationError(`Entity key belongs to not a brand - ${op.entityKey}`));
            }

            if (playersInBrand[brand.id]) {
                playersInBrand[brand.id].push(op.playerCode);
            } else {
                playersInBrand[brand.id] = [ op.playerCode ];
            }
        }

        let result = [];
        for (const brandId of Object.keys(playersInBrand)) {
            const players = await playerModel.findAll({
                where: {
                    code: {
                        [Op.in]: playersInBrand[brandId]
                    },
                    brandId
                }
            });

            if (players.length !== new Set(playersInBrand[brandId]).size) {
                return Promise.reject(new ValidationError("Players not found"));
            }

            result = [...result, ...players];
        }

        return result.map(player => new PlayerImpl(player));
    }

    private async getGameGroups(entity: BaseEntity, operations: PlayerBulkOperation[]): Promise<GameGroupAttributes[]> {
        const gameGroupsInBrand: { [field: number]: number[] } = {};
        const operationsToSet = operations.filter(operation => isSetOperation(operation));
        for (const op of operationsToSet) {
            const brand = entity.find({ key: op.entityKey });
            if (gameGroupsInBrand[brand.id]) {
                gameGroupsInBrand[brand.id].push(op.item.id);
            } else {
                gameGroupsInBrand[brand.id] = [ op.item.id ];
            }
        }

        let result = [];
        for (const brandId of Object.keys(gameGroupsInBrand)) {
            const gameGroups = await gameGroupModel.findAll({
                where: {
                    id: {
                        [Op.in]: gameGroupsInBrand[brandId]
                    },
                    brandId
                }
            });

            if (gameGroups.length !== new Set(gameGroupsInBrand[brandId]).size) {
                return Promise.reject(new ValidationError("GameGroups not found"));
            }

            result = [...result, ...gameGroups];
        }

        return result;
    }
}
