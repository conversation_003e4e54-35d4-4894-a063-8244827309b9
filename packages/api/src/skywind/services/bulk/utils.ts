import { BULK_OPERATION_ACTION, BulkOperation, EntityBulkOperationResult } from "../../entities/bulk";
import { BaseEntity } from "../../entities/entity";

export function isSetOperation(op: BulkOperation): boolean {
    return op.action === BULK_OPERATION_ACTION.SET;
}

export function isBaseEntity(result: EntityBulkOperationResult): result is BaseEntity {
    return (result as BaseEntity).isBrand !== undefined;
}
