import * as Errors from "../../errors";
import { Merchant } from "../../entities/merchant";
import { Op, Transaction } from "sequelize";
import {
    AVAILABLE_SITE_BULK_OPERATION_ACTION,
    AvailableSiteExecutorData,
    AvailableSiteOperation,
    AvailableSiteOperationDetails,
    AvailableSiteOperationResult,
    BulkOperation,
    EntityBulkOperation,
    EntityBulkOperationResult,
    EntityExecutorData,
    PlayerBulkOperation,
    PlayerBulkOperationResult,
    PlayerExecutorData
} from "../../entities/bulk";
import { BaseEntity, ChildEntity } from "../../entities/entity";
import { isSetOperation } from "./utils";
import { validateStaticDomain } from "../entityDomainService";
import { AVAILABLE_SITE_STATUSES } from "../../entities/availableSite";
import { Models } from "../../models/models";

const MerchantModel = Models.MerchantModel;
const PlayerModel = Models.PlayerModel;
const AvailableSiteModel = Models.AvailableSiteModel;

export interface BulkOperationExecutor<Operation extends BulkOperation, Result> {
    operation: Operation;

    execute(entity: BaseEntity, transaction: Transaction): Promise<Result>;
}

export class StaticDomainOperationExecutor
    implements BulkOperationExecutor<EntityBulkOperation, EntityBulkOperationResult> {

    constructor(public operation: EntityBulkOperation, private executorData: EntityExecutorData) {
    }

    public async execute(entity: BaseEntity, transaction: Transaction): Promise<EntityBulkOperationResult> {
        const operationEntity = entity.find({ key: this.operation.entityKey });
        if (isSetOperation(this.operation)) {

            const domain = this.executorData.staticDomains.find(c => c.id === this.operation.item.id);
            await validateStaticDomain(operationEntity, domain);

            operationEntity.staticDomainId = this.operation.item.id;
        } else {
            operationEntity.staticDomainId = null;
        }

        return operationEntity.save(transaction);
    }
}

export class DynamicDomainOperationExecutor
    implements BulkOperationExecutor<EntityBulkOperation, EntityBulkOperationResult> {

    constructor(public operation: EntityBulkOperation, private executorData: EntityExecutorData) {
    }

    public async execute(master: BaseEntity, transaction: Transaction): Promise<EntityBulkOperationResult> {
        const operationEntity = master.find({ key: this.operation.entityKey }) as ChildEntity;

        if (isSetOperation(this.operation)) {

            const domain = this.executorData.dynamicDomains.find(c => c.id === this.operation.item.id);
            if (operationEntity.inheritedEnvironment !== domain.environment) {
                return Promise.reject(new Errors.RequireMigrationError(operationEntity.id, domain.environment));
            }

            operationEntity.dynamicDomainId = domain.id;
            operationEntity.environment = domain.environment;
        } else {
            const prevEnvironment = operationEntity.inheritedEnvironment;

            operationEntity.dynamicDomainId = null;
            operationEntity.environment = null;

            const newEnvironment = operationEntity.inheritedEnvironment;

            if (newEnvironment !== prevEnvironment) {
                return Promise.reject(new Errors.RequireMigrationError(operationEntity.id, newEnvironment));
            }
        }

        return operationEntity.save(transaction);
    }
}

export class ProxyOperationExecutor
    implements BulkOperationExecutor<EntityBulkOperation, EntityBulkOperationResult> {

    constructor(public operation: EntityBulkOperation, private executorData: EntityExecutorData) {
    }

    public async execute(entity: BaseEntity, transaction: Transaction): Promise<EntityBulkOperationResult> {
        const brand = entity.find({ key: this.operation.entityKey });
        const merchant = this.executorData.merchants.find(c => c.brandId === brand.id);

        if (isSetOperation(this.operation)) {
            merchant.proxyId = this.operation.item.id;
            merchant.proxy = this.executorData.proxies.find(proxy => proxy.id === this.operation.item.id);
        } else {
            merchant.proxyId = null;
        }

        return MerchantModel.update({ proxyId: merchant.proxyId } as Merchant,
            { where: { brandId: merchant.brandId }, transaction }).then(() => merchant);
    }
}

export class GameGroupOperationExecutor
    implements BulkOperationExecutor<PlayerBulkOperation, PlayerBulkOperationResult> {

    constructor(public operation: PlayerBulkOperation, private executorData: PlayerExecutorData) {
    }

    public async execute(entity: BaseEntity, transaction: Transaction): Promise<PlayerBulkOperationResult> {
        const brand = entity.find({ key: this.operation.entityKey });
        const player = this.executorData.players.find(
            p => p.code === this.operation.playerCode && p.brandId === brand.id);

        if (isSetOperation(this.operation)) {
            const gameGroup = this.executorData.gameGroups.find(group => group.id === this.operation.item.id);
            player.updateGameGroup(gameGroup);
        } else {
            player.gamegroupId = null;
            player.gamegroupName = null;
        }

        return PlayerModel.update(
            { gamegroupId: player.gamegroupId },
            { where: { id: player.id }, transaction }).then(() => player.toInfo());
    }
}

export class AvailableSiteOperationExecutor
    implements BulkOperationExecutor<AvailableSiteOperation, AvailableSiteOperationResult> {

    constructor(public operation: AvailableSiteOperation, private executorData: AvailableSiteExecutorData) {
    }

    public async execute(entity: BaseEntity, transaction: Transaction): Promise<AvailableSiteOperationResult> {
        const action = this.operation.action;

        switch (action) {
            case AVAILABLE_SITE_BULK_OPERATION_ACTION.ACTIVATE:
                return this.updateOrCreateSites(entity, transaction,
                    this.executorData.sitesToActivate, AVAILABLE_SITE_STATUSES.NORMAL);

            case AVAILABLE_SITE_BULK_OPERATION_ACTION.DEACTIVATE:
                return this.updateOrCreateSites(entity, transaction,
                    this.executorData.sitesToDeactivate, AVAILABLE_SITE_STATUSES.SUSPENDED);

            case AVAILABLE_SITE_BULK_OPERATION_ACTION.REMOVE:
                return this.removeSites(entity, transaction, this.executorData.sitesToRemove);
        }
    }

    private async updateOrCreateSites(entity: BaseEntity,
                                      transaction: Transaction,
                                      siteDetails: AvailableSiteOperationDetails,
                                      status: AVAILABLE_SITE_STATUSES): Promise<number[]> {

        const siteModel = AvailableSiteModel;
        let updatedSiteIds: number[] = [];
        let createdSiteIds: number[] = [];

        if (siteDetails.existingSites.length) {
            updatedSiteIds = siteDetails.existingSites.map(site => site.id);
            const updateSitesSelector = {
                id: { [Op.in]: siteDetails.existingSites.map(site => site.id) }
            };
            await siteModel.update({ status } as any, { where: updateSitesSelector, transaction });
        }

        if (siteDetails.nonExistingSites.length) {
            const createSitesData = siteDetails.nonExistingSites.map(site => ({
                entityId: entity.id,
                url: site,
                status
            }));
            createdSiteIds = await siteModel
                .bulkCreate(createSitesData, { transaction, returning: true })
                .then(items => items.map(item => item.get("id")));
        }

        return [...updatedSiteIds, ...createdSiteIds];
    }

    private async removeSites(entity: BaseEntity,
                              transaction: Transaction,
                              siteDetails: AvailableSiteOperationDetails): Promise<void> {
        if (siteDetails.existingSites.length) {
            const removeSitesSelector = {
                id: { [Op.in]: siteDetails.existingSites.map(site => site.id) }
            };
            await AvailableSiteModel.destroy({ where: removeSitesSelector, transaction });
        }
    }
}
