import {
    AvailableSiteOperation,
    AvailableSiteOperationResult
} from "../../entities/bulk";
import { BulkServiceImpl } from "./bulkService";
import { BaseEntity } from "../../entities/entity";
import { AvailableSiteExecutorFactory } from "./availableSiteExecutorFactory";
import * as Errors from "../../errors";

export class AvailableSiteBulkService extends BulkServiceImpl<AvailableSiteOperation, AvailableSiteOperationResult> {

    constructor() {
        super(new AvailableSiteExecutorFactory());
    }

    public async process(entity: BaseEntity,
                         operations: AvailableSiteOperation[]): Promise<AvailableSiteOperationResult[]> {
        return super.process(entity, operations);
    }

    protected async beforeExecute(...args): Promise<void> { /* tslint:disable-line */
    }

    protected async afterExecute(...args): Promise<void> { /* tslint:disable-line */
    }

    protected async validateOperations(entity: BaseEntity, operations: AvailableSiteOperation[]): Promise<void> {
        if (!Array.isArray(operations) || !operations.length) {
            return;
        }

        const errorMessages = [];

        for (const op of operations) {
            if (typeof op !== "object" || Array.isArray(op) || op === null) {
                errorMessages.push("Operation should be an object");
                continue;
            }
            if (typeof op.action !== "string") {
                errorMessages.push("Action should be a string");
                continue;
            }
            if (!Array.isArray(op.sites)) {
                errorMessages.push("Sites should be an array");
                continue;
            }
            const noStringValues = op.sites.filter(site => typeof site !== "string");
            if (noStringValues.length) {
                errorMessages.push("Sites' values should be a string");
            }
        }

        if (errorMessages.length) {
            throw new Errors.ValidationError(errorMessages);
        }
    }
}
