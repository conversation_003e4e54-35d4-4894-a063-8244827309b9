import { BulkServiceImpl } from "./bulkService";
import {
    BULK_OPERATION_ACTION,
    ENTITY_BULK_OPERATION_TYPE,
    EntityBulkOperation,
    EntityBulkOperationResult
} from "../../entities/bulk";
import { ApplicationLock, ApplicationLockId } from "../../utils/applicationLock";
import { Transaction } from "sequelize";
import { EntityExecutorFactory } from "./bulkOperationExecutorFactory";
import { BaseEntity } from "../../entities/entity";
import EntitySettingsService from "../settings";
import * as Errors from "../../errors";

export class EntityBulkService extends BulkServiceImpl<EntityBulkOperation, EntityBulkOperationResult> {

    constructor() {
        super(new EntityExecutorFactory());
    }

    public async process(entity: BaseEntity,
                         operations: EntityBulkOperation[]): Promise<EntityBulkOperationResult[]> {
        return super.process(entity, operations);
    }

    protected async beforeExecute(operations: EntityBulkOperation[], transaction: Transaction): Promise<void> {
        if (this.shouldLockUpdateDynamicDomain(operations)) {
            await ApplicationLock.lock(transaction, ApplicationLockId.UPDATE_DYNAMIC_DOMAIN);
        }
    }

    protected async afterExecute(...args): Promise<void> { /* tslint:disable-line */
    }

    private shouldLockUpdateDynamicDomain(operations: EntityBulkOperation[]): boolean {
        return !!operations.find(
            op => op.action === BULK_OPERATION_ACTION.SET &&
                op.item.type === ENTITY_BULK_OPERATION_TYPE.DYNAMIC);
    }

    protected async validateOperations(entity: BaseEntity, operations: EntityBulkOperation[]): Promise<void> {
        await super.validateOperations(entity, operations);

        const errorMessages = [];
        for (const op of operations) {
            if (op.action === BULK_OPERATION_ACTION.SET && op.item.type === ENTITY_BULK_OPERATION_TYPE.STATIC) {
                const ent: any  = await entity.find({ key: op.entityKey });
                if (!ent.isMaster()) {
                    const parentSettings = await new EntitySettingsService(ent.getParent()).get();
                    if (parentSettings.allowedStaticDomainsForChildId) {
                        if (parentSettings.allowedStaticDomainsForChildId.indexOf(op.item.id) === -1) {
                            errorMessages.push(`Static domain cannot ${op.item.id} be applied to ${ent.path}, because parent's restriction`);
                        }
                    }
                }
            }
        }
        if (errorMessages.length) {
            throw new Errors.ValidationError(errorMessages);
        }
    }
}
