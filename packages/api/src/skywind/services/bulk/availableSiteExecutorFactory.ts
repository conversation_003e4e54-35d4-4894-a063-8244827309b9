import {
    AvailableSiteOperation,
    AvailableSiteOperationResult,
    AvailableSiteExecutorData,
    AvailableSiteOperationDetails,
    AVAILABLE_SITE_BULK_OPERATION_ACTION
} from "../../entities/bulk";
import { BulkOperationExecutorFactory } from "./bulkOperationExecutorFactory";
import { BaseEntity } from "../../entities/entity";
import { Op, Transaction } from "sequelize";
import { BulkOperationExecutor, AvailableSiteOperationExecutor } from "./executors";
import { getAvailableSiteService } from "../availableSites";
import { AvailableSite } from "../../entities/availableSite";

export class AvailableSiteExecutorFactory
    implements BulkOperationExecutorFactory<AvailableSiteOperation, AvailableSiteOperationResult> {

    public async createExecutors(
        entity: BaseEntity,
        operations: AvailableSiteOperation[],
        transaction: Transaction
    ): Promise<BulkOperationExecutor<AvailableSiteOperation, AvailableSiteOperationResult>[]> {

        const data = await this.loadExecutorData(entity, operations);
        const initExecutors = (op: AvailableSiteOperation) => (new AvailableSiteOperationExecutor(op, data));

        return operations.map(initExecutors);
    }

    private async loadExecutorData(entity: BaseEntity,
                                   operations: AvailableSiteOperation[]): Promise<AvailableSiteExecutorData> {

        const sitesToRemove = await this.getSitesOperationDetails(entity, operations.find(
            op => op.action === AVAILABLE_SITE_BULK_OPERATION_ACTION.REMOVE));
        const sitesToActivate = await this.getSitesOperationDetails(entity, operations.find(
            op => op.action === AVAILABLE_SITE_BULK_OPERATION_ACTION.ACTIVATE));
        const sitesToDeactivate = await this.getSitesOperationDetails(entity, operations.find(
            op => op.action === AVAILABLE_SITE_BULK_OPERATION_ACTION.DEACTIVATE));

        return {
            sitesToRemove,
            sitesToActivate,
            sitesToDeactivate
        };
    }

    private async getSitesOperationDetails(entity: BaseEntity,
                                           operation?: AvailableSiteOperation): Promise<AvailableSiteOperationDetails> {

        if (!operation) {
            return;
        }
        const uniqueSites = [...new Set(operation.sites)];
        const getSitesSelector = { url: { [Op.in]: uniqueSites }, entityId: entity.id };

        const existingSites: AvailableSite[] = await getAvailableSiteService(entity).list({ where: getSitesSelector });
        const nonExistingSites: string[] = uniqueSites.filter(site =>
            !existingSites.find(eSite => eSite.url === site));

        return {
            existingSites,
            nonExistingSites
        };
    }
}
