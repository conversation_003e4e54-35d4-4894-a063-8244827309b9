import { BaseEntity } from "../../entities/entity";
import * as Errors from "../../errors";
import { sequelize as db } from "../../storage/db";
import { BULK_OPERATION_ACTION, BulkOperation } from "../../entities/bulk";
import { BulkOperationExecutorFactory } from "./bulkOperationExecutorFactory";
import { isSetOperation } from "./utils";
import EntityCache from "../../cache/entity";
import { Transaction } from "sequelize";

export interface BulkService<Operation extends BulkOperation, Result> {
    process(entity: BaseEntity, operations: Operation[]): Promise<Result[]>;
}

export abstract class BulkServiceImpl<Operation extends BulkOperation, Result>
    implements BulkService<Operation, Result> {

    protected constructor(
        protected executorFactory: BulkOperationExecutorFactory<Operation, Result>) {
    }

    public async process(entity: BaseEntity, operations: Operation[]): Promise<Result[]> {

        await this.validateOperations(entity, operations);

        try {
            return await db.transaction(async (transaction) => {
                await this.beforeExecute(operations, transaction);

                try {
                    const executors = await this.executorFactory.createExecutors(entity, operations, transaction);
                    const executions = executors.map(op => op.execute(entity, transaction));
                    return await Promise.all(executions);
                } finally {
                    await this.afterExecute(operations, transaction);
                }
            });
        } finally {
            EntityCache.reset();
        }
    }

    protected async validateOperations(entity: BaseEntity, operations: Operation[]): Promise<void> {
        if (!Array.isArray(operations) || !operations.length) {
            throw new Errors.ValidationError("Operations should be non-empty array of objects");
        }

        const errorMessages = [];

        for (const op of operations) {
            if (typeof op !== "object") {
                errorMessages.push("Operation should be an object");
                continue;
            }

            if (!(op.entityKey && entity.find({ key: op.entityKey }))) {
                errorMessages.push(`Entity not found by key - ${op.entityKey}`);
            }

            if (!Object.values(BULK_OPERATION_ACTION).includes(op.action as any)) {
                errorMessages.push(`Unknown operation action - ${op.action}`);
            }

            if (typeof op.item !== "object") {
                errorMessages.push("Operation item should be an object");
                continue;
            }

            if (!this.executorFactory.isValidOperationType(op.item.type)) {
                errorMessages.push(`Unknown operation item type - ${op.item.type}`);
            }

            if (isSetOperation(op) && !op.item.id) {
                errorMessages.push(`Item ID is required for "${BULK_OPERATION_ACTION.SET}" operations`);
            }
        }

        if (errorMessages.length) {
            throw new Errors.ValidationError(errorMessages);
        }
    }

    protected abstract beforeExecute(operations: Operation[], transaction: Transaction): Promise<void>;
    protected abstract afterExecute(operations: Operation[], transaction: Transaction): Promise<void>;
}
