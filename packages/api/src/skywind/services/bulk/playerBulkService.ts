import {
    PlayerBulkOperation,
    PlayerBulkOperationResult
} from "../../entities/bulk";
import { BulkServiceImpl } from "./bulkService";
import { BaseEntity } from "../../entities/entity";
import { PlayerExecutorFactory } from "./playerExecutorFactory";

export class PlayerBulkService extends BulkServiceImpl<PlayerBulkOperation, PlayerBulkOperationResult> {

    constructor() {
        super(new PlayerExecutorFactory());
    }

    public async process(entity: BaseEntity,
                         operations: PlayerBulkOperation[]): Promise<PlayerBulkOperationResult[]> {
        return super.process(entity, operations);
    }

    protected async beforeExecute(...args): Promise<void> { /* tslint:disable-line */
    }

    protected async afterExecute(...args): Promise<void> { /* tslint:disable-line */
    }

}
