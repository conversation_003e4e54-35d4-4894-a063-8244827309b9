import { favoriteGamesCache, gameCategoryGamesCache } from "./gameCategoryGamesService";
import { GameCategoryItemsService } from "./gameCategoryItemsService";
import { BaseEntity } from "../../entities/entity";
import { GameCategoryImpl } from "./gameCategory";
import { GameCategoryDBInstance } from "../../models/gamecategory";
import { literal, Op, Transaction, UniqueConstraintError, WhereOptions } from "sequelize";
import * as Errors from "../../errors";
import { PagingHelper } from "../../utils/paginghelper";
import { ENTITY_GAME_STATUS, GAME_CATEGORY_TYPE, GAME_IMAGES } from "../../utils/common";
import EntityCache from "../../cache/entity";
import { getParentEntities } from "../entity";
import * as FilterService from "../filter";
import { GameCategory, GameCategoryCreateData, GameCategoryTerminalInfo } from "../../entities/gamecategory";
import { decodeId } from "../../utils/publicid";
import { validateTranslateIconField, validateTranslations } from "../../utils/validateTranslations";
import { sequelize as db } from "../../storage/db";
import { Cache } from "../../cache/cache";
import config from "../../config";
import { EntityGame } from "../../entities/game";
import { findGameLimits } from "../limits";
import * as LobbyCache from "../../cache/lobby";
import { Models } from "../../models/models";

const GameCategoryModel = Models.GameCategoryModel;

export const getGameCategoryService = (entity: BaseEntity) => new GameCategoryService(entity);

export class GameCategoryService {
    private static defaultOffset: number = 0;
    private static defaultLimit: number = 100;
    private static defaultSortOrder: string = "ASC";
    private static defaultSortKey: string = "ordering";

    public static queryParamsKeys = [
        "type"
    ];

    public static operatorQueryParamsKeys = [
        "sortBy",
        "sortOrder",
        "offset",
        "limit",
        "type"
    ];

    public static sortableKeys = ["title", "description", "status"];

    constructor(private readonly entity: BaseEntity) {
    }

    public async findOne(id: number): Promise<GameCategoryImpl> {
        const item: GameCategoryDBInstance = await this.findOneInstance(id);
        return new GameCategoryImpl(item);
    }

    private async findOneInstance(id: number): Promise<GameCategoryDBInstance> {
        const item: GameCategoryDBInstance = await GameCategoryModel.findOne({
            where: { id, brandId: this.entity.id },
        });
        if (!item) {
            throw new Errors.GameCategoryNotFound();
        }
        return item;
    }

    public async findAll(where: WhereOptions<any> = {},
                         offset: number = GameCategoryService.defaultOffset,
                         limit: number = GameCategoryService.defaultLimit,
                         order: any = literal(
                             `"${GameCategoryService.defaultSortKey}" ${GameCategoryService.defaultSortOrder}`
                         ))
        : Promise<GameCategoryImpl[]> {

        const entityWithCategories = await GameCategoryService.getEntityIdWithCategories(this.entity, where);
        if (!entityWithCategories) {
            return PagingHelper.fillInfo([], 0, { offset, limit });
        }

        return PagingHelper.findAndCountAll(GameCategoryModel, {
            where: {
                ...where,
                brandId: entityWithCategories.id
            },
            offset,
            limit,
            order
        }, item => new GameCategoryImpl(item));
    }

    public static async findAllCache(_: string, entityId: number): Promise<GameCategoryImpl[]> {

        const where: WhereOptions<any> = {
            status: "normal",
            type: GAME_CATEGORY_TYPE.GENERAL,
        };

        const entity = await EntityCache.findOne({ id: entityId });
        const entityWithCategories = await GameCategoryService.getEntityIdWithCategories(entity, where);
        if (!entityWithCategories) {
            return PagingHelper.fillInfo([], 0, {
                offset: GameCategoryService.defaultOffset, limit: GameCategoryService.defaultLimit
            });
        }

        return PagingHelper.findAndCountAll(GameCategoryModel, {
            where: {
                ...where,
                brandId: entityWithCategories.id
            },
            offset: GameCategoryService.defaultOffset,
            limit: GameCategoryService.defaultLimit,
            order: literal(`"${GameCategoryService.defaultSortKey}" ${GameCategoryService.defaultSortOrder}`),
        }, item => new GameCategoryImpl(item));
    }

    public static async getEntityIdWithCategories(entity: BaseEntity,
                                                  where: WhereOptions<any> = {}): Promise<BaseEntity> {
        const parentEntities = [entity, ...getParentEntities(entity)];
        const parentIds = parentEntities.map(ent => ent.id);

        const itemsExists = await GameCategoryModel.findOne({
            where: {
                ...where,
                brandId: { [Op.in]: parentIds }
            },
            attributes: { include: ["brandId"] },
            order: literal(`array_position(Array[${parentIds}], brand_id)`)
        });

        if (itemsExists && typeof itemsExists.get("brandId") === "number") {
            return parentEntities.find(ent => ent.id === itemsExists.get("brandId"));
        }
    }

    public async findAllWithGames(query: WhereOptions<any> = {},
                                  type: string = GAME_CATEGORY_TYPE.GENERAL,
                                  includeGamesAmount: boolean = false,
                                  includeGames: boolean = false) {

        const filter: WhereOptions<any> = { type };
        if (query["status"]) {
            filter["status"] = query["status"];
        }

        const sortBy = FilterService.getSortKey(query,
            GameCategoryService.sortableKeys,
            GameCategoryService.defaultSortKey);
        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "ASC";

        let categories = await this.findAll(
            filter,
            FilterService.valueFromQuery(query, "offset"),
            FilterService.valueFromQuery(query, "limit"),
            literal(`"${sortBy}" ${sortOrder}`),
        );

        if (includeGamesAmount || includeGames) {
            const games = await gameCategoryGamesCache.query(this.entity.id, categories);

            categories = categories.map(category => {
                const categoryGames = GameCategoryItemsService.filterGamesByItems(games, category.items);

                if (includeGames) {
                    category.games = categoryGames;
                }
                if (includeGamesAmount) {
                    category.gamesAmount = categoryGames.length;
                }
                return category;
            });
        }

        return categories;
    }

    public async findAllWithGamesForLobby(includeGames: boolean = false,
                                          currency?: string,
                                          gameGroupName?: string,
                                          gameGroupId?: number): Promise<GameCategoryTerminalInfo[]> {
        const categories = await gameCategoryCache.find(`${this.entity.id}`, this.entity.id);
        let categoryGames: Record<number, EntityGame[]> = {};
        if (includeGames) {
            const allGames = await gameCategoryGamesCache.query(this.entity.id, categories);
            const disabledStatuses = [ENTITY_GAME_STATUS.SUSPENDED, ENTITY_GAME_STATUS.HIDDEN];
            const games = allGames.filter(game => !disabledStatuses.includes(game.status as ENTITY_GAME_STATUS));
            categoryGames = categories.reduce<Record<number, EntityGame[]>>((result, { id, items }) => ({
                ...result,
                [id]: GameCategoryItemsService.filterGamesByItems(games, items)
            }), {});
        }
        const entityGames = Object.values(categoryGames).reduce<EntityGame[]>((result, games) => [
            ...result,
            ...games
        ], []);
        const gameLimits = await findGameLimits(this.entity, entityGames, currency, gameGroupName, gameGroupId);
        return categories.map(category => {
            const games = (categoryGames[category.id] || []).map(game => game.toInfo(true)).map(game => ({
                ...game,
                defaultInfo: {
                    ...(GAME_IMAGES[game.code] || {}),
                    ...(game.defaultInfo || {}),
                },
                limits: gameLimits[game.id]
            }));
            return category.toTerminalInfo(this.entity, games);
        });
    }

    public async create(data: GameCategoryCreateData): Promise<GameCategoryImpl> {
        try {
            GameCategoryService.validateData(data);
            const orderingField = await this.getLastOrdering();
            const createData: GameCategory = {
                title: data.title,
                description: data.description || "",
                brandId: this.entity.id,
                status: data.status,
                type: data.type,
                ordering: typeof orderingField === "number" ? orderingField + 1 : 0,
                items: GameCategoryItemsService.map(data.items, decodeId),
                icon: data.icon || "",
                translations: data.translations || {}
            };

            try {
                const item = await GameCategoryModel.create(createData);
                return new GameCategoryImpl(item);
            } catch (err) {
                if (err instanceof UniqueConstraintError) {
                    return Promise.reject(new Errors.GameCategoryAlreadyExists());
                }
                return Promise.reject(err);
            }
        } finally {
            await LobbyCache.reset();
        }
    }

    public async update(id: number,
                        data: GameCategoryCreateData): Promise<GameCategoryImpl> {
        try {
            GameCategoryService.validateData(data);

            const item: GameCategoryDBInstance = await this.findOneInstance(id);
            const updateData = {
                description: ("description" in data) ? data.description : item.get("description"),
                status: data.status ? data.status : item.get("status"),
                title: data.title ? data.title : item.get("title"),
                icon: ("icon" in data) ? data.icon : item.get("icon"),
                translations: data.translations ? data.translations : item.get("translations"),
                items: GameCategoryItemsService.map(data.items, decodeId) || item.get("items")
            };

            try {
                const updatedItem = await item.update(updateData);
                return new GameCategoryImpl(updatedItem);
            } catch (err) {
                if (err instanceof UniqueConstraintError) {
                    return Promise.reject(new Errors.GameCategoryAlreadyExists());
                }
                return Promise.reject(err);
            }
        } finally {
            await LobbyCache.reset();
            await favoriteGamesCache.reset();
        }
    }

    public async delete(id: number) {
        try {
            const gameCategory: GameCategoryDBInstance = await this.findOneInstance(id);
            return gameCategory.destroy();
        } finally {
            await LobbyCache.reset();
            await favoriteGamesCache.reset();
        }
    }

    public async findOneById(id: number): Promise<GameCategoryImpl> {
        const where: WhereOptions<any> = {};

        const entityWithCategories = await GameCategoryService.getEntityIdWithCategories(this.entity, where);

        if (!entityWithCategories) {
            throw new Errors.GameCategoryNotFound();
        }
        const dbItem = await GameCategoryModel.findOne({
            where: {
                brandId: entityWithCategories.id,
                id,
            }
        });

        if (!dbItem) {
            throw new Errors.GameCategoryNotFound();
        }

        return dbItem ? new GameCategoryImpl(dbItem) : null;
    }

    private static validateData(data: GameCategoryCreateData) {
        if ("translations" in data) {
            const errors = validateTranslations(data.translations, "", validateTranslateIconField);
            if (errors.length) {
                throw new Errors.ValidationError(errors);
            }
        }
    }

    private async getLastOrdering() {
        const orderingField = await GameCategoryModel.findOne({
            attributes: ["ordering"],
            where: {
                brandId: this.entity.id,
            },
            limit: 1,
            order: [["ordering", "DESC"]]
        });

        return orderingField ? +orderingField.get("ordering") : null;
    }

    public async move(id: number, newPosition: number): Promise<GameCategoryImpl> {
        await this.validateMoving(newPosition);

        return db.transaction({ isolationLevel: Transaction.ISOLATION_LEVELS.REPEATABLE_READ },
            async (transaction) => {
                const item: GameCategoryDBInstance = await this.findOneInstance(id);

                const currentPosition: number = item.get("ordering");
                let literalToShiftGameCategories: string;
                const whereQueryForOrdering = {};

                if (currentPosition - newPosition > 0) {
                    // move to the up
                    literalToShiftGameCategories = "ordering + 1";
                    whereQueryForOrdering[Op.lt] = currentPosition;
                    whereQueryForOrdering[Op.gte] = newPosition;
                } else {
                    literalToShiftGameCategories = "ordering - 1";
                    whereQueryForOrdering[Op.lte] = newPosition;
                    whereQueryForOrdering[Op.gt] = currentPosition;
                }

                await GameCategoryModel.update({
                    ordering: literal(literalToShiftGameCategories)
                } as any, {
                    where: {
                        brandId: this.entity.id,
                        type: item.get("type"),
                        ordering: whereQueryForOrdering
                    },
                    transaction
                });

                const updatedItem = await item.update({ ordering: newPosition }, { transaction });
                return new GameCategoryImpl(updatedItem);
            })
            .finally(async () => {
                await LobbyCache.reset();
            });

    }

    private async validateMoving(newPosition: number) {
        const errorMessage = "New position is out of range";
        if (typeof newPosition !== "number" || newPosition < 0) {
            throw new Errors.ValidationError(errorMessage);
        }

        if (newPosition > 0) {
            const count = await GameCategoryModel.count({
                where: { brandId: this.entity.id }
            });

            if (newPosition > (count - 1)) {
                throw new Errors.ValidationError(errorMessage);
            }
        }
    }
}

export const gameCategoryCache = new Cache<string, GameCategory[]>(
    "lobby-game-categories",
    GameCategoryService.findAllCache, {
        stdTTL: config.gameCategoriesCache.cache.ttl,
        checkperiod: config.gameCategoriesCache.cache.checkPeriod
    });
