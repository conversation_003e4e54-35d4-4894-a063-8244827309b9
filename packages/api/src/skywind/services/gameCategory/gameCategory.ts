import {
    GameCategory,
    GameCategoryItem,
    GameCategoryTerminalInfo,
    GameCategoryTranslations
} from "../../entities/gamecategory";
import { EntityGame, EntityGameInfo, EntityGameTerminalInfo } from "../../entities/game";
import { GameCategoryDBInstance } from "../../models/gamecategory";
import { BaseEntity } from "../../entities/entity";
import { GameCategoryItemsService } from "./gameCategoryItemsService";
import { encodeId } from "../../utils/publicid";

export class GameCategoryImpl implements GameCategory {
    public id: number;
    public title: string;
    public description: string;
    public brandId: number;
    public status: string;
    public type: string;
    public items: GameCategoryItem[];
    public gamesAmount?: number;
    public games?: EntityGame[];
    public ordering: number;
    public isEntityOwner: boolean;
    public icon: string;
    public translations: GameCategoryTranslations;

    constructor(item?: GameCategoryDBInstance) {
        if (!item) {
            return;
        }

        this.id = item.get("id");
        this.title = item.get("title");
        this.description = item.get("description") || "";
        this.brandId = item.get("brandId");
        this.status = item.get("status");
        this.items = item.get("items");
        this.type = item.get("type");
        this.ordering = item.get("ordering");
        this.icon = item.get("icon") || "";
        this.translations = item.get("translations") || {};
    }

    public toInfo(entity: BaseEntity) {
        const info: GameCategory = {
            id: this.id,
            title: this.title,
            description: this.description,
            brandId: this.brandId,
            status: this.status,
            type: this.type,
            items: GameCategoryItemsService.map(this.items, encodeId),
            ordering: this.ordering,
            isEntityOwner: entity.id === this.brandId,
            icon: this.icon,
            translations: this.translations
        };

        if (this.gamesAmount) {
            info.gamesAmount = this.gamesAmount;
        }

        if (this.games) {
            info.games = this.games;
        }
        return info;
    }

    public toTerminalInfo(entity: BaseEntity, entityGames?: EntityGameInfo[]): GameCategoryTerminalInfo {

        const info: GameCategoryTerminalInfo = {
            id: this.id,
            title: this.title,
            description: this.description,
            brandId: this.brandId,
            status: this.status,
            type: this.type,
            ordering: this.ordering,
            isEntityOwner: entity.id === this.brandId,
            icon: this.icon,
            translations: this.translations
        };

        if (this.gamesAmount) {
            info.gamesAmount = entityGames.length;
        }

        if (entityGames) {
            const gamesInfo: EntityGameTerminalInfo[] = [];

            for (const gameInfo of entityGames) {
                const gameTerminalInfo: EntityGameTerminalInfo = {
                    code: gameInfo.code,
                    title: gameInfo.title,
                    type: gameInfo.type,
                    defaultInfo: gameInfo.defaultInfo,
                    limits: gameInfo.limits,
                    providerTitle: gameInfo.providerTitle,
                    features: gameInfo.features
                };

                if (gameInfo.live) {
                    gameTerminalInfo.live = gameInfo.live;
                }
                gamesInfo.push(gameTerminalInfo);
            }

            info.games = gamesInfo;
        }
        return info;
    }
}
