import { GameCategoryItem, GameCategoryItemFilters } from "../../entities/gamecategory";
import { GAME_CATEGORY_ITEM_TYPE } from "../../utils/common";
import { EntityGame } from "../../entities/game";
import { decodeId } from "../../utils/publicid";

export class GameCategoryItemsService {

    public static map(items: GameCategoryItem[],
                      operation: (id: number | string) => number | string): GameCategoryItem[] {
        if (!Array.isArray(items)) {
            return;
        }
        return items.map(item => {
            const newItem: GameCategoryItem = { type: item.type };
            if (item.type.toLowerCase() === GAME_CATEGORY_ITEM_TYPE.GAME) {
                newItem.id = item.id;
            } else if (item.type.toLowerCase() === GAME_CATEGORY_ITEM_TYPE.INTERSECTION) {
                newItem.items = GameCategoryItemsService.map(item.items, operation);
            } else {
                newItem.id = operation(item.id);
            }
            return newItem;
        });
    }

    public static filterGamesByItems(entityGames: EntityGame[],
                                     items: GameCategoryItem[],
                                     intersection: boolean = false): EntityGame[] {

        let gamesByCategory: EntityGame[] = [];
        const usedGameCodes: { [field: string]: boolean } = {};

        if (!Array.isArray(items) || !items.length) {
            return [];
        }

        for (const item of items) {
            let filteredGames: EntityGame[] = [];
            switch (item.type.toLowerCase()) {
                case GAME_CATEGORY_ITEM_TYPE.GAME:
                    filteredGames = entityGames.filter(eg => eg.game.code === item.id);
                    break;
                case GAME_CATEGORY_ITEM_TYPE.PROVIDER:
                    filteredGames = entityGames.filter(eg => eg.game.gameProvider.id === item.id);
                    break;
                case GAME_CATEGORY_ITEM_TYPE.LABEL:
                    filteredGames = entityGames.filter(
                        eg => (eg.game.labels || []).some(label => decodeId(label.id) === +item.id));
                    break;
                case GAME_CATEGORY_ITEM_TYPE.INTERSECTION:
                    filteredGames = GameCategoryItemsService.filterGamesByItems(entityGames, item.items, true);
                    break;
                default:
            }

            if (intersection) {
                entityGames = filteredGames;
                gamesByCategory = filteredGames;
            } else {
                for (const entityGame of filteredGames) {
                    if (!usedGameCodes[entityGame.gameId]) {
                        gamesByCategory.push(entityGame);
                        usedGameCodes[entityGame.gameId] = true;
                    }
                }
            }
        }

        if (gamesByCategory && gamesByCategory.length && gamesByCategory?.[0]?.index) {
            gamesByCategory.sort((gameA, gameB) => gameA.index - gameB.index);
            gamesByCategory = gamesByCategory.map((game) => {
                delete game.index;
                return game;
            });
        }

        return gamesByCategory;
    }

    public static parseGameCategoryItem(items?: GameCategoryItem[]): GameCategoryItemFilters {

        let gameIds: string[] = [];
        let labelIds: number[] = [];
        let providerIds: number[] = [];

        for (const item of (items || [])) {
            switch (item.type.toLowerCase()) {
                case GAME_CATEGORY_ITEM_TYPE.GAME:
                    gameIds.push(item.id as string);
                    break;
                case GAME_CATEGORY_ITEM_TYPE.LABEL:
                    labelIds.push(decodeId(item.id));
                    break;
                case GAME_CATEGORY_ITEM_TYPE.PROVIDER:
                    providerIds.push(+item.id);
                    break;
                case GAME_CATEGORY_ITEM_TYPE.INTERSECTION:
                    const nestedInfo = GameCategoryItemsService.parseGameCategoryItem(item.items);
                    gameIds = gameIds.concat(nestedInfo.gameIds);
                    labelIds = labelIds.concat(nestedInfo.labelIds);
                    providerIds = providerIds.concat(nestedInfo.providerIds);
                    break;
            }
        }

        return { gameIds, labelIds, providerIds };
    }
}
