import {
    CategoryGamesParams,
    GameCategory,
    GameCategoryInfo,
    GameCategoryItemFilters
} from "../../entities/gamecategory";
import { literal } from "sequelize";
import { EntityGame } from "../../entities/game";
import * as FilterService from "../filter";
import { EntityGameImpl } from "../game";
import { GAME_CATEGORY_TYPE, SORT_ORDER } from "../../utils/common";
import { PagingHelper } from "../../utils/paginghelper";
import { GameCategoryItemsService } from "./gameCategoryItemsService";
import { Cache } from "../../cache/cache";
import config from "../../config";
import { Models } from "../../models/models";

const LabelModel = Models.LabelModel;
const GameProviderModel = Models.GameProviderModel;
const EntityGameModel = Models.EntityGameModel;
const GameModel = Models.GameModel;

export class GameCategoryGamesService {
    private static gameSortableKeys = ["title", "created_at"];
    private static defaultGameSortKey = "created_at";
    private static defaultSortOrder: string = "ASC";

    public static getSortKey(query: CategoryGamesParams = {}): string | undefined {
        return FilterService.getSortKey(
            query,
            GameCategoryGamesService.gameSortableKeys,
            GameCategoryGamesService.defaultGameSortKey,
            "sortBy"
        );
    }

    public static async findAllEntityGames(
        entityId: number,
        gameCodes: string[] = [],
        gameProviderIds: number[] = [],
        labelIds: number[] = [],
        sortBy: string = GameCategoryGamesService.defaultGameSortKey
    ): Promise<EntityGame[]> {
        if (!gameCodes.length && !gameProviderIds.length && !labelIds.length) {
            return [];
        }

        const gameModel = GameModel;
        const entityGameModel = EntityGameModel;
        const modelForOrder = sortBy === GameCategoryGamesService.defaultGameSortKey
            ? entityGameModel
            : gameModel;

        const whereQuery = [];
        if (gameCodes.length) {
            const queryGameCodes = gameCodes.map(code => `'${code}'`);
            whereQuery.push(`"game"."code" IN (${queryGameCodes})`);
        }
        if (gameProviderIds.length) {
            // TODO: "game->gameProvider" field should be generated automatically.
            // TODO: It can be problem after sequelize is updated
            whereQuery.push(`"game->gameProvider"."id" IN (${gameProviderIds})`);
        }
        if (labelIds.length) {
            // TODO: "game->labels" field should be generated automatically.
            // TODO: It can be problem after sequelize is updated
            whereQuery.push(`"game->labels"."id" IN (${labelIds})`);
        }

        const gameInstances = await entityGameModel.findAll({
            where: literal(`entity_id = ${entityId} AND (${whereQuery.join(" OR ")})`) as any,
            order: [[modelForOrder, sortBy, GameCategoryGamesService.defaultSortOrder]],
            include: [
                {
                    model: gameModel,
                    where: {
                        status: "available"
                    },
                    include: [
                        {
                            model: GameProviderModel,
                            required: false,
                            where: {
                                status: "normal"
                            }
                        },
                        {
                            model: LabelModel,
                            required: false
                        }
                    ]
                }
            ]
        });

        return gameInstances.map(item => new EntityGameImpl(item));
    }

    public static async findAllForCategory(entityId: number,
                                           category: GameCategory,
                                           query: CategoryGamesParams = {}): Promise<EntityGame[]> {

        const sortOrder: string = query.sortOrder && query.sortOrder.toUpperCase();
        const hasGameItemSortBy: string = query.sortBy;

        if (!Array.isArray(category.items) || !category.items.length) {
            return [];
        }
        const entityIdToGetGames = category.type === GAME_CATEGORY_TYPE.GAMESTORE
            ? category.brandId
            : entityId;

        const nestedInfo = GameCategoryItemsService.parseGameCategoryItem(category.items);

        let entityGames = await GameCategoryGamesService.findAllEntityGames(
            entityIdToGetGames,
            nestedInfo.gameIds,
            nestedInfo.providerIds,
            nestedInfo.labelIds,
            GameCategoryGamesService.getSortKey(query));

        if (hasGameItemSortBy) {
            entityGames = entityGames.map((game, index) => {
                game.index = index;
                return game;
            });
        }

        const filteredGames = GameCategoryItemsService.filterGamesByItems(entityGames, category.items);
        if (sortOrder === SORT_ORDER.DESC) {
            filteredGames.reverse();
        }

        return PagingHelper.fillInfo(
            filteredGames.slice(query.offset).slice(0, query.limit),
            filteredGames.length,
            { offset: query.offset, limit: query.limit });
    }
}

export class GameCategoriesGamesCache extends Cache<string, EntityGame[]> {

    constructor() {
        super(
            "lobby-games-for-game-categories",
            function (_: string, entityId: number, categories: GameCategoryInfo[] = []) {
                const { gameIds, labelIds, providerIds } = categories
                    .map<GameCategoryItemFilters>(({ items }) => GameCategoryItemsService.parseGameCategoryItem(items))
                    .reduce<GameCategoryItemFilters>((result, data) => {
                        return {
                            gameIds: result.gameIds.concat(data.gameIds),
                            labelIds: result.labelIds.concat(data.labelIds),
                            providerIds: result.providerIds.concat(data.providerIds)
                        };
                    }, { gameIds: [], labelIds: [], providerIds: [] });
                return GameCategoryGamesService.findAllEntityGames(entityId, gameIds, providerIds, labelIds);
            },
            {
                stdTTL: config.gameCategoriesCache.cache.ttl,
                checkperiod: config.gameCategoriesCache.cache.checkPeriod
            }
        );
    }

    public async query(entityId: number, categories: GameCategoryInfo[] = []): Promise<EntityGame[]> {
        const key = `${entityId}-${categories.map<string>(({ id }) => `${id}`).join(",")}`;
        return super.find(key, entityId, categories);
    }
}

export class FavoriteGamesCache extends Cache<string, EntityGame[]> {
    public static getKey(entityId: number, playerCode: string): string {
        return `${entityId}:${playerCode}`;
    }

    constructor() {
        super(
            "favorite-games-for-lobby",
            function (_: string,
                      entityId: number,
                      query: CategoryGamesParams = {},
                      gameIds: string[] = [],
                      providerIds: number[] = [],
                      labelIds: number[] = []): Promise<EntityGame[]> {
                return GameCategoryGamesService.findAllEntityGames(
                    entityId,
                    gameIds,
                    providerIds,
                    labelIds
                );
            },
            {
                stdTTL: config.favoriteGamesCache.cache.ttl,
                checkperiod: config.favoriteGamesCache.cache.checkPeriod
            }
        );
    }
}

export const gameCategoryGamesCache = new GameCategoriesGamesCache();
export const favoriteGamesCache = new FavoriteGamesCache();
