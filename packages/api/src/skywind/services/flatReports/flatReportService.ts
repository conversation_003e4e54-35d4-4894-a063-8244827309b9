import { CRUDServiceImpl } from "../crudService";
import { BaseEntity } from "../../entities/entity";
import { FindOptions, literal, Transaction } from "sequelize";
import * as Errors from "../../errors";
import * as FilterService from "../filter";
import { FlatReportDBInstance, FlatReportModel, getFlatReportModel } from "../../models/flatReports";
import { FlatReport } from "../../entities/flatReport";

const DEFAULT_SORT_KEY = "reportType";

export const sortableKeys = [
    "entityId",
    "reportType"
];

export const queryParamsKeys = [
    "reportType"
];

const FlatReportModel = getFlatReportModel();

export class FlatReportService extends CRUDServiceImpl<FlatReportDBInstance, FlatReport, FlatReportModel> {

    public getModel(): FlatReportModel {
        return FlatReportModel;
    }

    constructor(public entity: BaseEntity) {
        super();
    }

    protected async performCreate(cleanedData: FlatReport,
                                  transaction: Transaction): Promise<FlatReportDBInstance> {

        cleanedData.entityId = this.entity.id;

        try {
            return super.performCreate(cleanedData, transaction);
        } catch (err) {
            throw new Errors.FlatReportAlreadyExists();
        }
    }

    protected async performDestroy(instance: FlatReportDBInstance,
                                   transaction: Transaction): Promise<void> {

        if (instance.entityId !== this.entity.id) {
            throw new Errors.ValidationError("Flat report belongs to another entity");
        }

        await super.performDestroy(instance, transaction);
    }

    protected async performUpdate(instance: FlatReportDBInstance,
                                  cleanedData: Partial<FlatReport>,
                                  transaction: Transaction): Promise<FlatReportDBInstance> {

        if (instance.entityId !== this.entity.id) {
            throw new Errors.ValidationError("Flat report belongs to another entity");
        }

        try {
            return super.performUpdate(instance, cleanedData, transaction);
        } catch (err) {
            throw new Errors.FlatReportAlreadyExists();
        }
    }

    public async list(options?: FindOptions<any>): Promise<FlatReportDBInstance[]> {

        if (options?.where) {
            const sortBy = FilterService.getSortKey(options.where, sortableKeys, DEFAULT_SORT_KEY);
            const sortOrder = FilterService.valueFromQuery(options.where, "sortOrder") || "ASC";

            options.order = literal(`"${sortBy}" ${sortOrder}`);
        }

        return super.list(options);
    }
}

export const getFlatReportService = (entity) => new FlatReportService(entity);
