import { BaseEntity } from "../../entities/entity";
import { BrandEntity } from "../../entities/brand";
import { getChildEntities, loadHierarchy } from "../entity";
import { FLAT_REPORT_TYPE } from "../../entities/flatReport";
import * as FlatReportServiceDB from "../../models/flatReports";
import { getEntitySettings } from "../settings";

import { logging, lazy, Lazy } from "@skywind-group/sw-utils";
import { FlatReportsStorage } from "./flatReportsStorage";

const log = logging.logger("flat-report");

export interface FlatReportOptions {
    forAllEntities?: boolean;
    withChildren?: boolean;
    limit?: number;
}

export interface FlatReport {
    build(entityIds?: number[]): Promise<void>;
}

export abstract class AbstractBaseFlatReport implements FlatReport {

    protected constructor(
        protected type: FLAT_REPORT_TYPE,
        protected entityTypes: string[] = [],
        protected forAllEntities: boolean = false,
        protected withChildren = false
    ) {}

    protected async buildReports(entities: BrandEntity[]): Promise<number> {
        let updated = 0;
        for (const entity of entities) {
            const settings = await getEntitySettings(entity.path);
            if (!settings.flatReportsEnabled) {
                continue;
            }
            const report = await this.buildReport(entity);
            await FlatReportServiceDB.upsertFlatReport({
                entityId: entity.id,
                reportType: this.type,
                report: JSON.stringify(report),
                createdAt: new Date(),
                updatedAt: new Date()
            });
            updated++;
        }
        return updated;
    }

    public abstract build(entityIds?: number[]): Promise<void>;

    protected abstract buildReport(entity: BaseEntity): Promise<any>;

    protected abstract logUpdated(updated: number, data?: any);
}

export abstract class AbstractFlatReport extends AbstractBaseFlatReport {

    protected forAllEntities: boolean;
    protected withChildren: boolean;

    protected constructor(type: FLAT_REPORT_TYPE, entityTypes: string[] = [], options: FlatReportOptions) {
        super(type, entityTypes);
        this.forAllEntities = options.forAllEntities;
        this.withChildren = options.withChildren;
    }

    protected async getEntities(entityIds: number[]): Promise<BrandEntity[]> {
        if (!entityIds && !this.forAllEntities) {
            return [];
        }
        const allEntities = await getEntities(this.entityTypes);
        if (entityIds && entityIds.length) {
            const entities = allEntities.filter(e => entityIds.includes(e.id));
            return this.withChildren ?
                   entities.reduce((previous, current) => [...previous, current, ...getChildEntities(current)], []) :
                   entities;
        } else {
            return allEntities;
        }
    }

    public async build(entityIds: number[]): Promise<void> {
        try {
            const entities = await this.getEntities(entityIds);
            const updated = await this.buildReports(entities);
            this.logUpdated(updated);
        } catch (err) {
            log.error(err, "Error on building and saving flat report");
        }
    }

    protected logUpdated(updated: number, data?: any) {
        if (updated) {
            log.info(`FlatReport with ${this.type} type updated ${updated} entities`);
        }
    }
}

export abstract class AbstractNotifiedFlatReport extends AbstractBaseFlatReport {

    private allEntities: Lazy<Promise<BrandEntity[]>>;
    private readonly limit: number;

    protected constructor(type: FLAT_REPORT_TYPE, entityTypes: string[] = [], options: FlatReportOptions) {
        super(type, entityTypes);
        this.allEntities = lazy(async () => getEntities(this.entityTypes));
        this.limit = options.limit;
    }

    public async build(): Promise<void> {
        try {
            let nextEntityId;
            let currentLimit = this.limit;
            let updated = 0;
            const updatedEntityIds = [];
            do {
               nextEntityId = await this.getNotifiedEntityId();
               if (nextEntityId) {
                   const entities = await this.getEntities(await this.allEntities.get(), [nextEntityId]);
                   updated += await this.buildReports(entities);
                   updatedEntityIds.push(nextEntityId);
               }
               --currentLimit;
            } while (nextEntityId && currentLimit > 0);
            this.logUpdated(updated, { entityIds: updatedEntityIds });
        } catch (err) {
            log.error(err, "Error on building and saving flat report");
        }
    }

    protected logUpdated(updated: number, data?: any) {
        if (updated) {
            log.info(data, `NotifiedFlatReport with ${this.type} type updated ${updated} entities`);
        }
    }

    protected async getEntities(allEntities: BrandEntity[], entityIds: number[]) {
        if (entityIds && entityIds.length) {
            const entities = allEntities.filter(e => entityIds.includes(e.id));
            return entities.reduce((previous, current) => [...previous, current, ...getChildEntities(current)], []);
        } else {
            return [];
        }
    }

    private async getNotifiedEntityId() {
        const storage = new FlatReportsStorage(this.type);
        return storage.retrieve();
    }

    protected abstract buildReport(entity: BaseEntity): Promise<any>;
}

export function createFlatStructure(rootEntity): BaseEntity[] {
    const asEntity = rootEntity;
    let children = [];
    if (asEntity.child) {
        for (const child of asEntity.child) {
            children = [...children, ...createFlatStructure(child)];
        }
    }
    return [...children, asEntity];
}

export async function getEntities(types: string[]): Promise<BrandEntity[]> {
    const root = await loadHierarchy();
    const include = (e: BaseEntity) => types && types.length ? types.indexOf(e.type) >= 0 : true;
    return createFlatStructure(root).filter(e => include(e)).map(e => e as BrandEntity);
}
