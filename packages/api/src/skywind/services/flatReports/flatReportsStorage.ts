import * as redisClient from "../../storage/redis";
import { FLAT_REPORT_TYPE as FlatReportType } from "../../entities/flatReport";

export class FlatReportsStorage {

    private readonly globalFlatReportsKey: string = "sw-management-api:flat-reports";
    private readonly key;

    constructor(private flatReportType: FlatReportType) {
        this.key = `${this.globalFlatReportsKey}:${flatReportType}`;
    }

    public save(entityIds: number[]): Promise<void> {
        return redisClient.usingDb(async (db) => {
            db.sadd(this.key, ...entityIds);
        });
    }

    public retrieve(): Promise<number> {
        return redisClient.usingDb(async (db) => {
            const result = await db.spop(this.key);
            return result ? +result : null;
        });
    }
}
