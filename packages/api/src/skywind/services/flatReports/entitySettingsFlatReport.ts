import { AbstractFlatReport, AbstractNotifiedFlatReport, FlatReportOptions } from "./flatReport";
import { BaseEntity } from "../../entities/entity";
import { FLAT_REPORT_TYPE } from "../../entities/flatReport";
import { getEntitySettings } from "../settings";
import { EntitySettings } from "../../entities/settings";
import { HIDDEN_PASSWORD } from "../../utils/common";

export class EntitySettingsFlatReport extends AbstractFlatReport {

    constructor(options: FlatReportOptions = {}) {
        super(FLAT_REPORT_TYPE.ES, [], options);
        this.constructor.name;
    }

    protected async buildReport(entity: BaseEntity): Promise<any> {
        const entitySettings = await getEntitySettings(entity.path);
        maskSensitiveData(entitySettings);
        return entitySettings;
    }

}

export class EntitySettingsNotifiedFlatReport extends AbstractNotifiedFlatReport {

    constructor(options: FlatReportOptions = {}) {
        super(FLAT_REPORT_TYPE.ES, [], options);
    }

    protected async buildReport(entity: BaseEntity): Promise<any> {
        const entitySettings = await getEntitySettings(entity.path);
        maskSensitiveData(entitySettings);
        return entitySettings;
    }

}

function maskSensitiveData(settings: EntitySettings & { smtp?: { auth: { user: string; pass: string; } } }) {
    const auth = settings?.smtp?.auth;
    if (auth) {
        auth.user = HIDDEN_PASSWORD;
        auth.pass = HIDDEN_PASSWORD;
    }
}
