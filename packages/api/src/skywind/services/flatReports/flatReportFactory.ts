import { FLAT_REPORT_TYPE as FlatReportType } from "../../entities/flatReport";
import { FlatReport, FlatReportOptions } from "./flatReport";
import { EntitySettingsFlatReport, EntitySettingsNotifiedFlatReport } from "./entitySettingsFlatReport";
import { NewLimitsFlatReport, OldLimitsFlatReport } from "./limitsFlatReport";
import { lazy } from "@skywind-group/sw-utils";

const factory = lazy(() => new FlatReportFactoryImpl());

export function getFlatReportFactory(): FlatReportFactory {
    return factory.get();
}

export interface FlatReportFactory {
    createFlatReport(type: FlatReportType, options: FlatReportOptions): FlatReport;
    createNotifiedFlatReport(type: FlatReportType, options: FlatReportOptions): FlatReport;
}

export class FlatReportFactoryImpl implements FlatReportFactory {

    public createFlatReport(type: FlatReportType, options: FlatReportOptions): FlatReport {
        switch (type) {
            case FlatReportType.ES:
                return new EntitySettingsFlatReport(options);
            case FlatReportType.OLS:
                return new OldLimitsFlatReport(options);
            case FlatReportType.NLS:
                return new NewLimitsFlatReport(options);
            default:
                throw new Error("Unsupported FlatReportType");
        }
    }

    public createNotifiedFlatReport(type: FlatReportType, options: FlatReportOptions): FlatReport {
        if (type === FlatReportType.ES) {
            return new EntitySettingsNotifiedFlatReport(options);
        } else {
            throw new Error("Unsupported FlatReportType");
        }
    }
}
