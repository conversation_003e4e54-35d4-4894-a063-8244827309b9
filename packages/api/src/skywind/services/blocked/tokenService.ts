import * as redis from "../../storage/redis";
import * as Errors from "../../errors";

const KEY_BASE: string = "sw-management-api";
export interface TokenService {
    lockToken(token: string): Promise<void>;
    unlockToken(): Promise<number>;
    tokenExists(token: string): Promise<void>;
}

class TokenServiceImpl implements TokenService {
    constructor(protected entityId: number, protected type: string) {

    }

    public async lockToken(token: string): Promise<void> {
        return redis.usingDb<void>(async (db) => {
            const key = this.getKeyName();
            const isBlocked = await db.get(key);
            if (!!isBlocked) {
                throw new Errors.TokenAlreadyBlocked(this.type);
            }

            await db.set(key, token);
        });
    }

    public async unlockToken(): Promise<number> {
        return redis.usingDb<number>(async (db) => await db.del(this.getKeyName()));
    }

    public async tokenExists(token: string): Promise<void> {
        return redis.usingDb<void>(async (db) => {
            const blockedToken = await db.get(this.getKeyName());
            if (!!blockedToken && blockedToken === token) {
                throw new Errors.TokenBlocked(this.type);
            }
        });
    }

    protected getKeyName() {
        return `${KEY_BASE}:${this.entityId}:${this.type}:token`;
    }
}

export function getTokenService(entityId: number, type: string = "terminal") {
    return new TokenServiceImpl(entityId, type);
}
