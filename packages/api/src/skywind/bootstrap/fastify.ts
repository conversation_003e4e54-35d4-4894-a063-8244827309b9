import { FastifyInstance, FastifyReply, FastifyRequest, fastify } from "fastify";
import compression from "@fastify/compress";
import * as cookie from "@fastify/cookie";
import * as methodOverride from "method-override";
import { buildLogData, findIp, getRequestInfoFomRequest } from "../utils/requestHelper";
import { IpHolder } from "../services/security";
import { ApiNotFoundError } from "../errors";
import Translation from "@skywind-group/sw-management-i18n";
import { ILogger, default as logger } from "../utils/logger";
import { measureProvider } from "../utils/measures";
import { getVersion } from "../utils/version";
import { initDB, isMemoryMonitoring, isPrometheusMonitoring, setUpServerName } from "./common";
import config from "../config";

export interface WithCookies {
    cookies: any;
}

export type Request = WithCookies & IpHolder;
export type Response = FastifyReply;

export function create(): FastifyInstance {
    const app: FastifyInstance = fastify({
        bodyLimit: config.bodyParserJsonLimit
    });

    app.register(cookie);
    app.register(compression, { threshold: config.compressionThreshold });
    app.register(methodOverride());

    app.addHook("onRequest", async (req, reply) => {
        Translation.middleware(req, reply, undefined);
    });

    setUpMetricHandlers(app);

    return app;
}
export function setUpMetricHandlers(app: FastifyInstance): void {
    if (isPrometheusMonitoring()) {
        app.get("/metrics", async (req: FastifyRequest & Request, res: Response) => {
            res.code(200).type("text/plain").send(await measureProvider.getMeasuresStream());
        });
    } else if (isMemoryMonitoring()) {
        app.register(require("../api/measures").default, { prefix: "/v1" });
    }
}

export function createRequestLogger(logger: ILogger) {
    return async (req: FastifyRequest & Request, res: FastifyReply): Promise<void> => {
        if (req.url.endsWith("version") || req.url.endsWith("health")) {
            return;
        }
        const reqData = buildLogData(req.url, req.method, req.query, req.body, getRequestInfoFomRequest(req));
        logger.info({ reqData: reqData }, "Http request");
    };
}

export async function addHeaderCacheControl(req: FastifyRequest & Request, res: FastifyReply): Promise<void> {
    if (req.method === "GET") {
        res.header("Cache-Control", "no-cache, max-age=0");
    }
}

export function resolveIp(req: FastifyRequest & Request, res: Response, next) {
    req.resolvedIp = findIp(req);
    next();
}

export function notFoundHandler(req: FastifyRequest & Request, res: Response) {
    const error = new ApiNotFoundError();
    res.code(error.responseStatus).send({
        code: error.code,
        message: error.message,
    });
}

export async function startFastifyServer(app: FastifyInstance,
                                         name: string,
                                         port: number): Promise<FastifyInstance> {

    const log = logger("start-up");
    setUpServerName(name);
    log.info("Server is starting");

    if (!config.disableDbInitOnServerStart) {
        await initDB();
    }
    const version = getVersion("/../");
    return new Promise<FastifyInstance>((resolve, reject) => {
        app.server.timeout = config.server.timeout;
        app.server.keepAliveTimeout = config.keepAliveTimeout;

        app.listen({ port, host: "::" }, (err)=> {
            if (err) {
                return reject(err);
            }
            log.info(`${name} API listening on: ${port}`);
            log.info(`${name} API AppVersion: ${version}`);

            resolve(app);
        });
    });
}
