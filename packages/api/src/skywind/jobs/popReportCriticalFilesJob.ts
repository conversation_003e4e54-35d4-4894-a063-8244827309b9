import { logging, measures } from "@skywind-group/sw-utils";

import measureProvider = measures.measureProvider;

import { MerchantImpl } from "../services/merchant";
import { Models } from "../models/models";
import { MERCHANT_POP_TYPES } from "@skywind-group/sw-management-adapters";
import { CriticalFilesService } from "../services/criticalfiles/criticalFilesService";
import { ReportCriticalFilesToMerchantRequest } from "@skywind-group/sw-wallet-adapter-core";
import config from "../config";
import { Op } from "sequelize";

const log = logging.logger("report-critical-files-job");

export class POPReportCriticalFilesJob {

    private gameCriticalFilesService: CriticalFilesService;

    constructor() {
        this.gameCriticalFilesService = new CriticalFilesService();
    }

    public async fire() {
        await measureProvider.runInTransaction("Report critical files", async () => {
            try {
                await this.doWork();
            } catch (err) {
                measureProvider.saveError(err);
                log.error(err, "Error reporting critical files");
            }
        });
    }

    public async doWork(): Promise<void> {
        log.info("Started POPReportCriticalFilesJob");
        const merchants = await this.getPOPMerchants();

        for (const merchant of merchants) {
            try {
                if (merchant.isTest) {
                    continue;
                }

                const regulation = merchant.params?.regulatorySettings?.merchantRegulation;
                if (!config.reportPOPCriticalFilesJob.regulations.includes(regulation)) {
                    continue;
                }

                log.info({ code: merchant.code, type: merchant.type }, "Processing critical files for merchant");
                const gamesResponse = await this.gameCriticalFilesService
                    .getGameCriticalFilesListWithHashing(merchant, regulation);

                const adapter = await merchant.getAdapter();
                if (gamesResponse && gamesResponse.games && gamesResponse.games.length) {
                    const request: ReportCriticalFilesToMerchantRequest = {
                        games: gamesResponse.games
                    };
                    await adapter.reportCriticalFiles(merchant.toInfo(), request);
                }

                const platformResponse = await this.gameCriticalFilesService
                    .getPlatformCriticalFilesListWithHashing(merchant, regulation);

                if (platformResponse && platformResponse.modules && platformResponse.modules.length) {
                    const request: ReportCriticalFilesToMerchantRequest = {
                        modules: platformResponse.modules
                    };
                    await adapter.reportCriticalFiles(merchant.toInfo(), request);
                }
            } catch (err) {
                log.error(err, "Error reporting critical files for pop merchant");
            }
        }
    }

    private async getPOPMerchants() {
        const items = await Models.MerchantModel.findAll({
            where: {
                type: {
                    [Op.in]: MERCHANT_POP_TYPES
                }
            }
        });

        return items.map(c => new MerchantImpl(c));
    }
}
