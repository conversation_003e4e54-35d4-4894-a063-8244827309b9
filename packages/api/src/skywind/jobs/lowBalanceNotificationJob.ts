import { logging } from "@skywind-group/sw-utils";
import { EntityInfoService } from "../services/entityInfo";
import EntityCache from "../cache/entity";
import { getDefaultEmailService } from "../utils/emails";

const log = logging.logger("low-balance-notifications-job");

export async function lowBalanceNotificationsJob() {
    log.info("start low balance notification job");
    try {
        const entitiesForNotifications = await EntityInfoService.getEntitiesInfoForLowBalanceNotification();
        for await (const entityInfo of entitiesForNotifications) {
            const entity = await EntityCache.findOne({ id: entityInfo.entityId }, undefined, false);
            if (!entity) {
                log.error(`entity with id ${entityInfo.entityId} not found`);
                return;
            }
            const currencies = entityInfo.data.lowBalance?.currencies;
            if (!currencies) {
                log.error(`currencies for entity with id ${entityInfo.entityId} not found`);
                return;
            }
            const emails = entityInfo.data.lowBalance?.emails;
            if (!emails || !Array.isArray(emails)) {
                log.error(`wrong emails ${emails} for entity with id ${entityInfo.entityId}`);
                return;
            }
            for await (const currency of Object.keys(currencies)) {
                const balance = await entity.fetchBalance(currency);
                const minBalanceForNotification = currencies[currency].min;
                const isNumber = typeof minBalanceForNotification === "number" && isFinite(minBalanceForNotification);
                if (!isNumber) {
                    log.error(`min balance is not number for entity with id ${entityInfo.entityId} and currency ${currency}`);
                }
                if (balance.main < minBalanceForNotification) {
                    const service = getDefaultEmailService();
                    await service.sendEmail(emails, {
                        fromEmail: service.getFromEmail(),
                        fromName: "skywind",
                        subject: "Low balance notification",
                        htmlPart: `Please be aware this entity’s balance has dropped below the recommended value.
                            <br>
                            <br>Entity name: ${entity.title}
                            <br>Current balance: ${balance.main}
                            <br>Currency: ${currency}`
                    });
                }
            }
        }
    } catch (e) {
        log.error(e, "error low balance notification hob");
    }
}
