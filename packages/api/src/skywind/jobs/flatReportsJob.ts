import { logging, measures } from "@skywind-group/sw-utils";
import measureProvider = measures.measureProvider;
import config from "../config";
import { FLAT_REPORT_TYPE as FlatReportType } from "../entities/flatReport";
import { FlatReportOptions } from "../services/flatReports/flatReport";
import { getFlatReportFactory } from "../services/flatReports/flatReportFactory";

const log = logging.logger("flat-reports-job");

abstract class AbstractBaseFlatReportJob {

    protected constructor(
        protected name,
        protected flatReportTypes: string[],
        protected options: FlatReportOptions) {
    }

    public async fire() {
        await measureProvider.runInTransaction("Flat reports", async () => {
            log.info(`Started ${this.name}`);
            try {
                await this.doWork();
            } catch (err) {
                measureProvider.saveError(err);
                log.error(err, "Error on creating flat reports");
            }
            log.info(`Finished ${this.name}`);
        });
    }

    protected abstract doWork(): Promise<void>;
}

abstract class AbstractFlatReportJob extends AbstractBaseFlatReportJob {

    protected constructor(name: string, flatReportTypes: string[], options?: FlatReportOptions) {
        super(name, flatReportTypes, options);
    }

    protected async doWork(): Promise<void> {
        const flatReportTypes = this.flatReportTypes;
        for (const type of flatReportTypes) {
            const entityIds = await this.getEntityIds(type);
            await getFlatReportFactory().createFlatReport(type as FlatReportType, this.options).build(entityIds);
        }
    }

    public abstract getEntityIds(type: string);
}

export class FlatReportsJob extends AbstractFlatReportJob {

    constructor() {
        super("FlatReportsJob", config.flatReports.job.types, {
            forAllEntities: true,
            withChildren: config.flatReports.job.withChildren,
        });
    }

    public async getEntityIds(type: string) {
        return config.flatReports.job.entityIds;
    }
}

export class NotifiedFlatReportsJob extends AbstractBaseFlatReportJob {

    constructor() {
        super("NotifiedFlatReportsJob", config.flatReports.notifiedJob.types, {
            limit: config.flatReports.notifiedJob.limit,
        });
    }

    protected async doWork(): Promise<void> {
        const flatReportTypes = this.flatReportTypes;
        for (const type of flatReportTypes) {
            await getFlatReportFactory().createNotifiedFlatReport(type as FlatReportType, this.options).build();
        }
    }
}
