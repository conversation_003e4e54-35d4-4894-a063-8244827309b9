import config from "../config";
import { POPReportCriticalFilesJob } from "./popReportCriticalFilesJob";
import { CronJob } from "../utils/cronJob";
import { lowBalanceNotificationsJob } from "./lowBalanceNotificationJob";
import { FlatReportsJob, NotifiedFlatReportsJob } from "./flatReportsJob";

const reportCriticalFilesJob = new POPReportCriticalFilesJob();

let jobPop: CronJob;

export async function initReportingJobForPOP(): Promise<void> {
    if (!jobPop) {
        jobPop = new CronJob({
            name: "report-critical-files",
            schedule: config.reportPOPCriticalFilesJob.schedule,
            timeout: config.reportPOPCriticalFilesJob.timeout
        }, reportCriticalFilesJob.fire.bind(reportCriticalFilesJob));

        if (config.reportPOPCriticalFilesJob.runOnServerStart) {
            jobPop.invoke.bind(reportCriticalFilesJob);
            await jobPop.invoke();
        }
    }
}

let lowBalanceJob: CronJob;

export async function initLowBalanceNotificationsJob(): Promise<void> {
    if (!lowBalanceJob) {
        lowBalanceJob = new CronJob({
            name: "low-balance-notifications-job",
            schedule: config.lowBalanceNotificationsJob.schedule,
            timeout: config.lowBalanceNotificationsJob.timeout,
        }, lowBalanceNotificationsJob);
    }
}

const flatReportsJob = new FlatReportsJob();

let flatReportsCronJob: CronJob;

export async function initFlatReportsJob(): Promise<void> {
    if (!flatReportsCronJob) {

        flatReportsCronJob = new CronJob({
            name: "flat-reports",
            schedule: config.flatReports.job.schedule,
            timeout: config.flatReports.job.timeout
        }, flatReportsJob.fire.bind(flatReportsJob));

        if (config.flatReports.job.runOnServerStart) {
            flatReportsCronJob.invoke.bind(flatReportsJob);
            await flatReportsCronJob.invoke();
        }
    }
}

const notifiedFlatReportsJob = new NotifiedFlatReportsJob();

let notifiedFlatReportsCronJob: CronJob;

export async function initNotifiedFlatReportsJob(): Promise<void> {
    if (!notifiedFlatReportsCronJob) {

        notifiedFlatReportsCronJob = new CronJob({
            name: "notified-flat-reports",
            schedule: config.flatReports.notifiedJob.schedule,
            timeout: config.flatReports.notifiedJob.timeout
        }, notifiedFlatReportsJob.fire.bind(notifiedFlatReportsJob));

        if (config.flatReports.notifiedJob.runOnServerStart) {
            notifiedFlatReportsCronJob.invoke.bind(notifiedFlatReportsJob);
            await notifiedFlatReportsCronJob.invoke();
        }
    }
}
