/**
 * This API is deprecated.
 * TODO: Remove when BO stops using it.
 */

import * as express from "express";
import { authenticate, authorize, decodePid, getBrand, validate } from "../middleware/middleware";
import { KeyEntityHolder } from "../../services/security";
import { PlayerPromotionService } from "../../services/promotions/playerPromotionService";

const router: express.Router = express.Router();

type Request = express.Request & KeyEntityHolder;

const validateBonusCoinRequest = validate({
    playerCode: { notEmpty: true, isPlayerCode: true },
    promoId: { notEmpty: true }
});

async function addBonusCoinsToPlayer(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const balance = await PlayerPromotionService.addPromotionToPlayers(entity,
            req.params.promoId,
            [req.params.playerCode]);
        res.send(balance);
        next();
    } catch (err) {
        next(err);
    }
}

const validateBonusCoinProlongRequest = validate({
    playerCode: { notEmpty: true, isPlayerCode: true },
    promoId: { notEmpty: true },
    amount: { optional: true, isInt: { options: { strict: true } } },
    expirationPeriod: { optional: true, isInt: { options: { strict: true } } },
    expirationPeriodType: { optional: true }
});

async function prolongBonusCoinsForPlayer(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const balance = await PlayerPromotionService.updatePlayerPromotion(entity,
            req.params.playerCode,
            req.params.promoId,
            req.body);
        res.send(balance);
        next();
    } catch (err) {
        next(err);
    }
}

async function addBonusCoinsToPlayersGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const filters = Object.keys(req.query).length ? req.query : req.body;
        const promo = await PlayerPromotionService.addPromotionToPlayersByFilter(
            entity,
            req.params.promoId,
            filters);
        res.send(promo);
        next();
    } catch (err) {
        next(err);
    }
}

router.put("/players/:playerCode/bonuscoin/:promoId", authenticate, authorize,
    validateBonusCoinRequest, decodePid(), addBonusCoinsToPlayer);
router.put("/entities/:path/players/:playerCode/bonuscoin/:promoId",
    authenticate, authorize, validateBonusCoinRequest, decodePid(), addBonusCoinsToPlayer);
router.put("/players/:playerCode/bonuscoin/:promoId/prolong", authenticate, authorize,
    validateBonusCoinProlongRequest, decodePid(), prolongBonusCoinsForPlayer);
router.put("/entities/:path/players/:playerCode/bonuscoin/:promoId/prolong",
    authenticate, authorize, validateBonusCoinProlongRequest, decodePid(), prolongBonusCoinsForPlayer);
router.put("/promo/bonuscoin/:promoId/players/group",
    authenticate, authorize, decodePid(), addBonusCoinsToPlayersGroup);
router.put("/entities/:path/promo/bonuscoin/:promoId/players/group",
    authenticate, authorize, decodePid(), addBonusCoinsToPlayersGroup);

export default router;
