/**
 * This API is deprecated.
 * TODO: Remove when BO stops using it.
 */

import * as express from "express";
import { authenticate, authorize, decodePid, getBrand, validate } from "../middleware/middleware";
import { KeyEntityHolder } from "../../services/security";
import { PlayerPromotionService } from "../../services/promotions/playerPromotionService";
import { validatePlayerCodeForOperator } from "../playerAuthForBrands";
import { prepareScheme } from "../../services/filter";

const router: express.Router = express.Router();

type Request = express.Request & KeyEntityHolder;

export async function addFreebetsToPlayerFromPromo(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const promo = await PlayerPromotionService.addPromotionToPlayer(entity,
            req.params.promoId, req.params.playerCode);
        res.send(promo);
        next();
    } catch (err) {
        next(err);
    }
}

export async function addFreebetsToPlayers(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = await getBrand(req);
        const filters = Object.keys(req.query).length ? req.query : req.body;
        const promo = await PlayerPromotionService.addPromotionToPlayersByFilter(
            entity,
            req.params.promoId,
            filters);
        res.send(promo);
        next();
    } catch (err) {
        next(err);
    }
}

export const validateBulkCreate = validate(prepareScheme(["status", "createdAt", "updatedAt", "lastLogin"]));

router.put("/players/:playerCode/freebet/:promoId", authenticate, authorize, decodePid(), addFreebetsToPlayerFromPromo);
router.put("/entities/:path/players/:playerCode/freebet/:promoId",
    authenticate, authorize, validatePlayerCodeForOperator, decodePid(), addFreebetsToPlayerFromPromo);
router.put("/entities/:path/promo/freebet/:promoId/players/group",
    authenticate, authorize, decodePid(), validateBulkCreate, addFreebetsToPlayers);
router.put("/promo/freebet/:promoId/players/group",
    authenticate, authorize, decodePid(), validateBulkCreate, addFreebetsToPlayers);

export default router;
