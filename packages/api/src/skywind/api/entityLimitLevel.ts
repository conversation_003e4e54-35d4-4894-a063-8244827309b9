import * as express from "express";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    getEntity,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { getGameLimitLevelService } from "../services/gameLimits/entityLimitLevels";
import { parseFilter } from "../services/filter";

const router: express.Router = express.Router();

const validateGameLimitLevels = validate({
    gameCode: { notEmpty: true, isString: true },
    levelId: { notEmpty: true },
    currency: { optional: true, isString: true },
    isDefault: { optional: true, isBoolean: true },
    hidden: { optional: true, isBoolean: true }
});

router.get("/entities/:path/game-limit-levels",
    authenticate,
    authorize,
    validate({ gameCode: { optional: true, isString: true } }),
    getGameLimitLevelHandler);

router.get("/game-limit-levels",
    authenticate,
    authorize,
    validate({ gameCode: { optional: true, isString: true } }),
    getGameLimitLevelHandler);

router.post("/entities/:path/game-limit-levels",
    authenticate,
    authorize,
    auditable,
    decodePid(),
    validateGameLimitLevels,
    createGameLimitLevelHandler);

router.post("/game-limit-levels",
    authenticate,
    authorize,
    auditable,
    decodePid(),
    validateGameLimitLevels,
    createGameLimitLevelHandler);

router.delete("/entities/:path/game-limit-levels/:id",
    authenticate,
    authorize,
    auditable,
    decodePid(),
    deleteGameLimitLevelHandler);

router.delete("/game-limit-levels/:id",
    authenticate,
    authorize,
    auditable,
    decodePid(),
    deleteGameLimitLevelHandler);

async function createGameLimitLevelHandler(req: express.Request & KeyEntityHolder,
                                           res: express.Response,
                                           next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const instance = await getGameLimitLevelService(entity).create(req.body);

        res.send({ ...instance.toInfo(), gameCode: req.body.gameCode });
    } catch (err) {
        next(err);
    }
}

async function getGameLimitLevelHandler(req: express.Request & KeyEntityHolder,
                                        res: express.Response,
                                        next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const service = getGameLimitLevelService(entity);
        const results = await service.getGameLimitLevels(parseFilter(req.query, ["gameCode"]));

        res.send(results);
    } catch (err) {
        next(err);
    }
}

async function deleteGameLimitLevelHandler(req: express.Request & KeyEntityHolder,
                                           res: express.Response,
                                           next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        await getGameLimitLevelService(entity).destroy(req.params.id);

        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

export default router;
