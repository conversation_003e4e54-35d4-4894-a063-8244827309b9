import { Router } from "express";
import { auditLogin, finishAuditSession, loginRefreshUser, loginUser, logoutUser, validateLoginUser } from "./login";
import {
    auditable,
    authenticate, authenticateRefresh,
    authorize,
    decodePid,
    defineLimits,
    getEntityPath,
    validate
} from "./middleware/middleware";
import { prepareScheme } from "../services/filter";
import * as express from "express";
import { EntityGameInfo } from "../entities/game";
import * as GameService from "../services/game";
import { customDefineLimits } from "./entityGame";
import { IsLiveGameHolder, KeyEntityHolder, PermissionsHolder } from "../services/security";
import { getShortStructure, getUser, getUserValidator } from "./keyentity";
import { getEntitySettings } from "./settings";

const mung = require("express-mung");

type Request = express.Request & KeyEntityHolder & IsLiveGameHolder & PermissionsHolder;

const router: Router = Router();

router.post("/login",
    validateLoginUser,
    mung.json(auditLogin),
    loginUser);

router.post("/login/refresh",
    authenticateRefresh,
    auditable,
    loginRefreshUser);

router.post("/logout",
    authenticate,
    auditable,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["userId"] }),
    mung.headers(finishAuditSession),
    logoutUser);

router.get("/entities/:path/users", authenticate, authorize, defineLimits, getUserValidator, getUser);

router.get(["/entities/:path/short-structure", "/short-structure"],
    authenticate,
    authorize,
    getShortStructure);

router.get("/entities/:path/settings", authenticate, authorize, decodePid(), getEntitySettings);

router.get("/games",
    authenticate,
    authorize,
    decodePid(),
    customDefineLimits,
    validate(prepareScheme(["limit", "offset", "sortOrder", "currency"])),
    validate({ jpCurrency: { optional: true, isCurrency: true } }),
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const gameInfoList: EntityGameInfo[] = await GameService.getAllGames(
                req.keyEntity,
                null,
                req.query,
                req.baseUrl
            );
            res.send(gameInfoList);
            next();
        } catch (err) {
            return next(err);
        }
    });

router.get("/entities/:path/games",
    authenticate,
    authorize,
    decodePid(),
    customDefineLimits,
    validate(prepareScheme(["limit", "offset", "sortOrder", "currency"])),
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const gameInfoList: EntityGameInfo[] = await GameService.getAllGames(
                req.keyEntity,
                { path: getEntityPath(req) },
                req.query,
                req.baseUrl
            );
            res.send(gameInfoList);
            next();
        } catch (err) {
            return next(err);
        }
    });

export default router;
