import * as express from "express";
import { NextFunction, Response } from "express";
import {
    auditable,
    authenticate,
    authorize,
    getEntity,
    validate
} from "./middleware/middleware";

import { KeyEntityHolder } from "../services/security";
import {
    getEntityMerchantTypeService,
    getMerchantTypeService
} from "../services/merchantType";
import * as Errors from "../errors";
import { MerchantType, MerchantTypeUpdate } from "../entities/merchantType";

const router: express.Router = express.Router();

type Request = express.Request & KeyEntityHolder;

const merchantTypeValidator = {
    merchantType: {
        notEmpty: { errorMessage: "should be a not empty" },
        optional: false,
        isWord: { errorMessage: "should be a word" },
    }
};

const updateMerchantTypeValidator = {
    merchantType: {
        notEmpty: { errorMessage: "should be a not empty" },
        optional: false,
        isWord: { errorMessage: "should be a word" },
    },
    url: { optional: true, isUrl: true }
};

const createMerchantTypeValidator = {
    type: {
        notEmpty: { errorMessage: "should be a not empty" },
        optional: false,
        isWord: { errorMessage: "should be a word" },
    },
    url: { isUrl: true }
};

router.get("/entities/:path/merchant-types",
    authenticate,
    authorize,
    getMerchantTypes);

router.get("/merchant-types",
    authenticate,
    authorize,
    getMerchantTypes);

export async function getMerchantTypes(req: Request, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        const merchTypes = await getEntityMerchantTypeService().getList(entity);
        res.send(merchTypes);
    } catch (err) {
        next(err);
    }
}

router.get("/entities/:path/merchant-types/:merchantType",
    authenticate,
    authorize,
    validate(merchantTypeValidator),
    getMerchantType);

router.get("/merchant-types/:merchantType",
    authenticate,
    authorize,
    validate(merchantTypeValidator),
    getMerchantType);

export async function getMerchantType(req: Request, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        res.send(await getEntityMerchantTypeService().findOne(entity, req.params.merchantType));
    } catch (err) {
        next(err);
    }
}

router.put("/entities/:path/merchant-types/:merchantType",
    authenticate,
    authorize,
    validate(merchantTypeValidator),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const merchantType: string = req.params.merchantType;
            const entity = getEntity(req);
            const updated = await getEntityMerchantTypeService().add(entity, merchantType);
            res.send(await updated.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.delete("/entities/:path/merchant-types/:merchantType",
    authenticate,
    authorize,
    validate(merchantTypeValidator),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const merchantType: string = req.params.merchantType;
            const entity = getEntity(req);
            await getEntityMerchantTypeService().remove(entity, merchantType);

            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

router.patch("/merchant-types/:merchantType",
    authenticate,
    authorize,
    validate(updateMerchantTypeValidator),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            if (!req.keyEntity.isMaster()) {
                next(new Errors.NotMasterEntityError());
                return;
            }

            const merchantType: string = req.params.merchantType;
            const updated = await getMerchantTypeService().update(merchantType, req.body as MerchantTypeUpdate);

            res.send(updated);
        } catch (err) {
            next(err);
        }
    });

router.post("/merchant-types",
    authenticate,
    authorize,
    validate(createMerchantTypeValidator),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            if (!req.keyEntity.isMaster()) {
                next(new Errors.NotMasterEntityError());
                return;
            }

            const updated = await getMerchantTypeService().create(req.body as MerchantType);

            res.send(updated);
        } catch (err) {
            next(err);
        }
    });
export default router;
