import { RouteShorthandOptions } from "fastify";
export const LIVE_STUDIO_TOKEN = "x-live-studio-token";

const LIVE_STUDIO_HEADERS_VALIDATOR = {
    type: "object",
    properties: {
        [LIVE_STUDIO_TOKEN]: { type: "string" }
    },
    required: [LIVE_STUDIO_TOKEN]
};

export const LIVE_STUDIO_PLAYERS_VALIDATOR: RouteShorthandOptions = {
    schema: {
        headers: LIVE_STUDIO_HEADERS_VALIDATOR,
        params: {
            type: "object",
            properties: {
                brandId: { type: "string" },
                playerCode: { type: "string" }
            },
            required: ["brandId", "playerCode"]
        }
    }
};

export const LIVE_STUDIO_UPDATE_PLAYER_VALIDATOR: RouteShorthandOptions = {
    schema: {
        headers: LIVE_STUDIO_HEADERS_VALIDATOR,
        params: {
            type: "object",
            properties: {
                brandId: { type: "string" },
                playerCode: { type: "string" }
            },
            required: ["brandId", "playerCode"]
        },
        body: {
            type: "object"
        }
    }
};
