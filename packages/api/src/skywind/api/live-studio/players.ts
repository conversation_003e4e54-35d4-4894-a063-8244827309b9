import { Request, Response } from "../../bootstrap/fastify";
import { getPlayerInfoService } from "../../services/playerInfo";
import { FastifyInstance, FastifyRequest } from "fastify";
import { convertLiveStudioQuery } from "../../services/filter";
import { parseLiveStudioToken } from "../../services/security";
import { LIVE_STUDIO_PLAYERS_VALIDATOR, LIVE_STUDIO_TOKEN, LIVE_STUDIO_UPDATE_PLAYER_VALIDATOR } from "./validators";
import { PagingInfo } from "../../utils/paginghelper";
import { GetOnePlayerParams, LiveStudioTokenHeaders } from "./definition";

export const queryParamsKeys = [
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
    "playerCode",
    "brandId",
    "players",
    "isPublicChatBlock",
    "isPrivateChatBlock",
    "isMerchantPlayer",
    "hasWarn",
    "createdAt",
    "nickname"
];

export default function (router: FastifyInstance, options, done) {
    router.post("/players/search", LIVE_STUDIO_PLAYERS_VALIDATOR, getPlayers);
    router.get("/brands/:brandId/players/:playerCode", LIVE_STUDIO_PLAYERS_VALIDATOR, getOnePlayer);
    router.put("/brands/:brandId/players/:playerCode",
        LIVE_STUDIO_UPDATE_PLAYER_VALIDATOR, updateOnePlayer);
    done();
}

export async function getPlayers(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    const playersInfo = await getPlayerInfoService().find(convertLiveStudioQuery(req.body, queryParamsKeys));
    if (playersInfo) {
        const info: PagingInfo = playersInfo["PAGING_INFO"];
        const total: number = Array.isArray(info.total) ? info.total.length : info.total;
        res.header("x-paging-total", total.toString());
        res.header("x-paging-limit", info.limit.toString());
        res.header("x-paging-offset", info.offset.toString());
    }
    res.send(playersInfo);
}

export async function getOnePlayer(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders,
    Params: GetOnePlayerParams,
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    const playerInfo = await getPlayerInfoService().getPlayerInfo(req.params.playerCode, req.params.brandId as number);
    res.send(playerInfo);
}

export async function updateOnePlayer(req: FastifyRequest<{
    Headers: LiveStudioTokenHeaders,
    Params: GetOnePlayerParams,
}> & Request, res: Response) {
    await parseLiveStudioToken(req.headers[LIVE_STUDIO_TOKEN]);
    const playerInfo = await getPlayerInfoService().createOrUpdate({
        playerCode: req.params.playerCode,
        brandId: req.params.brandId,
        ...req.body as any
    }, { isSentByGame: true });
    res.send(playerInfo);
}
