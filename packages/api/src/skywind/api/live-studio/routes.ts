import { FastifyInstance } from "fastify";
import players from "./players";
import virtualGames from "./virtual-games";
import auth from "./auth";
import history from "./history";
import { apiSwaggerLiveStudio } from "../../utils/swagger";
import { defineSwaggerFastify } from "../fastifyRouters/swagger";

export async function define(app: FastifyInstance) {
    app.register(players, { prefix: "/v1" });
    app.register(virtualGames, { prefix: "/v1" });
    app.register(auth, { prefix: "/v1" });
    app.register(history, { prefix: "/v1" });

    await defineSwaggerFastify(app, await apiSwaggerLiveStudio());
}
