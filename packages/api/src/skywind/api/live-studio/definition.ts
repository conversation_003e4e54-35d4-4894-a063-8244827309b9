export interface LiveStudioTokenHeaders {
    "x-live-studio-token": string;
}

export interface GameHistoryRoundParams {
    roundId: string;
}
export interface GameHistorySpinDetailsParams {
    roundId: string;
    eventId: string;
}
export interface GameHistorySpinDetailsQuery {
    brandId: string;
    playerCode: string;
}

export interface GetOnePlayerParams {
    brandId: number | string;
    playerCode: string;
}

export interface UpdateGameSettingsBody {
    gameCodes: string[];
    settings: any;
}
