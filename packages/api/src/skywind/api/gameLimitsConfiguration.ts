import * as express from "express";
import { auditable, authenticate, authorize, decodePid, getEntity, validate } from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { getGameLimitsConfigurationService, queryParamsKeys } from "../services/gameLimits/gameLimitsConfiguration";
import { parseFilter } from "../services/filter";
import { MAX_INT_VALUE, VARCHAR_DEFAULT_LENGTH } from "../utils/common";
import { BrandEntity } from "../entities/brand";
import { buildExtendedPlayerLimitsBySchema } from "../services/gameLimits/limitsExtendedBuilder";
import { getNewLimitsFacade } from "../services/gameLimits/limitsFacade";
const router: express.Router = express.Router();

const validateGetGameList = validate({
    offset: {
        optional: true,
        isInt: { options: { min: 0, max: MAX_INT_VALUE } },
        toInt: true,
        errorMessage: "should be a non-negative number"
    },
    limit: {
        optional: true,
        isInt: { options: { min: 0, max: MAX_INT_VALUE } },
        toInt: true,
        errorMessage: "should be a non-negative number"
    }
});

const validateBody = validate({
    title: {
        optional: { options: [{ checkFalsy: true }] },
        isString: true,
        maxLength: VARCHAR_DEFAULT_LENGTH
    },
    description: {
        optional: { options: [{ checkFalsy: true }] },
        isString: true,
        maxLength: VARCHAR_DEFAULT_LENGTH
    }
});

async function getGameLimitsList(req: express.Request & KeyEntityHolder,
                                 res: express.Response,
                                 next: express.NextFunction) {
    try {
        const entity = getEntity(req);

        const configurations = await getGameLimitsConfigurationService(req.keyEntity)
            .list(entity, parseFilter(req.query, queryParamsKeys));
        res.send(configurations);
    } catch (err) {
        next(err);
    }
}
router.get("/game-limits", authenticate, authorize, decodePid(), validateGetGameList, getGameLimitsList);
router.get("/entities/:path/game-limits", authenticate, authorize, decodePid(), validateGetGameList, getGameLimitsList);

async function createGameLimits(req: express.Request & KeyEntityHolder,
                                res: express.Response,
                                next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const configuration = await getGameLimitsConfigurationService(req.keyEntity || entity, entity.isMaster())
            .create(entity, req.body);
        res.status(201).send(configuration);
    } catch (err) {
        next(err);
    }
}
router.post("/game-limits", authenticate, authorize, decodePid(),
    validateBody, auditable, createGameLimits);
router.post("/entities/:path/game-limits", authenticate, authorize, decodePid(),
    validateBody, auditable, createGameLimits);

async function getGameLimitConfiguration(req: express.Request & KeyEntityHolder,
                                         res: express.Response,
                                         next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const configuration = await getGameLimitsConfigurationService(req.keyEntity || entity)
            .retrieve(entity, req.params.gameLimitsConfigurationId);
        res.send(configuration);
    } catch (err) {
        next(err);
    }
}

router.get("/game-limits/:gameLimitsConfigurationId",
    authenticate,
    authorize,
    decodePid(),
    getGameLimitConfiguration);
router.get("/entities/:path/game-limits/:gameLimitsConfigurationId",
    authenticate,
    authorize,
    decodePid(),
    getGameLimitConfiguration);

async function updateGameLimitsConfiguration(req: express.Request & KeyEntityHolder,
                                             res: express.Response,
                                             next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const configuration = await getGameLimitsConfigurationService(req.keyEntity || entity, entity.isMaster())
            .update(entity, req.params.gameLimitsConfigurationId, req.body);
        res.send(configuration);
    } catch (err) {
        next(err);
    }
}
router.patch("/game-limits/:gameLimitsConfigurationId",
    authenticate,
    authorize,
    decodePid(),
    validateBody,
    auditable,
    updateGameLimitsConfiguration);
router.patch("/entities/:path/game-limits/:gameLimitsConfigurationId",
    authenticate,
    authorize,
    decodePid(),
    validateBody,
    auditable,
    updateGameLimitsConfiguration);

async function deleteGameLimitsConfiguration(req: express.Request & KeyEntityHolder,
                                             res: express.Response,
                                             next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        await getGameLimitsConfigurationService(req.keyEntity || entity)
            .destroy(entity, req.params.gameLimitsConfigurationId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}
router.delete("/game-limits/:gameLimitsConfigurationId",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    deleteGameLimitsConfiguration);

router.delete("/entities/:path/game-limits/:gameLimitsConfigurationId",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    deleteGameLimitsConfiguration);

router.get("/entities/:path/game-limits-extended/:gameCode",
    authenticate, authorize, decodePid(), getGameLimitsExtended);

router.get("/game-limits-extended/:gameCode",
    authenticate, authorize, decodePid(), getGameLimitsExtended);

async function getGameLimitsExtended(req: express.Request & KeyEntityHolder,
                                     res: express.Response,
                                     next: express.NextFunction) {
    try {
        const entity = getEntity(req) as BrandEntity;

        const limits = await buildExtendedPlayerLimitsBySchema(
            entity,
            req.params.gameCode,
            req.query.gameGroupName,
            req.query.currency);
        res.send(limits);
    } catch (err) {
        next(err);
    }
}

router.get("/game-limits/built/:gameCode", authenticate, authorize, decodePid(), getBuiltGameLimits);
router.get("/entities/:path/game-limits/built/:gameCode", authenticate, authorize, decodePid(), getBuiltGameLimits);

async function getBuiltGameLimits(req: express.Request & KeyEntityHolder,
                                  res: express.Response,
                                  next: express.NextFunction) {
    try {
        const entity = getEntity(req) as BrandEntity;

        const limits = await getNewLimitsFacade(entity).buildForAllCurrencies(
            req.params.gameCode,
            req.query.gameGroupName,
            req.query.currency);
        res.send(limits);
    } catch (err) {
        next(err);
    }
}

export default router;
