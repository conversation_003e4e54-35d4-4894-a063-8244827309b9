import * as express from "express";
import { NextFunction, Response } from "express";
import {
    auditable,
    authenticate,
    authorize,
    getBooleanParamFromRequestQuery,
    getEntity,
    getEntityPath,
    languageValidator,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import EntityCache from "../cache/entity";
import EntityLanguageService from "../services/entityLanguage";

const router: express.Router = express.Router();
const languagesValidator = validate({
    languages: {
        isLanguageCode: true
    }
}, true);

type Request = express.Request & KeyEntityHolder;

/**
 * Get list of available languages for a specific entity
 */
router.get("/entities/:path/languages",
    authenticate,
    authorize,
    getLanguages);

/**
 * Set list of available languages for a specific entity
 */
router.put("/entities/:path/languages",
    authenticate,
    authorize,
    languagesValidator,
    auditable,
    setLanguages);

/**
 * Delete list of available languages for a specific entity
 */
router.delete("/entities/:path/languages",
    authenticate,
    authorize,
    languagesValidator,
    auditable,
    removeLanguages);

/**
 * Add language to entity. should be available also in parent entity.
 */
router.put("/entities/:path/languages/:language",
    authenticate,
    authorize,
    validate({ language: languageValidator }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const language: string = req.params["language"];
            const entity = getEntity(req);
            const service = new EntityLanguageService(entity);

            await service.add(language).finally(() => EntityCache.reset());

            res.status(201).send(service.getList());
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Remove language from entity.
 */
router.delete("/entities/:path/languages/:language",
    authenticate,
    authorize,
    validate({
        language: languageValidator
    }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const language: string = req.params["language"];

            const entity = await EntityCache.findOne(
                { path: getEntityPath(req) },
                req.keyEntity.path, true, true);
            const service = new EntityLanguageService(entity);

            await service.remove(language, getBooleanParamFromRequestQuery(req, "force"))
                .finally(() => EntityCache.reset());

            res.status(204).end();
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Get list of available languages for a specific entity
 */
router.get("/languages",
    authenticate,
    authorize,
    getLanguages);

export async function getLanguages(req: Request, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        const service = new EntityLanguageService(entity);
        res.send(service.getList());
        next();
    } catch (err) {
        next(err);
    }
}

export async function setLanguages(req: Request, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        const service = new EntityLanguageService(entity);

        await service.addMany(req.body.languages).finally(() => EntityCache.reset());

        res.status(200).send(service.getList());
        next();
    } catch (err) {
        next(err);
    }
}

export async function removeLanguages(req: Request, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        const service = new EntityLanguageService(entity);

        await service.removeMany(req.body.languages).finally(() => EntityCache.reset());

        res.status(201).send(service.getList());
        next();
    } catch (err) {
        next(err);
    }
}

export default router;
