import * as express from "express";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    getEntity,
    parseCommaSeparatedString,
    validate
} from "./middleware/middleware";
import * as LobbyService from "../services/lobby";
import { KeyEntityHolder } from "../services/security";
import { INFO_FIELDS } from "../services/lobby";
import { pick } from "lodash";
import { EntityStatus } from "../entities/entity";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder;

router.get("/lobbies/downloadable",
    authenticate,
    authorize,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            res.send(await LobbyService.getDownloadableLobbies(EntityStatus.NORMAL));
        } catch (err) {
            next(err);
        }
    });

router.get("/lobbies/downloadable/:lobbyId",
    authenticate,
    authorize,
    decodePid(),
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            res.send(await LobbyService.getDownloadableLobbyWithTerminalToken(req.params.lobbyId, EntityStatus.NORMAL));
        } catch (err) {
            next(err);
        }
    });

router.patch("/lobbies/downloadable/:lobbyId",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            res.send(await LobbyService.patchLobbyMetaInfo(req.params.lobbyId, req.body, EntityStatus.NORMAL));
        } catch (err) {
            next(err);
        }
    });

router.post(["/lobbies", "/entities/:path/lobbies"],
    authenticate,
    authorize,
    decodePid(),
    validate({
        title: { notEmpty: true },
    }),
    auditable,
    async function (req: Request, res: express.Response, next: express.NextFunction) {
        try {
            const lobby = await LobbyService.createLobby(getEntity(req), req.body);
            res.status(201).send(lobby);
        } catch (err) {
            next(err);
        }
    });

router.get(["/lobbies", "/entities/:path/lobbies"],
    authenticate,
    authorize,
    decodePid(),
    async function (req: Request, res: express.Response, next: express.NextFunction) {
        try {
            const fields = parseCommaSeparatedString(req.query.fields, INFO_FIELDS);
            const lobbies = await LobbyService.getLobbies(getEntity(req));
            res.send(lobbies.map(item => pick(item, fields)));
        } catch (err) {
            next(err);
        }
    });

router.get(["/lobbies/:lobbyId", "/entities/:path/lobbies/:lobbyId"],
    authenticate,
    authorize,
    decodePid(),
    async function (req: Request, res: express.Response, next: express.NextFunction) {
        try {
            const fields = parseCommaSeparatedString(req.query.fields, INFO_FIELDS);
            const lobby = pick(await LobbyService.getLobby(getEntity(req), req.params.lobbyId), fields);
            res.send(lobby);
        } catch (err) {
            next(err);
        }
    });

router.patch(["/lobbies/:lobbyId", "/entities/:path/lobbies/:lobbyId"],
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async function (req: Request, res: express.Response, next: express.NextFunction) {
        try {
            const lobby = await LobbyService.updateLobby(getEntity(req), req.params.lobbyId, req.body);
            res.send(lobby);
        } catch (err) {
            next(err);
        }
    });

router.delete(["/lobbies/:lobbyId", "/entities/:path/lobbies/:lobbyId"],
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async function (req: Request, res: express.Response, next: express.NextFunction) {
        try {
            await LobbyService.deleteLobby(getEntity(req), req.params.lobbyId);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

export default router;
