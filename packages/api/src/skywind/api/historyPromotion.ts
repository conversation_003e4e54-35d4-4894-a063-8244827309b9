import * as express from "express";
import { authenticate, authorize, decodePid, FormattedResponse, getBrand, validate } from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { prepareScheme } from "../services/filter";
import {
    getBonusCoinsPlayerHistory,
    getBonusCoinsPromoHistory,
    getExpiredPromoPlayersStat,
    getPlayerStatsInPromo
} from "../history/promoHistory";
import { BrandEntity } from "../entities/brand";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder;

const validateLimitAndOffset = validate(prepareScheme(["limit", "offset"]));
const validateExpiredPromoRequest = validate(prepareScheme(["expired", "lastLogin", "amount"]));

async function bonusCoinsByPlayer(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        res.send(await getBonusCoinsPlayerHistory(brand.id, req.params.playerCode, req.query.limit, req.query.offset));
        next();
    } catch (err) {
        next(err);
    }
}

async function bonusCoinsForPromo(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        res.send(await getBonusCoinsPromoHistory(brand.id, req.params.promoId, req.query.limit, req.query.offset));
        next();
    } catch (err) {
        next(err);
    }
}

async function expiredPromoStat(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        res.send(await getExpiredPromoPlayersStat(brand.id, req.query));
        next();
    } catch (err) {
        next(err);
    }
}

async function statsInPromo(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        res.send(await getPlayerStatsInPromo(brand.id, req.params.promoId));
        next();
    } catch (err) {
        next(err);
    }
}

router.get("/entities/:path/history/bonuscoin/players/:playerCode",
    authenticate,
    authorize,
    validateLimitAndOffset,
    validate({
        playerCode: { notEmpty: true },
    }),
    bonusCoinsByPlayer);

router.get("/history/bonuscoin/players/:playerCode",
    authenticate,
    authorize,
    validateLimitAndOffset,
    validate({
        playerCode: { notEmpty: true },
    }),
    bonusCoinsByPlayer);

router.get("/entities/:path/history/bonuscoin/promo/:promoId",
    authenticate,
    authorize,
    validate({
        promoId: { notEmpty: true },
    }),
    validateLimitAndOffset,
    decodePid({ forceReturnIfNumber: true }),
    bonusCoinsForPromo);

router.get("/history/bonuscoin/promo/:promoId",
    authenticate,
    authorize,
    validate({
        promoId: { notEmpty: true },
    }),
    validateLimitAndOffset,
    decodePid({ forceReturnIfNumber: true }),
    bonusCoinsForPromo);

router.get("/entities/:path/history/bonuscoin/expired",
    authenticate,
    authorize,
    validateLimitAndOffset,
    validateExpiredPromoRequest,
    decodePid({ forceReturnIfNumber: true }),
    expiredPromoStat);

router.get("/history/bonuscoin/expired",
    authenticate,
    authorize,
    validateLimitAndOffset,
    validateExpiredPromoRequest,
    decodePid({ forceReturnIfNumber: true }),
    expiredPromoStat);

router.get("/entities/:path/history/promo/stats/:promoId",
    authenticate,
    authorize,
    validate({
        promoId: { notEmpty: true },
    }),
    decodePid({ forceReturnIfNumber: true }),
    statsInPromo);

router.get("/history/promo/stats/:promoId",
    authenticate,
    authorize,
    validate({
        promoId: { notEmpty: true },
    }),
    decodePid({ forceReturnIfNumber: true }),
    statsInPromo);

export default router;
