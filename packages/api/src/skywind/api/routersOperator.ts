import { Application } from "express";
import {
    apiSwaggerOperator,
} from "../utils/swagger";
import health from "./expressRouters/health";
import gamegroup from "./gamegroup";
import merchantPlayerGameGroup from "./merchantPlayerGameGroup";
import operator from "./operator";
import player<PERSON><PERSON>us<PERSON>oin from "./promotions/playerBonusCoinPromotion";
import version from "./expressRouters/version";
import logger from "../utils/logger";
import { encodePublicId, paging } from "./middleware/middleware";
import { addHeaderCORS, createRequestLogger, resolveIp, setUserAuthContext } from "./middleware/baseMiddleware";
import validator from "./middleware/validatorMiddleware";
import phantom from "./phantom";
import { defineSwaggerExpress } from "./expressRouters/swagger";
import { createErrorHandler } from "./expressRouters/general";

const log = logger("routers");

export async function defineRoutes(app: Application): Promise<Application> {

    app.use(resolveIp);
    app.use(setUserAuthContext);
    app.use(validator);
    app.use("/v1/*", createRequestLogger(log));

    await defineSwaggerExpress(app, await apiSwaggerOperator());

    app.use(paging);
    app.use(encodePublicId);
    app.use("/v1/health", health);
    app.use("/v1/version", version);
    app.use("/v1/*", addHeaderCORS);

    app.use("/v1", operator);
    app.use("/v1", phantom);
    app.use("/v1", gamegroup);
    app.use("/v1", merchantPlayerGameGroup);
    app.use("/v1", playerBonusCoin);
    app.use(createErrorHandler(log));

    // respond with status OK when a GET request is made to the root directory
    app.route("/").get((req, res, next) => {
        res.status(200).end();
    });

    return app;
}
