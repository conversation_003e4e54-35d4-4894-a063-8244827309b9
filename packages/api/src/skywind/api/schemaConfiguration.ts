import * as express from "express";
import { authenticate, authorize, decodePid, isMasterEntity, validate } from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { getSchemaConfigurationService } from "../services/gameLimits/schemaConfiguration";
import { VARCHAR_DEFAULT_LENGTH } from "../utils/common";
import { getSchemaDefinitionService } from "../services/gameLimits/schemaDefinition";
import { NewLimitsSystemBuilder } from "../services/gameLimits/limitsBuilder";
import { Limits } from "../entities/gamegroup";
import { getConfigurationFacade } from "../services/gameLimits/defaultConfigurationFacade";

const router: express.Router = express.Router();

router.post("/schema-configurations",
    authenticate,
    authorize,
    isMasterEntity,
    decodePid(),
    validate({
        schemaDefinitionId: { notEmpty: true },
        name: { notEmpty: true, isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] } },
        configuration: { notEmpty: true }
    }),
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const data = req.body;
            data.entityId = req.keyEntity.id;
            const config = await getSchemaConfigurationService().create(req.body);
            res.status(201).send(config.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.get("/schema-configuration-for-definition/:schemaDefinitionId",
    authenticate,
    authorize,
    decodePid(),
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const config = await getSchemaConfigurationService()
                .getByDefinition(req.params.schemaDefinitionId, undefined, true);
            res.send(config.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.get("/schema-configuration-for-definition/:schemaDefinitionId/currency/:currency",
    authenticate,
    authorize,
    decodePid(),
    validate({
        currency: { notEmpty: true, isCurrency: true }
    }),
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const schemaDefinition = await getSchemaDefinitionService().retrieve(req.params.schemaDefinitionId);
            const facade = await getConfigurationFacade(schemaDefinition, req.query.gameCode);
            const limitsBuilder = new NewLimitsSystemBuilder(facade, req.params.currency);

            const limits: Limits = await limitsBuilder.build();

            res.send(limits);
        } catch (err) {
            next(err);
        }
    });

router.get("/schema-configurations/:schemaConfigurationId",
    authenticate,
    authorize,
    decodePid(),
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const config = await getSchemaConfigurationService().retrieve(req.params.schemaConfigurationId);
            res.send(config.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.patch("/schema-configurations/:schemaConfigurationId",
    authenticate,
    authorize,
    decodePid(),
    isMasterEntity,
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const updated = await getSchemaConfigurationService().update(req.params.schemaConfigurationId, req.body);
            res.send(updated.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.delete("/schema-configurations/:schemaConfigurationId",
    authenticate,
    authorize,
    decodePid(),
    isMasterEntity,
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            await getSchemaConfigurationService().destroy(req.params.schemaConfigurationId);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

export default router;
