import { NextFunction, Request, Response, Router } from "express";
import {
    authenticate,
    authorize,
    convertDatesToISOMiddleware,
    defineDatesRangeLimitsMiddleware,
    defineLimits,
    FormattedResponse,
    getBrand,
    getEntity,
    sanitizeSearchingILIKE,
    setPaymentDateIfNotPresentInRequest,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { BaseEntity, ENTITY_TYPE } from "../entities/entity";
import { BrandEntity } from "../entities/brand";
import * as Errors from "../errors";
import * as CurrencyReportService from "../report/currency";
import { getWalletCurrencyReport } from "../report/currency";
import * as ReportGGRService from "../report/ggr";
import { DayGameReport, getDailyGamesReport, queryParamsKeys as dailyReportKeys } from "../report/dailyGames";
import { parseFilter, prepareScheme, valueFromQuery } from "../services/filter";
import * as WinBetService from "../services/winBet";
import { getSpinList } from "./history";
import { getChildIds } from "../services/entity";
import { setDatesRangeLimits, validatePeriodDatesRangeLimits } from "../utils/datesHelper";

const router: Router = Router();

export async function getCurrencyReportRequestHandler(req: Request & KeyEntityHolder,
                                                      res: Response,
                                                      next: NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        const currencyReport = await CurrencyReportService.getCurrencyReport(brand.id, req.query);
        res.send(currencyReport);
        next();
    } catch (err) {
        next(err);
    }
}

export const validateCurrencyFilter = validate(prepareScheme(["from", "to", "ts", "currency", "limit"]));

router.get("/report/currency",
    authenticate,
    authorize,
    validateCurrencyFilter,
    defineLimits,
    convertDatesToISOMiddleware(["ts"]),
    defineDatesRangeLimitsMiddleware(["ts"]),
    getCurrencyReportRequestHandler
);

router.get("/entities/:path/report/currency",
    authenticate,
    authorize,
    validateCurrencyFilter,
    defineLimits,
    convertDatesToISOMiddleware(["ts"]),
    defineDatesRangeLimitsMiddleware(["ts"]),
    getCurrencyReportRequestHandler
);

export interface BrandIdsHolder {
    brandIds: number[];
}

router.get(["/report/wallet/currency", "/entities/:path/report/wallet/currency"],
    authenticate,
    authorize,
    validateCurrencyFilter,
    convertDatesToISOMiddleware(["ts"]),
    function (req, res, next) {
        validatePeriodDatesRangeLimits(req, "ts");
        next();
    },
    function (req, res, next) {
        setDatesRangeLimits(req, "ts");
        next();
    },
    defineLimits,
    async function (req: Request & KeyEntityHolder & BrandIdsHolder, res, next) {
        try {
            const brand = getEntity(req) as BrandEntity;
            const includeSubBrands = valueFromQuery(req.query, "includeSubBrands") === "true";
            if (includeSubBrands || brand.isBrand()) {
                req.brandIds = [brand.id];
                if (includeSubBrands) {
                    req.brandIds = req.brandIds.concat(getChildIds(brand));
                }
                next();
            } else {
                next(new Errors.NotBrand());
            }
        } catch (err) {
            next(err);
        }
    },
    async function (req: Request & BrandIdsHolder, res, next) {
        try {
            res.send(await getWalletCurrencyReport(req.brandIds, req.query));
            next();
        } catch (err) {
            next(err);
        }
    }
);

export const validateFilter = validate(prepareScheme([
    "format",
    "currency",
    "totalBets",
    "totalWins",
    "playedGames",
    "paymentDate",
    "paymentDateHour",
]));

router.get("/report/players",
    authenticate,
    authorize,
    validateFilter,
    setPaymentDateIfNotPresentInRequest,
    defineLimits,
    sanitizeSearchingILIKE,
    convertDatesToISOMiddleware(["paymentDate", "paymentDateHour"]),
    defineDatesRangeLimitsMiddleware(["paymentDate", "paymentDateHour"]),
    playerReports);

router.get("/entities/:path/report/players",
    authenticate,
    authorize,
    validateFilter,
    setPaymentDateIfNotPresentInRequest,
    defineLimits,
    sanitizeSearchingILIKE,
    convertDatesToISOMiddleware(["paymentDate", "paymentDateHour"]),
    defineDatesRangeLimitsMiddleware(["paymentDate", "paymentDateHour"]),
    playerReports);

export async function playerReports(req: Request & KeyEntityHolder, res: FormattedResponse, next: NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        const playersList: WinBetService.WinBetInfo[] = await WinBetService.getPlayerReports(brand,
            parseFilter(req.query, WinBetService.queryParamsKeys));
        res.sendFormatted(req, playersList);
        next();
    } catch (err) {
        next(err);
    }
}

const validateDay = validate(prepareScheme(["currency", "ts"]));

router.get("/entities/:path/report/games/daily",
    authenticate,
    authorize,
    validateDay,
    defineLimits,
    convertDatesToISOMiddleware(["ts"]),
    defineDatesRangeLimitsMiddleware(["ts"]),
    dailyGamesReport
);
router.get("/report/games/daily",
    authenticate,
    authorize,
    validateDay,
    defineLimits,
    convertDatesToISOMiddleware(["ts"]),
    defineDatesRangeLimitsMiddleware(["ts"]),
    dailyGamesReport
);

async function dailyGamesReport(req: Request & KeyEntityHolder, res: FormattedResponse, next: NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        const dailyList: DayGameReport[] = await getDailyGamesReport(brand, parseFilter(req.query, dailyReportKeys));
        res.sendFormatted(req, dailyList);
        next();
    } catch (err) {
        next(err);
    }
}

router.get("/entities/:path/report/ggr", authenticate, authorize, validateCurrencyFilter, getReportGGR);
router.get("/report/ggr", authenticate, authorize, validateCurrencyFilter, getReportGGR);

async function getReportGGR(req: Request & KeyEntityHolder, res: FormattedResponse, next: NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        if (entity.type !== ENTITY_TYPE.BRAND && entity.type !== ENTITY_TYPE.MERCHANT) {
            return next(new Errors.NotBrandOrMerchant());
        }
        const list: ReportGGRService.BrandGGR[] = await ReportGGRService.getReportGGR(
            entity, parseFilter(req.query, ReportGGRService.queryParamsKeys)
        );
        res.sendFormatted(req, list);
        next();
    } catch (err) {
        next(err);
    }
}

export const validateSpinsFilter = validate(prepareScheme(
    ["limit", "offset", "sortOrder", "playerCode", "type", "insertedAt", "ts", "roundId", "gameCode"]
));

router.get("/entities/:path/history/spins", authenticate, authorize,
    validateSpinsFilter,
    convertDatesToISOMiddleware(["insertedAt", "ts"]),
    defineDatesRangeLimitsMiddleware(["insertedAt", "ts"]),
    getSpinList);

router.get("/history/spins", authenticate, authorize,
    validateSpinsFilter,
    convertDatesToISOMiddleware(["insertedAt", "ts"]),
    defineDatesRangeLimitsMiddleware(["insertedAt", "ts"]),
    getSpinList);

export default router;
