import { NextFunction, Request, Response, Router } from "express";
import PlayerResponsibleGamingServiceImpl, { getPlayerResponsibleGamingService } from "../services/playerResponsibleGaming";
import { authenticate, authorize, decodePid, getBrand, validate } from "./middleware/middleware";
import { parseFilter, prepareScheme } from "../services/filter";
import { BrandEntity } from "../entities/brand";
import { KeyEntityHolder } from "../services/security";

const router: Router = Router();

router.get("/responsiblegaming",
    authenticate,
    authorize,
    decodePid(),
    validate(prepareScheme(["limit", "offset", "sortOrder", "status"])),
    searchPlayerResponsibleGamingSettings);
router.get("/entities/:path/responsiblegaming",
    authenticate,
    authorize,
    decodePid(),
    validate(prepareScheme(["limit", "offset", "sortOrder", "status"])),
    searchPlayerResponsibleGamingSettings);

const suspensionTypesValidator = {
    optional: true, notEmpty: true,
    isCommaSeparatedPermissibleFields: {
        options: {
            fields: ["self-exclusion", "time-out"]
        }
    }
};
const suspensionTypesValidatorScheme = {
    ...prepareScheme(["limit", "offset", "sortOrder", "status"]),
    suspensionTypes: suspensionTypesValidator,
    suspensionTypes__in: suspensionTypesValidator
};

router.get("/responsiblegaming/reports/exclusions",
    authenticate,
    authorize,
    decodePid(),
    validate(suspensionTypesValidatorScheme),
    getExclusionAndTimeoutReport);
router.get("/entities/:path/responsiblegaming/reports/exclusions",
    authenticate,
    authorize,
    decodePid(),
    validate(suspensionTypesValidatorScheme),
    getExclusionAndTimeoutReport);

router.get("/responsiblegaming/:playerCode",
    authenticate,
    authorize,
    decodePid(),
    getPlayerResponsibleGamingSettings);
router.get("/entities/:path/responsiblegaming/:playerCode",
    authenticate,
    authorize,
    decodePid(),
    getPlayerResponsibleGamingSettings);

router.patch("/responsiblegaming/:playerCode",
    authenticate,
    authorize,
    decodePid(),
    validate({
        "*.casinoTimeoutTillDate": { optional: true, isAfter: new Date() },
        "*.selfExclusionTillDate": { optional: true, isAfter: new Date() }
    }),
    updatePlayerResponsibleGamingSettings);

router.patch("/entities/:path/responsiblegaming/:playerCode",
    authenticate,
    authorize,
    decodePid(),
    validate({
        "*.casinoTimeoutTillDate": { optional: true, isAfter: new Date() },
        "*.selfExclusionTillDate": { optional: true, isAfter: new Date() }
    }),
    updatePlayerResponsibleGamingSettings);

router.delete("/responsiblegaming/:playerCode",
    authenticate,
    authorize,
    decodePid(),
    deletePendingResponsibleGamingChange);

router.delete("/entities/:path/responsiblegaming/:playerCode",
    authenticate,
    authorize,
    decodePid(),
    deletePendingResponsibleGamingChange);

async function searchPlayerResponsibleGamingSettings(req: Request & KeyEntityHolder,
                                                     res: Response,
                                                     next: NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        res.send(await getPlayerResponsibleGamingService(brand).searchPlayerResponsibleGamingSettings(
            parseFilter(req.query, PlayerResponsibleGamingServiceImpl.queryParamsKeys)));
        next();
    } catch (err) {
        next(err);
    }
}

async function getExclusionAndTimeoutReport(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        res.send(await getPlayerResponsibleGamingService(brand).getExclusionAndTimeoutReport(
            parseFilter(req.query, PlayerResponsibleGamingServiceImpl.exclusionParamsKeys)));
        next();
    } catch (err) {
        next(err);
    }
}

async function getPlayerResponsibleGamingSettings(req: Request & KeyEntityHolder,
                                                  res: Response,
                                                  next: NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        res.send(await getPlayerResponsibleGamingService(brand).checkAndGetPlayerResponsibleGamingSettings(
            req.params.playerCode));
        next();
    } catch (err) {
        next(err);
    }
}

async function updatePlayerResponsibleGamingSettings(req: Request & KeyEntityHolder,
                                                     res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        res.send(await getPlayerResponsibleGamingService(brand)
            .updatePlayerResponsibleGamingSettings(req.params.playerCode, req.body));
        next();
    } catch (err) {
        next(err);
    }
}

async function deletePendingResponsibleGamingChange(req: Request & KeyEntityHolder,
                                                    res: Response,
                                                    next: NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        res.send(await getPlayerResponsibleGamingService(brand).deleteResponsibleGamingPendingChange(
            req.params.playerCode, req.body));
        next();
    } catch (err) {
        next(err);
    }
}

export default router;
