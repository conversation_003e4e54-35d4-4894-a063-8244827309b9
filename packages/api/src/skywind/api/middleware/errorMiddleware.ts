import { InternalServerError, MalformedJsonError, ValidationError } from "../../errors";
import { ValidationError as SequelizeValidationError } from "sequelize";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";

export function handleError(err, logger): SWError {
    let error: SWError;

    if (err?.validationContext === undefined && (err instanceof SWError || SWError.isSWError(err))) {
        const errorLevel = err.getErrorLevel ? err.getErrorLevel() : "error";
        logger[errorLevel](err, "SWError");
        error = err as SWError;
    } else if (err.validation) {
        error = new ValidationError(err.message);
    } else if (err instanceof SequelizeValidationError) {
        logger.warn(err, "Validation Error");
        error = new ValidationError((err as SequelizeValidationError).errors.map(item => item.message));
    } else if (err instanceof SyntaxError) {
        const syntaxErrorReason = err.message ? err.message : "N/A";
        logger.warn(err, `Malformed json - ${syntaxErrorReason}`);
        error = new MalformedJsonError(syntaxErrorReason);
    } else {
        logger.error(err, "Internal error");
        error = new InternalServerError(err);
    }

    return error;
}
