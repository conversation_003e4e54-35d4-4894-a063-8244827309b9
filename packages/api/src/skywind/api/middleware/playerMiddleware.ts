import { NextFunction, Response } from "express";
import { validateToken } from "../../services/playerSecurity";
import { X_PLAYER_TOKEN } from "../../utils/common";
import { PlayerRequest } from "../playerAPI";

export async function authenticate(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        const token = req.header(X_PLAYER_TOKEN);
        const result = await validateToken(token);

        req.tokenPayload = {
            exp: result.exp,
            iat: result.iat
        };
        delete result.iat;
        delete result.exp;
        delete result.iss;

        // For case  Brand-Merchants we can not do simple check entity.type == "merchant"
        if (result.customerSessionId || result.isExternalTerminal) {
            req.language = result.language;
            req.country = result.country;
            req.customerSessionId = result.customerSessionId;
        }

        req.gameGroup = result.gameGroup;
        req.gameGroupId = result.gameGroupId;
        req.currency = result.currency;
        req.test = result.test;
        req.playerCode = result.playerCode;
        req.brandId = result.brandId;
        req.sessionId = result.sessionId;
        req.userId = result.userId;
        req.isExternalTerminal = result.isExternalTerminal;

        req.brand = result.brand;
        delete result.brand;
        req.tokenData = result;
        next();
    } catch (err) {
        next(err);
    }
}
