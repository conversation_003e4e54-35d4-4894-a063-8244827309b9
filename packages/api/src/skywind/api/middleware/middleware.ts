import * as express from "express";
import { NextFunction, Request, Response } from "express";
import { FastifyRequest } from "fastify";
import { Request as FRequest } from "../../bootstrap/fastify";
import * as EntityService from "../../services/entity";
import {
    AuditHolder,
    IpHolder,
    IsLiveGameHolder,
    isPermitted,
    KeyEntityHolder,
    parseInternalToken,
    parseToken,
    PermissionsHolder,
    SessionHolder,
    SwaggerHolder,
    UserInfoHolder,
    validatePermissions
} from "../../services/security";
import * as Errors from "../../errors";
import { PagingHelper, PagingInfo } from "../../utils/paginghelper";
import { IncomingMessage, ServerResponse } from "node:http";
import * as FilterService from "../../services/filter";
import config from "../../config";
import { BrandEntity } from "../../entities/brand";
import { BaseEntity, Entity } from "../../entities/entity";
import { GAME_TYPES, VARCHAR_DEFAULT_LENGTH, X_ACCESS_TOKEN, X_OAUTH_TOKEN } from "../../utils/common";
import {
    convertDatesToISO,
    datePostfixes,
    defineDatesRangeLimits,
    setDatesRangeLimits,
    validatePeriodDatesRangeLimits,
    validateStartDateDatesRangeLimits
} from "../../utils/datesHelper";
import logger from "../../utils/logger";
import * as PromotionService from "../../services/promotions/promotion";
import { requestLogData } from "../../utils/requestHelper";

import { getAuthSessionService } from "../../services/authSessionService";
import { AuditableRequest, AuditInfo } from "../../utils/auditHelper";
import { AuditHistory, getAuditFacade } from "../../services/audit/audit";
import { FlatReportsStorage } from "../../services/flatReports/flatReportsStorage";
import { FLAT_REPORT_TYPE } from "../../entities/flatReport";
import { AuditDBInstance } from "../../models/audit";
import { MerchantGameInitRequest } from "@skywind-group/sw-wallet-adapter-core";
import { ContextVariables } from "../../utils/contextVariables";
import * as MerchantCache from "../../cache/merchant";
import { EGPPromoGateway } from "../../services/promotions/egpPromoGateway";
import { decodeId, encodeEachInObject, isPrivateKey } from "../../utils/publicid";
import { PROMO_OPERATIONS } from "../../entities/promotion";
import { getOAuthService } from "../../services/oAuthService";
import { OAUTH_TOKEN_COOKIE, OAuthTokenCookie } from "../../entities/oAuth";

require("./formattedResponse");
const mung = require("express-mung");
const log = logger("middleware");
export const FORMAT_CSV = "csv";

/**
 * access token middleware which get from the header the access token and find
 * the main key entity according to it
 *
 */
export async function authenticate(
    req: Request & KeyEntityHolder & PermissionsHolder & UserInfoHolder & IpHolder & SessionHolder,
    res: Response,
    next: NextFunction) {

    try {
        let result: KeyEntityHolder & PermissionsHolder & UserInfoHolder & SessionHolder;
        if (config.oAuth.isEnabled) {
            const tokenCookie = req.signedCookies[OAUTH_TOKEN_COOKIE] as OAuthTokenCookie;
            const oauthToken = req.header(X_OAUTH_TOKEN) || req.header(X_ACCESS_TOKEN);
            if (!tokenCookie?.accessToken && !oauthToken) {
                return next(new Errors.TokenIsMissing());
            }
            if (tokenCookie) {
                result = await handleCookieOAuth(tokenCookie, res);
            } else {
                result = await handleHeaderOAuth(oauthToken);
            }
        } else {
            const token = req.header(X_ACCESS_TOKEN);
            if (!token) {
                return next(new Errors.TokenIsMissing());
            }
            result = await parseToken(token);
            const authSessionService = getAuthSessionService();
            await authSessionService.checkSession(result.userId, result.sessionId);
        }

        req.keyEntity = result.keyEntity;
        req.permissions = result.permissions;
        req.userId = result.userId;
        req.username = result.username;
        req.sessionId = result.sessionId;
        req.isSuperAdmin = result.isSuperAdmin;

        log.info({
            path: req.params.path,
            userId: req.userId,
            username: req.username,
            entityId: req.keyEntity.id,
            reqData: requestLogData(req),
            sessionId: req.sessionId,
            isSuperAdmin: result.isSuperAdmin
        }, "Authenticate request");

        next();
    } catch (err) {
        return next(err);
    }
}

export async function authenticateRefresh(
    req: Request & KeyEntityHolder & PermissionsHolder & UserInfoHolder & IpHolder & SessionHolder,
    res: Response,
    next: NextFunction) {

    try {
        let result: KeyEntityHolder & PermissionsHolder & UserInfoHolder & SessionHolder;
        if (config.oAuth.isEnabled) {
            const tokenCookie = req.signedCookies[OAUTH_TOKEN_COOKIE] as OAuthTokenCookie;
            if (!tokenCookie?.accessToken) {
                return next(new Errors.TokenIsMissing());
            }
            result = await handleCookieOAuth(tokenCookie, res, true);
        } else {
            const token = req.header(X_ACCESS_TOKEN);
            if (!token) {
                return next(new Errors.TokenIsMissing());
            }
            result = await parseToken(token);
            const authSessionService = getAuthSessionService();
            await authSessionService.checkSession(result.userId, result.sessionId);
        }

        req.keyEntity = result.keyEntity;
        req.permissions = result.permissions;
        req.userId = result.userId;
        req.username = result.username;
        req.sessionId = result.sessionId;
        req.isSuperAdmin = result.isSuperAdmin;

        log.info({
            path: req.params.path,
            userId: req.userId,
            username: req.username,
            entityId: req.keyEntity.id,
            reqData: requestLogData(req),
            sessionId: req.sessionId,
            isSuperAdmin: result.isSuperAdmin
        }, "Authenticate request");

        next();
    } catch
        (err) {
        return next(err);
    }
}

async function handleCookieOAuth(
    { accessToken, expiresAt }: OAuthTokenCookie,
    res: Response,
    isRefresh?: boolean
): Promise<KeyEntityHolder & PermissionsHolder & UserInfoHolder & SessionHolder> {
    const oAuthService = getOAuthService();
    const accessTokenExpiration = new Date(expiresAt);
    const currentTime = new Date();
    const tokenRefreshGracePeriod = config.oAuth.tokenRefreshGracePeriod * 60 * 60 * 1000;
    if (accessTokenExpiration < currentTime) {
        if (isRefresh) {
            throw new Errors.InvalidAccessToken();
        }
        if (currentTime.getTime() - accessTokenExpiration.getTime() > tokenRefreshGracePeriod) {
            await oAuthService.logout(accessToken);
            throw new Errors.InvalidAccessToken();
        }
        const tokenInfo = await oAuthService.tokenRefresh(accessToken);
        res.cookie(OAUTH_TOKEN_COOKIE, {
            accessToken: tokenInfo.accessToken,
            expiresAt: tokenInfo.expiresAt
        }, {
            signed: true,
            httpOnly: true,
            secure: true,
            domain: config.oAuth.ssoCookieDomain,
            sameSite: "none",
            expires: new Date(tokenInfo.refreshTokenExpiresAt)
        });
        return oAuthService.parseToken(tokenInfo.accessToken);
    }
    return oAuthService.parseToken(accessToken);
}

async function handleHeaderOAuth(
    accessToken: string
): Promise<KeyEntityHolder & PermissionsHolder & UserInfoHolder & SessionHolder> {
    const oAuthService = getOAuthService();
    return oAuthService.parseToken(accessToken);
}

export async function authenticateLogout(
    req: Request & KeyEntityHolder & PermissionsHolder & UserInfoHolder & IpHolder & SessionHolder,
    res: Response,
    next: NextFunction) {

    const tokenCookie = req.signedCookies[OAUTH_TOKEN_COOKIE] as OAuthTokenCookie;
    if (!tokenCookie?.accessToken) {
        return next(new Errors.TokenIsMissing());
    }

    try {
        const oAuthService = getOAuthService();
        const result = await oAuthService.parseToken(tokenCookie.accessToken, true);

        req.keyEntity = result.keyEntity;
        req.permissions = result.permissions;
        req.userId = result.userId;
        req.username = result.username;
        req.sessionId = result.sessionId;
        req.isSuperAdmin = result.isSuperAdmin;

        log.info({
            path: req.params.path,
            userId: req.userId,
            username: req.username,
            entityId: req.keyEntity.id,
            reqData: requestLogData(req),
            sessionId: req.sessionId,
            isSuperAdmin: result.isSuperAdmin
        }, "Authenticate request");

        next();
    } catch (err) {
        return next(err);
    }
}

export async function checkOAuthEnabled(req: Request, res: Response, next: NextFunction): Promise<void> {
    if (!config.oAuth.isEnabled) {
        next(new Errors.OAuthNotEnabledError());
    }
    next();
}

export async function authenticateInternal(req: Request & IpHolder,
                                           res: Response,
                                           next: NextFunction) {

    const token = req.header("x-internal-token");
    if (!token) {
        return next(new Errors.InternalTokenIsMissing());
    }

    try {
        await parseInternalToken(token);
        log.info({
            path: req.params.path,
            reqData: requestLogData(req)
        }, "Authenticate internal request");
        next();
    } catch (err) {
        return next(err);
    }
}

export function authorize(req: Request & SwaggerHolder & PermissionsHolder, res: Response, next: NextFunction) {
    if (validatePermissions(req)) {
        next();
    } else {
        next(new Errors.OperationForbidden());
    }
}

export const validatePromoPermissionsPerType = (operation: PROMO_OPERATIONS) =>
    (req: Request & KeyEntityHolder & PermissionsHolder,
     res: Response,
     next: NextFunction) => {
        const requiredPermissions = PromotionService.getPromoPermissions(
            req.body.type,
            operation,
            !req.params.path as boolean);

        if (!isPermitted(requiredPermissions, req.permissions)) {
            next(new Errors.OperationForbidden());
        } else {
            next();
        }
    };

function getEntityPathInternal(rootPath: string, relPath: string) {
    if (relPath.trim() === EntityService.PATH_CHAR) {
        return rootPath;
    }

    const names: string[] = relPath.split(EntityService.PATH_CHAR);
    const errors: string[] = [];

    for (const name of names) {
        if (/[^\w-]/.test(name)) {
            errors.push(`${name} is not a valid entity.name`);
        }
    }

    if (errors.length > 0) {
        throw new Errors.ValidationError(errors);
    }

    let safeRelPath: string = names.join(EntityService.PATH_CHAR);
    if (!safeRelPath.endsWith(EntityService.PATH_CHAR)) {
        safeRelPath += EntityService.PATH_CHAR;
    }
    return rootPath + safeRelPath;
}

export function getEntityPath(req: Request & KeyEntityHolder) {
    return getEntityPathInternal(req.keyEntity.path, req.params.path);
}

export function getEntityPathFromQuery(req: Request & KeyEntityHolder) {
    return getEntityPathInternal(req.keyEntity.path, req.query.path);
}

const dateQueries: string[] = [
    "ts",
    "timestamp",
    "firstTs",
    "lastTs",
    "paymentDateHour",
    "paymentDate",
    "insertedAt"
];

function getTsFromQuery(query: any, postfix: string): number {
    const dates: string[] = dateQueries.filter(base => query[base + postfix]);
    if (dates.length) {
        return (new Date(query[dates[0] + postfix])).getTime() / 1000;
    }
    return undefined;
}

export function defineLimits(req: Request, res: Response, next: NextFunction) {
    req.query.offset = req.query.offset || FilterService.DEFAULT_OFFSET.toString();
    const maxLimit = res.locals.MAX_LIMIT || FilterService.MAX_LIMIT;

    if (req.query.format === FORMAT_CSV) {
        if (!req.query.limit) {
            const from = getTsFromQuery(req.query, "__gt") || getTsFromQuery(req.query, "__gte");
            const to = getTsFromQuery(req.query, "__lt") || getTsFromQuery(req.query, "__lte");
            req.query.limit = to - from <= config.csvExport.periodOfNoLimitation ?
                              config.csvExport.maxLimitLines : config.csvExport.defaultLimitLines;
        } else {
            req.query.limit = Math.min(req.query.limit, config.csvExport.defaultLimitLines);
        }

    } else {
        req.query.limit = req.query.limit || FilterService.DEFAULT_LIMIT;
        req.query.limit = Math.min(req.query.limit, maxLimit);
    }

    next();
}

export function defineLimitsWithBalance(req: Request, res: FormattedResponse, next: express.NextFunction) {
    if (req.query.format === FORMAT_CSV && req.query.withBalance === "true") {
        req.query.limit = Math.min(req.query.limit, config.csvExport.limitWithBalance);
    }

    next();
}

export function sanitizeSearchingILIKE(req: Request, res: Response, next: NextFunction) {
    // SWS-3990 fix db performance - we are not able to implement ILIKE-search on this table
    delete req.query["playerCode__contains"];
    delete req.query["playerCode__contains!"];
    delete req.query["gameCode__contains"];
    delete req.query["gameCode__contains!"];
    next();
}

export const validate = (schema: {}, onlyBodyCheck?: true) => (req: Request, res: Response, next: NextFunction) => {
    if (onlyBodyCheck) {
        req.checkBody(schema);
    } else {
        req.check(schema);
    }

    const errors = req.validationErrors();
    if (errors) {
        const errorsWithoutDuplicates = [];
        (errors as any []).forEach(error => {
            const found = errorsWithoutDuplicates.filter(errItem => error.param === errItem.param).length;
            if (!found) {
                errorsWithoutDuplicates.push(error);
            }
        });
        next(new Errors.ValidationError(errorsWithoutDuplicates.map(e => `${e.param} - ${e.msg.toLowerCase()}`)));
    } else {
        next();
    }
};

export const validateBody = (schema: {}) => validate(schema, true);
export const bodyIsArrayValidator = (req, res, next) => {
    if (!Array.isArray(req.body)) {
        throw new Errors.ValidationError("body should be an array");
    }
    next();
};
const decodeQueryString = (pid: string, paramName?: string, options: DecodePidOptions = {}): number => {
    const errorMessage: string = paramName ? paramName + " - invalid public id value" : "Public Id incorrect value";

    if (pid === null && options.allowNull) {
        return null;
    }

    if (!pid || typeof pid === "string" && pid.trim().length === 0) {
        throw new Errors.ValidationError(errorMessage);
    }

    if (options.forceReturnIfNumber && !Number.isNaN(+pid) && Number.isFinite(+pid)) {
        return +pid;
    }

    const value = decodeId(pid);
    if (Number.isFinite(value)) {
        return value;
    } else if (options.notRaiseError) {
        return null;
    } else {
        throw new Errors.ValidationError(errorMessage);
    }
};

export const decodeEachInObject = (obj: any, options: DecodePidOptions = {}) => {
    if (!obj) {
        return;
    }

    if (Array.isArray(obj)) {
        return obj.map(c => decodeEachInObject(c, options));
    }

    for (const key of Object.keys(obj)) {
        if (options.ignoredKeys && options.ignoredKeys.includes(key.split("__")[0])) {
            continue;
        }

        if (isCommaSeparatedKey(key)) {
            obj[key] = obj[key].split(",")
                .map((value) => decodeQueryString(value, key, options)).join(",");
        } else if (isPrivateKey(key)) {
            if (Array.isArray(obj[key])) {
                obj[key] = obj[key].map((value) => decodeQueryString(value, key, options));
                continue;
            }
            if (typeof obj[key] === "string" && obj[key].includes(EGPPromoGateway.egpEncodedPromoIdSeparator)) {
                continue;
            }
            obj[key] = decodeQueryString(obj[key], key, options);
        } else if (typeof obj[key] === "object" && (options.keysToParse || []).includes(key)) {
            decodeEachInObject(obj[key], options);
        }
    }
};

interface DecodePidOptions {
    ignoredKeys?: string[];
    forceReturnIfNumber?: boolean;
    keysToParse?: string[];
    allowNull?: boolean;
    notRaiseError?: boolean;
}

export const decodePid = (options: DecodePidOptions = {}) => (req, res, next) => {
    try {
        decodeEachInObject(req.body, options);
        decodeEachInObject(req.params, options);
        decodeEachInObject(req.query, options);
    } catch (err) {
        next(err);
    }
    next();
};

const isCommaSeparatedKey = (key: string): boolean => {
    return key.includes("Id__") || key.startsWith("id__");
};

export interface FormattedResponse extends Response {
    sendFormatted(req: Request, result: any, excludeFields?: Array<string>);

    csv(result: any);
}

export const countryValidator = {
    optional: true,
    isCountryCode: { options: { allowNull: false, isSingleValue: true } }
};

export const nullableCountryValidator = {
    optional: true,
    isCountryCode: { options: { allowNull: true, isSingleValue: true } }
};

export const currencyValidator = {
    notEmpty: { errorMessage: "should be a not empty" },
    optional: false,
    isWord: { errorMessage: "should be a word" },
    isLength: { options: [{ min: 3, max: 3 }], errorMessage: "length should be a 3" },
    isUppercase: { errorMessage: "should be a uppercase" },
};

export const languageValidator = {
    notEmpty: true,
    optional: false,
    isLanguage: true
};

export function pagingWrapper(body: any, req: IncomingMessage, res: ServerResponse) {
    if (body) {
        if (PagingHelper.hasPagingInfo(body)) {
            PagingHelper.fillHeaders(res, body as PagingInfo);
        }
    }
    return body;
}

export const paging = mung.json(pagingWrapper);

export function encodePublicIdWrapper(body: any) {
    if (Array.isArray(body)) {
        body.forEach(encodeEachInObject);
    }
    if (typeof body === "object") {
        encodeEachInObject(body);
    }
    return body;
}

export const encodePublicId = mung.json(encodePublicIdWrapper);

export function getMerchant(req: Request & KeyEntityHolder): BrandEntity {
    const entity: BrandEntity = getBrand(req);
    if (!entity.isMerchant) {
        throw new Errors.NotMerchantError();
    }
    return entity;
}

export function getBrand(req: Request & KeyEntityHolder, options: GetEntityOptions = {}): BrandEntity {

    const entity: BaseEntity = getEntity(req, options);

    if (!entity.isBrand()) {
        throw new Errors.NotBrand();
    }
    return entity as BrandEntity;
}

export interface GetEntityOptions {
    ignoreSuspended?: boolean;

    [flag: string]: any;
}

export function getEntity(req: Request & KeyEntityHolder, options: GetEntityOptions = {}): BaseEntity {
    let entity: BaseEntity;
    if (req.params.path) {
        entity = req.keyEntity.find({ path: getEntityPath(req) });
    } else {
        entity = req.keyEntity as Entity;
    }

    if (!entity) {
        throw new Errors.EntityCouldNotBeFound();
    }

    if (options.ignoreSuspended) {
        return entity;
    }

    if (entity.isSuspended()) {
        throw new Errors.ParentSuspendedError();
    }

    return entity;
}

export function getBooleanParamFromRequestQuery(req: Request | FastifyRequest & FRequest,
                                                paramName: string,
                                                defaultValue: boolean = false) {
    return getBooleanParamFromQuery(req.query, paramName, defaultValue);
}

/**
 * Looks for parameter in query that is supposed to be boolean.
 * As all query params come as string, let's compare that string with 'true' literal
 */
export function getBooleanParamFromQuery(query: any, paramName: string, defaultValue: boolean = false) {
    const param = query[paramName];
    return param === undefined ? defaultValue : param === "true";
}

export const sanitize = (sanitizer: (req: any) => void) => (req: Request, res: Response, next: NextFunction) => {
    sanitizer(req);
    next();
};

export const sanitizeBoolean = (...paramNames: string[]) => {
    return sanitize((req: { sanitize: (name: string) => ({ toBoolean: () => {} }) }) => {
        for (const name of paramNames) {
            req.sanitize(name).toBoolean();
        }
    });
};

export const sanitizeCommaSeparatedString = (...paramNames: string[]) => {
    return sanitize((req: { sanitize: (name: string) => ({ toCommaSeparatedStringToArray: () => {} }) }) => {
        for (const name of paramNames) {
            req.sanitize(name).toCommaSeparatedStringToArray();
        }
    });
};

export function parseCommaSeparatedString(value: string | undefined, def: string[] = []): string[] {
    if (value) {
        const fields = value.split(",").map(field => field.trim()).filter(Boolean);
        return fields.length ? fields : def;
    }
    return def;
}

export const validateChangePasswordValidator = validate({
    password: {
        notEmpty: true,
        errorMessage: "Password should be not empty"
    },
    newPassword: {
        notEmpty: true,
        errorMessage: "New password should be not empty",
        isLength: {
            options: [{ max: VARCHAR_DEFAULT_LENGTH }],
            errorMessage: `Max length - ${VARCHAR_DEFAULT_LENGTH} chars`
        },
    },
});

export const convertDatesToISOMiddleware = (dateKeys: string[]) => (req: Request,
                                                                    res: Response,
                                                                    next: NextFunction) => {
    try {
        convertDatesToISO(req, dateKeys);
        next();
    } catch (err) {
        next(err);
    }
};

export const defineDatesRangeLimitsMiddleware = (dateKeys: string[], isEndDateRequired: boolean = false) =>
    (req: Request & PermissionsHolder, res: Response, next: NextFunction) => {

        try {
            defineDatesRangeLimits(req, dateKeys, isEndDateRequired);
            next();
        } catch (err) {
            next(err);
        }
    };

export const defineDatesRangeLimitsForRoundHistory = (startDateKeys: string[],
                                                      endDateKey: string,
                                                      roundFinishedKey: string,
                                                      isEndDateRequired: boolean = false) =>
    (req: Request & PermissionsHolder, res: Response, next: NextFunction) => {
        const data: any = req.query;
        if (!Object.keys(data).length) {
            return;
        }
        try {
            if (data[roundFinishedKey] === "true" || requestHasEndDateCondition(data, endDateKey)) {
                defineDatesRangeLimits(req, [endDateKey], isEndDateRequired, startDateKeys);
            } else {
                defineDatesRangeLimits(req, startDateKeys, isEndDateRequired);
            }
            next();
        } catch (err) {
            next(err);
        }
    };

function requestHasEndDateCondition(reqData: any, endDateKey: string): boolean {
    if (reqData[endDateKey] !== undefined) {
        return true;
    }
    for (const postfix of datePostfixes) {
        if (reqData[endDateKey + postfix] !== undefined) {
            return true;
        }
    }
}

export const isMasterEntity = (req: Request & KeyEntityHolder,
                               res: Response,
                               next: NextFunction) => {
    try {
        if (!req.keyEntity.isMaster()) {
            return next(new Errors.NotMasterEntityError());
        }
        next();
    } catch (err) {
        next(err);
    }
};

// Works only if we have keyentity in request and request type is not "get", "options" or "head"
export const auditable = (req: Request & AuditHolder & KeyEntityHolder, res: Response, next: NextFunction) => {
    const excludeMethods = ["GET", "OPTIONS", "HEAD"];
    if (config.audit.on && req.keyEntity && !excludeMethods.includes(req.method.toUpperCase())) {
        req.isAudit = true;
        req.audited = false;
        req.currentEntity = getEntity(req, { ignoreSuspended: true });
    }
    next();
};

export const setAuditSwagger = (tag: string, summary: string) =>
    (req: Request & SwaggerHolder, res: Response, next: NextFunction) => {
        req.swagger = {
            operation: {
                tags: [tag],
                summary
            },
            apiPath: req.url
        };
        next();
    };

export const auditOnSend = mung.json(auditOnSendResponse, { mungError: true });
export const auditOnEnd = mung.headers(auditOnEndResponse);

function auditOnSendResponse(body: any, req) {
    audit(req, body);
    return body;
}

function auditOnEndResponse(req) {
    audit(req, {});
}

export function audit(req: Request & AuditableRequest, resBody): undefined | Promise<AuditDBInstance> {
    if (req.isAudit && !req.audited) {
        const auditFacade = getAuditFacade();
        const entity = req.currentEntity || req.keyEntity;
        const info = new AuditInfo(req, undefined, entity);
        const history = new AuditHistory(req.path, { ...req.body, ...req.params }, resBody);
        req.audited = true;
        notifyFlatReports(req, resBody, entity);
        return auditFacade.save(info, history);
    }
}

function notifyFlatReports(req: Request, resBody, entity: BaseEntity) {
    const flatReportsEnabled = resBody?.flatReportsEnabled;
    if (req.url.endsWith("/settings") && ["PUT", "PATCH"].includes(req.method) && flatReportsEnabled) {
        const flatReportStorage = new FlatReportsStorage(FLAT_REPORT_TYPE.ES);
        flatReportStorage.save([entity.id]);
    }
}

export const defineIsLiveGameFlagTrue = (
    req: Request & IsLiveGameHolder,
    res: Response,
    next: NextFunction
) => {
    req.isLiveGame = true;
    next();
};

export const defineIsLiveGameFlagFalse = (
    req: Request & IsLiveGameHolder,
    res: Response,
    next: NextFunction
) => {
    req.isLiveGame = false;
    next();
};

export function setPaymentDateIfNotPresentInRequest(req: Request & KeyEntityHolder & PermissionsHolder,
                                                    res: Response,
                                                    next: NextFunction) {
    setDatesRangeLimits(req, "paymentDateHour");
    validateStartDateDatesRangeLimits(req, "paymentDateHour");
    validatePeriodDatesRangeLimits(req, "paymentDateHour");
    next();
}

/**
 * Used to add brantId to the context. That field will be added in logs as 'sw-entity-id'.
 * @param req with body of merchant game init request.
 * @param res
 * @param next
 */
export function setMerchantAuthContext(req, res, next: NextFunction) {
    const { merchantType, merchantCode } = req.body as MerchantGameInitRequest;

    MerchantCache
        .findByTypeAndCode(merchantType, merchantCode)
        .then(merchant => {
            ContextVariables.setEntityId(merchant.brandId);
            next();
        })
        .catch((error) => {
            next(error);
        });
}

export function validateLiveGame(req: Request, res: Response, next: NextFunction) {
    const type = req.params?.type || req.body?.type;
    if (type === GAME_TYPES.live) {
        next(new Errors.ProhibitingCreationOfLiveGameError());
    }
    next();
}

interface ResponseWithSize extends Response {
    contentLength?: number;
}

export function requestLogger(req: Request & IpHolder, res: ResponseWithSize, next: NextFunction) {
    if (req.originalUrl.includes("/version") || req.originalUrl.includes("/health")) {
        return next();
    }

    const start = Date.now();
    res.contentLength = 0;

    const originalJson = res.json;
    const originalSend = res.send;

    res.json = function(body: any): Response {
        if (body) {
            res.contentLength = Buffer.byteLength(JSON.stringify(body));
        }
        return originalJson.call(this, body);
    };

    res.send = function(body: any): Response {
        if (body) {
            res.contentLength = Buffer.byteLength(body instanceof Buffer ? body : JSON.stringify(body));
        }
        return originalSend.call(this, body);
    };

    const logResponse = (err?: Error) => {
        const duration = (Date.now() - start) / 1000;

        const baseLogData = {
            statusCode: res.statusCode || 500,
            requestTime: duration,
            responseSize: res.contentLength,
            reqData: requestLogData(req)
        };

        if (err || res.statusCode >= 500) {
            log.error({
                error: err,
                ...baseLogData
            }, `Error occurred after ${duration} seconds. Size: ${res.contentLength} bytes.`);
        } else if (res.statusCode >= 400) {
            log.warn(baseLogData, `Request failed with status ${res.statusCode} after ${duration} seconds. Size: ${res.contentLength} bytes.`);
        } else {
            log.info(baseLogData, `Request completed successfully in ${duration} seconds. Size: ${res.contentLength} bytes.`);
        }
    };

    res.on("finish", logResponse);
    res.on("error", (err: Error) => logResponse(err));

    next();
}
