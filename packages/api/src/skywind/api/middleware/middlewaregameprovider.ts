import { Request, Response } from "../../bootstrap/fastify";
import { ProviderHolder, verifyProviderSecret, } from "../../services/security";
import * as Errors from "../../errors";
import { FastifyRequest } from "fastify";

/**
 * game server middleware which get from the header the provider code and secret code
 * TODO: I think it can be removed
 */
export async function authenticate(req: FastifyRequest & Request & ProviderHolder, res: Response, next) {
    const providerUser = req.headers["x-provider-user"] as string;
    const providerCode = req.headers["x-provider-code"] as string;
    const providerSecret = req.headers["x-provider-secret"] as string;

    if (!providerUser || !providerCode || !providerSecret) {
        return Promise.reject(new Errors.ProviderUserOrCodeOrSecretIsMissing());
    }

    const provider = await verifyProviderSecret(providerUser, providerCode, providerSecret);
    req.provider = provider;
}
