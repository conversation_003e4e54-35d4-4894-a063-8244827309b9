import { NextFunction, Request, Response } from "express";
import { SiteTokenData, verifySiteToken } from "../../utils/token";
import * as SiteTokenService from "../../services/siteToken";
import * as Errors from "../../errors";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import * as FilterService from "../../services/filter";
import { token } from "@skywind-group/sw-utils";

// Max limit for list of games. We just sent all games with hope that no more 50000 games in category.
const MAX_LIMIT = 50000;

export async function authenticate(req: Request & SiteTokenData, res: Response, next: NextFunction) {
    const siteToken = req.header("x-site-token");
    if (!siteToken) {
        return next(new Errors.TokenIsMissing());
    }
    try {
        const decoded: SiteTokenData = await verifySiteToken(siteToken);
        const tokenRecord = await SiteTokenService.findOne({
            brandId: decoded.brandId,
            status: "normal",
            token: siteToken,
        });
        req.brandId = tokenRecord.brandId;
        next();
    } catch (err) {
        if (err instanceof token.TokenVerifyException) {
            return next(new Errors.AccessTokenError());
        }
        if (err instanceof token.TokenExpiredException) {
            return next(new Errors.AccessTokenExpired());
        }
        return next(err);
    }
}

export const validate = (schema: {}) => (req: Request, res: Response, next: NextFunction) => {
    req.check(schema);
    const errors: any[] = req.validationErrors() as any[];
    if (!errors) {
        return next();
    }
    const invalidParams = errors.map(error => error.param);
    const subErrors: SWError[] = [];

    if (invalidParams.indexOf("email") !== -1) {
        subErrors.push(new Errors.ValidationEmailError());
    }

    if (invalidParams.indexOf("password") !== -1) {
        subErrors.push(new Errors.ValidationPasswordError());
    }

    if (invalidParams.indexOf("currency") !== -1) {
        subErrors.push(new Errors.ValidationError("Invalid currency format"));
    }

    if (invalidParams.indexOf("country") !== -1) {
        subErrors.push(new Errors.ValidationError("Invalid country format"));
    }

    if (invalidParams.indexOf("language") !== -1) {
        subErrors.push(new Errors.ValidationError("Invalid language format"));
    }

    if (invalidParams.indexOf("firstName") !== -1) {
        subErrors.push(new Errors.ValidationError("Invalid firstName format"));
    }

    if (invalidParams.indexOf("lastName") !== -1) {
        subErrors.push(new Errors.ValidationError("Invalid lastName format"));
    }

    return next(new Errors.ValidationError((errors as any[]).map(e => `${e.param} - ${e.msg.toLowerCase()}`),
        subErrors));
};

export function defineLimits(req: Request, res: Response, next: NextFunction) {
    req.query.offset = req.query.offset || FilterService.DEFAULT_OFFSET.toString();
    req.query.limit = Math.min(req.query.limit || MAX_LIMIT, MAX_LIMIT);
    next();
}
