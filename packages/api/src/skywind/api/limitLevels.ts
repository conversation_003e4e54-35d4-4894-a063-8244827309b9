import * as express from "express";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    getEntity,
    sanitizeBoolean,
    validate
} from "./middleware/middleware";
import { limitLevelService, SEARCH_FIELDS } from "../services/gameLimits/limitLevels";
import { parseFilter } from "../services/filter";
import { KeyEntityHolder } from "../services/security";
import { getParentIds } from "../services/entity";
import { ValidationError } from "../errors";
import { Op } from "sequelize";

const router: express.Router = express.Router();

const validateTitle = validate({ title: { isString: true, notEmpty: true } });

router.get("/entities/:path/limit-levels", authenticate, authorize,
    sanitizeBoolean("all"), getLimitLevelsHandler);
router.get("/limit-levels", authenticate, authorize, sanitizeBoolean("all"), getLimitLevelsHandler);
router.post("/entities/:path/limit-levels", authenticate, authorize, auditable,
    validateTitle, createLimitLevelsHandler);
router.post("/limit-levels", authenticate, authorize, auditable, validateTitle, createLimitLevelsHandler);
router.patch("/entities/:path/limit-levels/:levelId",
    authenticate, authorize, auditable, decodePid(), validateTitle, updateLimitLevelsHandler);
router.patch("/limit-levels/:levelId",
    authenticate, authorize, auditable, decodePid(), validateTitle, updateLimitLevelsHandler);
router.delete("/entities/:path/limit-levels/:levelId", authenticate, authorize, auditable,
    decodePid(), deleteLimitLevelHandler);
router.delete("/limit-levels/:levelId", authenticate, authorize, auditable, decodePid(), deleteLimitLevelHandler);

async function getLimitLevelsHandler(req: express.Request & KeyEntityHolder,
                                     res: express.Response,
                                     next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const service = limitLevelService.get();
        const entityIds = req.query.all ? [...getParentIds(entity), entity.id] : [entity.id];

        const levels = await service.list({
            where: {
                ...parseFilter(req.query, SEARCH_FIELDS),
                entityId: {
                    [Op.in]: entityIds
                }
            }
        });

        res.send(levels.map(level => level.toInfo()));
    } catch (err) {
        next(err);
    }
}

async function createLimitLevelsHandler(req: express.Request & KeyEntityHolder,
                                        res: express.Response,
                                        next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const service = limitLevelService.get();
        const level = await service.create({ entityId: entity.id, ...req.body });

        res.send(level.toInfo());
    } catch (err) {
        next(err);
    }
}

async function updateLimitLevelsHandler(req: express.Request & KeyEntityHolder,
                                        res: express.Response,
                                        next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const service = limitLevelService.get();
        const updatedLevel = await service.update(req.params.levelId, { title: req.body.title, entityId: entity.id });

        res.send(updatedLevel.toInfo());
    } catch (err) {
        next(err);
    }
}

async function deleteLimitLevelHandler(req: express.Request & KeyEntityHolder,
                                       res: express.Response,
                                       next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const service = limitLevelService.get();
        const level = await service.retrieve(req.params.levelId);

        if (level.entityId !== entity.id) {
            return next(new ValidationError("Limit level belong to another entity"));
        }

        await service.destroy(req.params.levelId);

        res.status(203).end();
    } catch (err) {
        next(err);
    }
}

export default router;
