import { NextFunction, Request as ExpressRequest, Response, Router } from "express";
import { auditable, authenticate, authorize, getBrand } from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import {
    createPlayerSessionFacade,
    PlayerSessionFacade,
} from "../services/player/playerSessionFacade";
import PlayerGameSessionService from "../services/player/playerGameSessionService";
import { PlayerSessionErrors } from "@skywind-group/sw-management-playersession";

const router: Router = Router();
type Request = ExpressRequest & KeyEntityHolder;

async function findSession(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const brand = await getBrand(req);
        const playerCode = req.params.playerCode;
        if (!brand.isMerchant) {
            await brand.findPlayer({ code: playerCode });
        }

        const ttl = await PlayerGameSessionService.find(brand.id, req.params.playerCode);
        if (ttl === 0) {
            return next(new PlayerSessionErrors.PlayerSessionExpiredError());
        }
        res.send({ ttl });
        next();
    } catch (err) {
        next(err);
    }
}

export async function killSession(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const brand = await getBrand(req);
        const playerCode = req.params.playerCode;
        if (!brand.isMerchant) {
            await brand.findPlayer({ code: playerCode });
        }

        const service: PlayerSessionFacade = createPlayerSessionFacade();
        const reason: string = req.query.reason;
        const result = await service.kill({brandId: brand.id, playerCode, reason});
        if (result) {
            res.sendStatus(202);
        } else {
            return next(new PlayerSessionErrors.PlayerSessionExpiredError());
        }
        next();
    } catch (err) {
        next(err);
    }
}

router.get("/players/:playerCode/session", authenticate, authorize, findSession);
router.delete("/players/:playerCode/session", authenticate, authorize, auditable, killSession);
router.get("/entities/:path/players/:playerCode/session", authenticate, authorize, findSession);
router.delete("/entities/:path/players/:playerCode/session", authenticate, authorize, auditable, killSession);

export default router;
