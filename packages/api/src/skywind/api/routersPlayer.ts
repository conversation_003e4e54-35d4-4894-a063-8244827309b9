import { Application, static as staticContent } from "express";
import { apiSwaggerPlayer } from "../utils/swagger";
import health from "./expressRouters/health";
import playerSelf from "./playerAPI";
import version from "./expressRouters/version";
import logger from "../utils/logger";
import { encodePublicId, paging } from "./middleware/middleware";
import { addHeaderCORS, createRequestLogger, resolveIp, setPlayerAuthContext } from "./middleware/baseMiddleware";
import validator from "./middleware/validatorMiddleware";
import config from "../config";
import { defineSwaggerExpress } from "./expressRouters/swagger";
import { createErrorHandler } from "./expressRouters/general";

const log = logger("routers");

export async function defineRoutes(app: Application): Promise<Application> {

    app.use(resolveIp);
    app.use(setPlayerAuthContext);
    app.use(validator);
    app.use(paging);
    app.use(encodePublicId);

    app.use("/v1/*", createRequestLogger(log), addHeaderCORS);

    app.use("/v1/health", health);
    app.use("/v1/version", version);

    if (!config.isProduction()) {
        app.use(staticContent("public", { index: "socket-player.html" }));
        await defineSwaggerExpress(app, await apiSwaggerPlayer());
    }

    app.use("/v1", playerSelf);
    app.use(createErrorHandler(log));

    // respond with status OK when a GET request is made to the root directory
    app.route("/").get((req, res, next) => {
        res.status(200).end();
    });

    return app;
}
