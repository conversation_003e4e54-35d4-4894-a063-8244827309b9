import { RouteShorthandOptions } from "fastify";
import { BAN_WORDS_TOKEN } from "../../utils/validateNickname";

const BAN_WORDS_HEADERS_VALIDATOR = {
    type: "object",
    properties: {
        [BAN_WORDS_TOKEN]: { type: "string" }
    },
    required: [BAN_WORDS_TOKEN]
};

export const BAN_WORDS_CHECK_PLAYER_NICKNAME_VALIDATOR: RouteShorthandOptions = {
    schema: {
        headers: BAN_WORDS_HEADERS_VALIDATOR
    }
};
