import { FastifyInstance } from "fastify";
import banWords from "./banWords";
import auth from "./auth";
import { apiSwaggerBanWords } from "../../utils/swagger";
import { defineSwaggerFastify } from "../fastifyRouters/swagger";

export async function define(app: FastifyInstance): Promise<void> {
    app.register(banWords, { prefix: "/v1" });
    app.register(auth, { prefix: "/v1" });

    await defineSwaggerFastify(app, await apiSwaggerBanWords());
}
