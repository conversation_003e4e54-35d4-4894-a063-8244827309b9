import { Request, Response } from "../../bootstrap/fastify";
import { FastifyInstance, FastifyRequest } from "fastify";
import { parseBanWordsToken } from "../../services/security";
import { BAN_WORDS_CHECK_PLAYER_NICKNAME_VALIDATOR } from "./validators";
import getPlayerNicknameService from "../../services/banWords/playerNickname";
import { BAN_WORDS_TOKEN } from "../../utils/validateNickname";

export default function(router: FastifyInstance, options, done) {
    router.get("/players/validate-nickname/:playerNickname",
        BAN_WORDS_CHECK_PLAYER_NICKNAME_VALIDATOR,
        checkPlayerNickname);
    done();
}

export async function checkPlayerNickname(req: FastifyRequest<{
    Headers: { "x-ban-words-token": string },
    Params: { playerNickname: string }
}> & Request, res: Response) {
    await parseBanWordsToken(req.headers[BAN_WORDS_TOKEN]);
    await getPlayerNicknameService().check(req.params.playerNickname);
    res.status(204).send();
}
