import { FastifyInstance, FastifyRequest } from "fastify";
import { Request, Response } from "../../bootstrap/fastify";
import { generateBanWordsToken } from "../../utils/token";

export default function(router: FastifyInstance, options, done) {
    router.post("/token", generateToken);
    done();
}

export async function generateToken(req: FastifyRequest & Request, res: Response) {
    const banWordsToken = await generateBanWordsToken({});
    res.send({ banWordsToken });
}
