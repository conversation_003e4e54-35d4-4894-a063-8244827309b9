import * as express from "express";
import {
    authenticate,
    authorize,
    decodePid,
    defineLimits,
    FormattedResponse,
    getEntity,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { parseFilter, prepareScheme } from "../services/filter";
import { getAuditSessionService } from "../services/audit/auditSession";
import { AuditSession } from "../entities/auditSession";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder;

const getAuditsSessionHandler = async (req: Request, res: FormattedResponse, next: express.NextFunction) => {
    try {
        const service = getAuditSessionService();
        const filter = parseFilter(req.query, service.getQueryParams());
        const entity = getEntity(req);
        const auditSessions: AuditSession[] = (await service.getAll(entity, filter)).map(a => a.toInfo());
        res.sendFormatted(req, auditSessions);
        next();
    } catch (err) {
        return next(err);
    }
};

const validateAuditSessions = validate(
    {
        ...prepareScheme(["limit", "offset", "sortOrder"]),
        includeSubEntities: { optional: true, isBoolean: true },
    });

router.get("/entities/:path/audits-sessions",
    authenticate,
    authorize,
    defineLimits,
    decodePid({ignoredKeys: ["id"]}),
    validateAuditSessions,
    getAuditsSessionHandler);

router.get("/audits-sessions",
    authenticate,
    authorize,
    defineLimits,
    decodePid({ignoredKeys: ["id"]}),
    validateAuditSessions,
    getAuditsSessionHandler);

export default router;
