import { NextFunction, Request, Response, Router } from "express";
import { auditable, authenticate, authorize, validate } from "./middleware/middleware";
import { ValidationError } from "../errors";
import * as cache from "../cache/gameLimitsCurrencies";
import * as service from "@skywind-group/sw-currency-exchange";

const router: Router = Router();

async function getGameLimitsCurrencies(req: Request, res: Response, next: NextFunction) {
    try {
        const gameLimitsCurrencies = await service.getGameLimitsCurrencies();
        if (Array.isArray(gameLimitsCurrencies) && gameLimitsCurrencies.length) {
            res.send(gameLimitsCurrencies);
        } else {
            res.send([]);
        }
    } catch (err) {
        next(err);
    }
}

async function getGameLimitsCurrency(req: Request, res: Response, next: NextFunction) {
    try {
        const currency = req.params.currency;
        const version = req.params.version;
        const gameLimitsCurrency = await service.getGameLimitsCurrency(currency, version);
        if (gameLimitsCurrency && gameLimitsCurrency.currency) {
            res.send(gameLimitsCurrency);
        } else {
            res.send({});
        }
    } catch (err) {
        next(err);
    }
}

async function updateGameLimitsCurrency(req: Request, res: Response, next: NextFunction) {
    try {
        const currency = req.params.currency;
        const version = req.params.version;
        const updatedGameLimitsCurrency = await service.updateGameLimitsCurrency(currency, version, req.body)
            .catch(err => {
                next(new ValidationError(err));
            });

        await cache.reset();

        res.send(updatedGameLimitsCurrency);
    } catch (err) {
        next(err);
    }
}

async function createGameLimitsCurrency(req: Request, res: Response, next: NextFunction) {
    try {
        const updatedGameLimitsCurrency = await service.createGameLimitsCurrency(req.body)
            .catch(err => {
                next(new ValidationError(err));
            });

        res.status(201).send(updatedGameLimitsCurrency);
    } catch (err) {
        next(err);
    }
}

async function dropGameLimitsCurrency(req: Request, res: Response, next: NextFunction) {
    try {
        const currency = req.params.currency;
        const version = req.params.version;

        await service.deleteGameLimitsCurrency(currency, version);
        await cache.reset();

        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

const updateValidationRules = {
    toEURMultiplier: { optional: true, isNumeric: { options: { min: 1, max: 25 } } },
    copyLimitsFrom: { optional: true, isCurrency: true },
};

const createValidationSchema = validate({
    currency: { notEmpty: true, isCurrency: true },
    version: { notEmpty: true, isInt: true, isPositive: true },
    ...updateValidationRules,
});

router.get("/game-limits-currencies", authenticate, authorize, getGameLimitsCurrencies);
router.get("/game-limits-currencies/:currency/:version", authenticate, authorize, getGameLimitsCurrency);
router.patch("/game-limits-currencies/:currency/:version",
    authenticate,
    authorize,
    auditable,
    validate(updateValidationRules),
    updateGameLimitsCurrency);
router.post("/game-limits-currencies",
    authenticate,
    authorize,
    auditable,
    createValidationSchema,
    createGameLimitsCurrency);
router.delete("/game-limits-currencies/:currency/:version", authenticate, authorize, auditable, dropGameLimitsCurrency);

export default router;
