import { Request, Router } from "express";
import * as proxy from "express-http-proxy";
import { RequestOptions } from "http";
import AuthGateway from "../services/authGateway";
import { X_ACCESS_TOKEN } from "../utils/common";

const router: Router = Router();

router.use(proxy((req) => AuthGateway.findRoute(req.url), {
    proxyReqPathResolver: async (req: Request) => AuthGateway.resolvePath(req.url),
    proxyReqOptDecorator: async (p1: RequestOptions, req: Request) => {
        p1.headers[X_ACCESS_TOKEN] = await AuthGateway.rewriteAccessToken(req.header("x-access-token"));
        return p1;
    }
}));

export default router;
