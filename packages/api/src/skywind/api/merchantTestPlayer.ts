import * as express from "express";
import {
    authenticate,
    authorize,
    defineLimits,
    FormattedResponse,
    getMerchant,
    validate
} from "./middleware/middleware";
import * as MerchantTestPlayersService from "../services/merchantTestPlayer";
import getMerchantTestPlayerService from "../services/merchantTestPlayer";
import { KeyEntityHolder, UserInfoHolder } from "../services/security";
import { parseFilter, prepareScheme } from "../services/filter";
import { validatePlayerCodeForOperator } from "./playerAuthForBrands";
import * as merchantTestPlayersCache from "../cache/testPlayers";
import { MerchantTestPlayer, SOURCE } from "../entities/merchantTestPlayer";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder;

const validateAddTestPlayer = validate({
    code: { notEmpty: true, isPlayerCodeFromOperator: true },
    startDate: { optional: true, isTimestampIso8601: true },
    endDate: { optional: true, isTimestampIso8601: true },
});

const validateUpdateTestPlayer = validate({
    code: { notEmpty: true, isPlayerCodeFromOperator: true },
    endDate: { notEmpty: true, isTimestampIso8601: true },
});
/**
 * Search merchant test players
 */

const getTestPlayers = async (req: Request, res: FormattedResponse, next: express.NextFunction) => {
    try {
        const entity = await getMerchant(req);
        const service = getMerchantTestPlayerService(entity);
        const players: MerchantTestPlayer[] = await service.find(
            parseFilter(req.query, MerchantTestPlayersService.queryParamsKeys)
        );
        res.send(players);
    } catch (err) {
        next(err);
    }
};

router.get("/entities/:path/testplayers",
    authenticate,
    authorize,
    defineLimits,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    getTestPlayers);

router.get("/testplayers",
    authenticate,
    authorize,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    defineLimits,
    getTestPlayers);

/**
 * Remove from test players
 */

const removeTestPlayers = async (req: Request & UserInfoHolder, res: express.Response, next: express.NextFunction) => {
    try {
        const entity = await getMerchant(req);
        const service = getMerchantTestPlayerService(entity);
        const playerInfo = await service.remove(req.params.playerCode);
        await merchantTestPlayersCache.reset();
        res.send(playerInfo);
    } catch (err) {
        next(err);
    }
};

router.delete("/testplayers/:playerCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    removeTestPlayers);

/**
 * Remove from test players
 */
router.delete("/entities/:path/testplayers/:playerCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    removeTestPlayers);

interface AddTestPlayer {
    code: string;
    endDate?: Date;
    startDate?: Date;
}

interface UpdateTestPlayer {
    code: string;
    endDate: Date;
}

const addTestPlayer = async (req: Request & UserInfoHolder, res: express.Response, next: express.NextFunction) => {
    try {
        const entity = await getMerchant(req);
        const service = getMerchantTestPlayerService(entity);
        const result = await service.add({ ...req.body as AddTestPlayer, source: SOURCE.SUPPORT });
        res.send(result);
    } catch (err) {
        next(err);
    }
};

router.post("/testplayers", authenticate, authorize, validateAddTestPlayer, addTestPlayer);
router.post("/entities/:path/testplayers", authenticate, authorize, validateAddTestPlayer, addTestPlayer);

const updateTestPlayer = async (req: Request & UserInfoHolder, res: express.Response, next: express.NextFunction) => {
    try {
        const entity = await getMerchant(req);
        const service = getMerchantTestPlayerService(entity);
        const result = await service.update(req.body.code, { endDate: req.body.endDate });
        res.send(result);
    } catch (err) {
        next(err);
    }
};

router.put("/testplayers", authenticate, authorize, validateUpdateTestPlayer, updateTestPlayer);
router.put("/entities/:path/testplayers", authenticate, authorize, validateUpdateTestPlayer, updateTestPlayer);

export default router;
