import * as express from "express";
import {
    auditable,
    authenticate,
    authorize,
    defineLimits,
    FormattedResponse,
    getBooleanParamFromRequestQuery,
    getBrand,
    validate
} from "./middleware/middleware";
import {
    KeyEntityHolder,
    UserInfoHolder
} from "../services/security";
import { parseFilter, prepareScheme } from "../services/filter";
import getBlockPlayerService, { queryParamsKeys } from "../services/blockedPlayer";
import { validatePlayerCodeForOperator } from "./playerAuthForBrands";
import { getBrandPlayerValidator } from "../services/brandPlayerValidator";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder;

/**
 * Find internal or external Player and get his info
 */
const getBlockedPlayerByCode = async(req: Request, res: FormattedResponse, next: express.NextFunction) => {
    try {
        const entity = await getBrand(req);
        const service = getBlockPlayerService(entity);

        const fetchAudit = getBooleanParamFromRequestQuery(req, "withAudit");
        const player = await service.getPlayerInfo(req.params.playerCode, fetchAudit);

        res.send(player);
    } catch (err) {
        next(err);
    }
};

router.get("/entities/:path/suspendedplayers/:playerCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    getBlockedPlayerByCode
);

router.get("/suspendedplayers/:playerCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    getBlockedPlayerByCode
);

/**
 * Search blocked players
 */

const getBlockedPlayers = async(req: Request, res: FormattedResponse, next: express.NextFunction) => {
    try {
        const entity = await getBrand(req, { ignoreSuspended: req.keyEntity.isMaster() });
        const service = getBlockPlayerService(entity);

        const query = parseFilter(req.query, queryParamsKeys);
        const players = await service.find(query);

        res.send(players);
    } catch (err) {
        next(err);
    }
};

router.get("/entities/:path/suspendedplayers",
    authenticate,
    authorize,
    defineLimits,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    getBlockedPlayers);

router.get("/suspendedplayers",
    authenticate,
    authorize,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    defineLimits,
    getBlockedPlayers);

/**
 * Block players
 */

const blockPlayerByCode = async(req: Request&UserInfoHolder, res: express.Response, next: express.NextFunction) => {
    try {
        const entity = await getBrand(req);
        const service = getBlockPlayerService(entity);
        const reason: string = req.query.reason;
        const playerInfo = await service.block(req.params.playerCode, reason);

        res.send(playerInfo);
    } catch (err) {
        next(err);
    }
};

router.put("/suspendedplayers/:playerCode/suspended",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    auditable,
    blockPlayerByCode);

router.put("/entities/:path/suspendedplayers/:playerCode/suspended",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    auditable,
    blockPlayerByCode);

/**
 * Unblock players
 */
const unblockPlayerByCode = async(req: Request&UserInfoHolder, res: express.Response, next: express.NextFunction) => {
    try {
        const entity = await getBrand(req);
        await getBrandPlayerValidator().validatePlayerCanUnblock(req.params.playerCode, entity);
        const service = getBlockPlayerService(entity);
        const playerInfo = await service.unblock(req.params.playerCode);

        res.send(playerInfo);
    } catch (err) {
        next(err);
    }
};

router.delete("/suspendedplayers/:playerCode/suspended",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    auditable,
    unblockPlayerByCode);

router.delete("/entities/:path/suspendedplayers/:playerCode/suspended",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    auditable,
    unblockPlayerByCode);

export default router;
