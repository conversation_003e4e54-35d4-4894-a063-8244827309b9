import { NextFunction, Request, Response, Router } from "express";
import { authenticate, authorize, getMerchant, validate } from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { CriticalFilesServiceImpl } from "../services/criticalfiles/criticalFilesService";
import { getMerchantSearchService, MerchantImpl } from "../services/merchant";
import { ValidationError } from "../errors";

const criticalFilesApiRouter: Router = Router();

const getGameCriticalFilesInfo = async (req: Request & KeyEntityHolder, res: Response, next: NextFunction) => {
    try {
        const brand = getMerchant(req);
        const mrch = await getMerchantSearchService().findOneByEntityId(brand.id);
        validateMerchantRegulation(req, mrch);
        const gamesGroupData = await CriticalFilesServiceImpl.getGamesGroupForkData(mrch, req.body.games);
        res.send(await CriticalFilesServiceImpl.getGameCriticalFilesList(mrch,
            gamesGroupData,
            req.body.regulation,
            req.body.includeVersions));
        next();
    } catch (err) {
        return next(err);
    }
};

const getPlatformCriticalFilesInfo = async (req: Request & KeyEntityHolder, res: Response, next: NextFunction) => {
    try {
        const brand = getMerchant(req);
        const mrch = await getMerchantSearchService().findOneByEntityId(brand.id);
        validateMerchantRegulation(req, mrch);
        res.send(await CriticalFilesServiceImpl.getPlatformCriticalFilesList(mrch, req.body.regulation));
        next();
    } catch (err) {
        return next(err);
    }
};

export function validateMerchantRegulation(req: Request, mrch: MerchantImpl) {
    const regulation = mrch.params?.regulatorySettings?.merchantRegulation;
    if (regulation !== req.body.regulation) {
        throw new ValidationError("Merchant has no such regulation");
    }
}

const getCriticalFilesVersions = async (req: Request & KeyEntityHolder, res: Response, next: NextFunction) => {
    try {
        const brand = getMerchant(req);
        const mrch = await getMerchantSearchService().findOneByEntityId(brand.id);
        validateMerchantRegulation(req, mrch);
        res.send(await CriticalFilesServiceImpl.getCriticalFileModulesVersions(mrch,
            req.body.regulation,
            req.body.modules
        ));
        next();
    } catch (err) {
        return next(err);
    }
};

criticalFilesApiRouter.post("/critical-files/games/info",
    authenticate,
    authorize,
    validate({
        regulation: { isRegulation: true },
        games: { optional: true, isArray: true }
    }),
    getGameCriticalFilesInfo);

criticalFilesApiRouter.post("/critical-files/platform/info",
    authenticate,
    authorize,
    validate({
        regulation: { isRegulation: true }
    }),
    getPlatformCriticalFilesInfo);

criticalFilesApiRouter.post("/critical-files/versions",
    authenticate,
    authorize,
    validate({
        modules: { isArray: true },
        regulation: { isRegulation: true }
    }),
    getCriticalFilesVersions);

criticalFilesApiRouter.post("/entities/:path/critical-files/games/info",
    authenticate,
    authorize,
    validate({
        regulation: { isRegulation: true },
        games: { optional: true, isArray: true }
    }),
    getGameCriticalFilesInfo);

criticalFilesApiRouter.post("/entities/:path/critical-files/platform/info",
    authenticate,
    authorize,
    validate({
        regulation: { isRegulation: true }
    }),
    getPlatformCriticalFilesInfo);

criticalFilesApiRouter.post("/entities/:path/critical-files/versions",
    authenticate,
    authorize,
    validate({
        modules: { isArray: true },
        regulation: { isRegulation: true }
    }),
    getCriticalFilesVersions);

export default criticalFilesApiRouter;
