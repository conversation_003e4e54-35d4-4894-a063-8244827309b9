import * as express from "express";
import { auditable, authenticate, authorize, decodePid, getEntity } from "./middleware/middleware";
import { getLabelGroupService, queryParamsKeys } from "../services/labelGroup";
import { getSortKey, parseFilter, valueFromQuery } from "../services/filter";
import { KeyEntityHolder } from "../services/security";
import {
    DEFAULT_SORT_KEY,
    DEFAULT_SORT_ORDER,
    getLabelService,
    queryParamsKeys as labelParams,
    sortableKeys,
    SortMapping
} from "../services/label";
import { Models } from "../models/models";

const router: express.Router = express.Router();

const getEntityLabelsHandler = async (req: express.Request & KeyEntityHolder,
                                      res: express.Response,
                                      next: express.NextFunction) => {
    try {
        const entity = getEntity(req);
        const results = await getLabelService.get().getEntityLabels(entity);

        res.send(results);
    } catch (err) {
        return next(err);
    }
};

const getGameLabelsHandler = async (req: express.Request & KeyEntityHolder,
                                    res: express.Response,
                                    next: express.NextFunction) => {
    try {
        const results = await getLabelService.get().getGameLabels(req.params.gameCode);

        res.send(results);
    } catch (err) {
        return next(err);
    }
};

const getPromoLabelsHandler = async (req: express.Request & KeyEntityHolder,
                                     res: express.Response,
                                     next: express.NextFunction) => {
    try {
        const entity = getEntity(req);
        const results = await getLabelService.get().getPromoLabels(entity, req.params.promoId);

        res.send(results);
    } catch (err) {
        return next(err);
    }
};

const createGameLabelsHandler = async (req: express.Request & KeyEntityHolder,
                                       res: express.Response,
                                       next: express.NextFunction) => {
    try {
        const results = await getLabelService.get().setGameLabels(req.params.gameCode, req.body);

        res.send(results);
    } catch (err) {
        return next(err);
    }
};

const createEntityLabelsHandler = async (req: express.Request & KeyEntityHolder,
                                         res: express.Response,
                                         next: express.NextFunction) => {
    try {
        const entity = getEntity(req);
        const results = await getLabelService.get().setEntityLabels(entity, req.body);

        res.send(results);
    } catch (err) {
        return next(err);
    }
};

const createPromoLabelsHandler = async (req: express.Request & KeyEntityHolder,
                                        res: express.Response,
                                        next: express.NextFunction) => {
    try {
        const entity = getEntity(req);
        const results = await getLabelService.get().setPromoLabels(entity, req.params.promoId, req.body);

        res.send(results);
    } catch (err) {
        return next(err);
    }
};

router.get("/entities/:path/entity-labels",
    authenticate,
    authorize,
    getEntityLabelsHandler);

router.put("/entities/:path/entity-labels",
    authenticate,
    authorize,
    auditable,
    decodePid(),
    createEntityLabelsHandler);

router.get("/entities/:path/promo/:promoId/promo-labels",
    authenticate,
    authorize,
    decodePid(),
    getPromoLabelsHandler);

router.put("/entities/:path/promo/:promoId/promo-labels",
    authenticate,
    authorize,
    auditable,
    decodePid(),
    createPromoLabelsHandler);

router.get("/games/:gameCode/game-labels",
    authenticate,
    authorize,
    getGameLabelsHandler);

router.put("/games/:gameCode/game-labels",
    authenticate,
    authorize,
    auditable,
    decodePid(),
    createGameLabelsHandler);

router.get("/entity-labels",
    authenticate,
    authorize,
    getEntityLabelsHandler);

router.put("/entity-labels",
    authenticate,
    authorize,
    auditable,
    decodePid(),
    createEntityLabelsHandler);

router.get("/promo/:promoId/promo-labels",
    authenticate,
    authorize,
    decodePid(),
    getPromoLabelsHandler);

router.put("/promo/:promoId/promo-labels",
    authenticate,
    authorize,
    auditable,
    decodePid(),
    createPromoLabelsHandler);

router.get("/labels",
    authenticate,
    authorize,
    async (req: express.Request, res: express.Response, next: express.NextFunction) => {
        try {
            const sortBy = getSortKey(req.query, sortableKeys, DEFAULT_SORT_KEY);
            const sortOrder = valueFromQuery(req.query, "sortOrder") || DEFAULT_SORT_ORDER;

            const instances = await getLabelService.get().list({
                include: [
                    {
                        model: Models.LabelGroupModel,
                        as: "group",
                        where: parseFilter(req.query, labelParams)
                    },
                ],
                where: parseFilter(req.query, ["title"]),
                order: [[SortMapping[sortBy], sortOrder]],
            });
            res.send(instances.map(instance => instance.toJSON()));
        } catch (err) {
            return next(err);
        }
    });

router.post("/labels",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: express.Request, res: express.Response, next: express.NextFunction) => {
        try {
            const label = await getLabelService.get().create(req.body);
            res.send(label.toJSON());
        } catch (err) {
            return next(err);
        }
    });

router.patch("/labels/:labelId",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: express.Request, res: express.Response, next: express.NextFunction) => {
        try {
            const label = await getLabelService.get().update(req.params.labelId, { title: req.body.title });
            res.send(label.toJSON());
        } catch (err) {
            return next(err);
        }
    });

router.get("/label-groups",
    authenticate,
    authorize,
    async (req: express.Request, res: express.Response, next: express.NextFunction) => {
        try {
            const labelGroups = await getLabelGroupService.get().list({
                where: parseFilter(req.query, queryParamsKeys)
            });
            res.send(labelGroups.map(item => item.toJSON()));
        } catch (err) {
            return next(err);
        }
    });

router.post("/label-groups",
    authenticate,
    authorize,
    auditable,
    async (req: express.Request, res: express.Response, next: express.NextFunction) => {
        try {
            const labelGroup = await getLabelGroupService.get().create(req.body);
            res.send(labelGroup.toJSON());
        } catch (err) {
            return next(err);
        }
    });

router.delete("/label",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: express.Request, res: express.Response, next: express.NextFunction) => {
        try {
            await getLabelService.get().destroy(req.body.id);
            res.status(204).end();
        } catch (err) {
            return next(err);
        }
    });

export default router;
