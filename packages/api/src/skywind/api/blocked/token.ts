import { NextFunction, Request as ExpressRequest, Response, Router } from "express";
import { auditable, authenticate, authorize, decodePid } from "../middleware/middleware";
import { KeyEntityHolder } from "../../services/security";
import { getTokenService } from "../../services/blocked/tokenService";
const router: Router = Router();
type Request = ExpressRequest & KeyEntityHolder;

router.post("/entities/:entityId/terminal-token/lock",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    lockTerminalToken
);
export async function lockTerminalToken(req: Request, res: Response, next: NextFunction) {
    try {
        await getTokenService(req.params.entityId).lockToken(req.body.token);
        res.status(201).end();
    } catch (err) {
        next(err);
    }
}

router.delete("/entities/:entityId/terminal-token/lock",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    unlockTerminalToken
);
export async function unlockTerminalToken(req: Request, res: Response, next: NextFunction) {
    try {
        await getTokenService(req.params.entityId).unlockToken();
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

export default router;
