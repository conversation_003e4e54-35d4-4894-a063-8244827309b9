import logger from "../../utils/logger";
import { FastifyInstance, FastifyRequest } from "fastify";
import { Request, Response } from "../../bootstrap/fastify";
import { parseInternalToken } from "../../services/security";
import { defaultOperatorDetailsRepository } from "../../services/gameauth/defaultOperatorInfoRepository";
import { INTERNAL_TOKEN_VALIDATOR } from "./validators";
import { getBooleanParamFromRequestQuery } from "../middleware/middleware";

const log = logger("game-auth");

export default function(router: FastifyInstance, options, done) {
    router.get("/operator", INTERNAL_TOKEN_VALIDATOR, getOperatorInfo);
    done();
}

async function getOperatorInfo(req: FastifyRequest<{ Querystring: { token: string } }> & Request, res: Response) {
    const { id }: { id: string } = await parseInternalToken(req.query.token);
    log.info({ id }, req.url);
    const result = await defaultOperatorDetailsRepository.get().findById(
        id,
        getBooleanParamFromRequestQuery(req, "ignoreValidations")
    );

    log.info({ response: result }, req.url);
    return res.send(result);
}
