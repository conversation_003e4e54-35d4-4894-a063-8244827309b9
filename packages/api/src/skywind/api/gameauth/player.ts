import { FastifyInstance, FastifyRequest } from "fastify";
import { CHANGE_NICKNAME_VALIDATOR } from "./validators";
import { Request, Response } from "../../bootstrap/fastify";
import { verifyGameToken } from "../../services/playService";
import EntityCache from "../../cache/entity";
import { GameTokenData } from "@skywind-group/sw-wallet-adapter-core";
import { PlayerChangeNicknameRequest } from "../../entities/gameprovider";
import { getPlayerInfoService } from "../../services/playerInfo";
import { ENTITY_TYPE } from "../../entities/entity";
import logger from "../../utils/logger";
import config from "../../config";

const log = logger("game-auth");

export default function(router: FastifyInstance, options, done) {
    router.put("/player/change-nickname", CHANGE_NICKNAME_VALIDATOR, putChangeNickname);
    done();
}

async function putChangeNickname(req: FastifyRequest<{ Body: PlayerChangeNicknameRequest }> & Request, res: Response) {
    const request: PlayerChangeNicknameRequest = req.body;
    log.info({ request }, req.url);
    const gameTokenData: GameTokenData = await verifyGameToken(request.gameToken);
    const entity = await EntityCache.findOne({ id: gameTokenData.brandId }, undefined, true);
    let result;
    try {
        const playerInfo = await getPlayerInfoService().createOrUpdate({
            playerCode: gameTokenData.playerCode,
            brandId: gameTokenData.brandId,
            nickname: request.nickname,
            isMerchantPlayer: entity.type === ENTITY_TYPE.MERCHANT
        }, {
            increaseNicknameChangeAttempts:
                typeof request.increaseNicknameChangeAttempts !== "undefined"
                ? request.increaseNicknameChangeAttempts
                : true
        });

        result = {
            nickname: request.nickname,
            nicknameChangeAttemptsLeft: config.playerNicknameChangeAttemptsLimit - playerInfo.nicknameChangeAttempts
        };

    } catch (err) {
        result = { code: err.code, message: err.message };
    }
    log.info({ response: result }, req.url);
    return res.send(result);
}
