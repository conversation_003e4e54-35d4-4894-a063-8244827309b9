import { FastifyInstance, FastifyRequest } from "fastify";
import * as PlayerGameHistoryService from "../../services/playerGameHistory";
import { encodePublicIdWrapper, pagingWrapper } from "../middleware/middleware";
import * as HistoryService from "../../history/gameHistory";
import { GameHistorySpinDetails } from "../../history/gameHistory";
import { Request, Response } from "../../bootstrap/fastify";
import {
    GET_EVENT_DETAILS_VALIDATOR,
    GET_EVENTS_VALIDATOR,
    GET_GAME_VERSION_VALIDATOR, GET_REPLAY_EVENTS_VALIDATOR, GET_REPLAY_GAME_VERSION_VALIDATOR,
    GET_ROUND_DETAILS_VALIDATOR,
    GET_ROUND_EVENTS_VALIDATOR,
    GET_ROUNDS_VALIDATOR,
    GET_SPIN_DETAILS_VALIDATOR
} from "./validators";
import { RoundHistory } from "@skywind-group/sw-wallet-adapter-core";
import { GameHistorySpin } from "../../history/spinHistory";
import { EventHistoryExtraDetails } from "../../entities/gameHistory";

export default function (router: FastifyInstance, options, done) {
    router.get("/history/rounds", GET_ROUNDS_VALIDATOR, getPlayerRoundsHistory);
    router.get("/history/rounds/:roundId", GET_ROUND_EVENTS_VALIDATOR, getPlayerRoundEventsHistory);
    router.get("/history/rounds/:roundId/events/:eventId", GET_EVENT_DETAILS_VALIDATOR, getPlayerEventDetails);
    router.get("/history/events", GET_EVENTS_VALIDATOR, getPlayerEventsHistory);
    router.get("/history/replay", GET_REPLAY_EVENTS_VALIDATOR, getReplayHistory);
    router.get("/history/replay/gameVersion", GET_REPLAY_GAME_VERSION_VALIDATOR, getReplayGameVersion);
    router.get("/history/gameVersion", GET_GAME_VERSION_VALIDATOR, getGameVersion);
    router.get("/history/gh-app/spin-details", GET_SPIN_DETAILS_VALIDATOR, getGameHistoryDetailsForGHApp);
    router.get("/history/gh-app/round-details", GET_ROUND_DETAILS_VALIDATOR, getRoundHistoryDetailsForGHApp);
    router.get("/history/gh-app/round-info", GET_SPIN_DETAILS_VALIDATOR, getRoundInfoForGHApp);

    done();
}

async function getGameHistoryDetailsForGHApp(req: FastifyRequest<{ Querystring: { token: string } }> & Request, res: Response) {
    const result: EventHistoryExtraDetails = await HistoryService.getGameHistoryDetailsForGHApp(req.query.token);
    return encodePublicIdWrapper(result);
}

async function getRoundHistoryDetailsForGHApp(req: FastifyRequest<{ Querystring: { token: string } }> & Request, res: Response) {
    const result: GameHistorySpinDetails[] = await HistoryService.getRoundHistoryDetailsForGHApp(
        req.query.token,
        req.query["type__in"]
    );

    return pagingWrapper(encodePublicIdWrapper(result), req.raw, res.raw);
}

async function getRoundInfoForGHApp(req: FastifyRequest<{ Querystring: { token: string } }> & Request, res: Response) {
    const result: RoundHistory = await HistoryService.getRoundInfoForGHApp(req.query.token);

    return pagingWrapper(encodePublicIdWrapper(result), req.raw, res.raw);
}

/**
 *  Get player round details
 *
 */
async function getPlayerRoundEventsHistory(req: FastifyRequest<{ Params: { roundId: number } }> & Request, res: Response) {
    const result: GameHistorySpin[] = await PlayerGameHistoryService.getPlayerRoundEventsHistory(
        req.query,
        req.params.roundId
    );
    return pagingWrapper(encodePublicIdWrapper(result), req.raw, res.raw);
}

/**
 *  Get player event details
 *
 */
async function getPlayerEventDetails(req: FastifyRequest<{
    Querystring: { gameToken: string },
    Params: { roundId: number, eventId: number },
}> & Request, res: Response) {
    const result: EventHistoryExtraDetails = await PlayerGameHistoryService.getPlayerEventDetails(
        req.query.gameToken,
        req.params.roundId,
        req.params.eventId
    );
    return encodePublicIdWrapper(result);
}

/**
 * Get player events list
 */
async function getPlayerEventsHistory(req: FastifyRequest & Request, res: Response) {
    const result: GameHistorySpinDetails[] = await PlayerGameHistoryService.getPlayerEventsHistory(req.query);
    return encodePublicIdWrapper(result);
}

/**
 * Get player replay events list
 */
async function getReplayHistory(req: FastifyRequest & Request, res: Response) {
    const result: GameHistorySpinDetails[] = await PlayerGameHistoryService.getReplayHistory(req.query);
    return encodePublicIdWrapper(result);
}

/**
 *  Get player round list
 */
async function getPlayerRoundsHistory(req: FastifyRequest & Request, res: Response) {
    const result: RoundHistory[] = await PlayerGameHistoryService.getPlayerRoundsHistory(req.query);

    return pagingWrapper(encodePublicIdWrapper(result), req.raw, res.raw);
}

/**
 * Get game version details
 */
async function getGameVersion(req: FastifyRequest & Request, res: Response) {
    return PlayerGameHistoryService.getGameVersion(req.query);
}

/**
 * Get replay game version details
 */
async function getReplayGameVersion(req: FastifyRequest & Request, res: Response) {
    return PlayerGameHistoryService.getReplayGameVersion(req.query);
}
