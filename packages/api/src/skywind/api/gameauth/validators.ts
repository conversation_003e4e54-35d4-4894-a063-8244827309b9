import { FastifyRequest, RouteShorthandOptions } from "fastify";
import { Request, Response } from "../../bootstrap/fastify";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { MAX_INT_VALUE } from "../../utils/common";
import { decodePid, setMerchantAuthContext } from "../middleware/middleware";

export function sanitizeLanguage(req: FastifyRequest & Request, res: Response, next) {
    if (req.query["language"]) {
        req.query["language"] = req.query["language"].toLowerCase();
    }
    next();
}

export const PLAYMODE_VALIDATOR = {
    playmode: { type: "string", enum: Object.values(PlayMode) },
    playMode: { type: "string", enum: Object.values(PlayMode) }
};

export const PLAYER_GAME_URL_VALIDATOR: RouteShorthandOptions = {
    schema: {
        body: {
            type: "object",
            properties: {
                gameId: { type: "string" },
                providerGameCode: {type: "string"},
                ...PLAYMODE_VALIDATOR
            },
            required: ["startGameToken"],
            anyOf: [
                {
                    required: ["gameId"],
                },
                {
                    required: ["providerGameCode"],
                },
            ]
        }
    }
};

export const PLAYER_GET_GAMES_VALIDATOR: RouteShorthandOptions = {
    schema: {
        body: {
            type: "object",
            properties: {
                startGameToken: { type: "string" },
                filter: { type: "object" }
            },
            required: ["startGameToken"]
        }
    }
};

export const GET_GAME_VERSION_VALIDATOR: RouteShorthandOptions = {
    schema: {
        querystring: {
            type: "object",
            properties: {
                gameToken: { type: "string" },
                gameVersion: { type: "string" },
            },
            required: ["gameToken", "gameVersion"]
        }
    }
};

export const GET_REPLAY_GAME_VERSION_VALIDATOR: RouteShorthandOptions = {
    schema: {
        querystring: {
            type: "object",
            properties: {
                replayToken: { type: "string" },
                gameVersion: { type: "string" },
                gameCode: { type: "string" }
            },
            required: ["replayToken", "gameVersion", "gameCode"]
        }
    }
};

export const MERCHANT_GAME_URL_VALIDATOR: RouteShorthandOptions = {
    preHandler: [sanitizeLanguage, setMerchantAuthContext],
    schema: {
        body: {
            type: "object",
            properties: {
                merchantCode: { type: "string" },
                gameCode: { type: "string" },
                ticket: { type: "string" },
                ...PLAYMODE_VALIDATOR
            },
            required: ["merchantType", "merchantCode", "gameCode"]
        }
    }
};

export const INTERNAL_TOKEN_VALIDATOR: RouteShorthandOptions = {
    schema: {
        querystring: {
            type: "object",
            properties: {
                token: { type: "string" },
            },
            required: ["token"]
        }
    }
};

export const PROVIDER_AUTH_TOKEN = "x-auth-token";

export const ANONYMOUS_SETTINGS_VALIDATOR: RouteShorthandOptions = {
    schema: {
        headers: {
            type: "object",
            properties: {
                [PROVIDER_AUTH_TOKEN]: { type: "string" }
            },
            required: [PROVIDER_AUTH_TOKEN]
        },
        querystring: {
            type: "object",
            properties: {
                brandId: { type: "number" },
                gameCode: { type: "string" },
                playMode: { type: "string" },
            },
            required: ["brandId", "gameCode", "playMode"]
        }
    }
};

export const PROVIDER_AUTH_TOKEN_VALIDATOR: RouteShorthandOptions = {
    schema: {
        headers: {
            type: "object",
            properties: {
                PROVIDER_AUTH_TOKEN: { type: "string" }
            },
            required: [PROVIDER_AUTH_TOKEN]
        }
    }
};

export const START_GAME_TOKEN_VALIDATOR: RouteShorthandOptions = {
    schema: {
        body: {
            type: "object",
            properties: {
                startGameRequest: {
                    type: "object",
                    properties: {
                        startGameToken: { type: "string" }
                    },
                    required: ["startGameToken"]
                },
                ip: { type: "string" },
                referrer: { type: "string" }
            },
            required: ["startGameRequest"]
        }
        /*body: {
            startGameRequest: {
                type: "object",
                properties: {
                    startGameToken: { type: "string" }
                },
                required: ["startGameToken"]
            },
            ip: {
                type: "string"
            },

            referrer: {
                type: "string"
            },
            required: ["startGameRequest"]
        }*/
    }
};

export const FUN_START_GAME_TOKEN_VALIDATOR: RouteShorthandOptions = {
    schema: {
        body: {
            type: "object",
            properties: {
                startGameRequest: {
                    type: "object",
                    properties: {
                        startGameToken: {
                            anyOf: [
                                { type: "string" },
                                { type: "object" }
                            ]
                        }
                    },
                    required: ["startGameToken"]
                },
                ip: { type: "string" },
                referrer: { type: "string" }
            },
            required: ["startGameRequest"]
        }
    }
};

const validatePaging = {
    offset: { type: "integer", minimum: 0, maximum: 10000 },
    limit: { type: "integer", minimum: 0, maximum: 200 },
};
const validateTsPaging = {
    ts: { type: "string", format: "date-time" },
    ts__lt: { type: "string", format: "date-time" },
    ts__lte: { type: "string", format: "date-time" },
    ts__gt: { type: "string", format: "date-time" },
    ts__gte: { type: "string", format: "date-time" },
    limit: { type: "integer", minimum: 1, maximum: MAX_INT_VALUE },
    offset: { type: "integer", minimum: 0, maximum: MAX_INT_VALUE },
    sortOrder: { type: "string", enum: ["ASC", "DESC"] }
};

const VALIDATE_GAME_TYPE = {
    type: { type: "string", pattern: "^[\\w\\-@._\\s]+$" },
    type__in: { type: "string", pattern: "^[\\w\\-@._\\s,]+$" }
};

const VALIDATE_INSERTED_AT = {
    insertedAt: { type: "string", format: "date-time" },
    insertedAt__lt: { type: "string", format: "date-time" },
    insertedAt__lte: { type: "string", format: "date-time" },
    insertedAt__gt: { type: "string", format: "date-time" },
    insertedAt__gte: { type: "string", format: "date-time" },
};

export const GET_ROUNDS_VALIDATOR: RouteShorthandOptions = {
    onRequest: decodePid(),
    schema: {
        querystring: {
            type: "object",
            properties: {
                gameToken: { type: "string" },
                ...validateTsPaging
            },
            required: ["gameToken"]
        }
    }
};

export const GET_ROUND_EVENTS_VALIDATOR: RouteShorthandOptions = {
    onRequest: decodePid(),
    schema: {
        params: {
            type: "object",
            properties: {
                roundId: { type: "string" }
            },
            required: ["roundId"]
        },
        querystring: {
            type: "object",
            properties: {
                sortOrder: { type: "string", enum: ["ASC", "DESC"] },
                gameToken: { type: "string" },
                roundId: { type: "integer" },
                ...validatePaging,
                ...VALIDATE_GAME_TYPE
            }
        }
    }
};

export const GET_EVENT_DETAILS_VALIDATOR: RouteShorthandOptions = {
    onRequest: decodePid({ forceReturnIfNumber: true }),
    schema: {
        params: {
            type: "object",
            properties: {
                roundId: { type: "string" },
                eventId: { type: "string" }
            },
            required: ["roundId", "eventId"]
        },
        querystring: {
            type: "object",
            properties: {
                gameToken: { type: "string" }
            },
            required: ["gameToken"]
        }
    }
};

export const GET_EVENTS_VALIDATOR: RouteShorthandOptions = {
    onRequest: decodePid({ forceReturnIfNumber: true }),
    schema: {
        querystring: {
            type: "object",
            properties: {
                gameToken: { type: "string" },
                roundId: { type: "string" },
                ...validateTsPaging,
                ...VALIDATE_INSERTED_AT,
                ...VALIDATE_GAME_TYPE
            },
            required: ["gameToken"]
        }
    }
};

export const GET_REPLAY_EVENTS_VALIDATOR: RouteShorthandOptions = {
    onRequest: decodePid({ forceReturnIfNumber: true }),
    schema: {
        querystring: {
            type: "object",
            properties: {
                replayToken: { type: "string" },
                ...validateTsPaging,
                ...VALIDATE_INSERTED_AT,
                ...VALIDATE_GAME_TYPE
            },
            required: ["replayToken"]
        }
    }
};

export const GET_SPIN_DETAILS_VALIDATOR: RouteShorthandOptions = {
    schema: {
        querystring: {
            type: "object",
            properties: {
                token: { type: "string" }
            },
            required: ["token"]
        }
    }
};

export const GET_ROUND_DETAILS_VALIDATOR: RouteShorthandOptions = {
    schema: {
        querystring: {
            type: "object",
            properties: {
                token: { type: "string" },
                type__in: { type: "string" }
            },
            required: ["token"]
        }
    }
};

export const CHANGE_NICKNAME_VALIDATOR: RouteShorthandOptions = {
    schema: {
        body: {
            type: "object",
            properties: {
                gameToken: { type: "string" },
                nickname: { type: "string" }
            },
            required: ["gameToken", "nickname"]
        }
    }
};
