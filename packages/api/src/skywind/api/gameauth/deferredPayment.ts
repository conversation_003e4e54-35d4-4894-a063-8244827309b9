import { FastifyInstance, FastifyRequest } from "fastify";
import { Request, Response } from "../../bootstrap/fastify";
import { defaultDeferredRegistrationService } from "../../services/deferredPayments";
import { BaseError as DeferredPaymentError, DeferredPaymentRegistration } from "@skywind-group/sw-deferred-payment";
import { InternalSecret, SW_SIGNATURE } from "@skywind-group/sw-deferred-payment-client";
import config from "../../config";
import { RAW_BODY } from "./jsonRawParser";

export default function(router: FastifyInstance, options, done) {
    router.post("/registration", register);
    done();
}

/**
 * This method copy the deferred-payment api protocol:
 *  - validate the checksum in x-sw-signature method
 *
 *  If case if we had error, we serialize it to the format of deferred-payment-api:
 *  {
 *      type: <classname>
 *      ....
 *  }
 */
interface DeferredPaymentHeaders {
    "x-sw-signature": string
}
async function register(req: FastifyRequest<{
    Body: DeferredPaymentRegistration,
    Headers: DeferredPaymentHeaders
}> & Request, res: Response) {
    try {
        validateSignature(req, res);
        const registration: DeferredPaymentRegistration = req.body;
        const result = await defaultDeferredRegistrationService.get().register(registration);
        return res.send(result);
    } catch (error) {
        if (error instanceof DeferredPaymentError) {
            res.code(error.responseStatus || 500).send({ type: error.type, ...error });
        } else {
            throw error;
        }
    }
}

/**
 * Validate signature for body
 */
function validateSignature(req: FastifyRequest<{ Headers: DeferredPaymentHeaders }> & Request, res: Response) {
    if (config.deferredPayment.validateSignature) {
        const internalSecret = new InternalSecret(config.internalServerToken.secret);
        internalSecret.verify(
            internalSecret.getSigningContent(req[RAW_BODY], req.query),
            req.headers[SW_SIGNATURE]
        );
    }
}
