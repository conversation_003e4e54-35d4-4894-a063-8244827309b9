import {
    apiSwaggeGameAuth,
} from "../../utils/swagger";
import { FastifyInstance } from "fastify";
import gameauth from "./gameauth";
import gameLaunch from "./gameLauncher";
import game from "./game";
import operator from "./operator";
import history from "./history";
import deferredPayment from "./deferredPayment";
import { parseJsonWithRaw } from "./jsonRawParser";
import player from "./player";
import config from "../../config";
import { defineSwaggerFastify } from "../fastifyRouters/swagger";

export async function define(app: FastifyInstance) {
    app.addContentTypeParser("application/json", { parseAs: "string" }, parseJsonWithRaw);
    app.register(gameauth, { prefix: "/v1" });
    app.register(gameLaunch, { prefix: "/v1" });
    app.register(game, { prefix: "/v1" });
    app.register(history, { prefix: "/v1" });
    app.register(operator, { prefix: "/v1" });
    app.register(player, { prefix: "/v1" });
    app.register(deferredPayment, { prefix: "/deferred-payment/v1" });

    if (!config.isProduction()) {
        const swaggerJson = apiSwaggeGameAuth();
        await defineSwaggerFastify(app, swaggerJson);
    }
}
