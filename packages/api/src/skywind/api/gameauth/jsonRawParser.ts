import { FastifyRequest } from "fastify";

export const RAW_BODY = Symbol("rawBody");

/**
 * Parse the body and attach additional information to the original request with the raw text body
 */
export function parseJsonWithRaw(req: FastifyRequest, body, done: (error: Error, result: any) => void) {
    try {
        req[RAW_BODY] = body;
        done(null, JSON.parse(body));
    } catch (error) {
        done(error, undefined);
    }
}
