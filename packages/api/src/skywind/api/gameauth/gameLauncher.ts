import { FastifyInstance, FastifyRequest } from "fastify";
import { Request, Response } from "../../bootstrap/fastify";
import * as gameLauncher from "../../services/gameLauncher";

export default function(router: FastifyInstance, options, done) {
    router.post("/game/launch", launchGame);
    done();
}

async function launchGame(req: FastifyRequest<{ Body: gameLauncher.GameLauncherRequest }> & Request, res: Response) {
    return gameLauncher.launch(req.body);
}
