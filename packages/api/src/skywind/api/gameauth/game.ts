import { Request, Response } from "../../bootstrap/fastify";
import { MerchantGameInitRequest } from "@skywind-group/sw-wallet-adapter-core";
import { PlayerGameURLInfo } from "../../entities/game";
import { getMerchantService } from "../../services/merchant";
import { GamesListRequest, PlayerGameInfoRequest, } from "../../entities/gameprovider";
import logger from "../../utils/logger";
import { FastifyInstance, FastifyRequest } from "fastify";
import * as PlayService from "../../services/playService";
import {
    MERCHANT_GAME_URL_VALIDATOR,
    PLAYER_GAME_URL_VALIDATOR,
    PLAYER_GET_GAMES_VALIDATOR
} from "./validators";

const log = logger("game-auth");

export default function(router: FastifyInstance, options, done) {

    router.post("/game/url", PLAYER_GAME_URL_VALIDATOR, getPlayerGameUrlInfo);
    router.post("/games", PLAYER_GET_GAMES_VALIDATOR, getGames);
    router.post("/merchants/game/url", MERCHANT_GAME_URL_VALIDATOR, getMerchantGameUrl);

    done();
}

export async function getMerchantGameUrl(req: FastifyRequest<{ Body: MerchantGameInitRequest }> & Request,
                                         res: Response) {
    const request: MerchantGameInitRequest = req.body;
    const info: PlayerGameURLInfo = await getMerchantService().getGameUrl(request, { ip: req.query["ip"] });
    return res.send(info);
}

async function getGames(req: FastifyRequest<{ Body: GamesListRequest }> & Request, res: Response) {
    const request: GamesListRequest = req.body;
    const result = await PlayService.getGames(request);
    return res.send(result);
}

async function getPlayerGameUrlInfo(req: FastifyRequest<{ Body: PlayerGameInfoRequest }> & Request, res: Response) {
    const request: PlayerGameInfoRequest = req.body;
    const result = await PlayService.getPlayerGameURLInfo(request, req.query["ip"]);
    return res.send(result);
}
