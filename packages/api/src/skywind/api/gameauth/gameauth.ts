import logger from "../../utils/logger";
import { FastifyInstance, FastifyRequest } from "fastify";
import { Request, Response } from "../../bootstrap/fastify";
import { GameTokenData } from "@skywind-group/sw-wallet-adapter-core";
import { parseInternalToken } from "../../services/security";
import {
    ProviderAuthDetails,
    SettingsRequest,
    SettingsWithoutToken,
    StartGameRequest
} from "@skywind-group/sw-management-gameprovider";
import { DefaultGameSessionFactory } from "../../services/gameauth/sessionFactory";
import { defaultOperatorDetailsRepository } from "../../services/gameauth/defaultOperatorInfoRepository";
import { DefaultSettingsWithoutTokenService } from "../../services/gameauth/defaultSettingsWithoutTokenService";
import { defaultStartGameService } from "../../services/gameauth/defaultStartGameService";
import {
    ANONYMOUS_SETTINGS_VALIDATOR,
    FUN_START_GAME_TOKEN_VALIDATOR,
    INTERNAL_TOKEN_VALIDATOR,
    PROVIDER_AUTH_TOKEN,
    PROVIDER_AUTH_TOKEN_VALIDATOR,
    START_GAME_TOKEN_VALIDATOR
} from "./validators";
import { verifyInternalToken } from "../../utils/token";
import { RawWallet } from "@skywind-group/sw-wallet";

const log = logger("game-auth");
 interface AuthTokenHeaders {
     "x-auth-token": string;
}

export default function(router: FastifyInstance, options, done) {
    router.get("/game/session", INTERNAL_TOKEN_VALIDATOR, getGameSessionInfo);
    router.get("/game/anonymous/settings", ANONYMOUS_SETTINGS_VALIDATOR, getSettingsWithoutToken);
    router.get("/game/anonymous/auth", PROVIDER_AUTH_TOKEN_VALIDATOR, authGameProvider);
    router.post("/game/start", START_GAME_TOKEN_VALIDATOR, startGame);
    router.post("/game/fun/start", FUN_START_GAME_TOKEN_VALIDATOR, startFunGame);
    done();
}

const gameSessionFactory = new DefaultGameSessionFactory();
const settingsWithoutTokenService =
    new DefaultSettingsWithoutTokenService(defaultOperatorDetailsRepository.get());

async function getGameSessionInfo(req: FastifyRequest<{ Querystring: { token: string } }> & Request, res: Response) {
    const gameTokenData: GameTokenData = await parseInternalToken(req.query.token);
    log.info({ gameTokenData }, req.url);
    const result = await gameSessionFactory.create(gameTokenData);
    log.info({ response: result }, req.url);
    return res.send(result);
}

async function getSettingsWithoutToken(req: FastifyRequest<{
    Querystring: SettingsRequest,
    Headers: AuthTokenHeaders,
}> & Request, res: Response) {
    const auth: ProviderAuthDetails = await verifyInternalToken<ProviderAuthDetails>(req.headers[PROVIDER_AUTH_TOKEN]);
    const request: SettingsRequest = req.query as SettingsRequest;
    log.info({ req }, req.url);
    const result: SettingsWithoutToken = await settingsWithoutTokenService.getSettings(auth, request);
    log.info({ response: result }, req.url);
    return res.send(result);
}

async function authGameProvider(req: FastifyRequest<{
    Headers: AuthTokenHeaders
}> & Request, res: Response) {
    const auth: ProviderAuthDetails = await verifyInternalToken<ProviderAuthDetails>(req.headers[PROVIDER_AUTH_TOKEN]);
    log.info({ req }, req.url);
    await settingsWithoutTokenService.auth(auth);
    res.code(201).send();
}

/**
 *  Authenticate game server and start game
 *
 */
interface AuthGameRequest {
    readonly startGameRequest: StartGameRequest;
    readonly ip?: string;
    readonly referrer?: string;
    readonly wallet?: RawWallet;
}

export async function startGame(req: FastifyRequest<{
    Body: AuthGameRequest
}> & Request, res: Response) {
    const { startGameRequest, ip, referrer, wallet }: AuthGameRequest = req.body;
    const result = await defaultStartGameService.get().startGame(startGameRequest, ip, referrer, wallet);
    return res.send(result);
}

/**
 *  Authenticate game server and start fun game
 *
 */
export async function startFunGame(req: FastifyRequest<{
    Body: AuthGameRequest
}> & Request, res: Response) {
    const { startGameRequest } = req.body;
    const result = await defaultStartGameService.get().startFunGame(startGameRequest);
    return res.send(result);
}
