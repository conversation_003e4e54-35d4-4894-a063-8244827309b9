import type { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import { getEnvironmentInfo } from "@skywind-group/sw-utils";
import config from "../../config";

export default function(router: FastifyInstance, options, done) {
    router.get("/health",
        (req: FastifyRequest, res: FastifyReply) => {
            res.send({
                serverName: config.server.getName(),
                ...getEnvironmentInfo()
            });
        });

    done();
}
