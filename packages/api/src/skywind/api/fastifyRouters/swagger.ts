import { resolve } from "node:path";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import { initializeSwaggerMiddleware } from "../../utils/swagger";
import * as fastifyStatic from "@fastify/static";

function getFullPath(version: string): string {
    return version === "" ? "" : `/${version}`;
}

async function initSwagger(app: FastifyInstance, swaggerJson: any, partOfPath: string = ""): Promise<any> {
    const middleware = await initializeSwaggerMiddleware(swaggerJson);
    const swaggerVersion = getFullPath(partOfPath);

    app.register(fastifyStatic, {
        root: resolve(process.cwd(), "swagger-resources"),
        prefix: "/swagger-resources",
        decorateReply: false,
        prefixAvoidTrailingSlash: true,
    });

    const swaggerResourcesPath = partOfPath === "" ? partOfPath : `-${partOfPath}`;
    app.register(fastifyStatic, {
        root: resolve(process.cwd(), `swagger-ui${swaggerResourcesPath}`),
        prefix: `${swaggerVersion}/docs`,
        decorateReply: false,
        prefixAvoidTrailingSlash: true
    });

    app.addHook("onRequest", async (request, reply): Promise<void> => {
        const url = request.url;
        if (
            url.startsWith("/swagger-resources") ||
            url.startsWith(`${swaggerVersion}/docs`) ||
            url.startsWith(`${swaggerVersion}/api-docs`)
        ) {
            await swaggerCspMiddleware(request, reply);
        }
    });

    // It is needed for checking permissions from swagger schema
    /*app.addHook("onRequest", (req, reply, next) => {
        middleware.swaggerMetadata()(req, reply, next);
    });*/
}

export async function defineSwaggerFastify(app: FastifyInstance, schema: any, partOfPath: string = "") {
    await initSwagger(app, schema, partOfPath);
    const swaggerVersion = getFullPath(partOfPath);
    app.get(`${swaggerVersion}/api-docs`, (req, res) => {
        res.send(schema);
    });
}

export const swaggerCspMiddleware = async (req: FastifyRequest, reply: FastifyReply) => {
    reply.header(
        "Content-Security-Policy",
        "default-src 'self'; " +
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
        "font-src 'self' https://fonts.gstatic.com; " +
        "img-src 'self' data: https://online.swagger.io https://validator.swagger.io; " +
        "script-src 'self' 'unsafe-inline'; " +
        "connect-src 'self' https://online.swagger.io;"
    );
};
