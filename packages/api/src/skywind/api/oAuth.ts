import { Router, Request, Response, NextFunction } from "express";
import {
    auditLogin,
    finishAuditSession,
    getDisplayLoginInfo,
    getLangFromAcceptLangHeader,
    getRefererDomain
} from "./login";
import { auditable, authenticateLogout, checkOAuthEnabled, validate } from "./middleware/middleware";
import { getOAuthService } from "../services/oAuthService";
import { IpHolder } from "../services/security";
import config from "../config";
import { OAUTH_TOKEN_COOKIE, OAuthTokenCookie } from "../entities/oAuth";
import { GrantType } from "@skywind-group/sw-falcon-oauth";
import { X_OAUTH_TOKEN } from "../utils/common";
import * as Errors from "../errors";

const mung = require("express-mung");

const validateOAuthTokenGrant = validate({
    authorizationCode: { notEmpty: true, isString: true },
    grantType: { optional: true, isString: true }
});

const router: Router = Router();

router.get("/oauth/callback", checkOAuthEnabled, oAuthCallback);
router.post("/oauth/token", checkOAuthEnabled, validateOAuthTokenGrant, mung.json(auditLogin), getToken);
router.post("/oauth/logout",
    checkOAuthEnabled,
    authenticateLogout,
    auditable,
    mung.headers(finishAuditSession),
    logout
);

async function oAuthCallback(req: Request & IpHolder, res: Response, next: NextFunction): Promise<void> {
    try {
        const { authorizationCode } = req.query;
        const oAuthService = getOAuthService();
        const tokenInfo = await oAuthService.getToken({
            authorizationCode,
            grantType: GrantType.REFRESH_TOKEN,
            redirectUri: config.oAuth.redirectUri,
            language: getLangFromAcceptLangHeader(req),
            referer: getRefererDomain(req),
            ip: req.resolvedIp
        });
        res.cookie(OAUTH_TOKEN_COOKIE, {
            accessToken: tokenInfo.accessToken,
            expiresAt: tokenInfo.expiresAt
        }, {
            signed: true,
            httpOnly: true,
            secure: true,
            domain: config.oAuth.ssoCookieDomain,
            sameSite: "none",
            expires: tokenInfo.refreshTokenExpiresAt ?
                     new Date(tokenInfo.refreshTokenExpiresAt) :
                     new Date(tokenInfo.expiresAt)
        });

        res.status(302).redirect(config.oAuth.webAppRedirectUrl);
    } catch (err) {
        next(err);
    }
}

async function getToken(req: Request & IpHolder, res: Response, next: NextFunction): Promise<void> {
    try {
        const { authorizationCode, grantType } = req.body;
        const oAuthService = getOAuthService();
        const tokenInfo = await oAuthService.getToken({
            authorizationCode,
            grantType,
            redirectUri: config.oAuth.redirectUri,
            language: getLangFromAcceptLangHeader(req),
            referer: getRefererDomain(req),
            ip: req.resolvedIp
        });
        res.cookie(OAUTH_TOKEN_COOKIE, {
            accessToken: tokenInfo.accessToken,
            expiresAt: tokenInfo.expiresAt
        }, {
            signed: true,
            httpOnly: true,
            secure: true,
            domain: config.oAuth.ssoCookieDomain,
            sameSite: "none",
            expires: tokenInfo.refreshTokenExpiresAt ?
                     new Date(tokenInfo.refreshTokenExpiresAt) :
                     new Date(tokenInfo.expiresAt)
        });

        res.status(200).send(await getDisplayLoginInfo(tokenInfo, req));
        next();
    } catch (err) {
        next(err);
    }
}

async function logout(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
        const oAuthService = getOAuthService();
        const tokenCookie = req.signedCookies[OAUTH_TOKEN_COOKIE] as OAuthTokenCookie;
        const oauthToken = req.header(X_OAUTH_TOKEN);

        if (!tokenCookie?.accessToken && !oauthToken) {
            if (!tokenCookie?.accessToken && !oauthToken) {
                return next(new Errors.TokenIsMissing());
            }
        }

        const response = await oAuthService.logout(tokenCookie?.accessToken || oauthToken);

        res.clearCookie(OAUTH_TOKEN_COOKIE, {
            signed: true,
            httpOnly: true,
            secure: true,
            domain: config.oAuth.ssoCookieDomain,
            sameSite: "none"
        });

        res.status(204).send(response);
        next();
    } catch (err) {
        next(err);
    }
}

export default router;
