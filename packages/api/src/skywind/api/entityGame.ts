import * as express from "express";
import { NextFunction, Response } from "express";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    defineIsLiveGameFlagFalse,
    getBooleanParamFromQuery,
    getBrand,
    getEntity,
    getEntityPath,
    sanitize,
    sanitizeBoolean,
    validate,
    validateBody
} from "./middleware/middleware";
import * as GameService from "../services/game";
import { getFavoriteGames, getRecentlyPlayedGames, toBriefInfo } from "../services/game";
import * as UrlManager from "../services/urlManager";
import { BrandEntity } from "../entities/brand";
import { IsLiveGameHolder, isPermitted, KeyEntityHolder, PermissionsHolder, } from "../services/security";
import { GamesRtpDeductionInvalidRequest, NotBrand, OperationForbidden, ValidationError } from "../errors";
import {
    BulkUpsertGamesRtp,
    EntityGameCodeInfo,
    EntityGameData,
    EntityGameInfo,
    GameCodesData,
    UpdateLimitsData,
    UpdateStatusesData,
} from "../entities/game";
import * as FilterService from "../services/filter";
import { prepareScheme } from "../services/filter";
import { BaseEntity, Entity } from "../entities/entity";
import { appendJackpots } from "../services/jackpot";
import { validatePlayerCodeForOperator } from "./playerAuthForBrands";
import { getEntityGameService } from "../services/entityGameService";
import {
    LiveManagerService
} from "../services/live";
import { validateCurrency } from "./reportJackpot";
import { customValidators } from "./middleware/validatorMiddleware";
import { getMerchantSearchService } from "../services/merchant";
import EntityCache from "../cache/entity";
import { buildDynamicLiveManagerUrl } from "../services/entityDomainService";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder & IsLiveGameHolder & PermissionsHolder;
const GAMES_MAX_LIMIT = 50000;

export function customDefineLimits(req: Request, res: express.Response, next: express.NextFunction) {
    req.query.offset = req.query.offset || FilterService.DEFAULT_OFFSET.toString();
    req.query.limit = req.query.limit || FilterService.DEFAULT_LIMIT;
    req.query.limit = Math.min(req.query.limit, GAMES_MAX_LIMIT);
    next();
}

const maxGameCodeLength = 255;

export const validateGameCode = validate({
    gameCode: { notEmpty: true, isWord: true, isLength: { options: { min: 1, max: maxGameCodeLength } } }
});

export const validateGameGroupName = validate({
    gameGroupName: { optional: true, isString: true }
});

export const validateSegmentId = validate({
    segmentId: { optional: true, isInt: true }
});

export const validateGameUrlParams = validate({
    urlParams: { optional: true },
    "urlParams.modules": { optional: true },
    "urlParams.balance_idle": { optional: true, isInt: { options: { strict: true } } },
    "urlParams.balance_ping": { optional: true, isInt: { options: { strict: true } } },
    "urlParams.keep_alive_idle": { optional: true, isInt: { options: { strict: true } } },
    "urlParams.keep_alive_ping": { optional: true, isInt: { options: { strict: true } } },
    "urlParams.disableBalancePing": { optional: true, isBoolean: true },
    "urlParams.history_url": { optional: true, isString: true }
});

export const validatePlayMode = validate({
    playmode: { optional: true, isPlayMode: true },
    playMode: { optional: true, isPlayMode: true },
});

export const validateGroupLimitFiltersUpdate = validate({
    merge: { optional: true, isBoolean: true }
});

export const validateLanguage = validate({
    language: { optional: true, isLanguage: true }
});

const validateGameCodes = validateBody({ codes: { isArray: { options: { notEmpty: true } } } });

const validateEachCode = (req: Request, res: express.Response, next: NextFunction) => {
    const { codes } = req.body;
    codes.forEach(code => {
        if (!customValidators.isWord(code)) {
            throw new ValidationError(`Wrong game code ${code}`);
        }
        if (code.length > maxGameCodeLength) {
            throw new ValidationError(`Game code too long ${code}`);
        }
    });
    next();
};

export function validateCountriesRestrictions(req: Request, res: Response, next: NextFunction) {
    const gameData: EntityGameData = req.body;
    const countries: string[] = gameData?.settings?.countries;
    if (countries) {
        if (!Array.isArray(countries)) {
            return next(new ValidationError("settings.countries must be array"));
        }
        if (countries.length) {
            const stringArray = countries.every(c => typeof c === "string");
            if (!stringArray) {
                return next(new ValidationError("settings.countries must be array of strings"));
            }
            const hasBlackListValue = countries.some(c => c.startsWith("!"));
            if (hasBlackListValue && !countries.every(c => c.startsWith("!"))) {
                return next(new ValidationError(
                    "settings.countries can be used as white-list or black-list. Mix is not supported"));
            }
            const countriesList = hasBlackListValue ? countries.map(c => c.substring(1)) : countries;
            if (!customValidators.isCountryCode(countriesList, undefined)) {
                return next(new ValidationError("settings.countries some country is unknown"));
            }
        }
    }
    next();
}

/**
 * Get all enabled games available for a specific entity
 */
router.get("/entities/:path/games",
    authenticate,
    authorize,
    decodePid(),
    customDefineLimits,
    validate(prepareScheme(["limit", "offset", "sortOrder", "currency"])),
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const gameInfoList: EntityGameInfo[] = await GameService.getAllGames(
                req.keyEntity,
                { path: getEntityPath(req) },
                req.query,
                req.baseUrl
            );
            res.send(gameInfoList);
            next();
        } catch (err) {
            return next(err);
        }
    });

/**
 * Get all enabled games available for a key entity
 */
router.get("/games",
    authenticate,
    authorize,
    decodePid(),
    customDefineLimits,
    validate(prepareScheme(["limit", "offset", "sortOrder", "currency"])),
    validate({ jpCurrency: { optional: true, isCurrency: true } }),
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const gameInfoList: EntityGameInfo[] = await GameService.getAllGames(
                req.keyEntity,
                null,
                req.query,
                req.baseUrl
            );
            res.send(gameInfoList);
            next();
        } catch (err) {
            return next(err);
        }
    });

/**
 * Update game for key entity
 *
 */
router.patch("/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    validateGameUrlParams,
    validate({
        status: { optional: true, isStatus: true }
    }),
    validateCountriesRestrictions,
    auditable,
    patchEntityGame);

async function patchEntityGame(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const gameCode: string = req.params["gameCode"];
        const allowToFinishCurrentSession: boolean = req.query["allowToFinishCurrentSession"] === "true";
        const gameData: EntityGameData = req.body;
        const entity = getEntity(req, { ignoreSuspended: true });
        const entityService = getEntityGameService(entity);
        const gameCodeInfo: EntityGameCodeInfo = await entityService.update(gameCode, gameData, undefined,
            allowToFinishCurrentSession);
        res.send(gameCodeInfo);
    } catch (err) {
        return next(err);
    }
}

/**
 * Get specific game information for key entity
 *
 */
router.get("/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    validateCurrency,
    validateGameGroupName,
    validateSegmentId,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const gameCode: string = req.params["gameCode"];
            const addAggregatedFinalLimits = req.query.addAggregatedFinalLimits === "true";
            const gameInfo: EntityGameInfo = await GameService.getOneGame(req.keyEntity,
                gameCode,
                {
                    addAggregatedFinalLimits,
                    currency: req.query.currency,
                    gameGroupName: req.query.gameGroupName,
                    segmentId: req.query.segmentId
                });
            res.send(gameInfo);
            next();
        } catch (err) {
            return next(err);
        }
    });

export const validateJpCurrency = validate({
    jpCurrency: { optional: true, isCurrency: true }
});

export const validateLimitAndOffset = validate(
    prepareScheme(["limit", "offset", "sortOrder"])
);

router.get(["/games/:gameCode/info", "/entities/:path/games/:gameCode/info"],
    authenticate,
    authorize,
    validateGameCode,
    validateJpCurrency,
    validateGameGroupName,
    validateSegmentId,
    getGamesInfo);

router.get("/games/info/search",
    authenticate,
    authorize,
    decodePid(),
    customDefineLimits,
    validateLimitAndOffset,
    validateJpCurrency,
    validateCurrency,
    getGamesInfoSearch);

export async function getGamesInfo(req: Request, res: express.Response, next: express.NextFunction) {
    const { type, code, gameCode } = req.params;
    const { currency, segmentId, gameGroupName, jpCurrency } = req.query;
    try {
        let entity: BaseEntity;
        if (type && code) {
            const merchant = await getMerchantSearchService().findOneByTypeAndCode(type, code);
            entity = await EntityCache.findById<BaseEntity>(merchant.brandId);
        } else {
            entity = getEntity(req);
        }

        const gameInfo = await GameService.getOneGame(entity, gameCode, {
            addAggregatedFinalLimits: getBooleanParamFromQuery(req.query, "addAggregatedFinalLimits"),
            skipJurisdictionFiltering: getBooleanParamFromQuery(req.query, "skipJurisdictionFiltering"),
            currencies: currency?.split(","),
            gameGroupName,
            segmentId
        });
        await appendJackpots(entity, [gameInfo], jpCurrency);
        const urlManager = await buildDynamicLiveManagerUrl(entity);
        await LiveManagerService.addLiveInfoToGame(urlManager, gameInfo, req.baseUrl);
        res.send(toBriefInfo([gameInfo])[0]);
        next();
    } catch (err) {
        return next(err);
    }
}

export async function getGamesInfoSearch(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        req.query.jackpots = req.query.jackpots || "true";
        const gameInfoList: EntityGameInfo[] = await GameService.getAllGames(
            req.keyEntity, null, req.query, req.baseUrl);
        res.send(toBriefInfo(gameInfoList));
        next();
    } catch (err) {
        return next(err);
    }
}

router.put("/entities/:path/games/rtp-deduction",
    authenticate,
    authorize,
    validateGamesRtpDeduction,
    handlerUpsertGamesRtpDeduction
);

function validateGamesRtpDeduction(req: Request, res: express.Response, next: NextFunction) {
    const listGames = req.body;
    if (!Array.isArray(listGames)) {
        throw new ValidationError(`Wrong listGames. Must be an array ${listGames}`);
    }

    const listErrors = [];
    listGames.forEach(game => {
        if (!game || Array.isArray(game) || typeof game !== "object") {
            listErrors.push(new ValidationError(`Wrong game object ${game}`));
            return;
        }

        const { gameCode, newRtpDeduction } = game;
        if (!customValidators.isWord(gameCode)) {
            listErrors.push(new ValidationError(`Wrong game code ${gameCode}`));
        }

        if (gameCode.length > maxGameCodeLength) {
            listErrors.push(new ValidationError(`Game code too long ${gameCode}`));
        }

        if (!customValidators.isFloat(newRtpDeduction, { min: 0, max: 100, strict: true })) {
            listErrors.push(new ValidationError(`newRtpDeduction must be in range [0, 100] value ${newRtpDeduction}`));
        }
    });

    if (listErrors.length > 0) {
        next(new GamesRtpDeductionInvalidRequest(listErrors.join(". ")));
        return;
    }
    next();
}

async function handlerUpsertGamesRtpDeduction(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const listGames: BulkUpsertGamesRtp = req.body;
        const entity: BaseEntity = getEntity(req, { ignoreSuspended: true });
        await GameService.upsertGamesRtpDeduction(entity, listGames);
    } catch (err) {
        next(err);
        return;
    }

    res.status(204).end();
    next();
}

router.put("/games/rtp-deduction",
    authenticate,
    authorize,
    validateGamesRtpDeduction,
    handlerUpsertGamesRtpDeduction
);

/**
 * Get specific game information for key entity with jackpot amounts
 *
 */
router.get("/games/:gameCode/jackpot",
    authenticate,
    authorize,
    validateGameCode,
    validate({
        jpCurrency: { optional: true, isCurrency: true }
    }),
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const data = await GameService.getGameWithJackpot(req.keyEntity,
                req.params["gameCode"],
                req.query.jpCurrency);
            res.send(data);
            next();
        } catch (err) {
            return next(err);
        }
    });

/**
 * Get specific game information
 *
 */
router.get("/entities/:path/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    validateCurrency,
    validateGameGroupName,
    validateSegmentId,
    async (req: Request,
           res: express.Response,
           next: express.NextFunction) => {
        try {
            const entity = getEntity(req, { ignoreSuspended: true });
            const gameCode: string = req.params["gameCode"];
            const addAggregatedFinalLimits = req.query.addAggregatedFinalLimits === "true";
            const gameInfo: EntityGameInfo = await GameService.getOneGame(entity,
                gameCode,
                {
                    addAggregatedFinalLimits,
                    currency: req.query.currency,
                    gameGroupName: req.query.gameGroupName,
                    segmentId: req.query.segmentId
                });
            await appendJackpots(entity, [gameInfo], req.query.jpCurrency);
            res.send(gameInfo);
            next();
        } catch (err) {
            return next(err);
        }
    });

/**
 *  Add game to a specific entity
 *
 */
router.post("/entities/:path/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    validateCountriesRestrictions,
    auditable,
    defineIsLiveGameFlagFalse,
    addGameToEntity
);

/**
 *  Update game for a specific entity
 *
 */
router.patch("/entities/:path/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    validateGameUrlParams,
    validate({
        status: { optional: true, isStatus: true },
    }),
    validateCountriesRestrictions,
    auditable,
    patchEntityGame);

/**
 *  Remove game from a specific entity
 *
 */
router.delete("/entities/:path/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    sanitizeBoolean("force"),
    auditable,
    defineIsLiveGameFlagFalse,
    removeGameFromEntity
);

export async function removeGameFromEntity(req: Request, res: express.Response, next: NextFunction) {
    try {
        const force = req.query.force;
        if (force && !isPermitted(["entity:game:delete-cascade"], req.permissions)) {
            return next(new OperationForbidden());
        }
        const entity = getEntity(req, { ignoreSuspended: true });
        const service = getEntityGameService(entity);
        const gameInfo = await service.delete(req.params.gameCode, req.isLiveGame, force);
        res.send(gameInfo);
    } catch
        (err) {
        return next(err);
    }
}

export const sanitizeLanguage = sanitize(req => {
    (req.sanitize("language") as any).toLowerCase();
});

/**
 * Get game URL for specific player
 *
 */
router.get("/players/:playerCode/games/:gameCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validateGameCode,
    validatePlayMode,
    sanitizeLanguage,
    validateLanguage,
    getGameByCode);

export async function getGameByCode(req: Request, res: express.Response, next: NextFunction) {
    try {
        const entity: BrandEntity = req.keyEntity as BrandEntity;
        if (!entity.isBrand()) {
            return next(new NotBrand());
        }
        const playerGameURL = await UrlManager.getPlayerGameURL({
            keyEntity: entity,
            playerCode: req.params.playerCode,
            gameCode: req.params.gameCode,
            playMode: req.query.playmode || req.query.playMode,
            ip: req.query.ip,
            language: req.query.language,
            ticket: req.query.ticket,
            lobby: req.query.lobby,
            cashier: req.query.cashier,
            aamsSessionId: req.query.aamsSessionId,
            aamsParticipationCode: req.query.aamsParticipationCode
        });
        res.send(playerGameURL);
        next();
    } catch (err) {
        return next(err);
    }
}

/**
 * Get game URL for specific player of entity
 *
 */
router.get("/entities/:path/players/:playerCode/games/:gameCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validateGameCode,
    validatePlayMode,
    sanitizeLanguage,
    validateLanguage,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const playerGameURL = await UrlManager.getPlayerGameURL({
                keyEntity: req.keyEntity,
                options: { path: getEntityPath(req) },
                playerCode: req.params.playerCode,
                gameCode: req.params.gameCode,
                playMode: req.query.playmode || req.query.playMode,
                ip: req.query.ip,
                language: req.query.language,
                ticket: req.query.ticket,
                lobby: req.query.lobby,
                cashier: req.query.cashier,
                aamsSessionId: req.query.aamsSessionId,
                aamsParticipationCode: req.query.aamsParticipationCode
            });
            res.send(playerGameURL);
            next();
        } catch (err) {
            return next(err);
        }
    });

/**
 * Get game URL for anonymous player
 *
 */
router.get("/fun/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    getFunGameByCode);

export async function getFunGameByCode(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const gameCode: string = req.params["gameCode"];
        const entity: BrandEntity = getBrand(req);

        const playerGameURL = await UrlManager.getAnonymousGameURL({
            keyEntity: entity,
            gameCode,
            ip: req.query.ip,
            language: req.query.language,
            ticket: req.query.ticket,
            merchantLoginUrl: req.query.merchantLoginUrl,
            lobby: req.query.lobby,
            cashier: req.query.cashier,
            gameGroup: req.query.gameGroup,
            currency: req.query.currency
        });
        res.send(playerGameURL);
        next();
    } catch (err) {
        return next(err);
    }
}

/**
 * Get game URL for anonymous player of entity
 *
 */
router.get("/entities/:path/fun/games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    getFunGameByCode);

/**
 *  Enable/disable game for a specific entity
 *
 */
export async function suspendGame(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const gameCode: string = req.params.gameCode;
        const entity: BaseEntity = getEntity(req, { ignoreSuspended: true });
        const reason: string = req.query.reason;
        const allowToFinishCurrentSession: boolean = req.query.allowToFinishCurrentSession === "true";
        const entityService = await getEntityGameService(entity);
        const skipStatusChangeValidation = isPermitted(["entity:game:change-state:enforce"], req.permissions);

        const info = await entityService.suspend(
            gameCode,
            req.isLiveGame,
            reason,
            allowToFinishCurrentSession,
            skipStatusChangeValidation);

        res.send(info);
    } catch (err) {
        next(err);
    }
}

export async function restoreGame(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const gameCode: string = req.params.gameCode;
        const entity: BaseEntity = getEntity(req, { ignoreSuspended: true });
        const entityService = await getEntityGameService(entity);

        const skipStatusChangeValidation = isPermitted(["entity:game:change-state:enforce"], req.permissions);

        const info = await entityService.restore(gameCode, req.isLiveGame, skipStatusChangeValidation);
        res.send(info);
    } catch (err) {
        next(err);
    }
}

/**
 *  Enable/disable games for a specific entity
 *
 */
async function updateGamesStatuses(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const data: UpdateStatusesData = req.body;
        const entity: BrandEntity = getBrand(req);
        const reason: string = req.query.reason;
        const entityService = await getEntityGameService(entity);

        await entityService.bulkUpdateStatus(data, reason);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

/**
 *  Add game to a specific entity
 *
 */
export async function addGameToEntity(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const gameCode: string = req.params["gameCode"];
        const gameData: EntityGameData = req.body;
        const gameCodeInfo: EntityGameCodeInfo = await GameService.addGameToEntity(
            req.keyEntity,
            { path: getEntityPath(req) },
            gameCode,
            req.isLiveGame,
            gameData
        );
        res.send(gameCodeInfo);
        next();
    } catch
        (err) {
        return next(err);
    }
}

/**
 *  Add games to a specific entity
 *
 */
export async function addGamesToEntity(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const data: GameCodesData = req.body;
        const entity: Entity = await getEntity(req, { ignoreSuspended: true }) as Entity;
        const gameCodeInfoList: EntityGameCodeInfo[] = await GameService.addGamesToEntity(entity, data, req.isLiveGame);
        res.send(gameCodeInfoList);
        next();
    } catch (err) {
        next(err);
    }
}

async function addGamesToAllEntities(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const data: GameCodesData = req.body;
        const entity: Entity = await getEntity(req, { ignoreSuspended: true }) as Entity;
        const gameCodes = await GameService.addGamesToAllEntities(entity, data);
        res.send(gameCodes);
        next();
    } catch (err) {
        next(err);
    }
}

/**
 *  Remove games from a specific entity
 *
 */
export async function removeGamesFromEntity(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const { codes }: GameCodesData = req.body;
        const entity = getEntity(req, { ignoreSuspended: true });
        const gameCodeInfos = await getEntityGameService(entity).bulkDelete(codes, req.isLiveGame);
        res.send(gameCodeInfos);
    } catch (err) {
        next(err);
    }
}

export async function updateGamesLimits(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const data: UpdateLimitsData = req.body;
        const entity: BaseEntity = getEntity(req, { ignoreSuspended: true });
        const service = await getEntityGameService(entity);
        await service.bulkUpdateLimits(data);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function getLiveTableInfo(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const urlManager = await buildDynamicLiveManagerUrl(req.keyEntity);
        const data = await LiveManagerService.getLiveInfoForProviderTables(
            urlManager,
            req.params["provider"],
            req.query["tableId__in"],
            req.baseUrl
        );
        res.send(data);
        next();
    } catch (err) {
        return next(err);
    }
}

router.post("/games/group/status", authenticate, authorize, auditable, updateGamesStatuses);
router.post("/entities/:path/games/group/status", authenticate, authorize, auditable, updateGamesStatuses);

router.put("/games/:gameCode/suspended", authenticate, authorize,
    validateGameCode, auditable, defineIsLiveGameFlagFalse, suspendGame);
router.put("/entities/:path/games/:gameCode/suspended", authenticate, authorize,
    validateGameCode, auditable, defineIsLiveGameFlagFalse, suspendGame);

router.delete("/games/:gameCode/suspended", authenticate, authorize,
    validateGameCode, auditable, defineIsLiveGameFlagFalse, restoreGame);
router.delete("/entities/:path/games/:gameCode/suspended", authenticate, authorize,
    validateGameCode, auditable, defineIsLiveGameFlagFalse, restoreGame);

router.post("/entities/:path/games", authenticate, authorize, auditable, defineIsLiveGameFlagFalse, addGamesToEntity);
router.post("/entities/group/:path/games",
    authenticate,
    authorize,
    validateGameCodes,
    validateEachCode,
    auditable,
    addGamesToAllEntities);

router.delete("/entities/:path/games",
    authenticate,
    authorize,
    auditable,
    defineIsLiveGameFlagFalse,
    removeGamesFromEntity);

router.post("/games/group/limits", authenticate, authorize,
    validateGroupLimitFiltersUpdate, auditable, updateGamesLimits);
router.post("/entities/:path/games/group/limits",
    authenticate, authorize, validateGroupLimitFiltersUpdate, auditable, updateGamesLimits);

router.get("/games/live/:provider/live-info", authenticate, getLiveTableInfo);

router.get(
    "/favoritegames/:playerCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validate(prepareScheme(["limit", "offset", "gameCodeOnly"])),
    favoriteGames);

async function favoriteGames(req: Request, res: Response, next: NextFunction) {
    try {
        const playerCode: string = req.params.playerCode;
        const games = await getFavoriteGames(req.keyEntity as BaseEntity, playerCode, req.query);
        res.send(games);
        next();
    } catch (err) {
        next(err);
    }
}

router.get("/recentlygames/:playerCode",
    authenticate,
    authorize,
    validatePlayerCodeForOperator,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    recentlyPlayedGames);

async function recentlyPlayedGames(req: Request, res: Response, next: NextFunction) {
    try {
        const playerCode: string = req.params.playerCode;
        const games = await getRecentlyPlayedGames(req.keyEntity as BaseEntity, playerCode, req.query);
        res.send(games);
        next();
    } catch (err) {
        next(err);
    }
}

export default router;
