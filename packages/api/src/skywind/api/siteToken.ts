import { NextFunction, Request as ExpressRequest, Response, Router } from "express";
import { authenticate, authorize, decodePid, getEntityPath } from "./middleware/middleware";
import * as SiteTokenService from "../services/siteToken";
import { BaseEntity } from "../entities/entity";
import { KeyEntityHolder } from "../services/security";
import * as Errors from "../errors";

const router: Router = Router();
type Request = ExpressRequest & KeyEntityHolder;

async function getBrandId(req: Request): Promise<number> {
    let entity: BaseEntity;
    if (req.params.path) {
        entity = req.keyEntity.find({ path: getEntityPath(req) });
    } else {
        entity = req.keyEntity;
    }
    if (!entity) {
        throw new Errors.EntityCouldNotBeFound();
    }
    if (!entity.isBrand()) {
        throw new Errors.NotBrand();
    }
    return entity.id;
}

async function getTokens(req: Request, res: Response, next: NextFunction) {
    try {
        const brandId: number = await getBrandId(req);
        res.send(await SiteTokenService.listForBrand(brandId));
        next();
    } catch (err) {
        next(err);
    }
}

async function generateToken(req: Request, res: Response, next: NextFunction) {
    try {
        const brandId: number = await getBrandId(req);
        res.send(await SiteTokenService.generateToken({
            brandId: brandId,
            ts: new Date(),
        }));
        next();
    } catch (err) {
        next(err);
    }
}

async function changeTokenStatus(req: Request, res: Response, next: NextFunction) {
    try {
        const newStatus: string = req.method === "PUT" ? "suspended" : "normal";
        const brandId: number = await getBrandId(req);
        res.send(await SiteTokenService.changeStatus(brandId, req.params.tokenId, newStatus));
        next();
    } catch (err) {
        next(err);
    }
}

router.post("/entities/:path/site/tokens", authenticate, authorize, generateToken);
router.get("/entities/:path/site/tokens", authenticate, authorize, getTokens);
router.put("/entities/:path/site/tokens/:tokenId/suspended", authenticate, authorize, decodePid(), changeTokenStatus);

router.post("/site/tokens", authenticate, authorize, generateToken);
router.get("/site/tokens", authenticate, authorize, getTokens);
router.put("/site/tokens/:tokenId/suspended", authenticate, authorize, decodePid(), changeTokenStatus);

export default router;
