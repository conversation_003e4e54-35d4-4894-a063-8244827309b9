import * as express from "express";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    defineLimits,
    getBooleanParamFromRequestQuery,
    getEntity,
    validate
} from "./middleware/middleware";
import { EntityGame } from "../entities/game";
import { GameCategory } from "../entities/gamecategory";
import { KeyEntityHolder } from "../services/security";
import { parseFilter, prepareScheme } from "../services/filter";
import { GAME_CATEGORY_TYPE } from "../utils/common";
import { GameCategoryGamesService } from "../services/gameCategory/gameCategoryGamesService";
import { GameCategoryImpl } from "../services/gameCategory/gameCategory";
import { GameCategoryService } from "../services/gameCategory/gameCategoryService";

const router: express.Router = express.Router();

type Request = express.Request & KeyEntityHolder;

/**
 * Gets all game categories
 */
router.get("/gamecategories",
    authenticate,
    authorize,
    defineLimits,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    validate({
        type: { optional: true, isGameCategoryType: true }
    }),
    getGameCategories);

router.get("/entities/:path/gamecategories",
    authenticate,
    authorize,
    defineLimits,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    validate({
        type: { optional: true, isGameCategoryType: true }
    }),
    getGameCategories);

/**
 * Gets a game category by public id
 */
router.get("/gamecategories/:gameCategoryId",
    authenticate,
    authorize,
    decodePid(),
    getGameCategoryById);

router.get("/entities/:path/gamecategories/:gameCategoryId",
    authenticate,
    authorize,
    decodePid(),
    getGameCategoryById);

/**
 * Gets a list of games by game category public id
 */
router.get("/gamecategories/:gameCategoryId/games",
    authenticate,
    authorize,
    decodePid(),
    defineLimits,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    getGamesByGameCategoryId);

router.get("/entities/:path/gamecategories/:gameCategoryId/games",
    authenticate,
    authorize,
    decodePid(),
    defineLimits,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    getGamesByGameCategoryId);

/**
 * Creates a game category
 */
const validateCreateData = validate({
    title: { isLength: { options: [{ min: 1, max: 100 }] } },
    status: { optional: true, isStatus: true },
    items: { optional: true, isGameCategoryItems: true },
    type: { optional: false, isGameCategoryType: true },
    icon: { optional: true }
});

router.post("/gamecategories",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["items"] }),
    validateCreateData,
    auditable,
    createGameCategory);

router.post("/entities/:path/gamecategories",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["items"] }),
    validateCreateData,
    auditable,
    createGameCategory);

/**
 * Updates a game category
 */
router.patch("/gamecategories/:gameCategoryId",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["items"] }),
    validateCreateData,
    auditable,
    updateGameCategory);

router.patch("/entities/:path/gamecategories/:gameCategoryId",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["items"] }),
    validateCreateData,
    auditable,
    updateGameCategory);

/**
 * Deletes a game category
 */
router.delete("/gamecategories/:gameCategoryId",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    deleteGameCategory);

router.delete("/entities/:path/gamecategories/:gameCategoryId",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    deleteGameCategory);

/**
 * Change game category order
 */
router.put("/gamecategories/:gameCategoryId/move",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["items"] }),
    validate({ newPosition: { notEmpty: true, isInt: { options: { min: 0 } } } }),
    auditable,
    moveGameCategory);

router.put("/entities/:path/gamecategories/:gameCategoryId/move",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["items"] }),
    validate({ newPosition: { notEmpty: true, isInt: { options: { min: 0 } } } }),
    auditable,
    moveGameCategory);

async function getGameCategories(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const gameCategories: GameCategory[] = await new GameCategoryService(entity).findAllWithGames(
            parseFilter(req.query, GameCategoryService.sortableKeys),
            req.query.type || GAME_CATEGORY_TYPE.GENERAL,
            getBooleanParamFromRequestQuery(req, "includeGamesAmount"),
            getBooleanParamFromRequestQuery(req, "includeGames")
        );

        res.send(gameCategories.map(c => c.toInfo(entity)));
        next();
    } catch (err) {
        next(err);
    }
}

async function getGameCategoryById(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const gameCategory: GameCategoryImpl = await new GameCategoryService(entity)
            .findOneById(req.params.gameCategoryId);
        res.send(gameCategory.toInfo(entity));
        next();
    } catch (err) {
        next(err);
    }
}

async function getGamesByGameCategoryId(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const category: GameCategoryImpl = await new GameCategoryService(entity).findOneById(req.params.gameCategoryId);
        const games: EntityGame[] = await GameCategoryGamesService.findAllForCategory(entity.id, category, req.query);
        res.send(games.map(game => game.toInfo()));
        next();
    } catch (err) {
        next(err);
    }
}

async function createGameCategory(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const gameCategory: GameCategory = await new GameCategoryService(entity).create(req.body);

        res.status(201).send(gameCategory.toInfo(entity));
        next();
    } catch (err) {
        next(err);
    }
}

async function updateGameCategory(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const gameCategory: GameCategoryImpl = await new GameCategoryService(entity)
            .update(req.params.gameCategoryId, req.body);
        res.send(gameCategory.toInfo(entity));
        next();
    } catch (err) {
        next(err);
    }
}

async function deleteGameCategory(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        await new GameCategoryService(entity).delete(req.params.gameCategoryId);

        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

async function moveGameCategory(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const gameCategory: GameCategoryImpl = await new GameCategoryService(entity)
            .move(req.params.gameCategoryId, req.body.newPosition);
        res.send(gameCategory.toInfo(entity));
    } catch (err) {
        next(err);
    }
}

export default router;
