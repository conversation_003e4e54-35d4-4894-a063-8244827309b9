import {
    authenticate, authorize,
    convertDatesToISOMiddleware,
    decodePid, defineDatesRangeLimitsForRoundHistory,
    defineLimits,
    FormattedResponse,
    getEntity,
    sanitizeSearchingILIKE,
    validate
} from "../middleware/middleware";
import { roundIdFilterPresents, validateSearch } from "../history";
import { parseFilter, prepareScheme } from "../../services/filter";
import * as express from "express";
import * as HistoryService from "../../history/gameHistory";
import { KeyEntityHolder } from "../../services/security";
import { RoundHistory } from "../../entities/gameHistory";
import { getRoundHistoryServiceFactory } from "../../history/gameHistoryServiceFactory";
import { validateOptionalRoundId } from "../history";
import { roundHistoryQueryKeysForRoundIdQueries } from "../../history/gameHistory";

async function getGameHistory(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const filterKeys = roundIdFilterPresents(req) ?
                           roundHistoryQueryKeysForRoundIdQueries :
            [...HistoryService.queryParamsKeys, "insertedAt", "internalRoundId"];
        const rounds: RoundHistory[] = await getRoundHistoryServiceFactory().getHistoryServiceV2()
            .getRounds(getEntity(req), parseFilter(req.query, filterKeys));

        res.sendFormatted(req, rounds);
        next();
    } catch (err) {
        next(err);
    }
}

const router: express.Router = express.Router();

type Request = express.Request & KeyEntityHolder;

const validateInternalRoundId = validate(prepareScheme(["internalRoundId"]));
const decodeMiddleware = decodePid({
    forceReturnIfNumber: true, ignoredKeys: ["internalRoundId", "internalRoundId__in"] });

router.get("/history/game",
    authenticate,
    authorize,
    validateSearch,
    validateInternalRoundId,
    defineLimits,
    decodeMiddleware,
    validateOptionalRoundId,
    sanitizeSearchingILIKE,
    convertDatesToISOMiddleware(["ts", "insertedAt", "firstTs"]),
    defineDatesRangeLimitsForRoundHistory(["insertedAt", "firstTs"], "ts", "finished"),
    getGameHistory);

router.get("/entities/:path/history/game",
    authenticate,
    authorize,
    validateSearch,
    validateInternalRoundId,
    defineLimits,
    decodeMiddleware,
    validateOptionalRoundId,
    sanitizeSearchingILIKE,
    convertDatesToISOMiddleware(["ts", "insertedAt", "firstTs"]),
    defineDatesRangeLimitsForRoundHistory(["insertedAt", "firstTs"], "ts", "finished"),
    getGameHistory);

export default router;
