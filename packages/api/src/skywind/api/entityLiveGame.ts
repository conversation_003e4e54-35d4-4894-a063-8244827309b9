import * as express from "express";
import {
    auditable,
    authenticate,
    authorize,
    sanitizeBoolean,
    defineIsLiveGameFlagTrue
} from "./middleware/middleware";
import {
    validateGameCode,
    addGamesToEntity,
    removeGamesFromEntity,
    suspendGame,
    restoreGame,
    addGameToEntity,
    validateCountriesRestrictions,
    removeGameFromEntity
} from "./entityGame";

const router: express.Router = express.Router();

router.post("/entities/:path/live-games",
    authenticate,
    authorize,
    validateCountriesRestrictions,
    auditable,
    defineIsLiveGameFlagTrue,
    addGamesToEntity
);

router.post("/entities/:path/live-games/:gameCode",
    authenticate,
    authorize,
    validateGameCode,
    validateCountriesRestrictions,
    auditable,
    defineIsLiveGameFlagTrue,
    addGameToEntity
);

router.delete("/entities/:path/live-games",
    authenticate,
    authorize,
    auditable,
    defineIsLiveGameFlagTrue,
    removeGamesFromEntity
);

router.delete("/entities/:path/live-games/:gameCode",
    authenticate,
    authorize,
    sanitizeBoolean("force"),
    auditable,
    defineIsLiveGameFlagTrue,
    removeGameFromEntity
);

router.put("/live-games/:gameCode/suspended",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    defineIsLiveGameFlagTrue,
    suspendGame
);

router.delete("/live-games/:gameCode/suspended",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    defineIsLiveGameFlagTrue,
    restoreGame
);

router.put("/entities/:path/live-games/:gameCode/suspended",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    defineIsLiveGameFlagTrue,
    suspendGame
);

router.delete("/entities/:path/live-games/:gameCode/suspended",
    authenticate,
    authorize,
    validateGameCode,
    auditable,
    defineIsLiveGameFlagTrue,
    restoreGame
);

export default router;
