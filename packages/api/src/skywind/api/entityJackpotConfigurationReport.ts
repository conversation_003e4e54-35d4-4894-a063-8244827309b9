import { NextFunction, Request as ExpressRequest, Router } from "express";
import { authenticate, authorize, FormattedResponse, getEntity } from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { entityJackpotReportService } from "../report/entityJackpotReport";
import { BaseEntity } from "../entities/entity";

type Request = ExpressRequest & KeyEntityHolder;

const router: Router = Router();

router.get("/entity/jackpots-configuration",
    authenticate,
    authorize,
    jackpotConfigurationHandler);

router.get("/entities/:path/jackpots-configuration",
    authenticate,
    authorize,
    jackpotConfigurationHandler);

export async function jackpotConfigurationHandler(req: Request, res: FormattedResponse, next: NextFunction) {
    try {
        const entity: BaseEntity = await getEntity(req);
        const jackpots = await entityJackpotReportService.get().getJackpotReport(entity);
        res.sendFormatted(req, jackpots);
        next();
    } catch (err) {
        next(err);
    }
}

export default router;
