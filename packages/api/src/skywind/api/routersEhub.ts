import { Application } from "express";
import {
    apiSwaggerEhub,
} from "../utils/swagger";
import health from "./expressRouters/health";
import ehub from "./ehub";
import version from "./expressRouters/version";
import logger from "../utils/logger";
import { encodePublicId, paging } from "./middleware/middleware";
import { addHeaderCORS, createRequestLogger, resolveIp, setUserAuthContext } from "./middleware/baseMiddleware";
import validator from "./middleware/validatorMiddleware";
import { defineJPNApi } from "./routers";
import { defineSwaggerExpress } from "./expressRouters/swagger";
import { createErrorHandler } from "./expressRouters/general";

const log = logger("routers");

export async function defineRoutes(app: Application): Promise<Application> {

    app.use(resolveIp);
    app.use(setUserAuthContext);
    app.use(validator);
    app.use("/v1/*", createRequestLogger(log));

    await defineSwaggerExpress(app, await apiSwaggerEhub());

    app.use(paging);
    app.use(encodePublicId);
    app.use("/v1/health", health);
    app.use("/v1/version", version);
    app.use("/v1/*", addHeaderCORS);

    await defineJPNApi(app);

    app.use("/v1", ehub);
    app.use(createErrorHandler(log));

    // respond with status OK when a GET request is made to the root directory
    app.route("/").get((req, res, next) => {
        res.status(200).end();
    });

    return app;
}
