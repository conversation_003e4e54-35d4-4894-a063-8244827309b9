import * as express from "express";
import { NextFunction, Request, Response, Router } from "express";
import EntityCache from "../cache/entity";
import {
    AddSecondStepAuthInfo,
    getUserAuthService,
    SecondStepAuthSelectInfo,
    SecondStepLoginInfo
} from "../services/user/userAuth";
import * as Errors from "../errors";
import { AuditLoginError, OperationForbidden } from "../errors";
import { BaseEntity } from "../entities/entity";
import { LoginInfo, UserInfo } from "../entities/user";
import {
    auditable,
    authenticate, authenticateRefresh,
    authorize,
    getBooleanParamFromRequestQuery,
    getEntityPath,
    validate
} from "./middleware/middleware";
import {
    Captcharized,
    IpHolder,
    KeyEntityHolder,
    PermissionsHolder,
    SessionHolder,
    UserInfoHolder,
    verifyAndParseTwoFAToken
} from "../services/security";
import * as TokenUtils from "../utils/token";
import { TwoFATokenData } from "../utils/token";
import { TWO_FA_TYPE, X_ACCESS_TOKEN } from "../utils/common";
import { validateUsername } from "./keyentity";
import { URL } from "url";
import logger from "../utils/logger";
import { getUserPasswordService } from "../services/user/userPassword";
import { getAuthSessionService } from "../services/authSessionService";
import { token } from "@skywind-group/sw-utils";
import config from "../config";
import { ContextVariables } from "../utils/contextVariables";
import { AuditableRequest, AuditInfo } from "../utils/auditHelper";
import {
    AuditSessionService,
    getAuditSessionService,
} from "../services/audit/auditSession";
import { AuditHistory, getLoginAuditFacade } from "../services/audit/audit";
import { findIp } from "../utils/requestHelper";
import { AuditSession } from "../entities/auditSession";
import { getOAuthService } from "../services/oAuthService";

const mung = require("express-mung");
const log = logger("sw-management-api:login");

const router: Router = Router();

export const validateLoginUser = (req: Request, res: Response, next: NextFunction) => {
    const schema = {
        secretKey: { notEmpty: true },
        username: { notEmpty: true, isWord: true },
        password: { notEmpty: true }
    };

    req.checkBody(schema);

    const errors = req.validationErrors();
    if (errors) {
        log.error(errors, "Validation errors");
        next(new Errors.GenericUserLoginError());
    } else {
        next();
    }
};

const validateUserAuthType = validate({
    authType: { notEmpty: true, isTwoFAType: true, errorMessage: "Invalid auth type" }
});

const validateSessionId = validate({
    sessionId: { notEmpty: true, errorMessage: "Invalid sessionId" }
});

const validateUserId = validate({
    userId: { notEmpty: true, errorMessage: "Invalid userId" }
});

/**
 * User login
 */
router.post("/login",
    validateLoginUser,
    mung.json(auditLogin),
    loginUser);

/**
 * User logout
 */
router.post("/logout",
    authenticate,
    auditable,
    mung.headers(finishAuditSession),
    logoutUser);

/**
 * Kill a user session as SUPERADMIN
 */
router.delete("/users/:userId/session/:sessionId",
    authenticate,
    auditable,
    validateUserId,
    validateSessionId,
    mung.headers(finishAuditSession),
    killUserSession);

/**
 * Refresh access token.
 */
router.post("/login/refresh",
    authenticateRefresh,
    auditable,
    loginRefreshUser);

export async function loginUser(req: Request & IpHolder, res: express.Response, next: express.NextFunction) {
    try {
        const { secretKey, username, password } = req.body;

        const entity: BaseEntity = await EntityCache.findOne({ key: secretKey });
        if (!entity) {
            log.error("Entity not found");
            return next(new Errors.GenericUserLoginError());
        }

        ContextVariables.setEntity(entity);
        const service = await getUserAuthService(entity);
        const loginInfo = await service.login({
            username,
            password,
            language: getLangFromAcceptLangHeader(req),
            referer: getRefererDomain(req),
            ip: req.resolvedIp
        });
        res.send(await getDisplayLoginInfo(loginInfo, req));
        next();
    } catch (err) {
        if (err instanceof Errors.TwoFATypeNotSetError) {
            res.status(err.responseStatus).json({
                code: err.code,
                message: err.message,
                brandAuthTypes: err.twoFATypes,
                token: err.twoFAToken,
                userHasPhoneNumber: err.userHasPhoneNumber
            });
            next();
        } else if (err instanceof Errors.ChangePasswordError) {
            log.error(err, "ChangePasswordError in /login");
            res.status(err.responseStatus).json({
                code: err.code,
                message: err.message,
                token: err.changePasswordToken
            });
        } else {
            return next(err);
        }
    }
}

export function auditLogin(resBody: any, req: Request & AuditableRequest) {
    if (config.audit.on) {
        auditLoginAsync(resBody, req)
            .catch(err => {
                log.error(err, "auditLoginAsync in /login");
            });
    }
    return resBody;
}

async function auditLoginAsync(resBody: any, req: Request & AuditableRequest) {
    const auditFacade = getLoginAuditFacade();
    const secretKey = req.body.secretKey;
    try {
        const entity: BaseEntity = await EntityCache.findOne({ key: secretKey });
        let tokenInfo: any;
        if (config.oAuth.isEnabled) {
            const oAuthService = getOAuthService();
            tokenInfo = await oAuthService.parseToken(resBody.accessToken, false, true);
        } else {
            tokenInfo = await TokenUtils.verifyAccessToken(resBody.accessToken);
        }
        const finishedAt = new Date(tokenInfo.exp * 1000);
        const auditSession: AuditSession = {
            id: tokenInfo.sessionId,
            initiatorName: tokenInfo.username,
            entityId: tokenInfo.entityId || tokenInfo.keyEntity?.id,
            startedAt: new Date(tokenInfo.iat * 1000),
            finishedAt
        };
        const info = new AuditInfo(req, auditSession, entity);
        const history = new AuditHistory(req.path, { ...req.body, ...req.params }, resBody);
        await auditFacade.save(info, history);
    } catch (error) {
        if (!error.code) {
            throw new AuditLoginError();
        }
        throw error;
    }
}

export async function getDisplayLoginInfo(info: LoginInfo, req: Request) {
    return {
        ...info,
        grantedPermissions: req.headers["initiatorservicename"] === "Backoffice" && info.grantedPermissions
            ?
            await token.generate({ permissions: info.grantedPermissions.permissions },
                config.accessToken)
            :
            undefined
    };
}

export async function logoutUser(req: Request & IpHolder & UserInfoHolder & SessionHolder,
                                 res: express.Response,
                                 next: express.NextFunction) {
    try {

        const deleteSessions = getBooleanParamFromRequestQuery(req, "deleteSessions");

        if (deleteSessions) {
            await getAuthSessionService().removeAllUserSessions(req.userId);
        } else {
            await getAuthSessionService().removeOneUserSession(req.userId, req.sessionId);
        }

        res.status(204).send();
        next();
    } catch (err) {
        return next(err);
    }
}

export async function killUserSession(req: Request & PermissionsHolder & UserInfoHolder,
                                      res: express.Response,
                                      next: express.NextFunction) {
    try {
        if (!req.isSuperAdmin) {
            return next(new OperationForbidden("Only SuperAdmins can perform this operation"));
        }
        await getAuthSessionService().removeOneUserSession(req.params.userId, req.params.sessionId);
        res.status(204).send();
        next();
    } catch (err) {
        return next(err);
    }
}

export function finishAuditSession(req: Request & SessionHolder) {
    if (config.audit.on) {
        finishAuditSessionAsync(req)
            .catch(err => {
                log.error(err, "finishAuditSession in /logout");
            });
    }
}

async function finishAuditSessionAsync(req: Request & SessionHolder) {
    const auditSessionService: AuditSessionService = getAuditSessionService();
    const updateData = {
        finishedAt: new Date()
    };
    await auditSessionService.update(req.sessionId, updateData);
}

export async function loginRefreshUser(req: Request & IpHolder & KeyEntityHolder & UserInfoHolder,
                                       res: Response, next: NextFunction) {
    try {
        const loginInfo: LoginInfo = await getUserAuthService(req.keyEntity).loginRefresh(req.username);
        res.header(X_ACCESS_TOKEN, loginInfo.accessToken).send(await getDisplayLoginInfo(loginInfo, req));
        next();
    } catch (err) {
        return next(err);
    }
}

router.post("/login/password/reset",
    validate({
        secretKey: { notEmpty: true },
        identifier: { notEmpty: true },
    }),
    auditable,
    async (req: Request & IpHolder & Captcharized, res: Response, next: NextFunction) => {
        try {
            res.send(await getUserPasswordService(undefined).requestPasswordReset({
                ...req.body,
                resolvedIp: findIp(req) || req.connection.remoteAddress,
                domain: getRefererDomain(req, true)
            }));
            next();
        } catch (err) {
            next(err);
        }
    });

router.post("/login/refresh-captcha", async (req: Request, res: Response, next: NextFunction) => {
    try {
        res.send(await getUserPasswordService().refreshCaptcha({
            resolvedIp: findIp(req) || req.connection.remoteAddress
        }));
        next();
    } catch (err) {
        next(err);
    }
});

router.post("/login/password/confirm",
    validate({
        secretKey: { notEmpty: true },
        username: { notEmpty: true },
        token: { notEmpty: true },
        newPassword: { notEmpty: true },
    }),
    auditable,
    async (req: Request & IpHolder, res: Response, next: NextFunction) => {
        try {
            const userInfo: UserInfo = await getUserPasswordService(undefined).resetPassword(req.body);
            res.send(userInfo);
            next();
        } catch (err) {
            next(err);
        }
    });

router.post("/login/secondstep",
    validate({
        token: { notEmpty: true },
        authCode: { notEmpty: true }
    }),
    auditable,
    async (req: Request & IpHolder & UserInfoHolder, res: Response, next: NextFunction) => {
        try {
            const info: SecondStepLoginInfo = req.body;
            const data = await verifyAndParseTwoFAToken(info.token);
            req.username = data.username;

            const service = getUserAuthService(data.keyEntity);
            const loginInfo: LoginInfo = await service.secondStepLogin(
                toTwoFATokenData(data),
                info.authCode.toString(),
                info.authType);

            res.header(X_ACCESS_TOKEN, loginInfo.accessToken).send(await getDisplayLoginInfo(loginInfo, req));
            next();
        } catch (err) {
            if (err instanceof Errors.ChangePasswordError) {
                log.error(err, "ChangePasswordError in /login/secondstep");
                res.status(err.responseStatus).json({
                    code: err.code,
                    message: err.message,
                    token: err.changePasswordToken
                });
            } else {
                return next(err);
            }
        }
    });

/**
 * Should be used to challenge user on login or when user selects auth type for the first time
 */
router.post("/login/secondstep/challenge",
    validate({
        authType: { notEmpty: true, isTwoFAType: true, errorMessage: "Invalid auth type" },
        token: { notEmpty: true },
        contactInfo: { optional: true, isAuthPhoneNumber: true, errorMessage: "Invalid phone number. Should be +xxxxx" }
    }),
    auditable,
    generateChallenge);

async function generateChallenge(req: Request, res: Response, next: NextFunction) {
    try {
        const info: SecondStepAuthSelectInfo = req.body;
        const data = await verifyAndParseTwoFAToken(info.token);

        const authService = getUserAuthService(data.keyEntity);

        if (TWO_FA_TYPE.GOOGLE === info.authType) {
            const result = await authService.makeQRCodeForGoogleAuthSelection(toTwoFATokenData(data));
            res.send(result);
        } else {
            const result = await authService.makeChallengeForSecondStepAuth(
                toTwoFATokenData(data),
                info.authType,
                info.contactInfo,
                getLangFromAcceptLangHeader(req));
            res.send(result);
        }
        next();
    } catch (err) {
        next(err);
    }
}

/**
 * Handles case when user adds auth type from his settings
 */
router.post("/login/secondstep/challenge-on-add",
    authenticate,
    validateUserAuthType,
    auditable,
    generateChallengeOnAdd);

async function generateChallengeOnAdd(req: Request & KeyEntityHolder & UserInfoHolder,
                                      res: Response,
                                      next: NextFunction) {
    try {
        const info: SecondStepAuthSelectInfo = req.body;
        const authService = getUserAuthService(req.keyEntity);

        if (TWO_FA_TYPE.GOOGLE === info.authType) {
            const result = await authService.makeQRCodeForGoogleAuthSelection(toTwoFATokenData(req));
            res.send(result);
        } else {
            const result = await authService.makeChallengeForSecondStepAuth(
                toTwoFATokenData(req),
                info.authType,
                info.contactInfo,
                getLangFromAcceptLangHeader(req));
            res.send(result);
        }
        next();
    } catch (err) {
        next(err);
    }
}

/**
 * Set user's first (default) auth type on his first two-fa login
 */
router.post("/login/secondstep/confirm",
    validate({
        authType: { notEmpty: true, isTwoFAType: true, errorMessage: "Invalid auth type" },
        token: { notEmpty: true },
        authCode: { notEmpty: true },
        contactInfo: { optional: true, isAuthPhoneNumber: true, errorMessage: "Invalid phone number. Should be +xxxxx" }
    }),
    auditable,
    async (req: Request & IpHolder & UserInfoHolder, res: Response, next: NextFunction) => {
        try {
            const info: AddSecondStepAuthInfo = req.body;
            const data = await verifyAndParseTwoFAToken(info.token);

            req.username = data.username;

            const service = getUserAuthService(data.keyEntity);
            const loginInfo: LoginInfo = await service.setSecondStepAuthType(
                toTwoFATokenData(data),
                info.authCode.toString(),
                info.authType,
                info.contactInfo);
            res.header(X_ACCESS_TOKEN, loginInfo.accessToken).send(await getDisplayLoginInfo(loginInfo, req));
            next();
        } catch (err) {
            if (err instanceof Errors.ChangePasswordError) {
                log.error(err, "ChangePasswordError in login/secondstep/confirm");
                res.status(err.responseStatus).json({
                    code: err.code,
                    message: err.message,
                    token: err.changePasswordToken
                });
            } else {
                return next(err);
            }
        }
    });

/**
 * Set user's first (default) auth type on his first two-fa login
 */
router.post("/login/secondstep/confirm-add",
    authenticate,
    validate({
        authType: { notEmpty: true, isTwoFAType: true, errorMessage: "Invalid auth type" },
        authCode: { notEmpty: true },
        contactInfo: { optional: true, isAuthPhoneNumber: true, errorMessage: "Invalid phone number. Should be +xxxxx" }
    }),
    auditable,
    async (req: Request & KeyEntityHolder & UserInfoHolder, res: Response, next: NextFunction) => {
        try {
            const info: AddSecondStepAuthInfo = req.body;

            await getUserAuthService(req.keyEntity).addSecondStepAuthType(
                toTwoFATokenData(req),
                info.authCode.toString(),
                info.authType,
                info.contactInfo,
                info.setAsDefault);
            res.status(204).end();
            next();
        } catch (err) {
            return next(err);
        }
    });

router.post("/login/secondstep/reset/:username",
    authenticate,
    authorize,
    validateUsername,
    validateUserAuthType,
    auditable,
    async (req: Request & KeyEntityHolder & UserInfoHolder, res: Response, next: NextFunction) => {
        try {
            await getUserAuthService(req.keyEntity).resetUserAuth(req.params["username"], req.body.authType);
            res.status(204).end();
            next();
        } catch (err) {
            return next(err);
        }
    });

router.post("/entities/:path/login/secondstep/reset/:username",
    authenticate,
    authorize,
    validateUsername,
    validateUserAuthType,
    auditable,
    async (req: Request & KeyEntityHolder & UserInfoHolder, res: Response, next: NextFunction) => {
        try {
            const entity = req.keyEntity.find({ path: getEntityPath(req) });
            if (!entity) {
                return next(new Errors.EntityCouldNotBeFound());
            }
            await getUserAuthService(entity).resetUserAuth(req.params["username"], req.body.authType);
            res.status(204).end();
            next();
        } catch (err) {
            return next(err);
        }
    });

router.post("/login/secondstep/setdefault",
    authenticate,
    validateUserAuthType,
    auditable,
    async (req: Request & KeyEntityHolder & UserInfoHolder, res: Response, next: NextFunction) => {
        try {
            await getUserAuthService(req.keyEntity).setUserAuthAsDefault(req.username, req.body.authType);
            res.status(204).end();
            next();
        } catch (err) {
            return next(err);
        }
    });

router.get("/login/secondstep/authtypes",
    authenticate,
    async (req: Request & KeyEntityHolder & UserInfoHolder, res: Response, next: NextFunction) => {
        try {
            const result = await getUserAuthService(req.keyEntity).getUserAuthTypes(req.username);
            res.send(result);
            next();
        } catch (err) {
            return next(err);
        }
    });

function toTwoFATokenData(data: KeyEntityHolder & UserInfoHolder): TwoFATokenData {
    return {
        entityId: data.keyEntity.id,
        username: data.username,
        userId: data.userId
    };
}

export function getLangFromAcceptLangHeader(req: Request): string {
    const langHeaderValue = req.header("Accept-Language");
    // get 'fr' lang from default browser's header like fr-CH, fr;q=0.9, en;q=0.8, de;
    if (langHeaderValue && langHeaderValue.indexOf(";") > -1) {
        try {
            return langHeaderValue.split(";")[0].split(",")[1];
        } catch (err) {
            return langHeaderValue;
        }
    }
    return langHeaderValue;
}

export function getRefererDomain(req, addProtocol: boolean = false): string {
    let referer = req.header("Referer");
    if (referer) {
        try {
            const url = new URL(referer);
            referer = addProtocol ? url.origin : url.hostname;
        } catch (err) {
            log.info(err, "Invalid Referer", referer);
        }
    }
    return referer;
}

export default router;
