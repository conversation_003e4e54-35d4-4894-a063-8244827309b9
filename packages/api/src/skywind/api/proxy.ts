import * as express from "express";
import { auditable, authenticate, authorize, decodePid } from "./middleware/middleware";
import { ProxyService } from "../services/proxy";

const router: express.Router = express.Router();

router.get("/proxy", authenticate, authorize, decodePid(),
    async (req: express.Request , res: express.Response, next: express.NextFunction) => {
        try {
            const service = new ProxyService();
            const proxies = await service.list();
            res.send(proxies.map(p => p.toInfo()));
        } catch (err) {
            next(err);
        }
    });

router.post("/proxy", authenticate, authorize, decodePid(), auditable,
    async (req: express.Request , res: express.Response, next: express.NextFunction) => {
        try {
            const service = new ProxyService();
            const proxy = await service.create(req.body);
            res.status(201).send(proxy.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.get("/proxy/:proxyId", authenticate, authorize, decodePid(),
    async (req: express.Request , res: express.Response, next: express.NextFunction) => {
        try {
            const service = new ProxyService();
            const proxy = await service.retrieve(req.params.proxyId);
            res.send(proxy.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.patch("/proxy/:proxyId", authenticate, authorize, decodePid(), auditable,
    async (req: express.Request , res: express.Response, next: express.NextFunction) => {
        try {
            const service = new ProxyService();
            const proxy = await service.update(req.params.proxyId, req.body);
            res.send(proxy.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.delete("/proxy/:proxyId", authenticate, authorize, decodePid(), auditable,
    async (req: express.Request , res: express.Response, next: express.NextFunction) => {
        try {
            const service = new ProxyService();
            await service.destroy(req.params.proxyId);
            res.sendStatus(204);
        } catch (err) {
            next(err);
        }
    });

export default router;
