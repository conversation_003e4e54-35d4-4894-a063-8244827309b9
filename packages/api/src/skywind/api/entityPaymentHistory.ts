import * as express from "express";
import {
    authenticate,
    authorize,
    decodePid,
    FormattedResponse,
    getEntityPath,
    validate
} from "./middleware/middleware";
import { BrandEntity } from "../entities/brand";
import * as EntityPaymentHistoryService from "../services/entityPaymentHistoryService";
import { KeyEntityHolder } from "../services/security";
import * as Errors from "../errors";
import { parseFilter, prepareScheme } from "../services/filter";
import { BaseEntity } from "../entities/entity";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder;

const validationScheme = prepareScheme(["amount", "ts", "currency", "limit", "offset", "sortOrder"]);
validationScheme["type"] = { optional: true, isDebitOrCredit: true };
const getHistoryValidation = validate(validationScheme);

/**
 * Gets credits that were made from entity
 */
router.get("/entities/:path/credits",
    authenticate,
    authorize,
    getHistoryValidation,
    decodePid(),
    async(req: Request, res: FormattedResponse, next: express.NextFunction) => {
        const entity: BaseEntity = req.keyEntity.find({
            path: getEntityPath(req),
        });
        if (!entity) {
            return next(new Errors.EntityCouldNotBeFound());
        }
        try {
            const result = await EntityPaymentHistoryService.findEntityCredits(
                entity, parseFilter(req.query, EntityPaymentHistoryService.queryParamsKeys)
            );
            res.send(result);
        } catch (err) {
            next(err);
        }
    });

/**
 * Gets credits that were made from key entity
 */
router.get("/credits",
    authenticate,
    authorize,
    getHistoryValidation,
    decodePid(),
    async(req: Request, res: FormattedResponse, next: express.NextFunction) => {
        try {
            const result = await EntityPaymentHistoryService.findEntityCredits(
                req.keyEntity, parseFilter(req.query, EntityPaymentHistoryService.queryParamsKeys)
            );
            res.send(result);
        } catch (err) {
            next(err);
        }
    });

/**
 * Gets debits that were made to entity
 */
router.get("/entities/:path/debits",
    authenticate,
    authorize,
    getHistoryValidation,
    decodePid(),
    async(req: Request, res: FormattedResponse, next: express.NextFunction) => {
        const entity: BrandEntity = req.keyEntity.find({
            path: getEntityPath(req),
        }) as BrandEntity;
        if (!entity) {
            return next(new Errors.EntityCouldNotBeFound());
        }
        try {
            const result = await EntityPaymentHistoryService.findEntityDebits(
                entity, parseFilter(req.query, EntityPaymentHistoryService.queryParamsKeys)
            );
            res.send(result);
        } catch (err) {
            next(err);
        }
    });

/**
 * Gets debits that were made to key entity
 */
router.get("/debits",
    authenticate,
    authorize,
    getHistoryValidation,
    decodePid(),
    async(req: Request, res: FormattedResponse, next: express.NextFunction) => {
        try {
            const result = await EntityPaymentHistoryService.findEntityDebits(
                req.keyEntity, parseFilter(req.query, EntityPaymentHistoryService.queryParamsKeys)
            );
            res.send(result);
        } catch (err) {
            next(err);
        }
    });

/**
 * Gets debits and credits that were made to entity
 */
router.get("/entities/:path/finance",
    authenticate,
    authorize,
    getHistoryValidation,
    decodePid(),
    async(req: Request, res: FormattedResponse, next: express.NextFunction) => {
        const entity: BrandEntity = req.keyEntity.find({
            path: getEntityPath(req),
        }) as BrandEntity;
        if (!entity) {
            return next(new Errors.EntityCouldNotBeFound());
        }
        try {
            const result = await EntityPaymentHistoryService.findEntityFinance(
                entity, parseFilter(req.query, EntityPaymentHistoryService.queryParamsKeys)
            );
            res.send(result);
        } catch (err) {
            next(err);
        }
    });

/**
 * Gets debits and credits that were made to key entity
 */
router.get("/finance",
    authenticate,
    authorize,
    getHistoryValidation,
    decodePid(),
    async(req: Request, res: FormattedResponse, next: express.NextFunction) => {
        try {
            const result = await EntityPaymentHistoryService.findEntityFinance(
                req.keyEntity, parseFilter(req.query, EntityPaymentHistoryService.queryParamsKeys)
            );
            res.send(result);
        } catch (err) {
            next(err);
        }
    });

export default router;
