import { KeyEntityHolder, PermissionsHolder } from "../../services/security";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    getBooleanParamFromRequestQuery,
    pagingWrapper,
    validate
} from "../middleware/middleware";
import { getBiReportDomainsService, queryParamsKeys } from "../../services/biReports/biReportDomainsService";
import { NextFunction, Request as ExpressRequest, Response, Router } from "express";
import { parseFilter } from "../../services/filter";

const biReportDomainsService = getBiReportDomainsService();

const router: Router = Router();
type Request = ExpressRequest & KeyEntityHolder;

const validateUpdateDomainsData = validate({
    trustServerUrl: { optional: true, isValidUrl: true },
    baseUrl: { optional: true, isValidUrl: true }
});

const validateCreateDomainsData = validate({
    trustServerUrl: { optional: false, isValidUrl: true },
    baseUrl: { optional: false, isValidUrl: true }
});

async function createBiReportDomains(req: Request & PermissionsHolder, res: Response, next: NextFunction) {
    try {
        const biReportDomains = await biReportDomainsService.createOne(req.body);

        res.send(biReportDomains.toInfo());
        next();
    } catch (err) {
        next(err);
    }
}

async function updateBiReportDomains(req: Request & PermissionsHolder, res: Response, next: NextFunction) {
    try {
        const biReportDomains = await biReportDomainsService.updateOne(req.params.reportDomainsId, req.body);

        res.send(biReportDomains.toInfo());
        next();
    } catch (err) {
        next(err);
    }
}

async function getOneBiReportDomains(req: Request & PermissionsHolder, res: Response, next: NextFunction) {
    try {
        const biReportDomains = await biReportDomainsService.getOne(req.params.reportDomainsId);

        res.send(biReportDomains.toInfo());
        next();
    } catch (err) {
        next(err);
    }
}

async function getManyBiReportDomains(req: Request & PermissionsHolder, res: Response, next: NextFunction) {
    try {
        const manyBiReportDomains = await biReportDomainsService.getMany({
            ...parseFilter(req.query, queryParamsKeys)
        });

        pagingWrapper(manyBiReportDomains, req, res);
        res.send(manyBiReportDomains.map(biReportDomains => biReportDomains.toInfo()));
        next();
    } catch (err) {
        next(err);
    }
}

async function selectBiReportDomains(req: Request & PermissionsHolder, res: Response, next: NextFunction) {
    try {
        const biReportDomains = await biReportDomainsService.selectOne(req.params.reportDomainsId);

        res.send(biReportDomains.toInfo());
        next();
    } catch (err) {
        next(err);
    }
}

async function deleteBiReportDomains(req: Request & PermissionsHolder, res: Response, next: NextFunction) {
    try {
        await biReportDomainsService.deleteOne(req.params.reportDomainsId, {
            forceDelete: getBooleanParamFromRequestQuery(req, "forceDelete")
        });

        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

router.post("/bi/reports/domains",
    authenticate,
    authorize,
    validateCreateDomainsData,
    auditable,
    createBiReportDomains);

router.get("/bi/reports/domains", authenticate, authorize, auditable, getManyBiReportDomains);

router.get("/bi/reports/domains/:reportDomainsId",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    getOneBiReportDomains);

router.put("/bi/reports/domains/:reportDomainsId",
    authenticate,
    authorize,
    decodePid(),
    validateUpdateDomainsData,
    auditable,
    updateBiReportDomains);

router.patch("/bi/reports/domains/:reportDomainsId/select",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    selectBiReportDomains);

router.delete("/bi/reports/domains/:reportDomainsId",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    deleteBiReportDomains);

export default router;
