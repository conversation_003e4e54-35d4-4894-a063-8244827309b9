import { ILogger } from "../../utils/logger";
import { NextFunction, Request, Response } from "express";
import { IpHolder } from "../../services/security";
import Translation, { TranslateHolder } from "@skywind-group/sw-management-i18n";
import { measures } from "@skywind-group/sw-utils";
import { requestLogData } from "../../utils/requestHelper";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { handleError } from "../middleware/errorMiddleware";

export function createErrorHandler(logger: ILogger): (err: any, req: Request, res: Response, next: NextFunction) => any {
    return (err: any, req: Request & IpHolder, res: Response & TranslateHolder, next: NextFunction): any => {
        return measures.measureProvider.runInTransaction("express-error-handler", () => {
            const reqData = requestLogData(req);

            measures.measureProvider.saveError(err);

            if (res.headersSent) {
                logger.warn(err, { reqData }, "HTTP headers have already been sent", reqData);
                return next(err);
            }

            if (err) {
                const error = handleError(err, logger);
                if ((error as any).translateMessage && !error.data.messages && error.data.reason) {
                    error.data.messages = error.data.reason;
                }
                res.status(error.responseStatus).json(Translation.getErrorResponse(error as SWError, res));
            }
        });
    };
}
