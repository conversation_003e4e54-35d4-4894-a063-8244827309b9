import { Application, NextFunction, Request, Response, static as staticContent } from "express";
import { resolve } from "node:path";
import { initializeSwaggerMiddleware } from "../../utils/swagger";

function getFullPath(version: string): string {
    return version === "" ? "" : `/${version}`;
}

async function initSwagger(app: Application, swaggerJson: any, partOfPath: string = ""): Promise<any> {
    const middleware = await initializeSwaggerMiddleware(swaggerJson);
    const swaggerVersion = getFullPath(partOfPath);
    app.use("/swagger-resources", swaggerCspMiddleware);
    app.use(`${swaggerVersion}/docs`, swaggerCspMiddleware);
    app.use(`${swaggerVersion}/api-docs`, swaggerCspMiddleware);

    const swaggerResourcesPath = partOfPath === "" ? partOfPath : `-${partOfPath}`;
    app.use("/swagger-resources", staticContent(resolve(process.cwd(), "swagger-resources")));
    app.use(`${swaggerVersion}/docs`, staticContent(resolve(process.cwd(), `swagger-ui${swaggerResourcesPath}`)));
    // It is needed for checking permissions from swagger schema
    app.use(middleware.swaggerMetadata());
}

export async function defineSwaggerExpress(app: Application, schema: any, partOfPath: string = "") {
    await initSwagger(app, schema, partOfPath);
    const swaggerVersion = getFullPath(partOfPath);
    app.route(`${swaggerVersion}/api-docs`).get((req, res) => res.send(schema));
}

const swaggerCspMiddleware = (req: Request, res: Response, next: NextFunction) => {
    res.setHeader(
        "Content-Security-Policy",
        "default-src 'self'; " +
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
        "font-src 'self' https://fonts.gstatic.com; " +
        "img-src 'self' data: https://online.swagger.io https://validator.swagger.io; " +
        "script-src 'self' 'unsafe-inline'; " +
        "connect-src 'self' https://online.swagger.io;"
    );
    next();
};
