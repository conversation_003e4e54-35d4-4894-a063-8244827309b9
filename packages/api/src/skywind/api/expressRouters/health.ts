import config from "../../config";
import * as http from "http";
import { getEnvironmentInfo } from "@skywind-group/sw-utils";

export default (req: http.IncomingMessage, res: http.ServerResponse, next) => {
    res.setHeader("Content-Type", "application/json");
    res.write(JSON.stringify({
        serverName: config.server.getName(),
        isProduction: config.isProduction(),
        ...getEnvironmentInfo()
    }));
    res.end();
    next();
};
