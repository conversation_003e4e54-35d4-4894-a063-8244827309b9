import * as express from "express";
import {
    authenticate,
    authorize,
    decodePid,
    defineLimits,
    FormattedResponse,
    getEntity,
    validate
} from "./middleware/middleware";
import { getActivities, queryParamsKeys } from "../services/audit";
import { Audit } from "../entities/audit";
import { KeyEntityHolder } from "../services/security";
import { parseFilter, prepareScheme } from "../services/filter";
import { AuditSummary } from "../entities/auditSummary";
import { AuditSummaryServiceImpl } from "../services/audit/auditSummary";
import config from "../config";
import { Op } from "sequelize";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder;

function increaseAuditLimits(req: Request, res: express.Response, next: express.NextFunction) {
    res.locals.MAX_LIMIT = config.limits.auditMaxLimit;
    next();
}

function getOperationFilter(operation?: string) {
    if (operation) {
        return {
            history: { operation: { [Op.iLike]: `%${operation.replace(/_/g, "\\_").replace(/%/g, "\\%")}%` } }
        };
    }
}

const getAuditsHandler = async (req: Request, res: FormattedResponse, next: express.NextFunction) => {
    try {
        const entity = getEntity(req);
        const auditInfoList: Audit[] = await getActivities(
            entity,
            { ...getOperationFilter(req.query.operation__contains), ...parseFilter(req.query, queryParamsKeys) }
        );
        res.sendFormatted(req, auditInfoList.map(c => c.toInfo(entity)));
        next();
    } catch (err) {
        return next(err);
    }
};

const getAuditsSummaryHandler = async (req: Request, res: FormattedResponse, next: express.NextFunction) => {
    try {
        const auditSummaryService = new AuditSummaryServiceImpl();
        const auditSummaries: AuditSummary[] = await auditSummaryService
            .getFiltered(parseFilter(req.query, auditSummaryService.getQueryParamsKey()));
        res.sendFormatted(req, auditSummaries);
        next();
    } catch (err) {
        return next(err);
    }
};

const auditsSummaryMethodInSchema = {
    optional: true,
    isAuditsSummaryMethod: true
};

const validateAudits = validate(
    {
        ...prepareScheme(["limit", "offset", "sortOrder"]),
        includeSubEntities: { optional: true, isBoolean: true },
        auditsSummaryMethod__in: auditsSummaryMethodInSchema,
        "auditsSummaryMethod__in!": auditsSummaryMethodInSchema
    });

router.get("/entities/:path/audits",
    authenticate,
    authorize,
    increaseAuditLimits,
    defineLimits,
    decodePid({ ignoredKeys: ["auditsSessionId", "initiatorIssueId"] }),
    validateAudits,
    getAuditsHandler);

router.get("/audits",
    authenticate,
    authorize,
    increaseAuditLimits,
    defineLimits,
    decodePid({ ignoredKeys: ["auditsSessionId", "initiatorIssueId"] }),
    validateAudits,
    getAuditsHandler);

router.get("/audits-summary",
    authenticate,
    authorize,
    increaseAuditLimits,
    defineLimits,
    decodePid(),
    getAuditsSummaryHandler);

export default router;
