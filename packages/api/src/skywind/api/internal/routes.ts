import internalApi from "./internal";
import merchantsApi from "./merchants";
import marketplaceApi from "./marketplace";
import promoApi from "./promo";
import expireGame from "./expireGame";
import reactivateGame from "./reactivateGame";
import checkEnvironmentId from "./checkEnvironmentId";
import criticalFilesApi from "./criticalFiles";
import validator from "../middleware/validatorMiddleware";
import { Application } from "express";
import config from "../../config";
import { verifyInternalToken } from "../../utils/token";
import MigrationService from "../../services/migrationService";
import logger from "../../utils/logger";
import * as express from "express";
import { encodePublicId } from "../middleware/middleware";
import version from "../expressRouters/version";
import health from "../expressRouters/health";
import { createErrorHandler } from "../expressRouters/general";

const log = logger("sw-management-internal-api");

export function defineRoutes(app: Application): Application {
    if (config.internalServer.api.isEnabled) {
        app.use(encodePublicId);
        app.use(validator);
        app.use("/v1/marketplace", marketplaceApi);
        app.use("/v1", merchantsApi);
        app.use("/v1", promoApi);
        app.use("/v1", internalApi);
        app.use("/v1", expireGame);
        app.use("/v1", checkEnvironmentId);
        app.use("/v1", reactivateGame);
        app.use("/v1", criticalFilesApi);
        app.use("/v1/version", version);
        app.use("/v1/health", health);
    }
    setUpMigration(app);
    app.use(createErrorHandler(log));

    return app;
}

function setUpMigration(app: Application) {
    if (config.migration.support) {
        app.post("/force-cleanup/finish",
            async (req: express.Request, res: express.Response, next: express.NextFunction) => {
                const { brandId } = await verifyInternalToken<{ brandId: number }>(req.body.token);
                try {
                    await MigrationService.markMigrationFinished(brandId);
                    res.sendStatus(201);
                    next();
                } catch (err) {
                    next(err);
                }
            });

        MigrationService.checkStuckMigrations();
    }
}
