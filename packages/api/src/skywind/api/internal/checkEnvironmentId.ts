import { NextFunction, Request, Response, Router } from "express";
import { verifyInternalToken } from "../../utils/token";
import EntityCache from "../../cache/entity";
import { validateEntityEnvironment } from "../../services/entityDomainService";

const router: Router = Router();

router.post("/check-environment", checkEnvironment);

interface CheckEnvironmentIdRequest {
    brandId: number;
    envId: string;
}

async function checkEnvironment(req: Request, res: Response, next: NextFunction) {
    try {
        const request: CheckEnvironmentIdRequest = await verifyInternalToken<CheckEnvironmentIdRequest>(req.body.token);
        await validateEntityEnvironment(await EntityCache.findById(+request.brandId), request.envId);
        res.status(201).send();
    } catch (err) {
        next(err);
    }
}

export default router;
