import { NextFunction, Request, Response, Router } from "express";
import { auditable, authenticateInternal, decodePid } from "../middleware/middleware";
import { getMerchantMarketplaceService } from "../../services/marketplace/merchantMarketplace";
import * as GameService from "../../services/game";
import { getSchemaDefinitionService } from "../../services/gameLimits/schemaDefinition";
import { getCurrencyMultiplierService } from "../../services/gameLimits/currencyMultiplier";
import { Merchant } from "../../entities/merchant";
import * as MerchantService from "../../services/merchant";
import { BaseEntity } from "../../entities/entity";
import EntityCache from "../../cache/entity";
import { getConfigurationFacade } from "../../services/gameLimits/defaultConfigurationFacade";

const router: Router = Router();

router.post("/merchants/game-limits/list",
    authenticateInternal,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["externalBrandId", "segmentId"] }),
    auditable,
    getMerchantGameLimitsConfigurations);

router.post("/merchants/game-limits",
    authenticateInternal,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["externalBrandId", "segmentId"] }),
    auditable,
    createMerchantGameLimitsConfigurations);

router.get("/merchants/:merchantType/games",
    authenticateInternal,
    decodePid({ forceReturnIfNumber: true }),
    getMarketplaceGames
);

router.post("/merchants/game-limits/:gameLimitsConfigurationId/status",
    authenticateInternal,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["externalBrandId", "segmentId"] }),
    auditable,
    setMerchantGameLimitsConfigurationStatus);

router.get("/games/:gameCode/schema-definition",
    authenticateInternal,
    getSchemaDefinition
);

router.put("/currency-multipliers", authenticateInternal, auditable, setCurrencyMultiplierList);

router.get("/currency-multipliers", authenticateInternal, getCurrencyMultiplierList);

async function getMerchantGameLimitsConfigurations(req: Request, res: Response, next: NextFunction) {
    try {
        const configurations = await getMerchantMarketplaceService(req.body.region)
            .getGameLimitsConfigurationsList(
            req.body.merchantType || req.body.merchantTypes,
            req.body.merchantCode,
            req.body.gameCode,
            req.body.externalBrandId,
            req.body.allStatuses,
            req.body.segment
        );

        res.send(configurations);
    } catch (err) {
        next(err);
    }
}

async function createMerchantGameLimitsConfigurations(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await MerchantService.getMerchantSearchService().findOneByTypeAndCode(
            req.body.merchantType || req.body.merchantTypes,
            req.body.merchantCode
        );
        const entity: BaseEntity = await EntityCache.findOne<BaseEntity>({ id: merchant.brandId });
        const configuration = await getMerchantMarketplaceService(req.body.region)
            .createBetConfiguration(entity, req.body);

        res.send(configuration);
    } catch (err) {
        next(err);
    }
}

async function getMarketplaceGames(req: Request, res: Response, next: NextFunction) {
    try {
        res.send(await getMerchantMarketplaceService(req.query.region).getGameList(
            req.params.merchantType || req.params.merchantTypes,
            req.query.gameCode));
    } catch (err) {
        next(err);
    }
}

async function setMerchantGameLimitsConfigurationStatus(req: Request, res: Response, next: NextFunction) {
    try {
        const result = await getMerchantMarketplaceService(req.body.region).setBetConfigurationStatus(
            req.params.gameLimitsConfigurationId,
            req.body);

        res.send(result);
    } catch (err) {
        next(err);
    }
}

async function getSchemaDefinition(req: Request, res: Response, next: NextFunction) {
    try {
        const gameCode = req.params.gameCode;
        const game = await GameService.findOne({
            code: gameCode,
        });

        if (game.schemaDefinitionId) {
            const schemaDefinition = await getSchemaDefinitionService().retrieve(game.schemaDefinitionId);
            if (schemaDefinition.levelsSupported()) {
                const facade = await getConfigurationFacade(schemaDefinition, game.code);
                
                res.send({
                    game,
                    schemaDefinition: schemaDefinition.toInfo(),
                    schemaConfiguration: { 
                        configuration: facade.configuration
                    }
                });
            } else {
                res.send({ game, schemaDefinition: schemaDefinition.toInfo() });
            }

        } else {
            res.sendStatus(404);
        }
    } catch (err) {
        next(err);
    }
}

async function setCurrencyMultiplierList(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await MerchantService
            .getMerchantSearchService().findOneByTypeAndCode(
                req.body.merchantType || req.body.merchantTypes,
                req.body.merchantCode);

        const entity: BaseEntity = await EntityCache.findOne({ id: merchant.brandId });

        res.send(await getCurrencyMultiplierService().createOrUpdate(entity, req.body));
    } catch (e) {
        next(e);
    }
}

async function getCurrencyMultiplierList(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await MerchantService
            .getMerchantSearchService().findOneByTypeAndCode(
                req.query.merchantType || req.query.merchantTypes,
                req.query.merchantCode);

        const entity: BaseEntity = await EntityCache.findOne({ id: merchant.brandId });

        res.send(await getCurrencyMultiplierService().findOne(entity));
    } catch (e) {
        next(e);
    }
}
export default router;
