import { NextFunction, Request, Response, Router } from "express";
import { authenticateInternal, decodePid, getBooleanParamFromRequestQuery } from "../middleware/middleware";
import { Merchant } from "../../entities/merchant";
import * as MerchantService from "../../services/merchant";
import { createPromo, getPromos, updatePromoStatuses, queryParamsKeys } from "../../services/promotions/promotion";
import EntityCache from "../../cache/entity";
import { BaseEntity } from "../../entities/entity";
import { parseFilter } from "../../services/filter";
import { PlayerPromotionService } from "../../services/promotions/playerPromotionService";
import { PROMO_STATUS, PromotionInfo } from "../../entities/promotion";
import { BrandEntity } from "../../entities/brand";
import logger from "../../utils/logger";
import {
    destructureEGPPromoId,
    getEGPConfiguration,
    getEGPPromoGateway
} from "../../services/promotions/egpPromoGateway";
import { decodeId } from "../../utils/publicid";
const log = logger("promotion");

const router: Router = Router();

router.get("/merchants/:type/:code/promo",
    authenticateInternal,
    getPromotionsHandler
);

router.post("/merchants/:type/:code/promo",
    authenticateInternal,
    createPromotionHandler
);

router.put("/merchants/:type/:code/promo",
    authenticateInternal,
    upsertPromotionHandler
);

router.delete("/merchants/:type/:code/promo",
    authenticateInternal,
    removePromotionFromPlayers
);

router.get("/merchants/:type/:code/players/:playerCode/promo",
    authenticateInternal,
    getPlayerPromotions
);

router.put("/merchants/:type/:code/promo/:promoId/players/:playerCode",
    authenticateInternal,
    decodePid(),
    addPromotionToPlayer
);

router.delete("/merchants/:type/:code/promo/:promoId/players/:playerCode",
    authenticateInternal,
    decodePid(),
    removePromotionFromPlayer);

router.delete("/merchants/:type/:code/promo/:promoId",
    authenticateInternal,
    decodePid(),
    removePromo);

async function getPromotionsHandler(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await MerchantService.getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);
        const entity: BaseEntity = await EntityCache.findById<BaseEntity>(merchant.brandId);

        const promotions = await getPromos(entity,
            parseFilter(req.query, queryParamsKeys),
            false,
            getBooleanParamFromRequestQuery(req, "includePlayers"),
            getBooleanParamFromRequestQuery(req, "includeGames"));

        res.send(promotions.map(promo => promo.toShortInfo()));
    } catch (err) {
        next(err);
    }
}

async function createPromotionHandler(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await MerchantService.getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);
        const entity: BaseEntity = await EntityCache.findById<BaseEntity>(merchant.brandId);
        const promo = await createPromo(req.body, entity, 1, false, false);

        res.status(201).send(promo.toInfo());
    } catch (err) {
        next(err);
    }
}

async function upsertPromotionHandler(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await MerchantService.getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);
        const entity: BaseEntity = await EntityCache.findById<BaseEntity>(merchant.brandId);
        const promoInfo = req.body as PromotionInfo;
        promoInfo.id = promoInfo.id ? decodeId(promoInfo.id) : undefined;
        if (promoInfo.id && promoInfo.players) {
            for (const player of promoInfo.players) {
                try {
                    await PlayerPromotionService.removePlayerFromPromotion(entity as any,
                        promoInfo.id, player.playerCode, true);
                } catch (err) {
                    log.error(err, `Failed to remove player ${player.playerCode} from promo ${promoInfo.id}`);
                }
            }
        }
        const promo = await createPromo(promoInfo, entity, 1, false, true);
        if (promoInfo.players) {
            await PlayerPromotionService.addPromotionToPlayers(entity as any, promo.getId(),
                promoInfo.players.map(p => p.playerCode));
        }

        res.send(promo);
    } catch (err) {
        next(err);
    }
}

async function addPromotionToPlayer(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await MerchantService.getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);
        const entity: BaseEntity = await EntityCache.findById<BaseEntity>(merchant.brandId);
        const info = await PlayerPromotionService.addPromotionToPlayer(entity as any,
            req.params.promoId, req.params.playerCode);
        res.send(info);
    } catch (err) {
        next(err);
    }
}

async function removePromotionFromPlayer(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await MerchantService.getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);
        const entity: BaseEntity = await EntityCache.findById<BaseEntity>(merchant.brandId);
        await PlayerPromotionService.removePlayerFromPromotion(entity as any,
            req.params.promoId, req.params.playerCode, true);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function removePromotionFromPlayers(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await MerchantService.getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);
        const entity: BaseEntity = await EntityCache.findById<BaseEntity>(merchant.brandId);
        const promoInfo = req.body as PromotionInfo;
        await PlayerPromotionService.removePlayersFromPromotion(entity as any,
            decodeId(promoInfo.id), promoInfo.players.map(player => player.playerCode));
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function removePromo(req: Request, res: Response, next: NextFunction) {
    try {
        const destructuredEGPPromoId = destructureEGPPromoId(req.params.promoId);
        if (destructuredEGPPromoId) {
            const { egpPromoId, gameProviderCode } = destructuredEGPPromoId;
            const { url } = getEGPConfiguration(gameProviderCode);
            return getEGPPromoGateway(url, gameProviderCode).removePromo(egpPromoId);
        }
        const merchant: Merchant = await MerchantService.getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);
        const entity = await EntityCache.findById<BrandEntity>(merchant.brandId);
        const [promo] = await updatePromoStatuses(entity, [req.params.promoId], PROMO_STATUS.INACTIVE, 1);
        res.send(promo.toShortInfo());
    } catch (err) {
        next(err);
    }
}

async function getPlayerPromotions(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await MerchantService.getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);
        const entity = await EntityCache.findById<BrandEntity>(merchant.brandId);
        const skipEGPPromotions = getBooleanParamFromRequestQuery(req, "skipEGPPromotions", false);
        const promos = await PlayerPromotionService.getPlayerPromotions(
            entity,
            req.params.playerCode,
            undefined,
            skipEGPPromotions
        );
        res.send(promos);
    } catch (err) {
        next(err);
    }
}

export default router;
