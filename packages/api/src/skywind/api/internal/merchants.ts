import {
    auditable,
    auditOnEnd,
    auditOnSend,
    authenticateInternal,
    decodePid,
    getBooleanParamFromQuery,
    getEntity,
    setAuditSwagger
} from "../middleware/middleware";
import { findMerchantByCode } from "../merchant";
import { NextFunction, Request, Response, Router } from "express";
import config from "../../config";
import { validateOptionalRoundId, validateRoundId } from "../history";
import { Merchant } from "../../entities/merchant";
import * as GameService from "../../services/game";
import EntityCache from "../../cache/entity";
import { KeyEntityHolder } from "../../services/security";
import { BrandEntity } from "../../entities/brand";
import * as MerchantService from "../../services/merchant";
import { PlayerPromotionService } from "../../services/promotions/playerPromotionService";
import { GameHistoryVisualisation } from "../../entities/gameHistory";
import { getMerchantCRUDService, getMerchantInternalService, getMerchantSearchService } from "../../services/merchant";
import { MerchantImpl } from "../../services/merchant";
import { CriticalFilesService } from "../../services/criticalfiles/criticalFilesService";
import { BaseEntity } from "../../entities/entity";
import {
    getGamesInfo,
    validateGameCode,
    validateGameGroupName,
    validateJpCurrency,
    validateSegmentId
} from "../entityGame";
import { getMerchantEntitiesByPartOfCode } from "../../services/entity";
import { appendJackpots } from "../../services/jackpot";

const router: Router = Router();

if (config.audit.on) {
    router.use(auditOnSend);
    router.use(auditOnEnd);
}

const forceFinishEvent: string = "FORCE_FINISH_ROUND";

router.get("/merchants/search",
    authenticateInternal,
    keyEntity,
    searchMerchantEntities
);

router.get("/merchants/:type/:code/short-structure",
    authenticateInternal,
    keyEntity,
    getEntityShortStructure
);

router.get("/merchants/:type/:code",
    authenticateInternal,
    keyEntity,
    findMerchantByCode
);

router.get("/merchants/:type/:code/games",
    authenticateInternal,
    keyEntity,
    getMerchantGamesHandler
);

router.get("/merchants/:type/:code/games/:gameCode/info",
    authenticateInternal,
    keyEntity,
    validateGameCode,
    validateJpCurrency,
    validateGameGroupName,
    validateSegmentId,
    getGamesInfo);

router.get("/merchants/:type/:code/history/rounds/:roundId",
    authenticateInternal,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    getMerchantGameRoundDetailsHandler);

router.get("/merchants/:type/:code/history/rounds",
    authenticateInternal,
    decodePid({ forceReturnIfNumber: true }),
    validateOptionalRoundId,
    getMerchantGameHistoryHandler);

export async function keyEntity(req: Request & KeyEntityHolder,
                                res: Response,
                                next: NextFunction) {
    try {
        req.keyEntity = await EntityCache.findOne({ path: ":" });
        next();
    } catch (err) {
        return next(err);
    }
}

async function searchMerchantEntities(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entityInfo = await getEntity(req).structureToInfo();
        const entityWithMerchant = await getMerchantCRUDService().includeTo(entityInfo);
        const entities = getMerchantEntitiesByPartOfCode(entityWithMerchant, req.query.code);
        res.send(entities);
    } catch (err) {
        next(err);
    }
}

async function getEntityShortStructure(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);
        const entity: BaseEntity = await EntityCache.findById<BaseEntity>(merchant.brandId);
        res.send(entity.structureToShortInfo());
        next();
    } catch (err) {
        next(err);
    }
}

async function getMerchantGamesHandler(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await MerchantService.getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);
        const brand: BrandEntity = await EntityCache.findById<BrandEntity>(merchant.brandId);
        const games = await GameService.getAllGames(brand, null, req.query);
        if (getBooleanParamFromQuery(req.query, "appendJackpots")) {
            await appendJackpots(brand, games, req.query.jpCurrency);
        }
        res.send(games);
    } catch (err) {
        next(err);
    }
}

async function getMerchantGameRoundDetailsHandler(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const roundDetail = await getMerchantInternalService().getGameRoundDetails(
            req.params.type,
            req.params.code,
            req.params.roundId,
            getBooleanParamFromQuery(req.query, "includeSpins"),
            getBooleanParamFromQuery(req.query, "includePending")
        );

        res.send(roundDetail);
    } catch (err) {
        next(err);
    }
}

async function getMerchantGameHistoryHandler(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const gameHistory = await getMerchantInternalService().getGameHistory(
            req.params.type,
            req.params.code,
            req,
            getBooleanParamFromQuery(req.query, "includeSpins"),
            getBooleanParamFromQuery(req.query, "includeSmResultExtraData")
        );

        res.send(gameHistory);
    } catch (err) {
        next(err);
    }
}

router.post("/merchants/history/image",
    authenticateInternal,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    getMerchantHistoryUrl
);

router.post("/merchants/history/details/image",
    authenticateInternal,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    getMerchantHistoryDetailsUrl
);

router.post("/merchants/force-finish",
    authenticateInternal,
    keyEntity,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    setAuditSwagger(forceFinishEvent, forceFinishEvent),
    forceFinishForMerchant
);

router.post("/merchants/start-finalize",
    authenticateInternal,
    auditable,
    startFinalizeForMerchant
);

router.post("/merchants/finish-finalize",
    authenticateInternal,
    auditable,
    finishFinalizeForMerchant
);

router.post("/merchants/revert",
    authenticateInternal,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    forceFinishWithRevertedFlag
);

router.put("/merchants/:type/:code/tests-passing",
    authenticateInternal,
    auditable,
    updateMerchantTestPassedDate
);

router.put("/merchants/promo/:promoId/player",
    authenticateInternal,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    applyPromoForMerchantPlayer
);

router.post("/merchants/kill-player-session",
    authenticateInternal,
    auditable,
    killPlayerSession
);

router.post("/merchants/get-platform-critical-files",
    authenticateInternal,
    auditable,
    getPlatformCriticalFiles
);

router.post("/merchants/:type/get-platform-critical-files",
    authenticateInternal,
    auditable,
    getPlatformCriticalFilesByMerchantType
);

router.post("/merchants/get-game-critical-files",
    authenticateInternal,
    auditable,
    getGameCriticalFiles
);

router.post("/merchants/:type/get-game-critical-files",
    authenticateInternal,
    auditable,
    getGameCriticalFilesByMerchantType
);

async function getMerchantHistoryUrl(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const historyUrl: GameHistoryVisualisation = await getMerchantInternalService().getHistoryUrl(req.body);
        res.send(historyUrl);
        next();
    } catch (err) {
        next(err);
    }
}

async function getMerchantHistoryDetailsUrl(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const historyDetailsUrl: GameHistoryVisualisation = await getMerchantInternalService()
            .getGameHistoryDetailsImage(req.body);
        res.send(historyDetailsUrl);
        next();
    } catch (err) {
        next(err);
    }
}

async function forceFinishForMerchant(req: Request, res: Response, next: NextFunction) {
    try {
        await getMerchantInternalService().forceFinishRound(req.body, false);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function startFinalizeForMerchant(req: Request, res: Response, next: NextFunction) {
    try {
        const response = await getMerchantInternalService().startFinalizeGame(req.body);
        res.send(response);
    } catch (err) {
        next(err);
    }
}

async function finishFinalizeForMerchant(req: Request, res: Response, next: NextFunction) {
    try {
        await getMerchantInternalService().completeFinalizeGame(req.body);
        res.status(201).end();
    } catch (err) {
        next(err);
    }
}

async function forceFinishWithRevertedFlag(req: Request, res: Response, next: NextFunction) {
    try {
        await getMerchantInternalService().forceFinishRound(req.body, true);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function killPlayerSession(req: Request, res: Response, next: NextFunction) {
    try {
        await getMerchantInternalService().killPlayerSession(req.body);
        res.end();
        next();
    } catch (err) {
        next(err);
    }
}

async function updateMerchantTestPassedDate(req: Request, res: Response) {
    const merchant: Merchant = await MerchantService.getMerchantSearchService()
        .findOneByTypeAndCode(req.params.type, req.params.code);
    const brand: BrandEntity = await EntityCache.findById<BrandEntity>(merchant.brandId);
    res.send(await getMerchantCRUDService().update(brand, { lastTestPassing: new Date() }));
}

async function applyPromoForMerchantPlayer(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await MerchantService.getMerchantSearchService().findOneByTypeAndCode(
            req.body.merchantType,
            req.body.merchantCode
        );
        const brand: BrandEntity = await EntityCache.findOne<BrandEntity>({ id: merchant.brandId });
        const filters = { "code__in": req.body.playerCode };

        const promo = await PlayerPromotionService.addPromotionToPlayersByFilter(
            brand,
            req.params.promoId,
            filters);

        res.send(promo);
    } catch (err) {
        next(err);
    }
}

async function getGameCriticalFiles(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: MerchantImpl = await getMerchantSearchService().findOneByTypeAndCode(
            req.body.merchantType,
            req.body.merchantCode
        );
        const service = new CriticalFilesService();
        const gamesGroupData = await service.getGamesGroupForkData(merchant, req.body.gameCodes);
        const result = await service.getGameCriticalFilesList(merchant, gamesGroupData, req.body.regulation);
        res.send(result);
    } catch (err) {
        next(err);
    }
}

async function getGameCriticalFilesByMerchantType(req: Request, res: Response, next: NextFunction) {
    try {
        const service = new CriticalFilesService();
        const result = await service.getGameCriticalFilesListByMerchantTypes(
            parseMerchantTypes(req),
            req.body.regulation,
            req.body.gameCodes
        );
        res.send(result);
    } catch (err) {
        next(err);
    }
}

async function getPlatformCriticalFiles(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: MerchantImpl = await getMerchantSearchService().findOneByTypeAndCode(
            req.body.merchantType,
            req.body.merchantCode
        );
        const service = new CriticalFilesService();
        const result = await service.getPlatformCriticalFilesList(merchant, req.body.regulation);
        res.send(result);
    } catch (err) {
        next(err);
    }
}

async function getPlatformCriticalFilesByMerchantType(req: Request, res: Response, next: NextFunction) {
    try {
        const service = new CriticalFilesService();
        const result = await service.getPlatformCriticalFilesListByMerchantTypes(
            parseMerchantTypes(req),
            req.body.regulation
        );
        res.send(result);
    } catch (err) {
        next(err);
    }
}

function parseMerchantTypes(req: Request): string[] {
    const merchantTypeStr: string = req.params.type;
    return merchantTypeStr.includes(",") && merchantTypeStr.split(",").map(t => t.trim()) || [merchantTypeStr];
}

export default router;
