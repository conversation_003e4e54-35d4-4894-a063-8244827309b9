import { NextFunction, Request, Response, Router } from "express";
import {
    authenticateInternal,
    decodePid,
    validate
} from "../middleware/middleware";
import { validateMerchantRegulation } from "../criticalFilesApi";
import { KeyEntityHolder } from "../../services/security";
import { MerchantImpl } from "../../services/merchant";
import { CriticalFilesServiceImpl } from "../../services/criticalfiles/criticalFilesService";
import * as MerchantService from "../../services/merchant";

const router: Router = Router();

const getGameCriticalFilesInfoForMerchant =
    async (req: Request & KeyEntityHolder, res: Response, next: NextFunction) => {
    try {
        const mrch: MerchantImpl = await MerchantService
            .getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);

        validateMerchantRegulation(req, mrch);
        const gamesGroupData = await CriticalFilesServiceImpl.getGamesGroupForkData(mrch, req.body.games);
        res.send(await CriticalFilesServiceImpl.getGameCriticalFilesList(
            mrch,
            gamesGroupData,
            req.body.regulation,
            req.body.includeVersions
        ));
        next();
    } catch (err) {
        return next(err);
    }
};

const getPlatformCriticalFilesInfoForMerchant =
    async (req: Request & KeyEntityHolder, res: Response, next: NextFunction) => {
    try {
        const mrch: MerchantImpl = await MerchantService
            .getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);

        validateMerchantRegulation(req, mrch);
        res.send(await CriticalFilesServiceImpl.getPlatformCriticalFilesList(mrch, req.body.regulation));
        next();
    } catch (err) {
        return next(err);
    }
};

router.post("/merchants/:type/:code/critical-files/games/info",
    authenticateInternal,
    decodePid(),
    validate({
        regulation: { isRegulation: true },
        games: { optional: true, isArray: true }
    }),
    getGameCriticalFilesInfoForMerchant
);

router.post("/merchants/:type/:code/critical-files/platform/info",
    authenticateInternal,
    decodePid(),
    validate({
        regulation: { isRegulation: true }
    }),
    getPlatformCriticalFilesInfoForMerchant
);

export default router;
