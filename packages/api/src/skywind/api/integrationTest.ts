import * as express from "express";
import { NextFunction, Response } from "express";
import { KeyEntityHolder, UserInfoHolder } from "../services/security";
import { authenticate, authorize, getMerchant, validate } from "./middleware/middleware";
import { getTestGatewayAPIService } from "../services/testGatewayService";
import { getMerchantCRUDService, getMerchantSearchService, MerchantImpl } from "../services/merchant";
import * as Errors from "../errors";
import { findAllEntityGames, findOneEntityGame } from "../services/game";
import { BrandEntity } from "../entities/brand";
import { Op, WhereOptions } from "sequelize";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder & UserInfoHolder;

const validateRunTest = validate({
    code: { notEmpty: true, isString: true },
    type: { notEmpty: true, isString: true }
});

const validateGetTestReport = validate({
    id: { notEmpty: true, isInt: true }
});

const validateMerchantCode = validate(({
    merchantCode: { notEmpty: true, isString: true }
}));

function checkCodeAndType(merchant: MerchantImpl, requestBody: { code: string, type: string }): void {
    const { code: merchantCode, type: merchantType } = merchant;
    const { code: requestCode, type: requestType } = requestBody;

    if (merchantCode !== requestCode) {
        throw new Errors.MerchantCodeNotFound(requestCode);
    }

    if (merchantType !== requestType) {
        throw new Errors.MerchantTypeNotFound(requestType);
    }
}

async function getMerchantSettings(req: Request & KeyEntityHolder)
    : Promise<{ merchant: MerchantImpl, merchantEntity: BrandEntity }> {
    const merchantEntity = getMerchant(req);
    const merchant = await getMerchantCRUDService().findOne(merchantEntity);
    return { merchant, merchantEntity };
}

async function runTests(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    async function getGameCodes(entity: BrandEntity, gameCode: string | undefined, limit = 1): Promise<string[]> {
        const queries = new Map<string, WhereOptions<any>>([["filter", { limit }]]);
        if (gameCode) {
            queries.set("game", { code: { [Op.not]: gameCode } });
        }
        const entityGames = await findAllEntityGames(entity, queries, true);
        if (entityGames.length < limit) {
            throw new Errors.GamesNotFound();
        }
        return entityGames.map(game => game.toCodeInfo().code);
    }

    try {
        const { merchant, merchantEntity } = await getMerchantSettings(req);
        checkCodeAndType(merchant, req.body);
        const hasGameCode = Boolean(req.body.gameCode);
        const hasSecondGameCode = Boolean(req.body.secondGameCode);
        if (hasGameCode) {
            await findOneEntityGame(merchantEntity, req.body.gameCode);
        }
        if (hasSecondGameCode) {
            await findOneEntityGame(merchantEntity, req.body.secondGameCode);
        }
        if (!hasGameCode && hasSecondGameCode) {
            req.body.gameCode = (await getGameCodes(merchantEntity, req.body.secondGameCode))[0];
        }
        if (hasGameCode && !hasSecondGameCode) {
            req.body.secondGameCode = (await getGameCodes(merchantEntity, req.body.gameCode))[0];
        }
        if (!hasGameCode && !hasSecondGameCode) {
            const gameCodes = await getGameCodes(merchantEntity, undefined, 2);
            req.body.gameCode = gameCodes[0];
            req.body.secondGameCode = gameCodes[1];
        }
        const response = await getTestGatewayAPIService().runTests(req.body);
        res.status(200).send(response);
    } catch (err) {
        next(err);
    }
}

export async function getAllReports(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const { merchant } = await getMerchantSettings(req);
        const { code: merchantCode } = merchant;
        const response = await getTestGatewayAPIService().getAllReports(merchantCode, req.query);
        res.status(200).send(response);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getReport(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const { merchant } = await getMerchantSettings(req);
        const { id } = req.params;
        const { format } = req.query;
        const { code: merchantCode } = merchant;
        const response = await getTestGatewayAPIService().getReport(
            Number.parseInt(id, 10),
            merchantCode,
            format
        );
        res.status(200).send(response);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getHistoryReports(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const { merchant } = await getMerchantSettings(req);
        const { type: merchantType } = merchant;
        const { merchantCode } = req.params;
        await getMerchantSearchService().findOneByTypeAndCode(merchantType, merchantCode);
        const response = await getTestGatewayAPIService().getHistoryReports(merchantCode, req.query);
        res.status(200).send(response);
        next();
    } catch (err) {
        next(err);
    }
}

router.post("/test/run",
    authenticate,
    authorize,
    validateRunTest,
    runTests);

router.get("/test",
    authenticate,
    authorize,
    getAllReports);

router.get("/test/:id",
    authenticate,
    authorize,
    validateGetTestReport,
    getReport);

router.get("/test/history/:merchantCode",
    authenticate,
    authorize,
    validateMerchantCode,
    getHistoryReports);

router.get("/merchants/:path/test/:id",
    authenticate,
    authorize,
    validateGetTestReport,
    getReport);

router.post("/merchants/:path/test/run",
    authenticate,
    authorize,
    validateRunTest,
    runTests);

router.get("/merchants/:path/test",
    authenticate,
    authorize,
    getAllReports);

router.get("/merchants/:path/test/history/:merchantCode",
    authenticate,
    authorize,
    validateMerchantCode,
    getHistoryReports);

export default router;
