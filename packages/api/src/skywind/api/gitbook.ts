import { Request, Response, NextFunction, Router } from "express";
import { authenticate, authorize } from "./middleware/middleware";
import { getTokenData } from "../services/gitbook";

const router: Router = Router();

router.post("/gitbook/login",
    authenticate,
    authorize,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            res.send(await getTokenData(req.body));
        } catch (err) {
            next(err);
        }
    }
);

export default router;
