import { NextFunction, Request as ExpressRequest, Response, Router } from "express";
import { KeyEntityHolder, UserInfoHolder } from "../services/security";
import { getEmailService } from "../services/email";
import { authenticate, authorize, validate } from "./middleware/middleware";
import { EmailData } from "../utils/emails";

const router: Router = Router();
type Request = ExpressRequest & KeyEntityHolder & UserInfoHolder;

const validateEmailPost = validate({
    recipients: { isEmailArray: true, notEmpty: true },
    subject: { notEmpty: true },
    body: { notEmpty: true },
    fromEmail: { optional: true, isEmail: true  },
    fromName: { optional: true, notEmpty: true },

});

router.post("/emails/send", authenticate, authorize, validateEmailPost, sendEmail);

async function sendEmail(req: Request, res: Response, next: NextFunction) {
    const emailService = getEmailService();

    const body = req.body;
    const recipients = body.recipients;

    const emailContent: EmailData = {
        fromEmail: body.fromEmail,
        fromName: body.fromName,
        subject: body.subject,
        htmlPart: body.body,
    };

    try {
        await emailService.sendEmail(recipients, emailContent);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

export default router;
