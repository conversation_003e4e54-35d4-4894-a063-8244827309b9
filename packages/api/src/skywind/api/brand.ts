import { NextFunction, Request, Response, Router } from "express";
import {
    authenticate,
    authorize,
    countryValidator,
    currencyValidator,
    getEntityPath,
    languageValidator,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { BaseEntity } from "../entities/entity";
import { CreateData, default as getEntityFactory } from "../services/entityFactory";
import EntityCache from "../cache/entity";

const router: Router = Router();

export async function createBrand(req: Request&KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        let entity: BaseEntity = req.keyEntity;
        if (req.params.path) {
            entity = req.keyEntity.find({ path: getEntityPath(req) });
        }
        const data: CreateData = req.body;

        const factory = getEntityFactory(entity);
        const brand: BaseEntity = await factory.createBrand(data).finally(() => EntityCache.reset());

        const entityInfo = await brand.toInfoWithBalances();
        res.status(201).send(entityInfo);
        next();
    } catch (err) {
        next(err);
    }
}

export const validateBrand = validate({
    name: { notEmpty: true, isWord: true },
    defaultCountry: countryValidator,
    defaultCurrency: currencyValidator,
    defaultLanguage: languageValidator
});

router.post("/brandentities", authenticate, authorize, validateBrand, createBrand);
router.post("/brandentities/:path", authenticate, authorize, validateBrand, createBrand);

export default router;
