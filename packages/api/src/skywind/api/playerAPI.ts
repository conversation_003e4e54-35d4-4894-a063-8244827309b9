import { NextFunction, Request, Response, Router } from "express";
import {
    getPlayerAPIService,
    isMerchantType,
    merchantPlayerInfo,
    PlayerRequestData,
    toPlayerInfo
} from "../services/playerAPIService";
import { PlayerPromotionService } from "../services/promotions/playerPromotionService";
import { getPlayerResponsibleGamingService } from "../services/playerResponsibleGaming";
import { PlayerLoginTokenData } from "../utils/token";
import {
    auditable,
    convertDatesToISOMiddleware,
    decodePid,
    defineDatesRangeLimitsMiddleware,
    defineLimits,
    FormattedResponse,
    getBooleanParamFromRequestQuery,
    parseCommaSeparatedString,
    validate,
} from "./middleware/middleware";
import { authenticate } from "./middleware/playerMiddleware";
import { parseFilter, prepareScheme } from "../services/filter";
import * as HistoryService from "../history/gameHistory";
import { sanitizeLanguage, validateGameCode, validateLanguage, validatePlayMode } from "./entityGame";
import { BrandEntity } from "../entities/brand";
import { getPaymentGatewayService } from "../services/paymentGatewayService";
import { validateFields, validateRoundId } from "./history";
import { GameHistorySpin, getSpinHistoryByRound } from "../history/spinHistory";
import { getRoundHistoryServiceFactory } from "../history/gameHistoryServiceFactory";
import config from "../config";
import { findAllEntityGameInfos, getFavoriteGames, getRecentlyPlayedGames, upsertFavoriteGame } from "../services/game";
import { GameCategoryService } from "../services/gameCategory/gameCategoryService";
import { ENTITY_GAME_STATUS, GAME_TYPES, X_PLAYER_TOKEN } from "../utils/common";
import { getPlayerInfoService } from "../services/playerInfo";
import { ValidationError } from "../errors";
import { ValidateNickname } from "../utils/validateNickname";
import { RoundHistory, UnfinishedRoundHistory } from "../entities/gameHistory";
import { setupDatesRangeLimits } from "../utils/datesHelper";
import logger from "../utils/logger";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { getLobbyPlayerGameURL } from "../services/urlManager";
import { SHORT_INFO_FIELDS } from "../services/lobby";
import { pick } from "lodash";
import { addHeaderCacheControl } from "./middleware/baseMiddleware";

const router: Router = Router();
const log = logger("player-service");

const validateNewPassword = validate({
    password: { notEmpty: true },
    newPassword: { notEmpty: true, }
});

const validateUpdatePlayerInfo = validate({
    firstName: { optional: { options: [{ checkFalsy: true }] }, isString: true },
    lastName: { optional: { options: [{ checkFalsy: true }] }, isString: true },
    email: { optional: { options: [{ checkFalsy: true }] }, isEmail: true },
    nickname: { optional: { options: [{ checkFalsy: true }] }, isString: true }
});

const validateUpdatePlayerTrx = validate({
    paymentMethodCode: { notEmpty: true, isWord: true },
    currency: { notEmpty: true },
});

const validatePaymentType = validate({ type: { notEmpty: true, isAlpha: true } });

const validateSearchHistory = validate(prepareScheme([
    "limit", "offset", "sortOrder", "bet", "win", "revenue", "ts", "firstTs", "balance", "insertedAt", "recoveryType",
    "playerCode", "gameCode", "finished", "device", "isTest", "roundId", "currency"
]));

export type PlayerRequest = Request & PlayerRequestData;

// The below methods are served in Player API. It is mostly use by Terminals and Lobbies.
router.post("/logout", authenticate, auditable, logoutPlayer);
router.post("/refresh", authenticate, auditable, refreshPlayerToken);

router.get(["/info", "/external/info"], authenticate, getPlayerInfo);
router.patch("/info", authenticate, validateUpdatePlayerInfo, updatePlayerInfo);
router.put("/external/info", authenticate, updatePlayerInfo);

router.post("/password", authenticate, validateNewPassword, setNewPassword);
router.put("/suspended", authenticate, suspendPlayer);

router.get("/history/game", authenticate, validateSearchHistory,
    decodePid({ forceReturnIfNumber: true }), getGameHistory);

router.get("/history/game/:roundId/events/:eventId",
    authenticate,
    decodePid({ forceReturnIfNumber: true }),
    getGameHistorySpinDetails);

router.get("/history/unfinished/game", authenticate, getUnfinishedRounds);

router.get("/history/game/:roundId",
    authenticate,
    validate({ ...prepareScheme(["limit", "offset", "sortOrder"]), roundId: { notEmpty: true } }),
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    validateFields("sortOrder", "type"),
    getGameHistoryRound);

router.get("/payments/methods", authenticate, validatePaymentType, getPaymentMethods);
router.get("/payments",
    authenticate,
    defineLimits,
    convertDatesToISOMiddleware(["startDate", "endDate"]),
    defineDatesRangeLimitsMiddleware(["startDate"], false),
    getPlayerPayments);

router.post("/payments/deposits", authenticate, validateUpdatePlayerTrx, auditable, playerDeposit);
router.post("/payments/withdrawals", authenticate, validateUpdatePlayerTrx, auditable, playerWithdrawal);

router.get("/games/:gameCode", authenticate, validateGameCode, validatePlayMode, sanitizeLanguage,
    validateLanguage, getGameURL);

router.get("/promo", authenticate, getPlayerPromotions);

router.get("/responsiblegaming", authenticate, getPlayerResponsibleGamingSettings);
router.patch("/responsiblegaming", authenticate, auditable, updatePlayerResponsibleGamingSettings);
router.delete("/responsiblegaming", authenticate, auditable, deletePendingResponsibleGamingChange);

router.get("/gamecategories",
    authenticate,
    async function (req: PlayerRequest, res: Response, next: NextFunction) {
        try {
            const gameCategories = await new GameCategoryService(req.brand as BrandEntity).findAllWithGamesForLobby(
                getBooleanParamFromRequestQuery(req, "includeGames"),
                req.currency,
                req.gameGroup,
                req.gameGroupId
            );

            res.send(gameCategories);
            next();
        } catch (err) {
            next(err);
        }
    });

router.get(["/lobbies/:lobbyId", "/lobbies/:lobbyId/menu-items"],
    addHeaderCacheControl,
    decodePid(),
    validate({
        socketVersion: { optional: true, hasSocketVersion: true },
    }),
    authenticate,
    async function (req: PlayerRequest, res: Response, next: NextFunction) {
        try {
            const lobby = await getPlayerAPIService().getLobby(
                req.brand,
                req.params.lobbyId,
                req.playerCode,
                {
                    includeGamesLimitRanges: getBooleanParamFromRequestQuery(req, "includeGamesLimits", false),
                    ...(req.test ? { gameStatuses: [ENTITY_GAME_STATUS.NORMAL, ENTITY_GAME_STATUS.TEST] } : {}),
                    currency: req.currency,
                    gameGroupId: req.gameGroupId,
                    gameGroupName: req.gameGroup,
                    socketVersion: req.query.socketVersion,
                }
            );
            res.send(pick(lobby, parseCommaSeparatedString(req.query.fields, SHORT_INFO_FIELDS)));
        } catch (err) {
            next(err);
        }
    });

router.get("/games",
    authenticate,
    validate({
        lobbyId: { notEmpty: true, isWord: true, isLength: { options: { min: 1, max: 255 } } }
    }),
    decodePid(),
    async function (req: PlayerRequest, res, next) {
        try {
            const paths = parseCommaSeparatedString(req.query.fields,
                ["code", "type", "title", "providerTitle", "info", "defaultInfo", "limits", "features"]);
            const games = await getPlayerAPIService().getGames(
                req.brand,
                req.query.lobbyId,
                {
                    gameStatuses: parseGameStatuses(req),
                    includeGamesLimits: paths.includes("limits"),
                    currency: req.currency,
                    gameGroupId: req.gameGroupId,
                    gameGroupName: req.gameGroup
                }
            );
            res.send(games.map(game => pick(game, paths)));
        } catch (err) {
            next(err);
        }
    }
);

function parseGameStatuses({ query, test }: PlayerRequest): ENTITY_GAME_STATUS[] | undefined {
    let statuses: string[] = [];
    if (query["status__in"]) {
        statuses = query["status__in"].toString().split(",");
    } else if (query["status"]) {
        statuses = [query["status"]];
    }
    const gameStatuses: ENTITY_GAME_STATUS[] = statuses.map(status => {
        if (status === "normal") {
            return ENTITY_GAME_STATUS.NORMAL;
        }
        if (status === "test" && test) {
            return ENTITY_GAME_STATUS.TEST;
        }
    }).filter(Boolean);
    return gameStatuses.length ? gameStatuses : undefined;
}

async function logoutPlayer(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        res.status(204).send(await getPlayerAPIService().logoutPlayer({
            brandId: req.brandId,
            code: req.playerCode,
            sessionId: req.sessionId,
        }));
        next();
    } catch (err) {
        next(err);
    }
}

async function refreshPlayerToken(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        const loginInfo = await getPlayerAPIService().refreshPlayerToken(req);
        res.header(X_PLAYER_TOKEN, loginInfo.token);
        res.send(loginInfo);
        next();
    } catch (err) {
        return next(err);
    }
}

async function getPaymentMethods(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        res.send(await getPlayerAPIService().getPaymentMethods(req.brandId, req.query.type));
        next();
    } catch (err) {
        next(err);
    }
}

async function getPlayerPayments(req: PlayerRequest, res: FormattedResponse, next: NextFunction) {
    try {
        const query = req.query;
        delete query["playerCode"];
        delete query["playerCode__in"];
        delete query["playerCode__contains!"];
        delete query["playerCode__contains"];
        query.playerCode = req.playerCode;

        res.sendFormatted(req,
            await getPlayerAPIService().getPlayerPayments(req.brandId, req.query),
            ["orderInfo"]);
        next();
    } catch (err) {
        next(err);
    }
}

async function playerDeposit(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        const service = getPaymentGatewayService();
        const info = await service.deposit(req.brand, { ...req.body, customerId: req.playerCode });
        res.send(info);
    } catch (err) {
        next(err);
    }
}

async function playerWithdrawal(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        const service = getPaymentGatewayService();
        const info = await service.withdraw(req.brand, { ...req.body, customerId: req.playerCode });
        res.send(info);
    } catch (err) {
        next(err);
    }
}

async function getGameURL({ params, query, brand, tokenData }: PlayerRequest, res: Response, next: NextFunction) {
    const { lobby, cashier, playmode, playMode, ip, aamsSessionId, aamsParticipationCode, fields } = query;
    const mode = playmode || playMode || PlayMode.REAL;
    const playerTokenData: PlayerLoginTokenData = {
        ...tokenData,
        playmode: mode,
        playMode: mode,
        ...(cashier ? { cashier } : {}),
        ...(lobby ? { lobby } : {}),
        ...(aamsSessionId ? { aamsSessionId } : {}),
        ...(aamsParticipationCode ? { aamsParticipationCode } : {})
    };
    try {
        const urlInfo = await getLobbyPlayerGameURL(
            brand as BrandEntity,
            playerTokenData,
            params.gameCode,
            ip
        );
        const props = parseCommaSeparatedString(fields);
        res.send(props.length ? pick(urlInfo, props) : urlInfo);
        next();
    } catch (err) {
        next(err);
    }
}

async function getGameHistory(req: PlayerRequest, res: Response, next: NextFunction) {
    req.query.playerCode = req.playerCode;
    try {
        setupDatesRangeLimits(req.query, { diffInMs: config.playerHistoryPeriod });

        let historyLines: (RoundHistory & { gameName?: string })[] = [];
        if (req.query.roundsForLiveGames === "true") {
            const queries = new Map();
            queries.set("game", { type: GAME_TYPES.live });
            const games = await findAllEntityGameInfos(req.brand, queries, true);
            req.query["gameCode__in"] = games
                .map(game => game.code)
                .join(",");

            const rounds = await HistoryService.findGameHistoryEntries(
                req.brand.id, parseFilter(req.query, HistoryService.queryParamsKeys)
            );

            for (const round of rounds) {
                const game = games.find(({ code }) => code === round.gameCode);
                historyLines.push({
                    ...round,
                    gameName: game && game.title
                });
            }
        } else {
            historyLines = await HistoryService.findGameHistoryEntries(
                req.brand.id, parseFilter(req.query, HistoryService.queryParamsKeys)
            );
        }
        res.send(historyLines);
    } catch (err) {
        next(err);
    }
}

async function getGameHistorySpinDetails(req: PlayerRequest, res: Response, next: NextFunction) {
    req.query.playerCode = req.playerCode;
    try {
        const spinDetails = await HistoryService.getGameHistoryDetails(
            req.brand,
            req.params.roundId,
            req.params.eventId,
            {
                playerCode: req.playerCode,
                addJurisdictionSettings: true
            });
        res.send(spinDetails);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getUnfinishedRounds(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        req.query.playerCode = req.playerCode;
        const rounds: UnfinishedRoundHistory[] = await getRoundHistoryServiceFactory()
            .getUnfinishedRoundsHistoryService()
            .getRounds(req.brand, req.query);
        res.send(rounds);
    } catch (err) {
        next(err);
    }
}

async function getGameHistoryRound(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        const historyLines: GameHistorySpin[] = await getSpinHistoryByRound(
            req.brand as BrandEntity, req.params.roundId, req.query, req.playerCode);
        res.send(historyLines);
        next();
    } catch (err) {
        next(err);
    }
}

async function getPlayerInfo(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        res.send(await toPlayerInfo(req, log));
    } catch (err) {
        next(err);
    }
}

async function updatePlayerInfo(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        if (isMerchantType(req)) {
            const forbiddenParameters = Object.keys(req.body).filter(key => key !== "nickname");
            if (forbiddenParameters.length) {
                return next(new ValidationError(`${forbiddenParameters} params are forbidden for merchant player`));
            }
            await getPlayerInfoService().createOrUpdate({
                playerCode: req.playerCode.toString(),
                brandId: req.brandId,
                nickname: req.body.nickname,
                isMerchantPlayer: true
            }, { increaseNicknameChangeAttempts: true });

            const playerInfo = await merchantPlayerInfo(req);
            res.send(playerInfo);
        } else {
            delete req.body.isVip;
            res.send(await getPlayerAPIService().updatePlayerInfo(req.brand, req.playerCode, req.body));
        }
    } catch (err) {
        next(err);
    }
}

async function suspendPlayer(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        await getPlayerAPIService().suspendPlayer(req.brandId, req.playerCode);
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

async function setNewPassword(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        res.send(await getPlayerAPIService().setNewPassword(req.brand, req.playerCode, req.body));
        next();
    } catch (err) {
        next(err);
    }
}

async function getPlayerResponsibleGamingSettings(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        res.send(await getPlayerResponsibleGamingService(req.brand).checkAndGetPlayerResponsibleGamingSettings(
            req.playerCode));
        next();
    } catch (err) {
        next(err);
    }
}

async function updatePlayerResponsibleGamingSettings(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        res.send(await getPlayerResponsibleGamingService(req.brand).updatePlayerResponsibleGamingSettings(
            req.playerCode,
            req.body));
        next();
    } catch (err) {
        next(err);
    }
}

async function deletePendingResponsibleGamingChange(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        res.send(await getPlayerResponsibleGamingService(req.brand).deleteResponsibleGamingPendingChange(
            req.playerCode,
            req.body));
        next();
    } catch (err) {
        next(err);
    }
}

async function getPlayerPromotions(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        const promos = await PlayerPromotionService.getPlayerPromotions(req.brand as BrandEntity,
            req.playerCode,
            req.query.type);
        res.send(promos);
        next();
    } catch (err) {
        next(err);
    }
}

router.get("/favoritegames",
    addHeaderCacheControl,
    authenticate,
    validate(prepareScheme(["limit", "offset", "gameCodeOnly"])),
    favoriteGames);

async function favoriteGames(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        const games = await getFavoriteGames(req.brand, req.playerCode, req.query);
        res.send(games);
        next();
    } catch (err) {
        next(err);
    }
}

router.post("/favoritegames/:gameCode",
    authenticate,
    validateGameCode,
    saveFavoriteGames);

async function saveFavoriteGames(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        await upsertFavoriteGame(req.brand,
            req.playerCode,
            req.params.gameCode,
            true);

        res.status(201).end();
    } catch (err) {
        next(err);
    }
}

router.delete("/favoritegames/:gameCode",
    authenticate,
    validateGameCode,
    deleteFavoriteGames);

async function deleteFavoriteGames(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        await upsertFavoriteGame(req.brand,
            req.playerCode,
            req.params.gameCode,
            false);
        res.status(200).end();
    } catch (err) {
        next(err);
    }
}

router.get("/recentlygames",
    addHeaderCacheControl,
    authenticate,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    recentlyPlayedGames);

async function recentlyPlayedGames(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        const games = await getRecentlyPlayedGames(req.brand, req.playerCode, req.query);
        res.send(games);
        next();
    } catch (err) {
        next(err);
    }
}

router.get("/players/validate-nickname/:playerNickname", authenticate, validatorNickname);

async function validatorNickname(req: PlayerRequest, res: Response, next: NextFunction) {
    try {
        const validator = new ValidateNickname();
        await validator.checkSymbols(req.params.playerNickname);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

export default router;
