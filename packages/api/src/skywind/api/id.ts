import * as express from "express";
import { authenticate, authorize, bodyIsArrayValidator, validate } from "./middleware/middleware";
import { ValidationError } from "../errors";
import { decodeId, encodeId } from "../utils/publicid";

const router: express.Router = express.Router();

router.get("/id/:encodedId/decode",
    authenticate,
    authorize,
    validate({
        encodedId: { notEmpty: true }
    }),
    (req: express.Request, res: express.Response, next: express.NextFunction) => {
        const decodedId: number = decodeId(req.params.encodedId);
        if (typeof decodedId !== "number") {
            return next(new ValidationError("encodedId is invalid value"));
        }
        res.send({ decoded: decodedId });
        next();
    });

router.get("/id/:decodedId/encode",
    authenticate,
    authorize,
    validate({
        decodedId: { notEmpty: true, isInt: { options: { min: 0 } } }
    }),
    (req: express.Request, res: express.Response, next: express.NextFunction) => {
        const encodedId: string = encodeId(+req.params.decodedId);
        if (!encodedId) {
            return next(new ValidationError("decodedId is invalid value"));
        }
        res.send({ encoded: encodedId });
        next();
    });
router.post("/ids/decode",
    authenticate,
    authorize,
    bodyIsArrayValidator,
    (req: express.Request, res: express.Response, next: express.NextFunction) => {
        const response = {};
        for (const encodedId of req.body) {
            const decodedId: number = decodeId(encodedId);

            if (typeof decodedId !== "number") {
                return next(new ValidationError(`encodedId "${encodedId}" is invalid value`));
            }
            response[encodedId] = decodedId;
        }
        res.send(response);
        next();
    });

router.post("/ids/encode",
    authenticate,
    authorize,
    bodyIsArrayValidator,
    (req: express.Request, res: express.Response, next: express.NextFunction) => {

        const response = {};
        for (const decodedId of req.body) {
            const encodedId: string = encodeId(+decodedId);
            if (!encodedId) {
                return next(new ValidationError(`decodedId "${decodedId}" is invalid value`));
            }
            response[decodedId] = encodedId;
        }

        res.send(response);
        next();
    });
export default router;
