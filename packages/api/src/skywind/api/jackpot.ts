import { NextFunction, Request, Response, Router } from "express";
import { authenticate, authorize, decodePid, getBrand, getEntity } from "./middleware/middleware";
import { getJPNServer } from "../services/jpnserver";
import { getJackpotAuditAuthToken, getJackpotAuthToken } from "../services/jackpot";
import { KeyEntityHolder, PermissionsHolder } from "../services/security";
import { GameSettings } from "../entities/game";
import {
    JackpotAudit,
    JackpotAuditType,
    JackpotConfigurationLevel,
    JackpotDisableMode,
    JackpotInstance,
    JackpotTicker,
    RegisterJackpotInstanceRequest
} from "../entities/jackpot";
import * as Errors from "../errors";
import { OperationForbidden, ValidationError } from "../errors";
import { JackpotType, JPInfo, JPInfoPool } from "../entities/jpinfo";
import { sequelize as db } from "../storage/db";
import config from "../config";
import EntitySettingsService, { getEntitySettings } from "../services/settings";
import { getPhantomService } from "../phantom/service";
import { BrandEntity } from "../entities/brand";
import logger from "../utils/logger";
import { getEntityJurisdictionService } from "../services/entityJurisdiction";
import { BaseEntity } from "../entities/entity";
import { getJurisdictionService } from "../services/jurisdiction";
import { Models } from "../models/models";

const log = logger("sw-management-api:jackpot");

const router: Router = Router();

const JP_QUERY = "SELECT * FROM fnc_entity_jackpots_simplified(:entityId)";

async function query(entity: BrandEntity): Promise<any> {
    return db.query(JP_QUERY, { replacements: { entityId: entity.id }, raw: true });
}

export const dbManager = {
    query
};

async function getJackpotInstances(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = getBrand(req);
        const { jpnUrl, jpnTickerUrl } = await getEntitySettings(entity.path);
        const token = await getJackpotAuthToken();
        const tokenAudit = await getJackpotAuditAuthToken();
        const JPNServer = getJPNServer(jpnUrl, jpnTickerUrl);

        let phantomJackpotIdsWithGameCodes;
        let jackpotIdsWithGameCodes;

        const gameCodes = req.query.gameCodes ? req.query.gameCodes.split(",").map(id => id.trim()) : [];
        const queryJackpotIdsArray = req.query.jackpotIds ? req.query.jackpotIds.split(",").map(id => id.trim()) : [];

        const gamesDBInstances = await Models.EntityGameModel.findAll({
            where: { entityId: entity.id }, include: [
                {
                    model: Models.GameModel,
                    required: true,
                    attributes: [
                        "code"
                    ]
                }
            ],
        });

        if (gamesDBInstances && gamesDBInstances.length) {
            let gamesSettingsArray = gamesDBInstances.map(game => ({
                code: game.get("game").get("code"),
                settings: game.get("settings")
            }));

            if (gameCodes.length) {
                gamesSettingsArray = gamesSettingsArray.filter(item => gameCodes.includes(item.code));
            }

            phantomJackpotIdsWithGameCodes = await getPhantomJackpotIdsWithGameCodes(entity,
                gamesSettingsArray.map(item => item.code));
            jackpotIdsWithGameCodes = prepareGameCodesByJackpotIds(gamesSettingsArray);

            if (queryJackpotIdsArray.length) {
                jackpotIdsWithGameCodes = Object.keys(jackpotIdsWithGameCodes)
                    .filter(jackpotId => queryJackpotIdsArray.includes(jackpotId))
                    .reduce((result: JackpotIdWithGameCodes, jackpotId: string) => {
                        result[jackpotId] = jackpotIdsWithGameCodes[jackpotId];
                        return result;
                    }, {});
                phantomJackpotIdsWithGameCodes = Object.keys(phantomJackpotIdsWithGameCodes)
                    .filter(jackpotId => queryJackpotIdsArray.includes(jackpotId))
                    .reduce((result: JackpotIdWithGameCodes, jackpotId: string) => {
                        result[jackpotId] = phantomJackpotIdsWithGameCodes[jackpotId];
                        return result;
                    }, {});
            }

            const parentJackpotsData = await dbManager.query(entity);
            if (parentJackpotsData && parentJackpotsData[0].length) {
                parentJackpotsData[0].forEach(parentJackpot => {
                    const game = gamesSettingsArray.find(item => item.code === parentJackpot.game_code);
                    if (game && !Object.keys(jackpotIdsWithGameCodes).includes(parentJackpot.jp_id)
                        && (!queryJackpotIdsArray.length || queryJackpotIdsArray.includes(parentJackpot.jp_id))) {
                        jackpotIdsWithGameCodes[parentJackpot.jp_id] = [game.code];
                    }
                });
            }
        } else {
            return next(new Errors.GamesNotFound());
        }

        const allJackpotIdsWithCodes = { ...jackpotIdsWithGameCodes, ...phantomJackpotIdsWithGameCodes };
        const allJackpotIds = Object.keys(allJackpotIdsWithCodes);

        const jackpots: JackpotInstance[] = allJackpotIds.length ?
                                            await JPNServer.getJackpots(allJackpotIds, token) :
            [];

        if (jackpots.length) {
            const jpInfoArray: JPInfo[] = [];

            for (const jackpotInstance of jackpots) {
                const tickersArray = await JPNServer.getJackpotTickers([jackpotInstance.id],
                    jackpotInstance.currency);
                const ticker = Array.isArray(tickersArray) && tickersArray.length &&
                    tickersArray.find(t => t.jackpotId === jackpotInstance.id);
                const jpInfo = mapJPNInstanceToJPInfo(jackpotInstance, ticker);

                try {
                    let disableAudits: JackpotAudit[] = await JPNServer.getJackpotAudits(jackpotInstance.id,
                        JackpotAuditType.DISABLE,
                        tokenAudit);
                    disableAudits = disableAudits
                        .filter(item => item.history.disableMode === JackpotDisableMode.IMMEDIATE);
                    const disableOnWinAudits: JackpotAudit[] = await JPNServer.getJackpotAudits(jackpotInstance.id,
                        JackpotAuditType.DISABLE_ON_WIN,
                        tokenAudit);
                    jpInfo.endDate = getEndDate([...disableAudits, ...disableOnWinAudits]) as string;
                } catch (err) {
                    log.error("error during fetching jackpot audits");
                }

                jpInfo.gameCodes = allJackpotIdsWithCodes[jackpotInstance.id];

                let isJackpotExpired = false;

                if (jpInfo.endDate) {
                    const endDate = new Date(jpInfo.endDate);
                    const now = new Date();

                    endDate.setDate(endDate.getDate() + config.jackpotEndDateNotOlderThanDaysAmount);

                    isJackpotExpired = endDate < now;
                }

                if (!isJackpotExpired) {
                    jpInfoArray.push(jpInfo);
                }
            }

            res.send(jpInfoArray);
        } else {
            res.send(jackpots);
        }
    } catch (err) {
        next(err);
    }
}

async function registerJackpotInstance(req: Request & KeyEntityHolder & PermissionsHolder,
                                       res: Response,
                                       next: NextFunction) {
    try {
        if (!req.isSuperAdmin) {
            return next(new OperationForbidden("Only SuperAdmins can perform this operation"));
        }
        await validateJackpotConfigurationLevel(req);
        const token = await getJackpotAuthToken();
        const registerJackpotInstanceRequest: RegisterJackpotInstanceRequest = req.body;
        const entity = getEntity(req);
        const { jpnUrl, jpnTickerUrl } = await getEntitySettings(entity.path);
        const JPNServer = getJPNServer(jpnUrl, jpnTickerUrl);
        const jackpotInstance = await JPNServer.registerJackpotInstance(registerJackpotInstanceRequest, token);
        res.send(jackpotInstance);
    } catch (err) {
        next(err);
    }
}

async function updateJackpotInstance(req: Request & KeyEntityHolder & PermissionsHolder,
                                     res: Response,
                                     next: NextFunction) {
    try {
        if (!req.isSuperAdmin) {
            return next(new OperationForbidden("Only SuperAdmins can perform this operation"));
        }
        if (!req.params?.id) {
            return next(new ValidationError("The id param is required"));
        }
        await validateJackpotConfigurationLevel(req);
        const token = await getJackpotAuthToken();
        const registerJackpotInstanceRequest: RegisterJackpotInstanceRequest = req.body;
        const entity = getEntity(req);
        const { jpnUrl, jpnTickerUrl } = await getEntitySettings(entity.path);
        const JPNServer = getJPNServer(jpnUrl, jpnTickerUrl);
        const jackpotInstance = await JPNServer
            .updateJackpotInstance(req.params.id, registerJackpotInstanceRequest, token);
        res.send(jackpotInstance);
    } catch (err) {
        next(err);
    }
}

async function validateJackpotConfigurationLevel(req: Request & KeyEntityHolder): Promise<void> {
    if (req.body.jackpotConfigurationLevel === undefined) {
        throw new Errors.ValidationError("jackpotConfigurationLevel is required");
    }
    switch (req.body.jackpotConfigurationLevel) {
        case JackpotConfigurationLevel.SPECIFIC_BRAND_ONLY:
            return handleSpecificBrandOnlyValidation(req);
        case JackpotConfigurationLevel.SHARED_BETWEEN_ONE_OPERATOR_FOR_ONE_JURISDICTION:
            return handleOneOperatorOneJurisdictionValidation(req);
        case JackpotConfigurationLevel.SHARED_BETWEEN_SEVERAL_OPERATORS_FOR_ONE_JURISDICTION:
            return handleSeveralOperatorsOneJurisdictionValidation(req);
        case JackpotConfigurationLevel.SHARED_BETWEEN_SEVERAL_JURISDICTIONS_FOR_ONE_OPERATOR:
            return handleSeveralJurisdictionsOneOperatorValidation(req);
        case JackpotConfigurationLevel.SHARED_BETWEEN_SEVERAL_OPERATORS:
        case JackpotConfigurationLevel.SHARED_GLOBALLY:
            // No validation required
            return;
    }
}

async function handleSeveralJurisdictionsOneOperatorValidation(req: Request & KeyEntityHolder): Promise<void> {
    const request: RegisterJackpotInstanceRequest = req.body;
    if (!request.entityId) {
        return;
    }
    const entity = await getEntityById(req.keyEntity, request.entityId);
    const entityJurisdictions = await getEntityJurisdictionService().findAll({ entityId: entity.id });
    let highestLevel: number = 0;
    let highestLevelJurisdictionCode: string;
    for (const jurisdiction of entityJurisdictions) {
        if (jurisdiction.allowedJackpotConfigurationLevel > highestLevel) {
            highestLevel = jurisdiction.allowedJackpotConfigurationLevel;
            highestLevelJurisdictionCode = jurisdiction.code;
        }
    }
    if (request.jackpotConfigurationLevel > highestLevel) {
        throw new Errors.ValidationJackpotConfigurationError(request.jackpotConfigurationLevel,
            highestLevel,
            highestLevelJurisdictionCode);
    }
}

async function handleSeveralOperatorsOneJurisdictionValidation(req: Request & KeyEntityHolder): Promise<void> {
    const request: RegisterJackpotInstanceRequest = req.body;
    if (!request.jurisdictionCode) {
        return;
    }
    const jurisdiction = await getJurisdictionService().findOne(request.jurisdictionCode);
    if (request.jackpotConfigurationLevel > jurisdiction.allowedJackpotConfigurationLevel) {
        throw new Errors.ValidationJackpotConfigurationError(request.jackpotConfigurationLevel,
            jurisdiction.allowedJackpotConfigurationLevel, jurisdiction.code);
    }
}

async function handleOneOperatorOneJurisdictionValidation(req: Request & KeyEntityHolder): Promise<void> {
    const request: RegisterJackpotInstanceRequest = req.body;
    if (!request.entityId || !request.jurisdictionCode) {
        return;
    }
    const entity = await getEntityById(req.keyEntity, request.entityId);
    const jurisdiction = await getJurisdictionService().findOne(request.jurisdictionCode);
    const entityJurisdictions = await getEntityJurisdictionService().findAll({ entityId: entity.id });
    if (!entityJurisdictions.map(j => j.code).includes(jurisdiction.code)) {
        throw new Errors.ValidationError(
            `Jurisdiction ${request.jurisdictionCode} is not available ` +
            `for entity ${entity.name} (id = ${entity.id})`
        );
    }
    if (request.jackpotConfigurationLevel > jurisdiction.allowedJackpotConfigurationLevel) {
        throw new Errors.ValidationJackpotConfigurationError(request.jackpotConfigurationLevel,
            jurisdiction.allowedJackpotConfigurationLevel, jurisdiction.code);
    }
}

async function handleSpecificBrandOnlyValidation(req: Request & KeyEntityHolder): Promise<void> {
    const request: RegisterJackpotInstanceRequest = req.body;
    if (!request.entityId) {
        return;
    }
    const brand = await getBrandById(req.keyEntity, request.entityId);
    const jurisdiction = await getEntityJurisdictionService().findOneByEntityId(brand.id);
    if (request.jackpotConfigurationLevel > jurisdiction.allowedJackpotConfigurationLevel) {
        throw new Errors.ValidationJackpotConfigurationError(request.jackpotConfigurationLevel,
            jurisdiction.allowedJackpotConfigurationLevel, jurisdiction.code);
    }
}

async function getEntityById<T extends BaseEntity>(keyEntity: BaseEntity, entityId): Promise<T> {
    const entity = keyEntity.find({ id: entityId }) as T;
    if (!entity) {
        throw new Errors.EntityCouldNotBeFound();
    }
    return entity;
}

async function getBrandById(keyEntity: BaseEntity, brandId: number): Promise<BrandEntity> {
    const entity = await getEntityById<BrandEntity>(keyEntity, brandId);
    if (!entity.isBrand()) {
        throw new Errors.NotBrandOrMerchant();
    }
    return entity;
}

async function getPhantomJackpotIdsWithGameCodes(entity: BrandEntity,
                                                 gameCodes: string[]): Promise<JackpotIdWithGameCodes> {
    const result = {};

    try {
        const entitySettings = new EntitySettingsService(entity);
        const settings = await entitySettings.get();

        settings.phantomJackpotApiUrl = settings.phantomJackpotApiUrl ?
                                        settings.phantomJackpotApiUrl.replace("features/jackpot", "features/jackpots") :
                                        config.phantom.apiUrl.replace("features/jackpot", "features/jackpots");

        const phantomService = getPhantomService(settings);
        const phantomJackpots = await phantomService.getJackpots({
            brandId: entity.id,
            customerId: "*"
        });

        if (phantomJackpots && Array.isArray(phantomJackpots.jackpots) && phantomJackpots.jackpots.length &&
            Array.isArray(gameCodes) && gameCodes.length) {

            phantomJackpots.jackpots.forEach(phantomJackpot => {
                if (gameCodes.some(gameCode => Array.isArray(phantomJackpot.games) && phantomJackpot.games.length &&
                    phantomJackpot.games.includes(gameCode))) {
                    result[phantomJackpot.jackpotId] = phantomJackpot.games.filter(game => gameCodes.includes(game));
                }
            });
        }
    } catch (err) {
        log.error(err, "error during fetching phantom jackpots");
    }

    return result;
}

interface JackpotIdWithGameCodes {
    [jackpotId: string]: string[];
}

function prepareGameCodesByJackpotIds(games: { code: string, settings: GameSettings }[]): JackpotIdWithGameCodes {
    return games
        .filter(game => !!game?.settings?.jackpotId)
        .reduce((result: JackpotIdWithGameCodes, game: { code: string, settings: GameSettings }) => {
            Object.values(game.settings.jackpotId).map(jackpotId => {
                result[jackpotId] = result.hasOwnProperty(jackpotId) ?
                    [...new Set([...result[jackpotId], game.code])] :
                    [game.code];
            });
            return result;
        }, {});
}

function getEndDate(audits: JackpotAudit[]): string | Date | undefined {
    return Array.isArray(audits) && audits.length ?
           audits.map(audit => audit.ts).sort()[audits.length - 1] :
           undefined;
}

function mapJPNInstanceToJPInfo(jackpotInstance: JackpotInstance, ticker: JackpotTicker): JPInfo {
    const jpInfo = {} as JPInfo;

    jpInfo.id = jackpotInstance.id;
    jpInfo.name = jackpotInstance.id;
    jpInfo.currency = jackpotInstance.definition && jackpotInstance.definition.currency;
    jpInfo.type = jackpotInstance.type && jackpotInstance.type.startsWith("ph-") ?
                  JackpotType.MWJP :
                  JackpotType.GAME_LEVEL;
    jpInfo.startDate = jackpotInstance.createdAt;
    jpInfo.status = jackpotInstance.isDisabled ? 0 : 1;
    jpInfo.endDate = jackpotInstance.endDate;
    jpInfo.poolsCount = jackpotInstance.definition && Array.isArray(jackpotInstance.definition.list) &&
        jackpotInstance.definition.list.length;

    const info = jackpotInstance.info;
    jpInfo.externalId = info && info.externalId || jackpotInstance.id;
    jpInfo.externalStartDate = info && info.externalStartDate || jackpotInstance.createdAt;

    jpInfo.jackpotPools = [];

    if (jpInfo.poolsCount) {
        jackpotInstance.definition.list.forEach(pool => {
            const jpInfoPool = {} as JPInfoPool;

            jpInfoPool.poolId = pool.id;

            if (pool.totalContribution) {
                jpInfoPool.contributionPercent = pool.totalContribution;
            } else if (Array.isArray(pool.contribution) && pool.contribution.length) {
                jpInfoPool.contributionPercent = (pool.contribution[0].seed || 0) +
                    (pool.contribution[0].progressive || 0);
                jpInfoPool.contributionPercent = jpInfoPool.contributionPercent &&
                    +jpInfoPool.contributionPercent.toFixed(2);
            }

            jpInfoPool.initialSeed = pool.seed && pool.seed.amount;

            const tickerAmount = ticker && ticker.pools && ticker.pools[jpInfoPool.poolId];
            jpInfoPool.tickerAmount = tickerAmount && tickerAmount.amount;

            jpInfo.jackpotPools.push(jpInfoPool);
        });
    }

    return jpInfo;
}

router.get("/entities/:path/jackpots", authenticate, authorize, getJackpotInstances);
router.get("/jackpots", authenticate, authorize, getJackpotInstances);
router.post(["/jackpots", "/entities/:path/jackpots"],
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["id"] }),
    registerJackpotInstance);
router.patch(["/jackpots/:id", "/entities/:path/jackpots/:id"],
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["id"] }),
    updateJackpotInstance);

export default router;
