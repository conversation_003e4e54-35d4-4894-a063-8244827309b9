import { NextFunction, Request, Response, Router } from "express";
import { authenticate } from "./middleware/middleware";
import { getAvailableOperations, getOperations, PermissionsHolder, } from "../services/security";
import { getPermissionsDescriptions } from "../services/permission";

const router: Router = Router();

router.get("/permissions",
    authenticate,
    async(req: Request, res: Response, next: NextFunction) => {
        res.send(await getPermissionsDescriptions());
    });

// NOTE: API returns only operations from management-api and doesn't include anything from other components
// TODO: Think about method usability
router.get("/operations",
    authenticate,
    async(req: Request, res: Response, next: NextFunction) => {
        res.send(await getOperations(req.query["permission"]));
    });

// NOTE: API returns only operations from management-api and doesn't include anything from other components
// TODO: Think about method usability
router.get("/availableOperations",
    authenticate,
    async(req: Request&PermissionsHolder, res: Response, next: NextFunction) => {
        res.send(await getAvailableOperations(req.permissions));
    });

export default router;
