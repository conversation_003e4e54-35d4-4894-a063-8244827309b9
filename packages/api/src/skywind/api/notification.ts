import { NextFunction, Request as ExpressRequest, Response, Router } from "express";
import { auditable, authenticate, authorize, decodePid, validate } from "./middleware/middleware";
import * as NotificationService from "../services/notification";
import { KeyEntityHolder, UserInfoHolder } from "../services/security";
import { parseFilter, prepareScheme } from "../services/filter";
import * as Errors from "../errors";

const router: Router = Router();
type Request = ExpressRequest & KeyEntityHolder & UserInfoHolder;

async function sendNotification(req: Request, res: Response, next: NextFunction) {
    try {
        await NotificationService.sendNotification(req.keyEntity, req.username, req.body);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function getNotifications(req: Request, res: Response, next: NextFunction) {
    try {
        res.send(await NotificationService.getNotifications(
            req.userId, parseFilter(req.query, NotificationService.queryParamsKeysReceived))
        );
        next();
    } catch (err) {
        next(err);
    }
}

async function getPostedNotifications(req: Request, res: Response, next: NextFunction) {
    try {
        res.send(await NotificationService.getPostedNotifications(
            req.userId, parseFilter(req.query, NotificationService.queryParamsKeysPosted))
        );
        next();
    } catch (err) {
        next(err);
    }
}

async function notificationBulkAction(req: Request, res: Response, next: NextFunction) {
    try {
        const data: any = req.body;
        if (data.status === undefined && data.unread === undefined) {
            return next(new Errors.ValidationError("status or unread should be defined"));
        }
        await NotificationService.notificationBulkAction(req.userId, data);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function changeStatus(req: Request, res: Response, next: NextFunction) {
    try {
        res.send(await NotificationService.changeStatus(req.userId, req.params.notificationId, "suspended"));
        next();
    } catch (err) {
        next(err);
    }
}

const validateNotificationPost = validate({
    message: { notEmpty: true },
    allSiblings: { optional: true, isBoolean: true },
    recursively: { optional: true, isBoolean: true },
    lifetime: { optional: true, isTimestampIso8601: true },
});

const schemaOfValidationUnreadAndStatus = {
    unread: { optional: true, isBoolean: true },
    status: { optional: true, isStatus: true },
};

const validateList = validate({
    ...prepareScheme(["limit", "offset", "sortOrder"]),
    ...schemaOfValidationUnreadAndStatus
});

const notificationIdsSchema = {
    id: { isArray: { options: { notEmpty: true } } }
};

router.post("/notifications/group/mark",
    authenticate, authorize, decodePid(), validate(schemaOfValidationUnreadAndStatus), validate(notificationIdsSchema),
    auditable,
    notificationBulkAction);
router.post("/notifications/send", authenticate, authorize, validateNotificationPost, decodePid(),
    auditable, sendNotification);
router.get("/notifications/list", authenticate, authorize, validateList, getNotifications);
router.get("/notifications/posted", authenticate, authorize, validateList, getPostedNotifications);
router.put("/notifications/:notificationId/suspended", authenticate, authorize, decodePid(),
    auditable, changeStatus);

export default router;
