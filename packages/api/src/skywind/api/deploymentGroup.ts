import * as express from "express";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    getEntityPath
} from "./middleware/middleware";
import { getDeploymentGroupService } from "../services/deploymentGroup";
import { KeyEntityHolder, UserInfoHolder } from "../services/security";
import { getGameClientVersionService, VersionsPerRoute } from "../services/gameVersionService";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder & UserInfoHolder;

router.put("/games/:gameCode/deployment-groups/:route", authenticate, authorize, decodePid(), auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const gameCode: string = req.params["gameCode"];
        const route: string = req.params["route"];

        try {
            await getDeploymentGroupService().assignDeploymentGroupToGame(gameCode, route);
            res.status(201).end();
        } catch (err) {
            return next(err);
        }
    });

router.delete("/games/:gameCode/deployment-groups", authenticate, authorize, auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const gameCode: string = req.params["gameCode"];
        try {
            await getDeploymentGroupService().unAssignDeploymentGroupFromGame(gameCode);
            res.status(201).end();
        } catch (err) {
            return next(err);
        }
    });

router.put("/entities/:path/deployment-groups/:route", authenticate, authorize, decodePid(), auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const entityPath: string = getEntityPath(req);
        const route: string = req.params["route"];

        try {
            await getDeploymentGroupService().assignDeploymentGroupToEntity(entityPath, route);
            res.status(201).end();
        } catch (err) {
            return next(err);
        }
    });

router.delete("/entities/:path/deployment-groups", authenticate, authorize, auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const entityPath: string = getEntityPath(req);
        try {
            await getDeploymentGroupService().unAssignDeploymentGroupFromEntity(entityPath);
            res.status(201).end();
        } catch (err) {
            return next(err);
        }
    });

router.get("/deployment-groups",
    authenticate, authorize, async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const deployments = await getDeploymentGroupService().getDeploymentGroups();
            res.status(200).send(deployments);
        } catch (err) {
            return next(err);
        }
    });

router.post("/deployment-groups", authenticate, authorize, auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const description = req.body["description"];
        const route = req.body["route"];
        const type = req.body["type"];
        try {
            const deployments = await getDeploymentGroupService().addDeploymentGroup(route, type, description);
            res.status(200).send({ id: deployments.id });
        } catch (err) {
            return next(err);
        }
    });

router.get("/deployment-groups/:route",
    authenticate, authorize, decodePid(), async (req: Request, res: express.Response, next: express.NextFunction) => {
        const route: string = req.params["route"];

        try {
            const deployment = await getDeploymentGroupService().getDeploymentGroupByRoute(route);
            res.status(200).send(deployment);
        } catch (err) {
            return next(err);
        }
    });

router.get("/deployment-groups/:route/entities",
    authenticate, authorize, decodePid(), async (req: Request, res: express.Response, next: express.NextFunction) => {
        const route: string = req.params["route"];

        try {
            const entityPaths = await getDeploymentGroupService().getAssignedEntities(route);
            res.status(200).send(entityPaths);
        } catch (err) {
            return next(err);
        }
    });

router.get("/deployment-groups/:route/games",
    authenticate, authorize, decodePid(), async (req: Request, res: express.Response, next: express.NextFunction) => {
        const route: string = req.params["route"];
        try {
            const gameCodes = await getDeploymentGroupService().getAssignedGames(route);
            res.status(200).send(gameCodes);
        } catch (err) {
            return next(err);
        }
    });

router.get("/deployment-groups/games/:gameCode/versions", authenticate,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
    const gameCode = req.params["gameCode"];
    try {
        const result = await getGameClientVersionService().getGameClientVersionsPerRoute(gameCode);
        res.status(200).send(result);
    } catch (err) {
        return next(err);
    }
});

router.patch("/deployment-groups/games/:gameCode/versions", authenticate, auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const gameCode = req.params["gameCode"];
        try {
            const versions = req.body as VersionsPerRoute;
            const result = await getGameClientVersionService().updateGameClientVersionPerRoute(gameCode, versions);
            res.status(201).send(result);
        } catch (err) {
            return next(err);
        }
    });

router.delete("/deployment-groups/:route/games/:gameCode/versions", authenticate, auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        const gameCode = req.params["gameCode"];
        const route = req.params["route"];
        try {
            const result = await getGameClientVersionService().removeGameClientVersionByRoute(route, gameCode);
            res.status(201).send(result);
        } catch (err) {
            return next(err);
        }
    });

export default router;
