import * as express from "express";
import { BaseEntity } from "../entities/entity";
import { KeyEntityHolder, PermissionsHolder } from "../services/security";
import * as HistoryService from "../history/gameHistory";
import {
    GameHistorySpinDetails,
    getSpinListByBrand,
    roundHistoryQueryKeysForRoundIdQueries
} from "../history/gameHistory";
import { parseFilter, prepareScheme } from "../services/filter";
import { BrandEntity } from "../entities/brand";
import { getRoundHistoryServiceFactory } from "../history/gameHistoryServiceFactory";
import {
    auditable,
    authenticate,
    authorize,
    convertDatesToISOMiddleware,
    decodePid,
    defineDatesRangeLimitsForRoundHistory,
    defineDatesRangeLimitsMiddleware,
    defineLimits,
    FormattedResponse,
    getBooleanParamFromQuery,
    getBrand,
    getEntity,
    sanitizeSearchingILIKE,
    validate
} from "./middleware/middleware";
import {
    ExternalHistoryService,
    ExternalForceFinishService,
} from "@skywind-group/sw-game-provider-ext-game-history";
import { GameHistorySpin, getSpinHistoryByRound } from "../history/spinHistory";
import { UnfinishedRoundManagementServiceFactory } from "../history/unfinishedRoundManagementService";
import { EventHistoryExtraDetails, RoundHistory, UnfinishedRoundHistory } from "../entities/gameHistory";
import { getEntitySettings } from "../services/settings";
import { PagingHelper, PagingInfo } from "../utils/paginghelper";
import { externalGameFinalizeRound, finalizeRound } from "../history/unfinishedRoundFinalizeService";
import {
    EntityCouldNotBeFound,
    GameNotFoundError,
    MerchantDoesntSupportError,
    OperationForbidden,
    RoundNotFoundError,
} from "../errors";
import { GetGameContextsRequest } from "../history/gameHistoryV2";
import { Merchant } from "../entities/merchant";
import * as MerchantCache from "../cache/merchant";
import * as GameService from "../services/game";
import { getGame } from "../services/gameprovider";
import config from "../config";

const MAX_INTEGER = **********;

const router: express.Router = express.Router();

type Request = express.Request & KeyEntityHolder & PermissionsHolder;

export async function getGameHistory(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const filterKeys = roundIdFilterPresents(req) ?
                           roundHistoryQueryKeysForRoundIdQueries : HistoryService.queryParamsKeys;
        const roundHistories: RoundHistory[] = await HistoryService.findGameHistoryEntries(
            getEntity(req),
            parseFilter(req.query, filterKeys)
        );
        res.sendFormatted(req, roundHistories);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getGameHistoryRound(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        const withDetails = getBooleanParamFromQuery(req.query, "withDetails", false);
        const gameHistorySpins: GameHistorySpin[] = await getSpinHistoryByRound(
            brand,
            req.params.roundId,
            req.query,
            undefined,
            withDetails
        );
        res.sendFormatted(req, gameHistorySpins);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getGameRoundEvents(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        Object.assign(req.query, req.params);
        const historySpinDetails: GameHistorySpinDetails[] = await HistoryService.getEvents(
            brand,
            req.query,
            { addExtraDataForGHApp: true, decorateQueryWithRoundTimestamps: true }
        );
        res.sendFormatted(req, historySpinDetails);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getGameRoundSmResult(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        Object.assign(req.query, req.params);
        res.sendFormatted(req, await HistoryService.getSmResult(brand, req.query));
        next();
    } catch (err) {
        next(err);
    }
}

export async function getGameHistoryDetails(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getBrand(req);
        const eventHistoryDetails: EventHistoryExtraDetails = await HistoryService.getGameHistoryDetails(
            entity,
            req.params.roundId,
            req.params.spinNumber,
            {
                addJurisdictionSettings: true
            }
        );
        res.sendFormatted(req, eventHistoryDetails);
        next();
    } catch (err) {
        next(err);
    }
}

export const validateSearch = validate(prepareScheme(
    [
        "limit",
        "offset",
        "sortOrder",
        "bet",
        "win",
        "revenue",
        "ts",
        "firstTs",
        "balance",
        "insertedAt",
        "recoveryType",
        "playerCode",
        "gameCode",
        "device",
        "isTest",
        "currency"
    ]
));

export async function getGameHistoryDetailsImage(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getBrand(req);
        const spinNumber = req.params.spinNumber ? req.params.spinNumber : 0;
        const roundId = +req.params.roundId;
        const { currency, gameCode } = await HistoryService.getGameHistoryDetails(entity, roundId, spinNumber);
        const image = await HistoryService.getGameHistoryDetailsImageUrl(entity, roundId, {
            language: req.query.language,
            timezone: req.query.timezone,
            spinNumber,
            gameCode,
            currency
        });
        res.send(image);
    } catch (err) {
        next(err);
    }
}

const validateLimitAndOffset = validate(prepareScheme(["limit", "offset"]));

async function forceFinishRound(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req, { ignoreSuspended: true });
        const closeInSWWalletOnly = getBooleanParamFromQuery(req.query, "closeInSWWalletOnly");
        const service = await UnfinishedRoundManagementServiceFactory.getUnfinishedRoundManagementService(
            brand,
            closeInSWWalletOnly,
            undefined,
            getBooleanParamFromQuery(req.query, "ignoreMerchantParams")
            && req.isSuperAdmin);
        res.send(await service.forceFinish(req.params.roundId.toString(), req.query.gameContextId,
            getBooleanParamFromQuery(req.query, "force"), false, closeInSWWalletOnly));
        next();
    } catch (err) {
        next(err);
    }
}

async function externalForceFinishRound(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req, { ignoreSuspended: true });
        if (brand.isMerchant) {
            const merchant: Merchant = await MerchantCache.findOne(brand);
            if (!merchant?.params?.supportForceFinishAndRevert && !getBooleanParamFromQuery(req.query,
                "ignoreMerchantParams")) {
                return next(new MerchantDoesntSupportError());
            }
        }
        // If the game is not found, it should throw GameNotFoundError
        const game = await GameService.findOne({ code: req.query.gameCode });

        const reqBody = {
            roundId: +req.params.roundId,
            gameProviderCode: game.gameProvider.code
        } as any;

        const response = await ExternalForceFinishService.forceFinish(reqBody);
        res.send(response);
        next();
    } catch (err) {
        next(err);
    }
}

export async function finalize(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand = getBrand(req);
        const result = await finalizeRound(
            brand,
            req.query.gameContextId,
            getBooleanParamFromQuery(req.query, "waitForCompletion", true),
            getBooleanParamFromQuery(req.query, "closeInSWWalletOnly", false));
        res.send(result);
        next();
    } catch (err) {
        next(err);
    }
}

export async function externalGameFinalize(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand = getBrand(req);
        const result = await externalGameFinalizeRound(brand, req.query.gameCode, req.query.playerCode, req.query.currency,
            getBooleanParamFromQuery(req.query, "waitForCompletion", true));
        res.send(result);
        next();
    } catch (err) {
        next(err);
    }
}

async function revertRound(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        const service = await UnfinishedRoundManagementServiceFactory.getUnfinishedRoundManagementService(
            brand,
            getBooleanParamFromQuery(req.query, "closeInSWWalletOnly"));
        res.send(await service.revert(req.params.roundId.toString(), req.query.gameContextId,
            getBooleanParamFromQuery(req.query, "force")));
        next();
    } catch (err) {
        next(err);
    }
}

async function retryPendingOperationForRound(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        const service = await UnfinishedRoundManagementServiceFactory
            .getUnfinishedRoundManagementService(brand);
        res.send(await service.retryPendingPayment(req.params.roundId.toString(), req.query.gameContextId));
        next();
    } catch (err) {
        next(err);
    }
}

async function transferOutForRound(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        const service = await UnfinishedRoundManagementServiceFactory
            .getUnfinishedRoundManagementService(brand);
        res.send(await service.transferOut(req.params.roundId.toString(), req.query.gameContextId));
        next();
    } catch (err) {
        next(err);
    }
}

export async function getUnfinishedRounds(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const brand = getBrand(req);
        const rounds: UnfinishedRoundHistory[] = await getRoundHistoryServiceFactory()
            .getUnfinishedRoundsHistoryService()
            .getRounds(brand, req.query);

        res.sendFormatted(req, rounds);
    } catch (err) {
        next(err);
    }
}

/**
 * Handler to get history of external gameproviders
 */
export async function getExternalHistory(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        req.query.brandId = entity.id;
        const settings = await getEntitySettings(entity.path);

        const result = await ExternalHistoryService.getWinBetHistory(req.query) || [];

        if (settings.addDecodedRoundIdToHistory) {
            const newResult = result.map(history => ({ ...history, game_id: history.roundId.toString() }));
            return res.sendFormatted(req, PagingHelper.copyInfo(
                newResult,
                result as (typeof result[] & PagingInfo[])
            ));
        }

        return res.sendFormatted(req, result);
    } catch (err) {
        next(err);
    }
}

/**
 * Handler to get round details of external gameproviders
 */
export async function getExternalHistoryDetails(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        if (req.query.extTrxId) {
            req.query.extTrxId = decodeURIComponent(req.query.extTrxId);
        }
        const entity = getEntity(req);
        req.query.brandId = entity.id;
        req.query.roundId = req.params.roundId;

        const roundId = req.query.roundId;

        const extHistoryEntries = await ExternalHistoryService.getWinBetHistory({ brandId: entity.id, roundId });
        if (!extHistoryEntries.length) {
            return next(new RoundNotFoundError());
        }

        const gameCode = extHistoryEntries[0].gameCode;
        const game = await getGame(gameCode);
        if (!game) {
            return next(new GameNotFoundError(gameCode));
        }

        if (req.query.gameProviderCode === "ITG") {
            req.query.licenseeId = game.features?.licenseeId || config.itgLicenseeId;
        }
        const result = await ExternalHistoryService.getWinBetHistoryDetails(req.query);
        res.send(result);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getSpinList(req: Request, res: FormattedResponse, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getBrand(req);
        const result: GameHistorySpinDetails[] = await getSpinListByBrand(entity, req.query);

        res.send(result);
        next();
    } catch (err) {
        next(err);
    }
}

export function roundIdFilterPresents(req): boolean {
    return req.query["roundId"] || req.query["roundId__in"];
}

export const validateRoundId = validate({
    roundId: { notEmpty: true, isInt: { options: { min: Number.MIN_SAFE_INTEGER, max: Number.MAX_SAFE_INTEGER } } },
});

export const validateOptionalRoundId = validate({
    roundId: {
        optional: { options: [{ checkFalsy: true }] },
        isInt: { options: { min: Number.MIN_SAFE_INTEGER, max: Number.MAX_SAFE_INTEGER } }
    },
    roundId__in: {
        optional: true,
        isCsvIntArray: { options: { min: Number.MIN_SAFE_INTEGER, max: Number.MAX_SAFE_INTEGER } }
    }
});

export const requireGameContextId = validate({
    gameContextId: { notEmpty: true }
});

export const validateSpinNumber = validate({
    spinNumber: { notEmpty: true, isInt: { options: { min: 0, max: MAX_INTEGER } } }
});

export const validateOptionalSpinNumber = validate({
    spinNumber: { optional: true, isInt: { options: { min: 0, max: MAX_INTEGER } } }
});

export const validateFields = (...fields) => validate(prepareScheme(fields));

router.get("/entities/:path/history/game",
    authenticate,
    authorize,
    validateSearch,
    defineLimits,
    decodePid({ forceReturnIfNumber: true, notRaiseError: true }),
    validateOptionalRoundId,
    sanitizeSearchingILIKE,
    convertDatesToISOMiddleware(["firstTs", "ts"]),
    defineDatesRangeLimitsForRoundHistory(["firstTs"], "ts", "finished"),
    getGameHistory);

router.get("/entities/:path/history/game/:roundId",
    authenticate,
    authorize,
    validateLimitAndOffset,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    validateOptionalSpinNumber,
    validateFields("sortOrder", "type"),
    getGameHistoryRound);

router.get("/entities/:path/history/game/:roundId/sm-result",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    convertDatesToISOMiddleware(["ts"]),
    getGameRoundSmResult);

router.get("/entities/:path/history/game/:roundId/details/:spinNumber",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    validateSpinNumber,
    getGameHistoryDetails);

router.get("/entities/:path/history/game/:roundId/image",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    getGameHistoryDetailsImage);

router.get("/entities/:path/history/game/:roundId/details/:spinNumber/image",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    validateSpinNumber,
    getGameHistoryDetailsImage);

router.get("/history/game",
    authenticate,
    authorize,
    validateSearch,
    defineLimits,
    decodePid({ forceReturnIfNumber: true, notRaiseError: true }),
    validateOptionalRoundId,
    sanitizeSearchingILIKE,
    convertDatesToISOMiddleware(["firstTs", "ts"]),
    defineDatesRangeLimitsForRoundHistory(["firstTs"], "ts", "finished"),
    getGameHistory);

router.get("/history/game/:roundId",
    authenticate,
    authorize,
    validateLimitAndOffset,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    validateOptionalSpinNumber,
    validateFields("sortOrder", "type"),
    getGameHistoryRound);

router.get("/entities/:path/history/events/:roundId",
    authenticate,
    authorize,
    validateLimitAndOffset,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    validateFields("type", "ts", "gameCode", "playerCode", "insertedAt", "sortOrder"),
    getGameRoundEvents);

router.get("/history/events/:roundId",
    authenticate,
    authorize,
    validateLimitAndOffset,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    validateFields("type", "ts", "gameCode", "playerCode", "insertedAt", "sortOrder"),
    getGameRoundEvents);

router.get("/history/game/:roundId/details/:spinNumber",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    validateSpinNumber,
    getGameHistoryDetails);

router.get("/history/game/:roundId/image",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    getGameHistoryDetailsImage);

router.get("/history/game/:roundId/sm-result",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    convertDatesToISOMiddleware(["ts"]),
    getGameRoundSmResult);

router.get("/history/game/:roundId/details/:spinNumber/image",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    validateSpinNumber,
    getGameHistoryDetailsImage);

router.post("/entities/:path/history/game/:roundId/forcefinish",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["gameContextId"] }),
    validateRoundId,
    auditable,
    forceFinishRound
);

router.post("/history/game/:roundId/forcefinish",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["gameContextId"] }),
    validateRoundId,
    auditable,
    forceFinishRound
);

router.post("/entities/:path/history/external/game/:roundId/forcefinish",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    auditable,
    externalForceFinishRound
);

router.post("/history/external/game/:roundId/forcefinish",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    validateRoundId,
    auditable,
    externalForceFinishRound
);

router.post("/entities/:path/history/game/:roundId/revert",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["gameContextId"] }),
    validateRoundId,
    requireGameContextId,
    revertRound);

router.post("/history/game/:roundId/revert",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["gameContextId"] }),
    validateRoundId,
    requireGameContextId,
    revertRound);

router.post("/entities/:path/history/game/:roundId/retry-pending",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["gameContextId"] }),
    validateRoundId,
    requireGameContextId,
    auditable,
    retryPendingOperationForRound);

router.post("/history/game/:roundId/retry-pending",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["gameContextId"] }),
    validateRoundId,
    requireGameContextId,
    auditable,
    retryPendingOperationForRound);

router.post("/entities/:path/history/game/:roundId/transfer-out",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["gameContextId"] }),
    validateRoundId,
    requireGameContextId,
    auditable,
    transferOutForRound);

router.post("/history/game/:roundId/transfer-out",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["gameContextId"] }),
    validateRoundId,
    requireGameContextId,
    auditable,
    transferOutForRound);

router.post("/entities/:path/history/game/recovery/finalize",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["gameContextId"] }),
    requireGameContextId,
    auditable,
    finalize);

router.post("/history/game/recovery/finalize",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["gameContextId"] }),
    requireGameContextId,
    auditable,
    finalize);

router.post("/history/external/game/recovery/finalize",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    externalGameFinalize);

router.post("/entities/:path/history/external/game/recovery/finalize",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    externalGameFinalize);

router.get(["/history/unfinished/game", "/entities/:path/history/unfinished/game"],
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["gameContextId"] }),
    validateFields("playerCode", "gameCode", "roundId", "ts", "sortOrder"),
    getUnfinishedRounds
);

router.post("/history/unfinished/get-contexts",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    validate({
        playerCode: { isMerchantPlayerCode: true },
        gameCode: { nonSqlString: true },
        brandId: { type: "number" }
    }),
    getGameContexts
);

const extHistoryValidation = {
    playerCode: { optional: true, isPlayerCode: true },
    currency: { optional: true, isCurrency: true },
    bet: { optional: true, isNumeric: { options: { min: 0, max: MAX_INTEGER } } },
    win: { optional: true, isNumeric: { options: { min: 0, max: MAX_INTEGER } } },
    balanceBefore: { optional: true, isNumeric: { options: { min: 0, max: MAX_INTEGER } } },
    balanceAfter: { optional: true, isNumeric: { options: { min: 0, max: MAX_INTEGER } } },
    insertedAt: { optional: true, isTimestampIso8601: true },
    insertedAt__gt: { optional: true, isTimestampIso8601: true },
    insertedAt__lt: { optional: true, isTimestampIso8601: true },
    format: { optional: true, isCSVFormat: true },
    roundId: { optional: true, isInt: { options: { min: Number.MIN_SAFE_INTEGER, max: Number.MAX_SAFE_INTEGER } } },
    roundId__in: { optional: true, isCommaSeparatedIntegers: { options: { isEmptyStringAllowed: false } } }
};

router.get("/history/external",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, notRaiseError: true, ignoredKeys: ["extTrxId"] }),
    validate(extHistoryValidation),
    defineDatesRangeLimitsMiddleware(["insertedAt"]),
    getExternalHistory
);

router.get("/entities/:path/history/external",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, notRaiseError: true, ignoredKeys: ["extTrxId"] }),
    validate(extHistoryValidation),
    defineDatesRangeLimitsMiddleware(["insertedAt"]),
    getExternalHistory
);

const extHistoryDetailsValidation = {
    roundId: { notEmpty: true },
    extTrxId: { notEmpty: true },
    gameProviderCode: { notEmpty: true }
};

router.get("/history/external/:roundId/details",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["extTrxId"] }),
    validate(extHistoryDetailsValidation),
    getExternalHistoryDetails
);

router.get("/entities/:path/history/external/:roundId/details",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true, ignoredKeys: ["extTrxId"] }),
    validate(extHistoryDetailsValidation),
    getExternalHistoryDetails
);

async function getGameContexts(req: Request, res: FormattedResponse, next: express.NextFunction) {
    if (!req.isSuperAdmin) {
        return next(new OperationForbidden("Only SuperAdmins can perform this operation"));
    }
    const data: GetGameContextsRequest = req.body;
    const entity = await getEntity(req);
    const brand = await entity.find({ id: data.brandId });
    if (!brand) {
        return next(new EntityCouldNotBeFound());
    }

    const unfinishedRoundsHistoryService = await getRoundHistoryServiceFactory().getUnfinishedRoundsHistoryService();
    try {
        const contexts: any[] = await unfinishedRoundsHistoryService.getGameContexts(brand, data);
        return res.send(contexts);
    } catch (err) {
        next(err);
    }
}

export default router;
