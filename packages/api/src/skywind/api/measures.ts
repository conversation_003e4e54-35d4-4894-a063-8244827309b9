import express = require("express");
import { measures } from "@skywind-group/sw-utils";

const router: express.Router = express.Router();

router.get("/measures/:name", async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
        const measureName: string = req.params.name;
        if (measureName === "all") {
            res.json(measures.measureProvider.getMeasures());
        } else {
            const measure = measures.getMeasure(measureName);
            if (measure) {
                res.json(measure);
            } else {
                res.sendStatus(404);
            }
        }
    } catch (err) {
        next(err);
    }
});

export default router;
