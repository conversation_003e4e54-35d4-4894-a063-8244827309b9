import * as express from "express";
import { auditable, authenticate, authorize } from "./middleware/middleware";
import { getGameServerSettingsService } from "../services/gameServerSettings";

const router: express.Router = express.Router();

router.get("/game-server/settings", authenticate, authorize, getGameServerSettings);
router.post("/game-server/settings", authenticate, authorize, auditable, createGameServerSettings);
router.get("/game-server/settings/:name", authenticate, authorize, findGameServerSettingsByName);
router.put("/game-server/settings/:name", authenticate, authorize, auditable, updateGameServerSettings);
router.delete("/game-server/settings/:name", authenticate, authorize, auditable, deleteGameServerSettings);

const service = getGameServerSettingsService();

async function getGameServerSettings(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const settings = await service.findAll();
        res.send(settings);
    } catch (err) {
        next(err);
    }
}

async function createGameServerSettings(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const newSettings = await service.create(req.body);
        res.status(201).send(newSettings);
    } catch (err) {
        next(err);
    }
}

async function findGameServerSettingsByName(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const settings = await service.findByName(req.params.name);
        res.send(settings);
    } catch (err) {
        next(err);
    }
}

async function updateGameServerSettings(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const settings = await service.update(req.params.name, req.body);
        res.send(settings);
    } catch (err) {
        next(err);
    }
}

async function deleteGameServerSettings(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        await service.remove(req.params.name);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

export default router;
