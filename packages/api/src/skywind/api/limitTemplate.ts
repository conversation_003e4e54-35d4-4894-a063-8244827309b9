import { VARCHAR_DEFAULT_LENGTH } from "../utils/common";
import { authenticate, authorize, decodePid, isMasterEntity, validate } from "./middleware/middleware";
import * as express from "express";
import { KeyEntityHolder } from "../services/security";
import router from "./schemaDefinition";
import { getLimitTemplateService } from "../services/gameLimits/template";

const validatePostTemplate = {
    name: { notEmpty: true, isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] } },
    template: { notEmpty: true }
};

const validatePatchTemplate = {
    template: { notEmpty: true }
};

const service = getLimitTemplateService();

router.get("/limit-templates",
    authenticate,
    authorize,
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const templates = await service.list();
            res.send(templates.map(s => s.toInfo()));
        } catch (err) {
            next(err);
        }
    });

router.get("/limit-templates/:limitTemplateId",
    authenticate,
    authorize,
    decodePid(),
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const template = await service.retrieve(req.params.limitTemplateId);
            res.send(template.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.post("/limit-templates",
    authenticate,
    authorize,
    isMasterEntity,
    validate(validatePostTemplate),
    decodePid(),
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const template = await service.create(req.body);
            res.status(201).send(template.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.patch("/limit-templates/:limitTemplateId",
    authenticate,
    authorize,
    isMasterEntity,
    validate(validatePatchTemplate),
    decodePid(),
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const template = await service.update(req.params.limitTemplateId, req.body);
            res.send(template.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.delete("/limit-templates/:limitTemplateId",
    authenticate,
    authorize,
    isMasterEntity,
    decodePid(),
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            await service.destroy(req.params.limitTemplateId);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

export default router;
