import { NextFunction, Request, Response, Router } from "express";
import { authenticate, defineLimits, validate } from "./middleware/siteMiddleware";
import {
    auditable,
    countryValidator,
    decodePid,
    getBooleanParamFromRequestQuery,
    languageValidator
} from "./middleware/middleware";
import { PlayerLoginTokenData, SiteTokenData } from "../utils/token";
import * as GameService from "../services/game";
import * as GameProviderService from "../services/gameprovider";
import getPlayerService from "../services/brandPlayer";
import * as PlayerLoginService from "../services/playerLogin";
import { BaseEntity } from "../entities/entity";
import { BrandEntity } from "../entities/brand";
import { EntityGameInfo } from "../entities/game";
import { parseFilter, prepareScheme } from "../services/filter";
import EntityLanguageService from "../services/entityLanguage";
import EntityCountryService from "../services/entityCountry";
import EntityCurrencyService from "../services/entityCurrency";
import { validateGameCode } from "./entityGame";
import { validatePasswordConfirm, validatePlayerCodeForOperator, validateResetPassword } from "./playerAuthForBrands";
import { getEntityJurisdictionService } from "../services/entityJurisdiction";
import { getJurisdictionService } from "../services/jurisdiction";
import { Jurisdiction } from "../entities/jurisdiction";
import EntityCache from "../cache/entity";
import {
    GAME_CATEGORY_TYPE,
    PLAYER_CODE_MAX_LENGTH,
    PLAYER_CODE_MIN_LENGTH,
    X_PLAYER_TOKEN
} from "../utils/common";
import { GameCategoryService } from "../services/gameCategory/gameCategoryService";
import { getPlayerLoginService } from "../services/player/playerLogin";
import { SiteService } from "../services/site";
import { Op } from "sequelize";

const router: Router = Router();

type SiteRequest = Request & SiteTokenData;

const validatePlayerCredentials = validate({
    code: { notEmpty: true, isWord: true },
    password: { notEmpty: true, isPassword: true },
});

const validatePlayerCreateInfo = validate({
    code: {
        notEmpty: true,
        isWord: { errorMessage: "code should be a string" },
        isLength: { options: [{ min: PLAYER_CODE_MIN_LENGTH, max: PLAYER_CODE_MAX_LENGTH }] }
    },
    password: { notEmpty: true, isPassword: true },
    email: { optional: { options: [{ checkFalsy: true }] }, isEmail: true },
    currency: { optional: true, isCurrency: true },
    firstName: { optional: true, isWord: true },
    lastName: { optional: true, isWord: true },
    country: { optional: true, ...countryValidator },
    language: { optional: true, ...languageValidator }
});

export const validateMail = validate({
    email: { notEmpty: true, isEmail: true }
});

router.get("/languages", authenticate, getLanguagesList);
router.get("/currencies", authenticate, getCurrencyList);
router.get("/countries", authenticate, getCountryList);

router.get("/games", authenticate, defineLimits, decodePid(), getAllGames);

router.get("/gameproviders", authenticate, getGameproviders);

router.get("/gamecategories", authenticate,
    validate(prepareScheme(["limit", "offset", "sortOrder"])),
    validate({
        type: { optional: true, isGameCategoryType: true }
    }),
    async (req: SiteRequest, res: Response, next: NextFunction) => {
        try {
            const entity: BaseEntity = await EntityCache.findOne({ id: req.brandId });
            const filter = parseFilter(req.query, GameCategoryService.operatorQueryParamsKeys);
            filter["status"] = "normal";

            const gameCategories = await new GameCategoryService(entity).findAllWithGames(
                filter,
                req.query.type || GAME_CATEGORY_TYPE.GENERAL,
                getBooleanParamFromRequestQuery(req, "includeGamesAmount"),
                getBooleanParamFromRequestQuery(req, "includeGames"));

            res.send(gameCategories);
            next();
        } catch (err) {
            next(err);
        }
    });

router.get("/gamecategories/:gameCategoryId",
    authenticate,
    decodePid(),
    async (req: SiteRequest, res: Response, next: NextFunction) => {
        try {
            const entity: BaseEntity = await EntityCache.findOne({ id: req.brandId });
            res.send(await new GameCategoryService(entity).findOneById(req.params.gameCategoryId));
            next();
        } catch (err) {
            next(err);
        }
    });

router.post("/customer/register", authenticate, validatePlayerCreateInfo, auditable, createPlayer);

router.post("/customer/login", authenticate, validatePlayerCredentials, auditable, loginPlayer);

router.get("/token/status",
    authenticate,
    decodePid(),
    async (req: SiteRequest, res: Response, next: NextFunction) => {
        try {
            res.send();
            next();
        } catch (err) {
            next(err);
        }
    });

router.get("/fun/games/:gameCode", validateGameCode, authenticate, getFunGameURL);

router.get("/customer/code-is-used/:playerCode", validatePlayerCodeForOperator, authenticate, checkPlayerCodeIsTaken);

router.post("/customer/password/reset", authenticate, validateResetPassword, auditable, playerResetPassword);

router.post("/customer/password/confirm", authenticate, validatePasswordConfirm, auditable, playerPasswordConfirm);

router.get("/customer/mail-is-used/:email", validateMail, authenticate, checkMailIsTaken);

router.get("/jurisdictions", authenticate, async (req: SiteRequest, res: Response, next: NextFunction) => {
    try {
        const entity = await EntityCache.findOne({ id: req.brandId });
        let jurisdictions;
        if (entity.isMaster()) {
            jurisdictions = await getJurisdictionService().findAll();
        } else {
            jurisdictions = await getEntityJurisdictionService().findAll({
                entityId: entity.id
            });
        }

        res.send(jurisdictions);
    } catch (err) {
        next(err);
    }
});

router.get("/jurisdictions/:jurisdictionCode",
    authenticate,
    decodePid(),
    async (req: SiteRequest, res: Response, next: NextFunction) => {
        try {
            const entity = await EntityCache.findOne({ id: req.brandId });
            let jurisdiction: Jurisdiction;

            if (entity.isMaster()) {
                jurisdiction = await getJurisdictionService().findOne(req.params.jurisdictionCode);
            } else {
                jurisdiction = await getEntityJurisdictionService()
                    .findOne(entity.id, req.params.jurisdictionCode);
            }
            res.send(jurisdiction);
        } catch (err) {
            next(err);
        }
    });

async function createPlayer(req: SiteRequest, res: Response, next: NextFunction) {
    const entity: BrandEntity = await EntityCache.findOne({ id: req.brandId }) as BrandEntity;
    try {
        const player = await getPlayerService().create(entity, req.body);
        res.status(201).send(await player.toInfo());
    } catch (err) {
        next(err);
    }
}

async function loginPlayer(req: SiteRequest, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await EntityCache.findOne({ id: req.brandId }) as BrandEntity;
        const loginData: PlayerLoginService.LoginData = req.body;
        const playerAuthService = getPlayerLoginService(brand);
        const loginInfo: PlayerLoginService.LoginInfo = await playerAuthService.login(loginData);
        res.header(X_PLAYER_TOKEN, loginInfo.token);
        res.send(loginInfo);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getFilteredGames(entity: BaseEntity, req: Request,
                                       relativeUrl: string): Promise<EntityGameInfo[]> {
    req.query.status = "normal";
    req.query.isTest = false;
    return GameService.getAllGames(entity, null, req.query, relativeUrl);
}

async function getAllGames(req: SiteRequest, res: Response, next: NextFunction) {
    try {
        const gamesList = await getFilteredGames(await EntityCache.findOne({ id: req.brandId }), req, req.baseUrl);
        res.send(gamesList);
        next();
    } catch (err) {
        next(err);
    }
}

async function getLanguagesList(req: SiteRequest, res: Response, next: NextFunction) {
    try {
        const entity: BaseEntity = await EntityCache.findOne({ id: req.brandId }, undefined, true);

        const service = new EntityLanguageService(entity);
        res.send(await service.getList());
        next();
    } catch (err) {
        next(err);
    }
}

async function getCurrencyList(req: SiteRequest, res: Response, next: NextFunction) {
    try {
        const entity: BaseEntity = await EntityCache.findOne({ id: req.brandId }, undefined, true);
        const service = new EntityCurrencyService(entity);

        res.send(service.getList());
        next();
    } catch (err) {
        next(err);
    }
}

async function getCountryList(req: SiteRequest, res: Response, next: NextFunction) {
    try {
        const entity: BaseEntity = await EntityCache.findOne({ id: req.brandId }, undefined, true);
        const service = new EntityCountryService(entity);
        res.send(service.getList());
        next();
    } catch (err) {
        next(err);
    }
}

async function getGameproviders(req: SiteRequest, res: Response, next: NextFunction) {
    try {
        const entity: BaseEntity = await EntityCache.findOne({ id: req.brandId });
        req.query.shortInfo = "true";
        const gamesList: EntityGameInfo[] = await getFilteredGames(entity, req, req.baseUrl);
        const providersCodes = gamesList.map(game => game.providerCode);
        res.send(await GameProviderService.search({
            status: "normal",
            code: { [Op.in]: providersCodes },
        }));
        next();
    } catch (err) {
        next(err);
    }
}

async function getFunGameURL(req: Request & PlayerLoginTokenData, res: Response, next: NextFunction) {
    try {
        res.send(await SiteService.getFunGameURLForSite(req.brandId,
            req.params.gameCode,
            req.query.ip,
            req.query.platform));
        next();
    } catch (err) {
        next(err);
    }
}

async function checkPlayerCodeIsTaken(req: SiteRequest, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await EntityCache.findOne({ id: req.brandId }) as BrandEntity;
        await PlayerLoginService.checkPlayerCodeIsTaken(brand, req.params.playerCode);
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

async function checkMailIsTaken(req: SiteRequest, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await EntityCache.findOne({ id: req.brandId }) as BrandEntity;
        await PlayerLoginService.checkPlayerMailIsTaken(brand, req.params.email);
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

async function playerResetPassword(req: SiteRequest, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await EntityCache.findOne({ id: req.brandId }) as BrandEntity;
        res.status(204).send(await PlayerLoginService.resetPlayerPassword(brand, req.body));
        next();
    } catch (err) {
        next(err);
    }
}

async function playerPasswordConfirm(req: SiteRequest, res: Response, next: NextFunction) {
    try {
        const brand: BrandEntity = await EntityCache.findOne({ id: req.brandId }) as BrandEntity;
        res.status(204).send(await PlayerLoginService.playerPasswordConfirm(brand, req.body));
        next();
    } catch (err) {
        next(err);
    }
}

export default router;
