import * as express from "express";
import { KeyEntityHolder, PermissionsHolder } from "../services/security";
import {
    authenticate,
    authorize,
    encodePublicIdWrapper,
    getEntity,
    pagingWrapper,
    validate
} from "./middleware/middleware";
import { BaseEntity } from "../entities/entity";
import { GameRTPReportEx, gameRTPReportService } from "../services/gameRTPHistory";
import { TIMESTAMP_PATTERN } from "../utils/common";
import { PagingHelper } from "../utils/paginghelper";

const router: express.Router = express.Router();

type Request = express.Request & KeyEntityHolder;

const validateGetReport = {
    ts__lt: { type: "string", pattern: TIMESTAMP_PATTERN },
    ts__gt: { type: "string", pattern: TIMESTAMP_PATTERN },
    rtpDeduction__gte: { optional: true, isDecimal: true, min: 0, max: 100 },
    gameCode__in: { type: "string" }
};

export const keyentityGameRtpDetailedViewPermissions: string[] = [
    "keyentity:gamertp",
    "keyentity:gamertp:detailed-view",
];

export const gameRtpDetailedViewPermissions: string[] = [
    "gamertp",
    "gamertp:detailed-view"
];

/**
 * Gets list of rtp report
 */
router.get("/rtp-report",
    authenticate,
    authorize,
    validate(validateGetReport),
    getRTPReport);

router.get("/entities/:path/rtp-report",
    authenticate,
    authorize,
    validate(validateGetReport),
    getRTPReport);

async function getRTPReport(req: Request & PermissionsHolder, res: express.Response, next: express.NextFunction) {
    try {
        const entity: BaseEntity = getEntity(req);
        const { offset, limit } = req.query;

        const reportData: GameRTPReportEx = await gameRTPReportService.get().findAll(
            entity,
            {
                limit,
                offset,
                gameCodes: req.query["gameCode__in"],
                tsFrom: req.query["ts__gt"],
                tsTo: req.query["ts__lt"],
                rtpDeductionGte: req.query["rtpDeduction__gte"]
            },
            req.permissions,
            req.params.path ? gameRtpDetailedViewPermissions : keyentityGameRtpDetailedViewPermissions
        );

        PagingHelper.fillInfo(
            reportData.records,
            reportData.count,
            { offset, limit });
        res.send(pagingWrapper(encodePublicIdWrapper(reportData.records), req, res));

    } catch (err) {
        next(err);
    }
}

export default router;
