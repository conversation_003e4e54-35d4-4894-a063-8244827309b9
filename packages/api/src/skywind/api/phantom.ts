import { authenticate, authorize, getEntity } from "./middleware/middleware";
import { NextFunction, Request, Response, Router } from "express";
import { KeyEntityHolder } from "../services/security";
import { getPhantomService } from "../phantom/service";
import EntitySettingsService from "../services/settings";

const router: Router = Router();

router.get("/entities/:path/phantom/jackpots", authenticate, authorize, getPhantomJackpots);
router.get("/phantom/jackpots", authenticate, authorize, getPhantomJackpots);

async function getPhantomJackpots(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        const entitySettings = new EntitySettingsService(entity);
        const settings = await entitySettings.get();

        const phantomService = getPhantomService(settings);
        const phantomJackpotIds = await phantomService.getJackpots({
            brandId: entity.id,
            currency: req.query.jpCurrency,
            playerCode: req.query.playerCode,
            gameCode: req.query.gameCode,
            customerId: settings.env || "skywindgroup"
        });
        res.send(phantomJackpotIds);
    } catch (err) {
        return next(err);
    }
}

export default router;
