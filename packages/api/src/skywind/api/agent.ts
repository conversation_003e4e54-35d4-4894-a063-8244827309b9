import { NextFunction, Request as ExpressRequest, Response, Router } from "express";
import {
    authenticate,
    authorize,
    decodePid,
    defineLimits,
    FormattedResponse,
    getEntityPath,
    validate
} from "./middleware/middleware";
import {
    AgentService,
    CreateData,
    UpdateData,
    UpdateStatusesData,
    queryParamsKeys,
    updatableKeys,
} from "../services/agent";
import { BaseEntity } from "../entities/entity";
import { ItemInfo } from "../entities/agent";
import { KeyEntityHolder } from "../services/security";
import { parseFilter, prepareScheme } from "../services/filter";
import * as Errors from "../errors";
import { pick } from "lodash";
import { VARCHAR_DEFAULT_LENGTH } from "../utils/common";

const router: Router = Router();
type Request = ExpressRequest & KeyEntityHolder;

async function getBrandId(req: Request): Promise<number> {
    let entity: BaseEntity;
    if (req.params.path) {
        entity = req.keyEntity.find({ path: getEntityPath(req) });
    } else {
        entity = req.keyEntity;
    }
    if (!entity) {
        throw new Errors.EntityCouldNotBeFound();
    }
    if (!entity.isBrand()) {
        throw new Errors.NotBrand();
    }
    return entity.id;
}

async function addRecordToAgentList(req: Request, res: Response, next: NextFunction) {
    try {
        const data: CreateData = req.body;
        data.brandId = await getBrandId(req);
        res.status(201).send(await AgentService.create(data));
        next();
    } catch (err) {
        next(err);
    }
}

async function getAgentsForBrand(req: Request, res: FormattedResponse, next: NextFunction) {
    try {
        const brandId: number = await getBrandId(req);
        const agents: ItemInfo[] = await AgentService.listForBrand(
            brandId,
            parseFilter(req.query, queryParamsKeys)
        );
        res.sendFormatted(req, agents);
        next();
    } catch (err) {
        next(err);
    }
}

async function changeDomainStatus(req: Request, res: Response, next: NextFunction) {
    const newStatus: string = req.method === "PUT" ? "suspended" : "normal";
    try {
        const brandId: number = await getBrandId(req);
        const changedRecord = await AgentService.changeStatus(brandId, req.params.agentId, newStatus);
        res.send(changedRecord);
        next();
    } catch (err) {
        next(err);
    }
}

async function updateAgent(req: Request, res: Response, next: NextFunction) {
    try {
        const data: UpdateData = pick(req.body, updatableKeys);
        const brandId = await getBrandId(req);
        const updatedItem = await AgentService.update(brandId, req.params.agentId, data);
        res.send(updatedItem);
        next();
    } catch (err) {
        next(err);
    }
}

async function updateStatuses(req: Request, res: Response, next: NextFunction) {
    try {
        const data: UpdateStatusesData = req.body;
        const brandId = await getBrandId(req);
        await AgentService.updateStatuses(brandId, data);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

const validateDomain = validate({
    domain: { notEmpty: true },
    title: {
        optional: true,
        isLength: { options: [{ min: 0, max: VARCHAR_DEFAULT_LENGTH }] },
    }
});

const validateSearch = validate(prepareScheme(["limit", "offset", "sortOrder", "status"]));

router.post("/agents", authenticate, authorize, validateDomain, addRecordToAgentList);
router.patch("/agents/:agentId", authenticate, authorize, decodePid(), updateAgent);
router.get("/agents", authenticate, authorize, validateSearch, defineLimits, getAgentsForBrand);
router.put("/agents/:agentId/suspended", authenticate, authorize, decodePid(), changeDomainStatus);
router.delete("/agents/:agentId/suspended", authenticate, authorize, decodePid(), changeDomainStatus);
router.post("/agents/group/status", authenticate, authorize, decodePid(), updateStatuses);

router.post("/entities/:path/agents", authenticate, authorize, validateDomain, addRecordToAgentList);
router.patch("/entities/:path/agents/:agentId", authenticate, authorize, decodePid(), updateAgent);
router.get("/entities/:path/agents", authenticate, authorize, validateSearch, defineLimits, getAgentsForBrand);
router.put("/entities/:path/agents/:agentId/suspended", authenticate, authorize, decodePid(), changeDomainStatus);
router.delete("/entities/:path/agents/:agentId/suspended", authenticate, authorize, decodePid(), changeDomainStatus);
router.post("/entities/:path/agents/group/status", authenticate, authorize, decodePid(), updateStatuses);

export default router;
