import { NextFunction, Request, Response, Router } from "express";
import { authenticate, authorize, decodePid, getEntity, validate } from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import {
    getFlatReportService,
    queryParamsKeys,
} from "../services/flatReports/flatReportService";
import { parseFilter } from "../services/filter";
import { FlatReportDBInstance } from "../models/flatReports";
import { FLAT_REPORT_TYPE } from "../entities/flatReport";

const router: Router = Router();

type EntityRequest = Request & KeyEntityHolder;

const validateReportType = validate({
    reportType: { isIn: { options: [Object.values(FLAT_REPORT_TYPE)] } }
});

router.get("/entities/:path/flat-reports/:reportType",
    authenticate,
    authorize,
    decodePid(),
    validateReportType,
    getReport);
router.get("/entities/:path/flat-reports", authenticate, authorize, decodePid(), getReports);

router.get("/flat-reports/:reportType", authenticate, authorize, decodePid(), validateReportType, getReport);
router.get("/flat-reports", authenticate, authorize, decodePid(), getReports);

async function getReport(req: EntityRequest, res: Response, next: NextFunction) {
    try {
        const entity = await getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        const reports: FlatReportDBInstance[] = await getFlatReportService(entity).list({
            where: {
                reportType: req.params.reportType,
                entityId: entity.id
            }
        });
        res.send(reports.length ? reports[0].toInfo() : null);
        next();
    } catch (err) {
        next(err);
    }
}

async function getReports(req: EntityRequest, res: Response, next: NextFunction) {
    try {
        const entity = await getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        const reports: FlatReportDBInstance[] = await getFlatReportService(entity).list({
            where: {
                ...parseFilter(req.query, queryParamsKeys),
                entityId: entity.id
            }
        });
        res.send(reports.map(report => report.toInfo()));
        next();
    } catch (err) {
        next(err);
    }
}

export default router;
