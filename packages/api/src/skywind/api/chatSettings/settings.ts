import { FastifyInstance, FastifyRequest } from "fastify";
import { Request, Response } from "../../bootstrap/fastify";
import { StartGameTokenData } from "@skywind-group/sw-wallet-adapter-core";
import { parseStartGameToken } from "../../utils/token";
import { BaseEntity } from "../../entities/entity";
import EntityCache from "../../cache/entity";
import EntitySettingsService from "../../services/settings";
import { getPlayerInfoService } from "../../services/playerInfo";
import { Game } from "../../entities/game";
import * as GameProviderService from "../../services/gameprovider";
import logger from "../../utils/logger";
import { isChatDisabled } from "../../utils/chatHelper";
const log = logger("chat-settings");
export default function (router: FastifyInstance, options, done) {
    router.post("/chat-settings", getSettings);
    done();
}

export async function getSettings(req: FastifyRequest<{ Body: { startGameToken: string | object }}> & Request, res: Response) {
    const body = req.body;
    const chatSettings = {
        noBetNoChat: false,
        hasWarn: false,
        isPublicChatBlock: false,
        isPrivateChatBlock: false,
        isPublicChatDisabled: true,
        isPrivateChatDisabled: true,
    };
    const player = {
        isVip: false,
        isTracked: false,
        nickname: "",
    };
    const tableSettings = {
        isSocial: false,
        useOperator: "",
        tableId: "",
        provider: "",
    };
    try {
        const decoded: StartGameTokenData = await parseStartGameToken(body.startGameToken);

        const playerInfo = await getPlayerInfoService().getPlayerInfo(decoded.playerCode, decoded.brandId);
        const game: Game = await GameProviderService.getGame(decoded.gameCode);
        const entity: BaseEntity = await EntityCache.findById(decoded.brandId);
        const entitySettings = new EntitySettingsService(entity);
        const settings = await entitySettings.get();

        tableSettings.isSocial = "social" in settings ? settings.social : false;
        tableSettings.useOperator = "useOperator" in settings ? settings.useSocialCasinoOperator : "";
        tableSettings.tableId = game?.features?.live?.tableId;
        tableSettings.provider = game?.features?.live?.provider;

        chatSettings.noBetNoChat = playerInfo.noBetNoChat;
        chatSettings.hasWarn = playerInfo.hasWarn;
        chatSettings.isPublicChatBlock = playerInfo.isPublicChatBlock;
        chatSettings.isPrivateChatBlock = playerInfo.isPrivateChatBlock;
        chatSettings.isPublicChatDisabled = isChatDisabled(game?.features?.live, "public", "Chat");
        chatSettings.isPrivateChatDisabled = isChatDisabled(game?.features?.live, "private", "PrivateChat");

        player.isVip = playerInfo.isVip;
        player.isTracked = playerInfo.isTracked;
        player.nickname = playerInfo.nickname;
    } catch (error) {
        log.error(error);
    }
    return res.send({ chatSettings, player, tableSettings });
}
