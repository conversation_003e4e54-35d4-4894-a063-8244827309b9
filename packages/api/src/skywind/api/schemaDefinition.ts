import * as express from "express";
import { authenticate, authorize, decodePid, isMasterEntity, validate } from "./middleware/middleware";
import { VARCHAR_DEFAULT_LENGTH } from "../utils/common";
import { getSchemaDefinitionService } from "../services/gameLimits/schemaDefinition";
import { KeyEntityHolder } from "../services/security";
const router: express.Router = express.Router();

const validatePostSchemaDefinition = {
    name: { notEmpty: true, isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] } },
    "schema.properties": { notEmpty: true },
    "schema.type": { notEmpty: true }
};

const validatePatchSchemaDefinition = {
    "schema.properties": { notEmpty: true },
    "schema.type": { notEmpty: true }
};

router.get("/schema-definitions",
    authenticate,
    authorize,
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const schemas = await getSchemaDefinitionService().list();
            res.send(schemas.map(s => s.toInfo()));
        } catch (err) {
            next(err);
        }
    });

router.get("/schema-definitions/:schemaDefinitionId",
    authenticate,
    authorize,
    decodePid(),
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const schema = await getSchemaDefinitionService().retrieve(req.params.schemaDefinitionId);
            res.send(schema.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.post("/schema-definitions",
    authenticate,
    authorize,
    isMasterEntity,
    validate(validatePostSchemaDefinition),
    decodePid(),
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const schema = await getSchemaDefinitionService().create(req.body);
            res.status(201).send(schema.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.patch("/schema-definitions/:schemaDefinitionId",
    authenticate,
    authorize,
    isMasterEntity,
    validate(validatePatchSchemaDefinition),
    decodePid(),
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const schema = await getSchemaDefinitionService().update(req.params.schemaDefinitionId, req.body);
            res.send(schema.toInfo());
        } catch (err) {
            next(err);
        }
    });

router.delete("/schema-definitions/:schemaDefinitionId",
    authenticate,
    authorize,
    isMasterEntity,
    decodePid(),
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            await getSchemaDefinitionService().destroy(req.params.schemaDefinitionId);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

export default router;
