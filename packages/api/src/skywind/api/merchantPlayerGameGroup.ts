import * as express from "express";
import { authenticate, authorize, getEntity } from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { getMerchantSearchService, MerchantImpl } from "../services/merchant";
import { getMerchantPlayerGameGroupService } from "../services/merchantPlayerGameGroup";
import { MerchantPlayerGameGroupEntitySettingIsInactive, HiddenMerchantNotFound } from "../errors";
import { EntitySettings } from "../entities/settings";
import { getEntitySettings } from "../services/settings";

const router: express.Router = express.Router();

type Request = express.Request & KeyEntityHolder;

async function getMerchant(req: Request): Promise<MerchantImpl> {
    const entity = getEntity(req);
    const settings: EntitySettings = await getEntitySettings(entity.path);

    if (!settings.useMerchantPlayerGameGroup) {
        throw new MerchantPlayerGameGroupEntitySettingIsInactive();
    }

    const merchant = await getMerchantSearchService().findOneByEntityId(entity.id);

    if (!merchant) {
        throw new HiddenMerchantNotFound();
    }

    return merchant;
}

async function getAllPlayersGameGroups(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const merchant = await getMerchant(req);
        const items = await getMerchantPlayerGameGroupService().getAll(merchant.brandId);

        res.status(200).send(items);
    } catch (err) {
        next(err);
    }
}

async function getPlayerGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const merchant = await getMerchant(req);
        const item = await getMerchantPlayerGameGroupService().getSingle(merchant.brandId, req.params.playerCode);

        res.status(200).send(item);
    } catch (err) {
        next(err);
    }
}

async function assignPlayerGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const merchant = await getMerchant(req);
        await getMerchantPlayerGameGroupService().assign(merchant.brandId,
            req.params.playerCode,
            req.params.gameGroup);

        res.status(200).send({});
    } catch (err) {
        next(err);
    }
}

async function unassignPlayerGameGroup(req: Request, res: express.Response, next: express.NextFunction) {
    try {
        const merchant = await getMerchant(req);
        await getMerchantPlayerGameGroupService().unassign(merchant.brandId,
            req.params.playerCode, req.params.gameGroup);

        res.status(200).send({});
    } catch (err) {
        next(err);
    }
}

router.get("/merchant/players/gameGroup", authenticate, authorize, getAllPlayersGameGroups);
router.get("/merchant/player/:playerCode/gameGroup", authenticate, authorize, getPlayerGameGroup);
router.patch("/merchant/player/:playerCode/gameGroup/:gameGroup", authenticate, authorize, assignPlayerGameGroup);
router.delete("/merchant/player/:playerCode/gameGroup/:gameGroup", authenticate, authorize, unassignPlayerGameGroup);

export default router;
