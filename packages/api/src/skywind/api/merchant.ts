import { NextFunction, Request, Response, Router } from "express";
import {
    auditable,
    authenticate,
    authorize,
    countryValidator,
    currencyValidator,
    decodePid,
    getEntity,
    getEntityPath,
    getMerchant,
    languageValidator, nullableCountryValidator,
    sanitize,
    setMerchantAuthContext,
    validate
} from "./middleware/middleware";
import { hasPermissions, KeyEntityHolder, PermissionsHolder } from "../services/security";
import * as MerchantService from "../services/merchant";
import { getMerchantCRUDService, getMerchantSearchService, getMerchantService } from "../services/merchant";
import { BrandEntity } from "../entities/brand";
import { Merchant } from "../entities/merchant";
import { PlayerGameURLInfo } from "../entities/game";
import { BaseEntity, EntityInfo, WithBalances, WithMerchant } from "../entities/entity";
import * as EntityService from "../services/entity";
import { getMerchantEntitiesByPartOfCode } from "../services/entity";
import { CreateMerchantEntityData, default as getEntityFactory, UpdateData } from "../services/entityFactory";
import { validatePlayMode } from "./entityGame";

import EntityCache from "../cache/entity";
import { MerchantGameInitRequest } from "@skywind-group/sw-wallet-adapter-core";
import { getEntitySettings } from "../services/settings";
import { getEntityJurisdictionService } from "../services/entityJurisdiction";

const router: Router = Router();

const paramsValidationSchema = {
    params: { optional: true },
    "params.serverUrl": {
        optional: true,
        isOptionalUrl: {
            errorMessage: "Server url should be a url"
        }
    },
    "params.password": {
        optional: true,
        isPassword: {
            options: { empty: true },
            errorMessage: "Password did not pass complexity check"
        }
    }
};

const validateUpdateMerchantData = validate({
    ...paramsValidationSchema,
    type: { optional: true },
    code: { optional: true }
});

export const validateCreateMerchantEntityData = validate({
    // merchant params
    ...paramsValidationSchema,
    code: { notEmpty: true },
    type: { notEmpty: true },
    // entity params
    name: { notEmpty: true, isWord: true },
    defaultCountry: countryValidator,
    defaultCurrency: currencyValidator,
    defaultLanguage: languageValidator,
});

export const validateUpdateMerchantEntityData = validate({
    defaultCountry: nullableCountryValidator,
    defaultCurrency: {
        ...currencyValidator,
        optional: true
    },
    defaultLanguage: {
        ...languageValidator,
        optional: true
    },
    status: { optional: true, isStatus: true },
    ...paramsValidationSchema
});

// find merchant of key entity
router.get("/merchants",
    authenticate,
    authorize,
    findMerchant
);

// find merchant by entity path
router.get("/merchants/:path",
    authenticate,
    authorize,
    findMerchant
);

export async function findMerchant(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const merchantEntity = getEntity(req);
        const merchant: Merchant = await getMerchantCRUDService().findOne(merchantEntity);
        res.send(merchant.toInfo());
        next();
    } catch (err) {
        next(err);
    }
}

export async function findMerchantByCode(req: Request, res: Response, next: NextFunction) {
    try {
        const merchant: Merchant = await getMerchantSearchService()
            .findOneByTypeAndCode(req.params.type, req.params.code);
        if (req.query.withSettings) {
            const brand: BrandEntity = await EntityCache.findById<BrandEntity>(merchant.brandId);
            const settings = await getEntitySettings(brand.path);
            res.send({
                ...merchant.toInfo(),
                settings
            });
            return;
        }
        if (req.query.withChild) {
            const entity: BaseEntity = await EntityCache.findById<BaseEntity>(merchant.brandId);
            res.send(entity.structureToShortInfo());
            return;
        }
        res.send(merchant.toInfo());
        next();
    } catch (err) {
        next(err);
    }
}

// patch merchant of key entity
router.patch("/merchants",
    authenticate,
    authorize,
    validateUpdateMerchantData,
    decodePid({ allowNull: true }),
    auditable,
    updateMerchant
);

// patch merchant of entity at path
router.patch("/merchants/:path",
    authenticate,
    authorize,
    validateUpdateMerchantData,
    decodePid({ allowNull: true }),
    auditable,
    updateMerchant
);

// todo remove in future versions
router.post("/merchants/game/url",
    validatePlayMode,
    sanitize(req => {
        (req.sanitize("language") as any).toLowerCase();
    }),
    setMerchantAuthContext,
    getMerchantGameUrl
);

router.post("/merchants/lobby/url",
    sanitize(req => {
        (req.sanitize("language") as any).toLowerCase();
    }),
    validate({
        merchantType: { notEmpty: true, isWord: true },
        merchantCode: { notEmpty: true, isWord: true },
        ticket: { notEmpty: true },
        lobbyId: { optional: true, isWord: true, isLength: { options: { min: 1, max: 255 } } },
        language: { optional: true, isLanguage: true }
    }),
    setMerchantAuthContext,
    decodePid({ forceReturnIfNumber: true }),
    getMerchantLobbyUrl
);

export async function getMerchantGameUrl(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const request: MerchantGameInitRequest = req.body;
        const info: PlayerGameURLInfo = await getMerchantService()
            .getGameUrl(request, { ip: req.query.ip || req.body.ip });
        res.send(info);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getMerchantLobbyUrl(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const { merchantCode, merchantType, ticket, lobbyId, language } = req.body;
        const url = await getMerchantService().getLobbyUrl(merchantCode, merchantType, ticket, lobbyId, language);
        res.send({ url });
        next();
    } catch (err) {
        next(err);
    }
}

async function updateMerchant(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const brandEntity = getEntity(req);
        const data: MerchantService.UpdateData = req.body;
        const merchant: Merchant = await getMerchantCRUDService().updateOrCreate(brandEntity as any, data);
        res.send(merchant.toInfo());
        next();
    } catch (err) {
        next(err);
    }
}

router.get("/merchantentities/search",
    authenticate,
    authorize,
    async (req: Request & KeyEntityHolder & PermissionsHolder, res: Response, next: NextFunction) => {
        try {
            const entity = await getEntity(req).structureToInfo({
                decryptId: hasPermissions(req, ["id:decode"])
            });
            const entityWithMerchant = await getMerchantCRUDService().includeTo(entity);
            const entities = await getMerchantEntitiesByPartOfCode(entityWithMerchant, req.query.code);

            res.send(entities);
        } catch (err) {
            next(err);
        }
    });

// create merchant entity under key entity
router.post("/merchantentities",
    authenticate,
    authorize,
    validateCreateMerchantEntityData,
    decodePid(),
    auditable,
    createMerchantEntity
);

// create merchant entity under specific path
router.post("/merchantentities/:path",
    authenticate,
    authorize,
    validateCreateMerchantEntityData,
    decodePid(),
    auditable,
    createMerchantEntity
);

export async function createMerchantEntity(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const parentEntity = getEntity(req);
        const entityData: CreateMerchantEntityData = req.body;
        const factory = getEntityFactory(parentEntity);
        const entityInfo = await factory.createMerchant(entityData).finally(() => EntityCache.reset());
        res.status(201).send(entityInfo);
        next();
    } catch (err) {
        next(err);
    }
}

// get info of key entity
router.get("/merchantentities", authenticate, authorize, getMerchantEntityInfo);

// find merchant entity by path
router.get("/merchantentities/:path", authenticate, authorize, getMerchantEntityInfo);

export async function getMerchantEntityInfo(req: Request & KeyEntityHolder & PermissionsHolder,
                                            res: Response,
                                            next: NextFunction) {
    try {
        const merchantEntity: BrandEntity = getMerchant(req);
        const merchant: Merchant = await getMerchantCRUDService().findOne(merchantEntity);
        const entityInfo = await merchantEntity
            .toInfoWithBalances(hasPermissions(req, ["id:decode"])) as EntityInfo & WithBalances & WithMerchant;

        const [jurisdiction] = await getEntityJurisdictionService().findAll({ entityId: merchantEntity.id });
        if (jurisdiction) {
            entityInfo.jurisdictionCode = jurisdiction.code;
        }

        entityInfo.merchant = merchant.toInfo();

        res.status(200).send(entityInfo);
        next();
    } catch (err) {
        next(err);
    }
}

/**
 * Update merchant entity and merchant params
 *
 */
router.patch("/merchantentities/:path",
    authenticate,
    authorize,
    validateUpdateMerchantEntityData,
    decodePid({ allowNull: true }),
    auditable,
    updateMerchantEntity
);

/**
 * Update merchant entity and merchant params
 *
 */
router.patch("/merchantentities",
    authenticate,
    authorize,
    validateUpdateMerchantEntityData,
    decodePid({ allowNull: true }),
    auditable,
    updateMerchantEntity
);

export async function updateMerchantEntity(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const merchantEntity: BrandEntity = getMerchant(req);
        let info: EntityInfo;
        if (shouldUpdateEntity(req.body)) {
            info = await getEntityFactory(merchantEntity).update(req.body).finally(() => EntityCache.reset());
        } else {
            info = await merchantEntity.toInfo();
        }

        let merchant: Merchant;
        if (shouldUpdateMerchant(req.body)) {
            const data: MerchantService.UpdateData = buildMerchantDataToUpdate(req.body, info);
            merchant = await getMerchantCRUDService().update(merchantEntity, data, { allowToUpdateIsTest: true });
        } else {
            merchant = await getMerchantCRUDService().findOne(merchantEntity);
        }
        info["merchant"] = merchant.toInfo();

        res.send(info);
        next();
    } catch (err) {
        next(err);
    }
}

function buildMerchantDataToUpdate(data, info: EntityInfo): MerchantService.UpdateData {
    const updateData = data.merchant || {};

    if (typeof data.isTest === "boolean") {
        updateData.isTest = info.isTest; // merchant isTest is overrode with entity isTest
    }
    if (data.params) {
        updateData.params = data.params;
    }
    return updateData;
}

function shouldUpdateMerchant(data): boolean {
    return !!(data.merchant || data.params) || data.isTest !== undefined;
}

function shouldUpdateEntity(updateData: UpdateData): boolean {
    return !!(updateData.status || updateData.description || updateData.defaultCurrency || updateData.defaultCountry
        || updateData.defaultLanguage || updateData.languages
        || updateData.title) || updateData.isTest != null;
}

/**
 * Remove merchant entity and it's merchant entry. Finds an entity under the main key, and removes it, if it's empty
 */
router.delete("/merchantentities/:path",
    authenticate,
    authorize,
    auditable,
    deleteEntity);

export async function deleteEntity(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    const keyEntity: BaseEntity = req.keyEntity;
    try {
        getMerchant(req); // just to ensure this is merchant
        const removedItem: EntityInfo = await EntityService.removeEntity(keyEntity, { path: getEntityPath(req) });
        res.send(removedItem);
        next();
    } catch (err) {
        next(err);
    }
}

export default router;
