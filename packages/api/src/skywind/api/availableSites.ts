import { NextFunction, Request, Response, Router } from "express";
import { auditable, authenticate, authorize, decodePid, getEntity, validate } from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import {
    getAvailableSiteService,
    queryParamsKeys,
} from "../services/availableSites";
import { parseFilter, prepareScheme } from "../services/filter";
import { AvailableSiteBulkService } from "../services/bulk/availableSiteBulkService";
import { Op } from "sequelize";
import { AvailableSite } from "../entities/availableSite";

const router: Router = Router();

type EntityRequest = Request & KeyEntityHolder;

const validateSearch = validate(prepareScheme(["sortOrder", "status"]));

const validateCreation = validate({
    url: { notEmpty: true },
    status: { optional: true, isStatus: true },
});

const validateUpdate = validate({
    status: { optional: true, isStatus: true },
});

router.get("/entities/:path/sites/available", authenticate, authorize, decodePid(), validateSearch, getSites);
router.post("/entities/:path/sites/available/", authenticate, authorize, validateCreation, auditable, postSite);
router.patch("/entities/:path/sites/available/:siteId", authenticate, authorize,
    validateUpdate, decodePid(), auditable, updateSite);
router.delete("/entities/:path/sites/available/:siteId", authenticate, authorize, decodePid(), auditable, deleteSite);
router.post("/entities/:path/sites/bulk-operation", authenticate, auditable, bulkOperationHandler);

async function getSites(req: EntityRequest, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        const sites = await getAvailableSiteService(entity).list({
            where: {
                ...parseFilter(req.query, queryParamsKeys),
                entityId: entity.id
            }
        });
        res.send(sites.map(site => site.toInfo()));
        next();
    } catch (err) {
        next(err);
    }
}

async function postSite(req: EntityRequest, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        const site = await getAvailableSiteService(entity).create(req.body);
        res.send(site.toInfo());
        next();
    } catch (err) {
        next(err);
    }
}

async function updateSite(req: EntityRequest, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        const site = await getAvailableSiteService(entity)
            .update(req.params["siteId"], req.body);
        res.send(site.toInfo());
        next();
    } catch (err) {
        next(err);
    }
}

async function deleteSite(req: EntityRequest, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        await getAvailableSiteService(entity).destroy(req.params["siteId"]);
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

async function bulkOperationHandler(req: EntityRequest, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        const availableSiteBulkService = new AvailableSiteBulkService();
        const availableSiteIds = await availableSiteBulkService.process(entity, req.body);
        const getAvailableSitesSelector = {
            id: { [Op.in]: [].concat(...availableSiteIds).filter(siteId => siteId) },
            entityId: entity.id
        };
        const availableSites: AvailableSite[] = (await getAvailableSiteService(entity)
            .list({ where: getAvailableSitesSelector }))
            .map(inst => inst.toInfo());

        res.status(201).send(availableSites);
        next();
    } catch (err) {
        next(err);
    }
}

export default router;
