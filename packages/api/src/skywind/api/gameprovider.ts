import * as express from "express";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    defineLimits,
    getEntity,
    sanitize,
    sanitizeBoolean,
    validate,
    validateLiveGame
} from "./middleware/middleware";
import { KeyEntityHolder, UserInfoHolder } from "../services/security";
import * as GameProviderService from "../services/gameprovider";
import { parseFilter } from "../services/filter";
import { validateLimits } from "../services/limits";
import { Game, GameClientFeatures } from "../entities/game";
import { GAME_TYPES, VARCHAR_DEFAULT_LENGTH } from "../utils/common";
import { ValidationError } from "../errors";
import * as gameService from "../services/game";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder & UserInfoHolder;

const MAX_GAME_URL_LENGTH = 1024;

router.get("/gameproviders",
    authenticate,
    authorize,
    defineLimits,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const providersInfo = await GameProviderService.search(parseFilter(req.query, ["isTest"]));
            res.status(200).send(providersInfo);
            next();
        } catch (err) {
            return next(err);
        }
    });

router.post("/gameproviders",
    authenticate,
    authorize,
    validate({
        code: { notEmpty: true, isWord: true, isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] } },
        title: { notEmpty: true, isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] } },
        secret: { notEmpty: true, isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] } }
    }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const data: GameProviderService.CreateProviderData = req.body;
            const providerInfo = await GameProviderService.create(req.keyEntity, data);
            res.status(201).send(providerInfo);
            next();
        } catch (err) {
            return next(err);
        }
    });

router.put("/gameproviders/:providerId/secret",
    authenticate,
    authorize,
    decodePid(),
    validate({
        secret: { notEmpty: true },
    }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const providerId: number = req.params["providerId"];
            const providerInfo = await GameProviderService.changeSecret(req.keyEntity, providerId, req.body);
            res.send(providerInfo);
            next();
        } catch (err) {
            return next(err);
        }
    });

router.put("/gameproviders/:providerId/suspended",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const providerId: number = req.params["providerId"];
            const providerInfo = await GameProviderService.suspend(req.keyEntity, providerId);
            res.send(providerInfo);
            next();
        } catch (err) {
            return next(err);
        }
    });

router.delete("/gameproviders/:providerId/suspended",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const providerId: number = req.params["providerId"];
            const providerInfo = await GameProviderService.restore(req.keyEntity, providerId);
            res.send(providerInfo);
            next();
        } catch (err) {
            return next(err);
        }
    });

// --------Game section----------

/**
 *  Register new game
 */

const registerGameSanitizer = sanitize((req: Request) => {
    req.sanitize("title").trim();
});
const validateCreateGame = validate({
    gameCode: { notEmpty: true },
    providerGameCode: { notEmpty: true, isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] } },
    type: { optional: true, isIn: { options: [Object.values(GAME_TYPES)] } },
    title: {
        notEmpty: { errorMessage: "title should be not empty" },
        isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] }
    },
    historyUrl: { optional: true, isUrl: true },
    historyRenderType: { optional: true, isHistoryRenderType: true },
    "features.transferEnabled": { optional: true, isBoolean: { errorMessage: "should be a boolean" } },
    "features.isGRCGame": { optional: true, isBoolean: { errorMessage: "should be a boolean" } },
    countries: { optional: true, isCountryCode: true },
    "features.currenciesSupport": { optional: true, isCurrencyCode: true }
});

router.post("/game",
    authenticate,
    authorize,
    validateLiveGame,
    decodePid({ ignoredKeys: ["tableId"] }),
    validateCreateGame,
    registerGameSanitizer,
    auditable,
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const data: GameProviderService.RegisterGameData = req.body;
            validateGameRequiredParams(req);
            await validateLimits(data.type, data.limits);
            const game: Game = await GameProviderService.register(data);

            res.status(201).send(game.toCodeInfo());
            next();
        } catch (err) {
            return next(err);
        }
    });

router.post("/game/:type",
    authenticate,
    authorize,
    validateLiveGame,
    decodePid(),
    validateCreateGame,
    registerGameSanitizer,
    auditable,
    async (req: express.Request & KeyEntityHolder, res: express.Response, next: express.NextFunction) => {
        try {
            const data: GameProviderService.RegisterGameData = req.body;
            data.type = req.params.type;
            validateGameRequiredParams(req, req.params.type === GAME_TYPES.external);
            await validateLimits(data.type, data.limits);
            const game: Game = await GameProviderService.register(data);

            res.status(201).send(game.toCodeInfo());
            next();
        } catch (err) {
            return next(err);
        }
    });

/**
 *  Update game
 */
router.patch("/game/:gameCode",
    authenticate,
    authorize,
    decodePid({ allowNull: true }),
    validate({
        type: { optional: true, isIn: { options: [Object.values(GAME_TYPES)] } },
        title: { optional: true, isLength: { options: [{ min: 1, max: VARCHAR_DEFAULT_LENGTH }] } },
        labels: { optional: true, isArray: true },
        url: { optional: true, isGameUrl: true, isLength: { options: [{ min: 1, max: MAX_GAME_URL_LENGTH }] } },
        historyUrl: { optional: true, isUrl: true },
        historyRenderType: { optional: true, isHistoryRenderType: true },
        "features.transferEnabled": { optional: true, isBoolean: { errorMessage: "should be a boolean" } },
        "features.isGRCGame": { optional: true, isBoolean: { errorMessage: "should be a boolean" } },
        countries: { optional: true, isCountryCode: { options: { allowNull: true} } },
        "features.currenciesSupport": { optional: true, isCurrencyCode: true },
        isMergeLiveSettings: { optional: true, isBoolean: { errorMessage: "should be a boolean" } },
        "features.supportsRtpConfigurator": { optional: true, isBoolean: { errorMessage: "should be a boolean" } },
        "features.ignoreJackpotTypesValidation": { optional: true, isBoolean: { errorMessage: "should be a boolean" } },
        "features.ignoreJackpotIdValidation": { optional: true, isBoolean: { errorMessage: "should be a boolean" } },
        "features.isExternalJackpotSupported": { optional: true, isBoolean: { errorMessage: "should be a boolean" } }
    }),
    registerGameSanitizer,
    auditable,
    async (req: express.Request, res: express.Response, next: express.NextFunction) => {
        try {
            const gameCode: string = req.params.gameCode;
            const data: GameProviderService.UpdateGameData = req.body;
            const game: Game = await GameProviderService.update(gameCode, data);
            res.send(game.toCodeInfo());
            next();
        } catch (err) {
            return next(err);
        }
    });

/**
 *  Get game info
 */
router.get("/game/:gameCode",
    authenticate,
    authorize,
    decodePid(),
    async (req: express.Request,
           res: express.Response,
           next: express.NextFunction) => {
        try {
            const gameCode: string = req.params.gameCode;
            const game: Game = await GameProviderService.getGame(gameCode);
            res.send(game.toProviderInfo());
            next();
        } catch (err) {
            return next(err);
        }
    });

const disableGame = [
    authenticate,
    authorize,
    decodePid(),
    sanitizeBoolean("removeEntityGames"),
    auditable,
    async function ({ params, query }: Request, res: express.Response, next: express.NextFunction) {
        try {
            res.send((await GameProviderService.disableGame(params.gameCode, query.removeEntityGames)).toCodeInfo());
        } catch (err) {
            return next(err);
        }
    }
];
router.delete("/game/:gameCode", disableGame);
router.patch("/game/:gameCode/deactivate", disableGame);

/**
 * Enable game
 */
router.patch("/game/:gameCode/activate",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async ({ params }: express.Request, res: express.Response, next: express.NextFunction) => {
        try {
            res.send((await GameProviderService.enableGame(params.gameCode)).toCodeInfo());
        } catch (err) {
            return next(err);
        }
    });

/**
 * Verify required params for defined game type.
 * Throw ValidationError if required field for specified game type not presented.
 * @throws ValidationError
 */
function validateGameRequiredParams(req: express.Request, isExternalGame?: boolean) {
    const validationObject = {
        url: { isGameUrl: true, isLength: { options: [{ min: 1, max: MAX_GAME_URL_LENGTH }] } },
        "features.supportsRtpConfigurator": { optional: true, isBoolean: { errorMessage: "should be a boolean" } }
    };
    // Check that limits are present only for non-external games
    if (!isExternalGame) {
        req.check({ ...validationObject, limits: { notEmpty: true }, });
    } else {
        req.check(validationObject);
    }
    const errors = req.validationErrors();
    if (errors) {
        throw new ValidationError((errors as any[]).map(e => `${e.param} - ${e.msg.toLowerCase()}`));
    }
}

/**
 * Get game providers that are currently in use by key entity
 */
router.get([
        "/gameproviders/available",
        "/entities/:path/gameproviders/available",
    ],
    authenticate,
    authorize,
    defineLimits,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const providersInfo = await gameService.findAllEntityGames(getEntity(req));

            // get unique game providers
            const providers = providersInfo.reduce((result, entityGame: any) => {
                if (entityGame && entityGame.game && entityGame.game.gameProvider) {
                    if (!result.some(provider => provider.code === entityGame.game.gameProvider.code)) {
                        result.push(entityGame.game.gameProvider.toInfo());
                    }
                }
                return result;
            }, []);

            res.status(200).send(providers);
            next();
        } catch (err) {
            return next(err);
        }
    });

/**
 * Set game client features
 */
router.patch("/game/:gameCode/client-features",
    authenticate,
    authorize,
    auditable,
    async function (req: Request, res: express.Response, next: express.NextFunction) {
        try {
            const gameCode: string = req.params["gameCode"];
            const data: GameProviderService.UpdateGameData = {
                clientFeatures: req.body
            };
            const game: Game = await GameProviderService.update(gameCode, data);
            const clientFeatures: GameClientFeatures = game.toInfo().clientFeatures;
            res.send(clientFeatures);
            next();
        } catch (err) {
            return next(err);
        }
    }
);

/**
 * Get game client features
 */
router.get("/game/:gameCode/client-features",
    authenticate,
    authorize,
    async function (req: Request, res: express.Response, next: express.NextFunction) {
        try {
            const gameCode: string = req.params["gameCode"];
            const game: Game = await GameProviderService.getGame(gameCode);
            const clientFeatures: GameClientFeatures = game.toInfo().clientFeatures;
            res.send(clientFeatures);
            next();
        } catch (err) {
            return next(err);
        }
    }
);

/**
 * Delete game client features
 */
router.delete("/game/:gameCode/client-features",
    authenticate,
    authorize,
    auditable,
    async function (req: Request, res: express.Response, next: express.NextFunction) {
        try {
            const gameCode: string = req.params["gameCode"];
            const data: GameProviderService.UpdateGameData = {
                clientFeatures: {}
            };
            await GameProviderService.update(gameCode, data);
            res.status(204).send();
            next();
        } catch (err) {
            return next(err);
        }
    }
);

export default router;
