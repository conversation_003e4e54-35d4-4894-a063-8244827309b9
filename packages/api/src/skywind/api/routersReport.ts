import { Application } from "express";
import {
    apiSwaggerReport,
    apiSwaggerReportV2,
} from "../utils/swagger";
import health from "./expressRouters/health";
import version from "./expressRouters/version";
import logger from "../utils/logger";
import { encodePublicId, paging, requestLogger } from "./middleware/middleware";
import {
    addHeaderCacheControl,
    addHeaderCORS,
    createRequestLogger,
    resolveIp,
    setUserAuthContext
} from "./middleware/baseMiddleware";
import history from "./history";
import historyV2 from "./v2/history";
import reportJackpot from "./reportJackpot";
import reportJackpotV2 from "./v2/reportJackpot";
import report from "./report";
import validator from "./middleware/validatorMiddleware";
import { defineSwaggerExpress } from "./expressRouters/swagger";
import { createErrorHandler } from "./expressRouters/general";

const log = logger("routers");

export async function defineRoutes(app: Application): Promise<Application> {

    app.use(resolveIp);
    app.use(requestLogger);
    app.use(setUserAuthContext);
    app.use(validator);
    app.use(paging);
    app.use(encodePublicId);

    await defineV1(app);
    await defineV2(app);
    app.use(createErrorHandler(log));
    // respond with status OK when a GET request is made to the root directory
    app.route("/").get((req, res, next) => {
        res.status(200).end();
    });

    return app;
}

async function defineV1(app: Application): Promise<void> {
    await defineSwaggerExpress(app, await apiSwaggerReport());

    app.use("/v1/*", createRequestLogger(log));
    app.use("/v1/health", health);
    app.use("/v1/version", version);
    app.use("/v1/*", addHeaderCORS);
    app.use("/v1", report);
    app.use("/v1", history);
    app.use("/v1", reportJackpot);
}

async function defineV2(app: Application): Promise<void> {
    await defineSwaggerExpress(app, await apiSwaggerReportV2(), "v2");

    app.use("/v2/*", createRequestLogger(log));
    app.use("/v2/*", addHeaderCORS);
    app.use("/v2/*", addHeaderCacheControl);
    app.use("/v2", historyV2);
    app.use("/v2", reportJackpotV2);
}
