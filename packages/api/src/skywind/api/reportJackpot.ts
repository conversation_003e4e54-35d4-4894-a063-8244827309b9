import { NextFunction, Request as ExpressRequest, Router } from "express";
import {
    authenticate,
    authorize,
    convertDatesToISOMiddleware,
    defineDatesRangeLimitsMiddleware,
    FormattedResponse,
    getBrand,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { BrandEntity } from "../entities/brand";
import {
    getBrandContributions,
    getPlayersContributions,
    queryParamsKeysBrandContributions as brandReportKeys,
    queryParamsKeysPlayerContributions as playersReportKeys
} from "../report/jpContributions";
import { parseFilter, prepareScheme } from "../services/filter";
import JpContributionLogService from "../report/jpContributionLogReport";
import JpWinLogService from "../report/jpWinLogReport";

type Request = ExpressRequest & KeyEntityHolder;

const router: Router = Router();

export const validateOffsetAndLimit = validate(prepareScheme(["offset", "limit"]));
export const validateDateHour = validate(prepareScheme(["dateHour"]));
export const validateCurrency = validate(prepareScheme(["currency"]));

router.get("/report/jackpot/contributions/players",
    authenticate,
    authorize,
    validateOffsetAndLimit,
    validateDateHour,
    validateCurrency,
    convertDatesToISOMiddleware(["dateHour"]),
    defineDatesRangeLimitsMiddleware(["dateHour"]),
    playersJackpotContributionReportHandler);

router.get("/entities/:path/report/jackpot/contributions/players",
    authenticate,
    authorize,
    validateOffsetAndLimit,
    validateDateHour,
    validateCurrency,
    convertDatesToISOMiddleware(["dateHour"]),
    defineDatesRangeLimitsMiddleware(["dateHour"]),
    playersJackpotContributionReportHandler);

router.get("/report/jackpot/contributions",
    authenticate,
    authorize,
    validateOffsetAndLimit,
    validateDateHour,
    convertDatesToISOMiddleware(["dateHour"]),
    defineDatesRangeLimitsMiddleware(["dateHour"]),
    brandJackpotContributionReportHandler);

router.get("/entities/:path/report/jackpot/contributions",
    authenticate,
    authorize,
    validateOffsetAndLimit,
    validateDateHour,
    convertDatesToISOMiddleware(["dateHour"]),
    defineDatesRangeLimitsMiddleware(["dateHour"]),
    brandJackpotContributionReportHandler);

export const jpLogsValidator = validate({
    ...prepareScheme(["offset", "limit"]),
    trxDate__gt: {
        notEmpty: { errorMessage: "TrxDate should be not empty"},
        isTimestampIso8601: { errorMessage: "Invalid format of ISO 8601" }
    }
});

router.get("/report/jackpot/contributions/logs",
    authenticate,
    authorize,
    jpLogsValidator,
    convertDatesToISOMiddleware(["trxDate"]),
    defineDatesRangeLimitsMiddleware(["trxDate"]),
    getJPContributionLogsHandler);

router.get("/entities/:path/report/jackpot/contributions/logs",
    authenticate,
    authorize,
    jpLogsValidator,
    convertDatesToISOMiddleware(["trxDate"]),
    defineDatesRangeLimitsMiddleware(["trxDate"]),
    getJPContributionLogsHandler);

router.get("/report/jackpot/contributions/wins",
    authenticate,
    authorize,
    jpLogsValidator,
    convertDatesToISOMiddleware(["trxDate"]),
    defineDatesRangeLimitsMiddleware(["trxDate"]),
    getJPWinLogsHandler);

router.get("/entities/:path/report/jackpot/contributions/wins",
    authenticate,
    authorize,
    jpLogsValidator,
    convertDatesToISOMiddleware(["trxDate"]),
    defineDatesRangeLimitsMiddleware(["trxDate"]),
    getJPWinLogsHandler);

export async function playersJackpotContributionReportHandler(req: Request,
                                                              res: FormattedResponse, next: NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        const jpPlayersContributions = await getPlayersContributions(brand, parseFilter(req.query, playersReportKeys));
        res.sendFormatted(req, jpPlayersContributions);
        next();
    } catch (err) {
        next(err);
    }
}

export async function brandJackpotContributionReportHandler(req: Request, res: FormattedResponse, next: NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        const jpContributions = await getBrandContributions(brand, parseFilter(req.query, brandReportKeys));
        res.sendFormatted(req, jpContributions);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getJPContributionLogsHandler(req: Request, res: FormattedResponse, next: NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        const jpContributions = await JpContributionLogService.getReport(brand, req.query);
        res.sendFormatted(req, jpContributions);
        next();
    } catch (err) {
        next(err);
    }
}

export async function getJPWinLogsHandler(req: Request, res: FormattedResponse, next: NextFunction) {
    try {
        const brand: BrandEntity = getBrand(req);
        const jpContributions = await JpWinLogService.getReport(brand, req.query);
        res.sendFormatted(req, jpContributions);
        next();
    } catch (err) {
        next(err);
    }
}

export default router;
