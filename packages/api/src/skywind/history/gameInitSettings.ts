import { getGameInitSettingsModel } from "../models/gameInitSettings";

interface Key {
    key: string;
}

const cache = new WeakMap<Key, Record<string, any>>();

export async function findGameInitSettings(gameId: string, gameVersion: string) {
    const key = { key: `${gameId}@${gameVersion}` };
    let result = cache.get(key);
    if (!result) {
        const gameInitDBSettings = await getGameInitSettingsModel().findOne({
            where: {
                gameId: gameId,
                version: gameVersion,
            }
        });
        if (gameInitDBSettings) {
            result = gameInitDBSettings.data;
            cache.set(key, result);
        }
    }

    return result;
}
