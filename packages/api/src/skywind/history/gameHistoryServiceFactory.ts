import config from "../config";
import {
    RoundHistoryService,
    RoundHistoryServiceBackOfficeV1,
    RoundHistoryServiceBackOfficeV2,
    RoundHistoryServiceV1,
    RoundHistoryServiceV2
} from "./gameHistoryV2";
import { lazy } from "@skywind-group/sw-utils";
import { UnfinishedRoundsHistoryService } from "./unfinishedRoundsHistoryService";

const factory = lazy(() => new RoundHistoryServiceFactoryImpl());

export function getRoundHistoryServiceFactory(): RoundHistoryServiceFactory {
    return factory.get();
}

const historyServiceV1 = lazy(() => new RoundHistoryServiceV1());
const historyServiceV2 = lazy(() => new RoundHistoryServiceV2());
const historyServiceBOV1 = lazy(() => new RoundHistoryServiceBackOfficeV1());
const historyServiceBOV2 = lazy(() => new RoundHistoryServiceBackOfficeV2());
const unfinishedRoundsService = lazy(() => new UnfinishedRoundsHistoryService());

interface RoundHistoryServiceFactory {
    getHistoryServiceV1(): RoundHistoryService;
    getHistoryServiceV2(): RoundHistoryService;
    getUnfinishedRoundsHistoryService(): UnfinishedRoundsHistoryService;
}

class RoundHistoryServiceFactoryImpl implements RoundHistoryServiceFactory {

    public getHistoryServiceV1(): RoundHistoryService {
        if (config.gameHistory.newRounds) {
            return historyServiceV1.get();
        } else {
            return historyServiceBOV1.get();
        }
    }

    public getHistoryServiceV2(): RoundHistoryService {
        if (config.gameHistory.newRounds) {
            return historyServiceV2.get();
        } else {
            return historyServiceBOV2.get();
        }
    }

    public getUnfinishedRoundsHistoryService(): UnfinishedRoundsHistoryService {
        return unfinishedRoundsService.get();
    }
}
