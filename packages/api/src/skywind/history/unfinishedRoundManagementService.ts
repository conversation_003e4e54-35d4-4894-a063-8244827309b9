import {
    ForceFinishRoundResponse,
    RetryRoundResponse,
    RevertRoundResponse,
    RoundHistory
} from "../entities/gameHistory";
import { generateInternalToken, RoundRecoveryRequestData } from "../utils/token";
import { OperationForbidden, ValidationError, MerchantDoesntSupportError } from "../errors";

import PlayerGameSessionService from "../services/player/playerGameSessionService";
import { BrandEntity } from "../entities/brand";
import { Merchant } from "../entities/merchant";
import * as MerchantCache from "../cache/merchant";
import {
    MERCHANT_POP_MOORGATE_TYPE,
    MERCHANT_POP_MOORGATE_TYPE_EXT_ADAPTER,
    MERCHANT_POP_TYPES
} from "@skywind-group/sw-management-adapters";
import { getGameServerApiProvider } from "../services/deploymentGroup";
import { getEntityGame } from "../services/entityGameService";
import { findOne } from "../services/entity";
import { getMerchantCRUDService } from "../services/merchant";
import logger from "../utils/logger";
import { createFinalizeService } from "./unfinishedRoundFinalizeService";
import { isForceFinishAndRevertInSWWalletOnly } from "@skywind-group/sw-management-playservice";
import { decodeId, encodeId } from "../utils/publicid";

const FORCE_FINISH_ROUND_ENDPOINT = "gamerecovery/forcefinish";
const REVERT_ROUND_ENDPOINT = "gamerecovery/revert";
const RETRY_ROUND_ENDPOINT = "gamerecovery/retry";
const TRANSFER_OUT_ROUND_ENDPOINT = "gamerecovery/transfer-out";

const log = logger("unfinished-round-management-service");

export class UnfinishedRoundManagementServiceFactory {

    public static async getUnfinishedRoundManagementService(entity: BrandEntity,
                                                            closeInSWWalletOnly: boolean = false,
                                                            isInternalAPICall: boolean = false,
                                                            isIgnoringMerchantParams: boolean = false):
        Promise<UnfinishedRoundManagementService> {

        if (!entity.isMerchant) {
            return new UnfinishedRoundManagementServiceImpl(entity, undefined);
        }

        const merchant: Merchant = await MerchantCache.findOne(entity);
        if (!merchant?.params?.supportForceFinishAndRevert && !isIgnoringMerchantParams) {
            return Promise.reject(new MerchantDoesntSupportError());
        }

        closeInSWWalletOnly = isForceFinishAndRevertInSWWalletOnly(merchant) || closeInSWWalletOnly;

        if (closeInSWWalletOnly && this.isPopMerchantThatNeedsFinalizeInsteadOfForceFinish(merchant)) {
            return new CloseRoundForPOPviaFinalizeService(entity, merchant).setIsInternalAPICall(isInternalAPICall);
        }

        return new MerchantUnfinishedRoundManagementServiceImpl(entity, merchant)
            .setIsInternalAPICall(isInternalAPICall);
    }

    private static isPopMerchantThatNeedsFinalizeInsteadOfForceFinish(merchant: Merchant): boolean {
        return merchant.type === MERCHANT_POP_MOORGATE_TYPE || merchant.type === MERCHANT_POP_MOORGATE_TYPE_EXT_ADAPTER;
    }
}

export interface UnfinishedRoundManagementService {
    forceFinish(
        roundId: string | RoundHistory,
        gameContextId?: string,
        force?: boolean,
        reverted?: boolean,
        closeInSWWalletOnly?: boolean): Promise<ForceFinishRoundResponse>;

    revert(roundId: string, gameContextId: string, force?: boolean): Promise<RevertRoundResponse>;

    retryPendingPayment(roundId: string, gameContextId: string): Promise<RetryRoundResponse>;

    transferOut(roundId: string, gameContextId: string): Promise<RetryRoundResponse>;
}

class UnfinishedRoundManagementServiceImpl implements UnfinishedRoundManagementService {

    // TODO Move to constructor
    private readonly gsApiProvider = getGameServerApiProvider();

    constructor(public brand: BrandEntity,
                public merchant: Merchant) {
    }

    public async forceFinish(
        roundId: string | RoundHistory,
        gameContextId?: string,
        force: boolean = false,
        reverted?: boolean,
        closeInSWWalletOnly?: boolean): Promise<ForceFinishRoundResponse> {

        const body = await this.prepareRoundSettlePayload(roundId, gameContextId, force, reverted, closeInSWWalletOnly);
        return this.gsApiProvider.sendPostForForceFinish<ForceFinishRoundResponse>(FORCE_FINISH_ROUND_ENDPOINT,
            body, this.brand, gameContextId);
    }

    public async revert(roundId: string | RoundHistory,
                        gameContextId: string,
                        force: boolean = false): Promise<RevertRoundResponse> {
        await this.checkGameRestriction(gameContextId);

        const body = await this.prepareRoundSettlePayload(roundId, gameContextId, force);
        return this.gsApiProvider.senPostByCtxId<RevertRoundResponse>(REVERT_ROUND_ENDPOINT,
            body, this.brand, gameContextId);
    }

    public async retryPendingPayment(roundId: string | RoundHistory,
                                     gameContextId: string): Promise<RetryRoundResponse> {
        const body = await this.prepareRoundSettlePayload(roundId, gameContextId);

        return this.gsApiProvider.senPostByCtxId<RetryRoundResponse>(RETRY_ROUND_ENDPOINT,
            body, this.brand, gameContextId);
    }

    public async transferOut(roundId: string | RoundHistory,
                             gameContextId: string): Promise<RetryRoundResponse> {
        const body = await this.prepareRoundSettlePayload(roundId, gameContextId);

        return this.gsApiProvider.senPostByCtxId<RetryRoundResponse>(TRANSFER_OUT_ROUND_ENDPOINT,
            body, this.brand, gameContextId);
    }

    protected async prepareRoundSettlePayload(roundId: string | RoundHistory,
                                              gameContextIdString: string,
                                              force: boolean = false,
                                              reverted?: boolean,
                                              closeInSWWalletOnly?: boolean): Promise<{ token: string }> {
        const roundRecoveryRequestData: RoundRecoveryRequestData = {
            round: roundId,
            force,
            reverted,
            closeInSWWalletOnly
        };

        if (gameContextIdString) {
            const decodedGameContextId = GameContextID.decodeBrandIdInGameContextId(gameContextIdString);
            roundRecoveryRequestData.gameContextId = decodedGameContextId;
            const gameContextID: GameContextID = GameContextID.createFromString(decodedGameContextId);
            try {
                await PlayerGameSessionService.checkAndExtend(
                    gameContextID.brandId,
                    gameContextID.playerCode,
                    gameContextID.gameCode
                );
            } catch (err) {
                // If any error at all is thrown, create an empty session
                await PlayerGameSessionService.create(
                    gameContextID.brandId,
                    gameContextID.playerCode,
                    gameContextID.gameCode,
                    {}
                );
            }
        }

        const token = await generateInternalToken(roundRecoveryRequestData);
        return { token };
    }

    private async checkGameRestriction(gameContextId: string) {
        const decodedGameContextId = GameContextID.decodeBrandIdInGameContextId(gameContextId);
        const gameContext: GameContextID = GameContextID.createFromString(decodedGameContextId);

        const brand = await findOne({ id: gameContext.brandId });
        if (!brand) {
            return;
        }

        const merchant = await getMerchantCRUDService().findOne(brand, false);
        if (!merchant) {
            return;
        }

        if (!merchant.params.supportTransfer) {
            return;
        }

        const game = await getEntityGame(brand, gameContext.gameCode);
        if (game.game.features.transferEnabled) {
            throw new OperationForbidden();
        }
    }
}

class MerchantUnfinishedRoundManagementServiceImpl extends UnfinishedRoundManagementServiceImpl {

    private isInternalAPICall: boolean = false;

    private readonly restrictedMerchantTypes = {
        forceFinish: MERCHANT_POP_TYPES,
        revert: MERCHANT_POP_TYPES,
        retry: []
    };

    constructor(public brand: BrandEntity,
                public merchant: Merchant) {

        super(brand, merchant);
    }

    public async revert(roundId: string,
                        gameContextId: string,
                        force: boolean = false,
                        closeInSWWalletOnly?: boolean): Promise<RevertRoundResponse> {
        this.checkPOPRestrictions("revert");

        if (!gameContextId) {
            return Promise.reject(new ValidationError("gameContextId param is required"));
        }

        await super.forceFinish(roundId, gameContextId, force, true, closeInSWWalletOnly);
        return { result: "reverted" };
    }

    private checkPOPRestrictions(operation: string) {
        const restrictedTypes = this.restrictedMerchantTypes[operation];
        if (this.isInternalAPICall || !this.merchant || !restrictedTypes || !restrictedTypes.length) {
            return;
        }

        if (restrictedTypes.includes(this.merchant.type)) {
            throw new OperationForbidden();
        }
    }

    public setIsInternalAPICall(value: boolean): MerchantUnfinishedRoundManagementServiceImpl {
        this.isInternalAPICall = value;
        return this;
    }
}

/**
 * Service for POP Moorgate operator that needs to finalize game contexts instead of forсe finish.
 * Thing is that pop uses our external pop api to trigger round's force-closure, and for POP Moorgate force-finish
 * is not good, so they want a Finalize approach.
 */
class CloseRoundForPOPviaFinalizeService extends MerchantUnfinishedRoundManagementServiceImpl {
    // roundId may be a string or round object depending if this method is called from history api or merchant service
    public async forceFinish(roundId: string | RoundHistory,
                             gameContextId?: string,
                             force: boolean = false,
                             reverted?: boolean,
                             closeInSWWalletOnly?: boolean): Promise<ForceFinishRoundResponse> {
        if (reverted) {
            return super.forceFinish(roundId, gameContextId, force, reverted, closeInSWWalletOnly);
        }
        const finalizeService = createFinalizeService(this.brand, undefined);

        let gameCode = typeof roundId === "string" ? undefined : roundId.gameCode;
        if (!gameCode && gameContextId) { // case when roundId is string
            const gameContextID: GameContextID = GameContextID.createFromString(gameContextId);
            gameCode = gameContextID.gameCode;
        }
        if (gameContextId) {
            gameContextId = GameContextID.decodeBrandIdInGameContextId(gameContextId);
        }
        await finalizeService.forceFinishUsingFinalization({ round: roundId as RoundHistory, gameCode, gameContextId });
        return { result: "force-finished" };
    }
}

export class GameContextID {
    private constructor(public readonly gameCode: string,
                        public readonly brandId: number,
                        public readonly playerCode: string,
                        public readonly deviceId: string,
                        // ${common-prefix}:${context-prefix}:${brandId}:${playerCode}:${gameCode}:${deviceId}
                        private readonly idValue: string
    ) {
    }

    public static createFromString(id: string): GameContextID {
        const ids: string[] = id.split(":");

        return new GameContextID(ids[ids.length - 2],
            parseInt(ids[ids.length - 4], 10),
            ids[ids.length - 3],
            ids[ids.length - 1],
            id);
    }

    public asString(): string {
        return this.idValue;
    }

    /**
     * Traverse gameContextId string in order to replace brandId value with the encoded one
     */
    public static encodeBrandIdInGameContextId(gameContextId: string): string {
        let encodedBrandId = false;
        return gameContextId.split(":").reduce((result, currentKey, currentIndex) => {
            const separator = currentIndex === 0 ? "" : ":";
            // we assume that first numeric value in an array is brandId
            if (!encodedBrandId && !isNaN(+currentKey)) {
                currentKey = encodeId(+currentKey);
                encodedBrandId = true;
            }
            return result + separator + currentKey;
        }, "");
    }

    /**
     * Traverse gameContextId string in order to replace encoded brandId value with the decoded one
     */
    public static decodeBrandIdInGameContextId(gameContextId: string): string {
        return gameContextId.split(":").reduce((result, currentKey, currentIndex) => {
            const separator = currentIndex === 0 ? "" : ":";
            // we assume that third value in an array is brandId
            if (currentIndex === 2 && isNaN(+currentKey)) {
                currentKey = decodeId(currentKey).toString();
            }
            return result + separator + currentKey;
        }, "");
    }
}

export function isSWGameServerError(err) {
    return err.code && err.message;
}
