import config from "../config";
import * as Errors from "../errors";
import request = require("request");
import logger from "../utils/logger";
import { IncomingMessage } from "http";
import { generateHistoryServiceToken } from "../utils/token";
import { Models } from "../models/models";
import { PromotionInfo } from "../entities/promotion";
import { PromotionDBInstance } from "../models/promotion";
import { X_ACCESS_TOKEN } from "../utils/common";
import { PromotionImpl } from "../services/promotions/promotion";
import { Op } from "sequelize";

export const DEFAULT_OFFSET = 0;
export const DEFAULT_LIMIT = 30;

const log = logger("history-promo-server");

const BONUS_COIN_PLAYER_HISTORY_URL = "/history/promo/bonusCoins/byPlayer/";
const BONUS_COIN_PROMO_HISTORY_URL = "/history/promo/bonusCoins/byPromo/";
const PROMO_HISTORY_EXPIRED_URL = "/history/promo/bonusCoins/expired";

const DATE_QUERY_PARAMS = ["lastLogin__gte", "lastLogin__lte", "expired__gte", "expired__lte"];
const HISTORY_SERVER_DATE_QUERY_PARAMS = ["lastLoginFrom", "lastLoginTo", "expiredFrom", "expiredTo"];

export const queryParamsKeys = [
    "offset",
    "limit"
];

export class BonusCoinsPlayerHistory {
    constructor(public readonly playerCode: number,
                public readonly brandId: string,
                public readonly promoId: string,
                public promo: PromotionInfo,
                public readonly dateReceived: string,
                public readonly startDate: string,
                public readonly expirationDate: string,
    ) {
    }
}

export class BonusExpiredStats {
    constructor(public readonly playerCode: number,
                public readonly brandId: string,
                public readonly promoId: string,
                public readonly amountExpired: number,
                public readonly lastLogin: string,
                public readonly expirationDate: string,
    ) {
    }
}

export async function getBonusCoinsPlayerHistory(brandId: number,
                                                 playerCode: string,
                                                 limit?: number,
                                                 offset?: number): Promise<BonusCoinsPlayerHistory[]> {
    const accessToken = await generateHistoryServiceToken({ entityId: brandId });
    const playerBonusCoinList: BonusCoinsPlayerHistory[] = await get(
        BONUS_COIN_PLAYER_HISTORY_URL + playerCode,
        { limit: limit || DEFAULT_LIMIT, offset: offset || DEFAULT_OFFSET },
        accessToken) as BonusCoinsPlayerHistory[];
    const allPromosList: PromotionDBInstance[] = await Models.Promotion.findAll({
        where: {
            id: { [Op.in]: playerBonusCoinList.map((playerPromoInfo) => playerPromoInfo.promoId) },
            brandId,
        }
    });
    const allPromos: PromotionInfo[] = allPromosList.map((promoDbItem) => new PromotionImpl(promoDbItem).toShortInfo());
    for (const playerPromo of playerBonusCoinList) {
        playerPromo.promo = allPromos.find((promo) => promo.id.toString() === playerPromo.promoId.toString());
    }
    return playerBonusCoinList;
}

export async function getBonusCoinsPromoHistory(brandId: number,
                                                promoId: number,
                                                limit?: number,
                                                offset?: number): Promise<BonusCoinsPlayerHistory[]> {
    const accessToken = await generateHistoryServiceToken({ entityId: brandId });
    return await get(
        BONUS_COIN_PROMO_HISTORY_URL + promoId,
        { limit: limit || DEFAULT_LIMIT, offset: offset || DEFAULT_OFFSET },
        accessToken) as BonusCoinsPlayerHistory[];
}

export async function getExpiredPromoPlayersStat(brandId: number, query: any): Promise<BonusExpiredStats[]> {
    const accessToken = await generateHistoryServiceToken({ entityId: brandId });
    const requestParams = {
        limit: query.limit || DEFAULT_LIMIT,
        offset: query.offset || DEFAULT_OFFSET
    };
    mapQueryToHistoryServerFormat(requestParams, query);
    return await get(
        PROMO_HISTORY_EXPIRED_URL,
        requestParams,
        accessToken) as BonusExpiredStats[];
}

export async function getPlayerStatsInPromo(brandId: number,
                                            promoId: number): Promise<BonusExpiredStats[]> {
    const PROMO_PLAYERS_STATS_URL = `/history/promo/${promoId}/stats`;
    const accessToken = await generateHistoryServiceToken({ entityId: brandId });
    return await get(
        PROMO_PLAYERS_STATS_URL,
        {},
        accessToken) as BonusExpiredStats[];
}

async function get<T>(url: string, req: object, token: string): Promise<T> {
    log.info(`Querying history-server with url "${url}" and token "${token}"`);
    return new Promise<T>((resolve, reject) => {
        request.get(url, {
            baseUrl: config.historyService.baseUrl,
            json: true,
            headers: {
                [X_ACCESS_TOKEN]: token,
                "Content-Type": "application/json",
            },
            qs: req,
            qsStringifyOptions: { arrayFormat: "repeat" },
        }, processResponse(resolve, reject));
    });
}

function processResponse(resolve, reject): (error: any, response: IncomingMessage, body: any) => Promise<any> {
    return function (error: Error, response: IncomingMessage, body: any): Promise<any> {
        if (error) {
            log.warn("Querying history-server error ", error);
            return reject(new Errors.HistoryServerConnectionError());
        } else if (response.statusCode !== 200) {
            log.warn("Querying history-server error ", body);
            return reject(new Errors.HistoryServerError(response.statusCode, body.toString()));
        } else {
            return resolve(body);
        }
    };
}

function mapQueryToHistoryServerFormat(requestParams: any, query: any) {
    if (query.playerCode__in) {
        requestParams["players"] = query.playerCode__in.split(",");
    }
    if (query.promosId__in) {
        requestParams["promos"] = query.promosId__in.split(",");
    }
    if (query.amount__gte) {
        requestParams["minAmount"] = query.amount__gte;
    }
    if (query.amount__lte) {
        requestParams["maxAmount"] = query.amount__lte;
    }
    for (let i = 0; i < DATE_QUERY_PARAMS.length; i++) {
        if (query[DATE_QUERY_PARAMS[i]]) {
            requestParams[HISTORY_SERVER_DATE_QUERY_PARAMS[i]] = +new Date(query[DATE_QUERY_PARAMS[i]]) / 1000 | 0;
        }
    }
}
