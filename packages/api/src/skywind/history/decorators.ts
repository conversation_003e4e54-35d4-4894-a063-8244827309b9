import { FindOptions, Op, WhereOptions } from "sequelize";
import { RoundHistory } from "../entities/gameHistory";

export const DEFAULT_OFFSET = 0;
export const DEFAULT_LIMIT = 30;

export function decorateFindOptionsWithLimitOffset(findOptions: FindOptions<any>, reqQuery: any) {
    findOptions["limit"] = reqQuery.limit || DEFAULT_LIMIT;
    findOptions["offset"] = reqQuery.offset || DEFAULT_OFFSET;
}

export function decorateWhereOptionsWithIsPayment(whereOptions: WhereOptions<any>, reqQuery: any) {
    if (reqQuery.isPayment !== undefined) {
        if (reqQuery.isPayment === "true") {
            whereOptions["walletTransactionId"] = { [Op.ne]: null };
        } else {
            whereOptions["walletTransactionId"] = null;
        }
    }
}

export async function decorateQueryWithTs(
    query: { ts: { [key: symbol]: string }, brandId: number, roundId: number},
    params: { ts__gte: string },
    roundLookup: (brandId, options?: WhereOptions<any>) => Promise<RoundHistory[]>) {

    const ts = {
        [Op.gte]: params.ts__gte,
        [Op.lte]: new Date().toISOString()
    };

    if (!params.ts__gte) {
        const roundOptions: WhereOptions<any> = {
            roundId: query.roundId,
            limit: 1
        };

        const rounds: RoundHistory[] = await roundLookup(query.brandId, roundOptions);
        if (!rounds || rounds.length === 0) {
            return query;
        }
        const round = rounds[0];

        ts[Op.gte] = round.firstTs.toISOString();
        if (round.finished) {
            ts[Op.lte] = round.ts.toISOString();
        }
    }

    query.ts = ts;
}
