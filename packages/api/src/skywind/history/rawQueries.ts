import { FindOptions } from "sequelize";
import { sequelizeFindToStoredFunctionInput } from "../services/filter";

const mapping = {
    "type": "game_type",
    "spinNumber": "spin_serial_number",
    "brandId": "brand_id",
    "roundId": "round_id",
    "playerCode": "player_code",
    "ts": "ts",
    "win": "win",
    "bet": "bet",
    "walletTransactionId": "wallet_transaction_id",
    "insertedAt": "inserted_at",
    "gameCode": "game_code",
    "isHidden": "is_hidden",
};

const QUERY: string = "FROM fnc_bo_spins_history(p_where_filters => '{$whereFilters}', " +
    "p_sort_by => '{$sortBy}', p_limit => :limit, p_offset => :offset)";

/**
 * @deprecated use buildSpinHistoryQuery()
 */
export const spinHistoryMapping = mapping;

/**
 * @deprecated use buildSpinHistoryQuery()
 */
export const SPIN_HISTORY_QUERY = QUERY;

export function buildSpinHistoryQuery(findOptions: FindOptions<any>): string {
    const { sortOrder, whereFilters } = sequelizeFindToStoredFunctionInput(findOptions, mapping);
    return QUERY
        .replace("$whereFilters", () => whereFilters)
        .replace("$sortBy", sortOrder);
}
