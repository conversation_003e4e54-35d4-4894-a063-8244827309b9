import logger from "../utils/logger";
import config from "../config";
import { keepalive, lazy } from "@skywind-group/sw-utils";
import * as crypto from "node:crypto";
import * as request from "request";
import { IncomingMessage } from "node:http";
import { PhantomError } from "../errors";

const log = logger("phantom-service");

const agent = lazy(() => keepalive.createAgent(config.phantom.keepAlive, config.phantom.apiUrl));

/**
 * @throws RequestError, StatusCodeError, TransformError
 */
export async function httpGet<T>(url: string, header?: any): Promise<T> {
    const options = {
        uri: url,
        json: true,
        headers: header,
        agent: agent.get(),
        timeout: config.phantom.requestTimeout,
    };

    log.info("MAPI -> Phantom HTTP GET request:", { method: "GET", uri: url });

    const response = await new Promise<T>((resolve, reject) => {
        request.get(options, (err, res: IncomingMessage, body: any) => {
            if (err) {
                return reject(err);
            } else if (res.statusCode === 404) { // suppress error to prevent building stacktrace
                return resolve(undefined);
            } else if (res.statusCode !== 200) {
                return reject(new PhantomError(body, res.statusCode));
            } else {
                resolve(body);
            }
        });
    });

    log.info("Phantom -> MAPI HTTP GET response:", response);

    return response;
}

export function signRequest(data: any, secret: string): string {
    const keys = Object.keys(data).sort();
    const md5 = crypto.createHash("md5");

    for (const key of keys) {
        const value = data[key];
        if (value !== undefined) {
            md5.update(value.toString());
        }
    }

    md5.update(secret);
    return md5.digest("hex");
}
