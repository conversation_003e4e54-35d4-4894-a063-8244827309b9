import { JackpotSettings } from "../entities/game";

export interface GetPhantomJackpotsRequest {
    brandId: number;
    gameCode?: string;
    playerCode?: string;
    currency?: string;
    customerId?: string | "skywindgroup";
    isTest?: boolean;
    deviceData?: any;
}

export interface JackpotInfo extends JackpotSettings {
    jackpotId: string;
}

export interface PhantomJackpotsInfo extends JackpotInfo {
    rtp: number;
    type?: "instant" | "mwjp"; // Considered mwjp if undefined
}

export interface GetPhantomJackpotsResponse {
    jackpots?: PhantomJackpotsInfo[];
}

export interface PhantomPayload extends GetPhantomJackpotsResponse {
    [key: string]: any;
}
