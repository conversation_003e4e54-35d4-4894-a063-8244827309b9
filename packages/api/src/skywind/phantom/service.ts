import { GetPhantomJackpotsRequest, GetPhantomJackpotsResponse, PhantomJackpotsInfo } from "./protocol";
import { stringify } from "node:querystring";
import { retry } from "@skywind-group/sw-utils";
import { PhantomError } from "../errors";
import { httpGet, signRequest } from "./http";
import config from "../config";
import logger from "../utils/logger";
import { EntitySettings } from "../entities/settings";

const log = logger("phantom-service");

export interface PhantomService {
    getJackpots(request: GetPhantomJackpotsRequest): Promise<GetPhantomJackpotsResponse>;
}

class HttpPhantomService implements PhantomService {

    private readonly phantomUrl;

    public constructor(entitySettings: EntitySettings) {
        this.phantomUrl  = entitySettings.phantomJackpotApiUrl || config.phantom.apiUrl;
    }
    /**
     * Returns list of phantom jackpots or undefined if the is no jackpots or Phantom API not available.
     *
     * @param req
     */
    public async getJackpots(req: GetPhantomJackpotsRequest): Promise<GetPhantomJackpotsResponse | undefined>  {

        if (req.deviceData) {
            req.deviceData = JSON.stringify(req.deviceData);
        }
        const data = this.cleanAllUndefined(req);

        const sign = signRequest(data, config.phantom.secret);
        const params = stringify( { ...data, sign: sign } );
        const url = this.phantomUrl + `?${params}`;

        log.debug("Phantom get config url :" + url);

        try {
            const response = await retry(
                config.phantom.retries,
                () => httpGet<GetPhantomJackpotsResponse>(url)
            );
            // Remove jackpot duplicates
            if (response) {
                const uniqueJackpots = new Map<string, PhantomJackpotsInfo>();
                response.jackpots.forEach((jpInfo: PhantomJackpotsInfo) => {
                    uniqueJackpots.set(jpInfo.jackpotId, jpInfo);
                });
                response.jackpots = [...uniqueJackpots.values()];

                return response;
            }
        } catch (err) {
            log.warn(err, "Fail to fetch data from phantom");

            if (config.phantom.failFast) {
                throw new PhantomError(err);
            } else {
                return undefined;
            }
        }
    }

    /**
     * Remove all keys where value is undefined.
     * Works only with flat object
     * @param obj
     */
    private cleanAllUndefined(obj: any): any {
        const keys = Object.keys(obj);
        const result = {};
        for (const key of keys) {
            const value = obj[key];
            if (value !== undefined && value !== null) {
                result[key] = value;
            }
        }
        return result;
    }
}

const phantomService = (eSettings: EntitySettings) => new HttpPhantomService(eSettings);

export function getPhantomService(eSettings: EntitySettings): PhantomService {
    return phantomService(eSettings);
}

/**
 *  Returns true if value is "true" otherwise undefined
 * @param value
 */
export function simplisticBooleanParam(value: boolean): boolean {
    if (value === true) {
        return true;
    }
    return undefined;
}
