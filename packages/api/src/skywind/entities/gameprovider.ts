import { PlayerIn<PERSON> as Player } from "./player";
import { GameFeatures, GameSettings, JackpotSettings } from "./game";
import { Limits } from "./gamegroup";
import { JurisdictionSettings } from "./jurisdiction";
import { AutoPlaySetting, LogoutControl } from "./settings";
import {
    GameLogoutOptions,
    PlayMode,
    SpinType,
    MerchantGameTokenData
} from "@skywind-group/sw-wallet-adapter-core";
import { PhantomPayload } from "../phantom/protocol";
import { Balance } from "@skywind-group/sw-management-playservice";
import { StartGameResponse } from "@skywind-group/sw-management-gameprovider";
import { BrandFinalizationType } from "@skywind-group/sw-wallet-adapter-core";

export interface PlayerGameInfoRequest {
    startGameToken: string | object;
    gameToken?: string;
    gameId?: string;
    playmode?: PlayMode;
    aamsSessionId?: string;
    aamsParticipationCode?: string;
    request?: string;
    gameTokenData?: MerchantGameTokenData;
    providerGameCode?: string;
}

export interface GamesListRequest {
    startGameToken: string;
    filter?: GameFeatures;
}

export interface GamesListResponse {
    games: string[];
}

export enum GameLogoutType {
    /**
     *
     *  logout for all type of games  (finished or not finished)
     */
    ALL = "all",
    /**
     * Logout only for game that expected player action  (to notify about broken games)
     */
    UNFINISHED = "unfinished"
}

export interface BrandInfo {
    name: string;
    title?: string;
}

export interface DetailedStartGameResponse extends StartGameResponse {
    gameToken: string;
    logoutOptions?: GameLogoutOptions;
    balance?: Balance;
    player?: Player;
    settings?: StartGameSettings;
    gameSettings?: GameSettings;
    brandSettings?: BrandSettings;
    limits?: Limits;
    gameMode?: string;
    jrsdSettings?: JurisdictionSettings;
    playedFromCountry?: string;
    operatorPlayerCountry?: string;
    region: string;
    jurisdictionCode?: string;
    currencyReplacement?: string;
    phantom?: PhantomPayload;
    jackpots?: JackpotSettings[];
    localizedGameName?: string;
    brandInfo?: BrandInfo;
    operatorSiteId?: number;
}

export interface StartGameSettings extends GameSettings {
    transferEnabled?: boolean;
    maxPaymentRetryAttempts?: number;
    minPaymentRetryTimeout?: number;
    keepAliveSec?: number;
    isGRCGame?: boolean;
    contributionPrecision?: number; // Value precision: Number of digits after dot.
    splitPayment?: boolean; // Indicates that games transactions bet + win must be split on 2 phases bet and win.
    autoSpinsOptions?: AutoPlaySetting[]; // auto play list (now it is [10, 25, 50, 99, until feature]) according to
                                          // data from server. This data should come in init response.
    jpTickerRefreshPeriod?: number; // Jackpot ticker refresh time period in seconds. Used by game wrapper.
    fastPlay?: boolean; // Triggered the next spin within less than 1 second of all reels stopping.
    turboPlus?: boolean; // There is an additional faster mode. Spin time of Turbo+ mode will be shorter than in Turbo.
    turbo?: boolean; // Is Turbo button enabled or disabled by default. (true - enabled)

    roundExpireAt?: number;
    finalizationRetryPolicy?: {
        factor?: number;
        maxRetries?: number;
        initialRetryTimeout?: number;
    };

    deferredContribution?: boolean; // Should be true if you need contribution amount before bet payment

    // Game RTP (or range). Exposed only if regulation must show RTP.
    baseRTP?: number;
    baseRTPRange?: {
        min: number;
        max: number;
    };
    jpRTP?: number;
    totalRTP?: number;
    mwjpRTP?: number;
    iwjpRTP?: number;

    possibleLimits?: Limits;

    autoCreateTestJackpot?: boolean;    // Auto create test jackpot instances

    totalRTPRange?: {
        min: number;
        max: number;
    };

    featuresRTP?: {
        [name: string]: {
            RTP: number;
            totalRTP: number;
        }
    };
    highestPrizeProbability?: number;
    gamble?: boolean;
    validateRequestsExtensionEnabled?: boolean; // Game supports ValidateRequestsExtension (it was tested)
    zeroBetCheckEnabled?: boolean; // Enable zero bet check for this game
    logoutControl?: LogoutControl;
    hideBalanceBeforeAndAfter?: boolean;
    brandFinalizationType?: BrandFinalizationType;
    skipPendingPaymentReAuthentication?: boolean;
    addBetAmountOnFreeBetRollback?: boolean;
}

export interface BrandSettings {
    [field: string]: any;
}

export interface JackpotValues {
    seed?: number;
    progressive?: number;
}

export interface JackpotPoolDetails {
    contribution: JackpotValues;
    win: number;
    seedWin?: number;
    progressiveWin?: number;
}

export interface JackpotIdDetails {
    [pool: string]: JackpotPoolDetails;
}

export interface JackpotStatistic {
    [jpId: string]: JackpotIdDetails;
}

export interface JackpotWinDetails {
    [jpId: string]: {
        [pool: string]: number;
    };
}

export interface PlayerInfo {
    code: string;
    brandId: number;
    currency: string;
    roundEnded?: boolean;
    deviceId?: string;
    extTransactionId?: string;
    spinType?: SpinType;
    gameCode?: string;
    isTest?: boolean;
    playmode?: string;
}

export interface GameProvider {
    id?: number;
    code: string;
    user: string;
    title: string;
    secret: string;
    status: string;
    isTest: boolean;
    // boolean flag that claims that bet win history of this game provider's game operations must be stored at our DB
    mustStoreExtHistory: boolean;
    createdAt?: Date;
    updatedAt?: Date;

    isSuspended(): boolean;

    save(): Promise<this>;

    toInfo(): GameProviderInfo;
}

export interface GameProviderInfo {
    id: number;
    code: string;
    title: string;
    status: string;
    isTest?: boolean;
    mustStoreExtHistory?: boolean;
}

export interface LoginInfo {
    userId: number;
    entityId: number;
    username: string;
    accessToken: string;
}

export interface PlayerChangeNicknameRequest {
    gameToken: string;
    nickname: string;
    increaseNicknameChangeAttempts?: boolean;
}
