import { Merchant, MerchantInfo } from "./merchant";
import { BaseEntity, EntityDomainInfo } from "./entity";
import { Player, PlayerInfo } from "./player";
import { GameGroupAttributes } from "./gamegroup";
import { DynamicDomain, StaticDomain } from "./domain";
import { AvailableSite } from "./availableSite";
import { Proxy } from "./proxy";

export enum ENTITY_BULK_OPERATION_TYPE {
    STATIC = "static",
    DYNAMIC = "dynamic",
    PROXY = "proxy"
}

export enum PLAYER_BULK_OPERATION_TYPE {
    GROUP = "group"
}

export enum BULK_OPERATION_ACTION {
    SET = "set",
    RESET = "reset"
}

export enum AVAILABLE_SITE_BULK_OPERATION_ACTION {
    ACTIVATE = "activate",
    DEACTIVATE = "deactivate",
    REMOVE = "remove"
}

export interface BulkOperationItem {
    type: string;
    id?: number;
}

export interface BulkOperation {
    action: BULK_OPERATION_ACTION | AVAILABLE_SITE_BULK_OPERATION_ACTION;
    item?: BulkOperationItem;
    entityKey?: string;
}

export interface EntityBulkOperationItem extends BulkOperationItem {
    type: ENTITY_BULK_OPERATION_TYPE;
}

export interface EntityBulkOperation extends BulkOperation {
    item: EntityBulkOperationItem;
}

export interface PlayerBulkOperationItem extends BulkOperationItem {
    type: PLAYER_BULK_OPERATION_TYPE;
}

export interface PlayerBulkOperation extends BulkOperation {
    playerCode: string;
    item: PlayerBulkOperationItem;
}

export interface AvailableSiteOperation extends BulkOperation {
    action: AVAILABLE_SITE_BULK_OPERATION_ACTION;
    sites: string[];
}

export interface EntityExecutorData {
    dynamicDomains: DynamicDomain[];
    staticDomains: StaticDomain[];
    merchants: Merchant[];
    proxies: Proxy[];
}

export interface PlayerExecutorData {
    players: Player[];
    gameGroups: GameGroupAttributes[];
}

export interface AvailableSiteExecutorData {
    sitesToRemove: AvailableSiteOperationDetails;
    sitesToActivate: AvailableSiteOperationDetails;
    sitesToDeactivate: AvailableSiteOperationDetails;
}

export interface AvailableSiteOperationDetails {
    existingSites: AvailableSite[];
    nonExistingSites: string[];
}

export type EntityBulkOperationResult = Merchant | BaseEntity;
export type EntityBulkOperationResultInfo = MerchantInfo | EntityDomainInfo;

export type PlayerBulkOperationResult = PlayerInfo;

export type AvailableSiteOperationResult = number[] | void;
