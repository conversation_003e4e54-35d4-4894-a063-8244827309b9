import { AuditSummary, AuditSummaryInfo } from "./auditSummary";

interface BaseAudit {
    ts: Date;
    entityId: number;
    history: object;
    initiatorType: string;
    initiatorName: string;
    initiatorServiceName: string;
    initiatorIssueId?: string;
    ip: string;
    userAgent: string;
    auditsSummaryId: number;
    auditsSessionId: string;
}

export interface AuditInfo extends BaseAudit {
    auditId: number;
    entity?: string;
    auditsSummary: AuditSummaryInfo;
}

export interface Audit extends BaseAudit {
    auditId?: number;
    auditsSummary?: AuditSummary;

    toInfo?(entity?): AuditInfo;
}

export enum AUDIT_INITIATOR {
    USER = "user",
    PLAYER = "player",
    SYSTEM = "system"
}
