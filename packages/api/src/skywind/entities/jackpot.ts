export interface JackpotInstance {
    id: string;
    type: string;
    jpGameId?: string;
    jackpotConfigurationLevel?: JackpotConfigurationLevel;
    entityId?: number;
    jurisdictionCode?: string;
    info?: JackpotInstanceInfo;

    [key: string]: any;
}

export interface JackpotType {
    name: string;
    jpGameId: string;
    definition: any;
}

export interface JackpotTickerMapping {
    [jackpotId: string]: {
        id: string;
        currency: string;
        pools: JackpotPools;
    };
}

export interface JackpotTicker {
    jackpotId: string;
    jackpotType: string;
    jackpotBaseType: string;
    currency: string;
    pools: JackpotPools;
}

export interface JackpotPools {
    [poolSize: string]: JackpotPool;
}

export interface JackpotPool {
    amount: number;
}

export enum JackpotAuditInitiator {
    USER = "user",
    SYSTEM = "system"
}

export interface AuditInfo {
    initiatorType: JackpotAuditInitiator;
    initiatorName?: string;
    ip?: string;
    userAgent?: string;
    id?: string;
}

export enum JackpotAuditType {
    CREATE = "create",
    CLONE = "clone",
    UPDATE = "update",
    TYPE_UPDATE = "type-update",
    ARCHIVE = "archive",
    ENABLE = "enable",
    DISABLE = "disable",
    GAME_ACTION = "game-action",
    DISABLE_ON_WIN = "disable-on-win",
}

export interface JackpotAudit extends AuditInfo {
    jackpotId: string;
    type: JackpotAuditType;
    history: any;
    ts: Date;
}

export enum JackpotDisableMode {
    IMMEDIATE = 0, NEXT_WIN = 1
}

export enum JackpotConfigurationLevel {
    SPECIFIC_BRAND_ONLY = 1,
    SHARED_BETWEEN_ONE_OPERATOR_FOR_ONE_JURISDICTION = 2,
    SHARED_BETWEEN_SEVERAL_OPERATORS_FOR_ONE_JURISDICTION = 3,
    SHARED_BETWEEN_SEVERAL_JURISDICTIONS_FOR_ONE_OPERATOR = 4,
    SHARED_BETWEEN_SEVERAL_OPERATORS = 5,
    SHARED_GLOBALLY = 6
}

export interface RegisterJackpotInstanceRequest {
    id: string;
    type: string;
    isTest?: boolean;
    isOwned?: boolean;
    isLocal?: boolean;
    isGlobal?: boolean;
    jackpotConfigurationLevel?: JackpotConfigurationLevel;
    entityId?: number;
    jurisdictionCode?: string;
}

export interface JackpotInstanceInfo {
    externalId?: string;
    externalStartDate?: Date;
}
