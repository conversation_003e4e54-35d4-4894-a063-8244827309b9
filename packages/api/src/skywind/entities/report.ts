export interface JpReportParams {
    trxDate__gt: string;
    trxDate__lt?: string;
    limit: number;
    offset: number;
}

export interface JpReportReplacements extends JpReportParams {
    brandId: number;
}

export interface JpReport {
    currency: string;
    currencyRate: number;
    gameId: string;
    gameCode: string;
    roundId: number;
    playerCode: string;
    progressiveAmount: number;
    seedAmount: number;
    trxDate: Date;
    jackpotId: string;
    pool: string;
}

export interface JpContributionLogReport extends JpReport {
    contributionAmount: number;
    insertedAt: Date;
}

export interface JpContributionLogReportV2 extends JpReport {
    insertedAt: Date;
    betAmount: number;
    externalId: string;
    trxId: string;
}

export interface JpWinInfo {
    external?: {
        externalId: string;
        externalStartDate: Date;
    };
}

export interface JpWinReport extends JpReport {
    eventId: number;
    externalId: string;
    initialSeedAmount: string;
    playerCurrency: string;
    totalProgressiveAmount: number;
    totalSeedAmount: number;
    winAmount: number;
    winAmountCurrency: number;
    info: JpWinInfo;
}

export interface JpWinReportV2 extends JpWinReport {
    trxId: string;
}

export interface RoundedValue {
    roundId: string;
}
