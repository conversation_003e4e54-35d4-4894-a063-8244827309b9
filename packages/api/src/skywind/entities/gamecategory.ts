import { EntityGame, EntityGameInfo, EntityGameTerminalInfo } from "./game";
import { BaseEntity } from "./entity";

export interface GameCategory extends GameCategoryInfo {
    id?: number;

    toInfo?(entity: BaseEntity): GameCategory;

    toTerminalInfo?(entity: BaseEntity, entityGames?: EntityGameInfo[]): GameCategoryTerminalInfo;
}

export interface GameCategoryTerminalInfo {
    id?: number;
    title: string;
    description: string;
    brandId: number;
    status: string;
    type: string;
    gamesAmount?: number;
    games?: EntityGameTerminalInfo[];
    ordering?: number;
    isEntityOwner?: boolean;
    icon: string;
    translations?: GameCategoryTranslations;
}

export interface GameCategoryItem {
    type: string;
    id?: number | string;
    items?: GameCategoryItem[];
}

export interface GameCategoryItemFilters {
    gameIds: string[];
    labelIds: number[];
    providerIds?: number[];
}

export interface GameCategoryTranslations {
    [languageCode: string]: GameCategoryTranslation;
}

export interface GameCategoryTranslation {
    icon?: string;
    description?: string;
    title: string;
}

export interface GameCategoryInfo {
    id?: number;
    title: string;
    description: string;
    brandId: number;
    status: string;
    type: string;
    items: GameCategoryItem[];
    gamesAmount?: number;
    games?: EntityGame[];
    ordering?: number;
    isEntityOwner?: boolean;
    icon: string;
    translations?: GameCategoryTranslations;
}

export interface GameCategoryCreateData {
    title: string;
    description?: string;
    status: string;
    type: string;
    items?: GameCategoryItem[];
    icon?: string;
    translations?: GameCategoryTranslations;
}

export interface CategoryGamesParams {
    sortBy?: string;
    sortOrder?: string;
    offset?: number;
    limit?: number;
}
