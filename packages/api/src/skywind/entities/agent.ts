export interface AgentAttributes {
    id: number;
    brandId: number;
    status: string;
    domain: string;
    title?: string;
    affiliateCode?: string;
    description?: string;
    createdAt?: Date;
    updatedAt?: Date;

    entity?: any;
}

export interface FindOptions {
    brandId: number;
    title?: string;
    affiliateCode?: string;
}

export interface ItemInfo {
    id: number;
    status?: string;
    domain: string;
    title?: string;
    affiliateCode?: string;
    description?: string;
    brandId?: string | number;
    brandTitle?: string;
}
