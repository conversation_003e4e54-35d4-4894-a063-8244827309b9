export enum LIMIT_CONFIGURATION_TYPE {
    CALCULATED = "calculated",
    CONFIGURABLE = "configurable",
    FIXED = "fixed",
    CONST = "constForAllCurrencies"
}

export enum GameLimitsPermissionType {
    ENTITY = "entity",
    ADMIN = "admin"
}

export interface SchemaDefinition {
    id?: number;
    name?: string;
    definitions?: Definition;
    schema?: Schema;

    permissions?: SchemaPermissions;
    createdAt?: Date;
    updatedAt?: Date;
    betTypes?: string[];
}

export interface Definition {
    [field: string]: Schema;
}

export interface SchemaPermissions {
    default: {
        [field: string]: GameLimitsPermissionType;
    };

    [field: string]: {
        [field: string]: string;
    };
}

export interface PropertySchema {
    type?: string;
    "$ref"?: string;

    limitConfigurationType: LIMIT_CONFIGURATION_TYPE;

    [field: string]: any;
}

export interface PropertiesSchema {
    [field: string]: PropertySchema;
}

export interface Schema {
    $id?: string;
    type: string;
    required?: string[];
    properties?: PropertiesSchema;
    levels?: boolean;
}
