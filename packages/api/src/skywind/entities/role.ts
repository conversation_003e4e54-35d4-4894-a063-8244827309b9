import { PermissionsList } from "./user";

export interface Role {
    id?: number;
    title: string;
    description?: string;
    permissions: PermissionsList;
    entityId: number;
    isShared: boolean;

    entity?: any;

    toInfo?(): RoleInfo;
    toExtendedInfo?(): ExtendedRoleInfo;
    toDetailedInfo?(): DetailedRoleInfo;
}

export interface UserRole {
    id?: number;
    userId: number;
    roleId: number;
    role?: Role;
}

export interface RoleInfo {
    id: number;
    title: string;
    description?: string;
    owned: boolean;
    ownedBy?: string;
}

export interface ExtendedRoleInfo extends RoleInfo {
    isShared: boolean;
}

export interface DetailedRoleInfo extends ExtendedRoleInfo {
    permissions: PermissionsList;
}
