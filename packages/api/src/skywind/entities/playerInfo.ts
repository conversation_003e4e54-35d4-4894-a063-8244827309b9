import { EntityCountryRestrictions } from "./settings";

export interface PlayerInfo {
    brandId: number | string;
    playerCode: string;
    nickname?: string;
    isVip?: boolean;
    isTracked?: boolean;
    isPublicChatBlock?: boolean;
    isPrivateChatBlock?: boolean;
    isMerchantPlayer: boolean;
    hasWarn?: boolean;
    nicknameChangeAttempts?: number;
    restrictedIpCountries?: EntityCountryRestrictions;
    noBetNoChat?: boolean; // flag which means auto-disable chat using special rules from live games
}
