export interface JPInfoPool {
    poolId: string;
    contributionPercent: number;
    initialSeed: number;
    timeLimit?: string;
    tickerAmount: number;
}

export interface JPInfoTransfer {
    transferAmount: number;
    oldPoolId: string;
    newJackpotId: string;
    newPoolId: number;
    transferDate: string;
}

export enum JackpotType {
    GAME_LEVEL = "Game Level",
    MWJP = "MWJP",
}

export interface JPInfo {
    id: string;
    name: string;
    currency: string;
    type: JackpotType;
    startDate: string;
    status: number;
    endDate?: string;
    poolsCount: number;
    jackpotPools: JPInfoPool[];
    jackpotTransfers?: JPInfoTransfer[];
    gameCodes?: string[];
    externalId?: string;
    externalStartDate?: string;
}
