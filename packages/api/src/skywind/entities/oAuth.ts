import { LoginInfo } from "./user";
import { GrantType } from "@skywind-group/sw-falcon-oauth";
import {
    KeyEntityHolder,
    OAuthAuditInfoHolder,
    PermissionsHolder,
    SessionHolder,
    UserInfoHolder
} from "../services/security";
import config from "../config";

export const OAUTH_TOKEN_COOKIE = `tokenCookie${config.oAuth.ssoCookieEnv}`;

export interface OAuthTokenCookie {
    accessToken: string;
    expiresAt: Date;
}

export interface OAuthTokenInfo extends LoginInfo {
    expiresAt: Date;
    refreshTokenExpiresAt: Date;
}

export type OAuthTokenResult =
    KeyEntityHolder
    & PermissionsHolder
    & UserInfoHolder
    & SessionHolder
    & OAuthAuditInfoHolder;

export interface OAuthData {
    authorizationCode: string;
    redirectUri?: string;
    grantType?: GrantType;
    language?: string;
    referer?: string;
    ip?: string;
}
