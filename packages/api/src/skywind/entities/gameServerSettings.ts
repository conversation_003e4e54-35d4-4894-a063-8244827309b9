export interface GameServerSettings {
    name?: string;
    description?: string;
    roundIdRange?: [string, string];
    sessionIdRange?: [string, string];
    createdAt?: Date;
    updatedAt?: Date;
}

export interface CreateDataBody {
    name: string;
    description?: string;
    roundIdRange: [string, string];
    sessionIdRange: [string, string];
}

export interface RangeTypeInstance {
    value: string;
    inclusive: boolean;
}

export type UpdateData = Partial<CreateDataBody>;

export interface GameServerSettingsModelData {
    name?: string;
    description?: string;
    roundIdRange?: [RangeTypeInstance, RangeTypeInstance];
    sessionIdRange?: [RangeTypeInstance, RangeTypeInstance];
}
