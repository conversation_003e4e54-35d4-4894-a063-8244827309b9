import { Transaction } from "sequelize";

export interface BiReportDomains {
    id?: number;
    trustServerUrl: string;
    baseUrl: string;
    isSelected?: boolean;
    version?: number;
    createdAt?: Date;
    updatedAt?: Date;

    toInfo?(): BiReportDomainsInfo;
    save?(transaction?: Transaction): Promise<this>;
}

export interface BiReportDomainsInfo {
    pid: string;
    trustServerUrl: string;
    baseUrl: string;
    isSelected?: boolean;
    version?: number;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface CreateBiReportDomains {
    trustServerUrl: string;
    baseUrl: string;
}

export interface UpdateBiReportDomains {
    trustServerUrl?: string;
    baseUrl?: string;
}
