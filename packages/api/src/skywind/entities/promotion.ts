import { Label } from "./label";
import { PromotionToPlayer } from "../models/promotionPlayer";
import { PROMO_TYPE } from "@skywind-group/sw-management-promo-wallet";

export const PROMO_STATUS = {
    ACTIVE: "active",
    INACTIVE: "inactive"
};

export const PROMO_STATE = {
    PENDING: "pending",
    IN_PROGRESS: "in_progress",
    FINISHED: "finished",
    EXPIRED: "expired"
};

export type PROMO_CONDITION_VALUE_FIELD = "deposit_amount" | "deposit_count";
export type PROMO_CONDITION_VALUE_OPERATOR = "<" | "<=" | "=" | ">" | ">=";

export interface Condition {
    and?: Condition[];
    or?: Condition[];
    value?: number;
    operator?: PROMO_CONDITION_VALUE_OPERATOR;
    valueField?: PROMO_CONDITION_VALUE_FIELD;
}

export const PROMO_REWARD_INTERVAL_TYPE = {
    MINUTELY: "minutely",
    HOURLY: "hourly",
    DAILY: "daily",
    WEEKLY: "weekly",
    MONTHLY: "monthly"
};
export const PROMO_REWARD_INTERVAL_TYPES: string[] = Object.values(PROMO_REWARD_INTERVAL_TYPE);

export enum PROMO_OPERATIONS {
    CREATE = "create",
    EDIT = "edit"
}

export const MAP_PROMO_PERMISSIONS = new Map([
    [PROMO_TYPE.FREEBET, "promotion:freebet"],
    [PROMO_TYPE.BONUS_COIN, "promotion:bonuscoin"]
]);
export const PROMO_OWNER = {
    SKYWIND: "skywind",
    OPERATOR: "operator"
};

export interface PromotionAttributes {
    id: number;
    title: string;
    type: string;
    active: boolean;
    archived: boolean;
    everStarted: boolean;
    startRewardOnGameOpen: boolean;
    brandId: number;
    startDate: Date;
    endDate: Date;
    timezone: string;
    createdUserId: number;
    updatedUserId?: number;
    description: string;
    conditions?: Condition;

    intervalType?: string;
    daysOfWeek?: string[];
    daysOfMonth?: string[];
    timeOfDay?: string;

    owner: string;
    customerIds?: string[];
    version: number;
    externalId?: string;

    createdAt?: Date;
    updatedAt?: Date;
}

export interface PromotionInfo {
    id?: number;
    title: string;
    type: string;
    conditions?: Condition;
    archived?: boolean;
    status?: string;
    state?: string;
    startRewardOnGameOpen: boolean;
    everStarted?: boolean;
    brandId: number;
    totalParticipated?: number;
    totalPayout?: number;
    startDate?: string;
    endDate: string;
    timezone?: string;
    createdUserId?: number;
    createdUsername?: string;
    updatedUserId?: number;
    updatedUsername?: string;
    description: string;
    createdAt?: Date;
    updatedAt?: Date;
    rewards?: CommonRewardData[];
    games?: string[];
    labels?: Label[];

    intervalType?: string;
    daysOfWeek?: string[];
    daysOfMonth?: string[];
    timeOfDay?: string;

    customerIds?: string[];
    owner?: string;
    brandPath?: string;
    externalId?: string;
    players?: PromotionToPlayer[];

    providerSpecificOptions?: ProviderSpecificOptions;
    overridePreviousPlayerPromo?: boolean;
}

export interface ProviderSpecificOptions {
    [field: string]: any;
}

export interface CommonRewardData {
    id?: number;
    promoId?: number;
}

export interface FreebetSimplePromoInfo {
    title: string;
    startDate: string;
    endDate: string;
    timezone?: string;
    reward: FreebetRewardInfo;
}

export interface ExpiringReward {
    expirationPeriod?: number;
    expirationPeriodType?: string;
    expirationDate?: string;
}

export interface FreebetRewardInfo extends CommonRewardData, ExpiringReward {
    freebetAmount: number;
    games: FreebetGameConfig[];
}

export interface FreebetGameConfig {
    gameCode: string;
    coins: FreebetGameCoinConfig[];
}

export interface FreebetGameCoinConfig {
    [field: string]: { coin: number };
}

export interface QualifyingGameConfig {
    [field: string]: { coeff: number };
}

export interface PromoProjection {
    projected: {
        reach: number;
        participants: number;
        payout: number;
    };
    actual: {
        reach: number;
        participants: number;
        payout: number;
    };
}

export interface BonusCoinRewardInfo extends CommonRewardData, ExpiringReward {
    amount: number;
    redeemMinAmount: number;
    redeemMaxAmount?: number;
    games: string[];
    exchangeRates?: BonusCoinExchangeRates;
}

export interface BonusCoinExchangeRates {
    [currency: string]: number;
}
