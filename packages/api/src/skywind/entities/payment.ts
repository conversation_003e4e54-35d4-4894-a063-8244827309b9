import { PaymentDeclineReason } from "../models/payment";

export type OrderStatus = "approved" | "declined" | "init" | "blocked";

/**
 * Players deposit/withdraw response data interface
 */
export interface OrderInfo {
    // internal transaction Id
    trxId: string;
    extTrxId?: string;
    brandId: number;
    brandTitle?: string;
    playerCode: string;
    orderId: string;
    orderType: string;
    orderDate: string;
    orderInfo: any;
    orderStatus: string;
    currencyCode: string;
    amount: number;
    playerBalanceAfter?: number;
    startDate: string;
    endDate: string;
    paymentMethodCode: string;
    isTest?: boolean;
    isNew?: boolean;
    declineReason?: PaymentDeclineReason;
}

export interface TransactionInfo {
    walletTrx: string;
    extTrxId: string;
    playerCode: string;
    type: string;
    amount: number;
    currency: string;
    status: string;
}

export interface TransferData {
    playerCode: string;
    currency: string;
    amount: number;
    extTrxId: string;
    isTest?: boolean;
}

export interface DirectTransferData extends TransferData {
    brandPath: string;
}

export type TrxStatus = "absent" | "processing" | "committed";

export interface GetTransactionInfoResponse {
    status: TrxStatus;
    paymentStatus?: string;
    playerBalanceAfter?: number;
    declineReason?: string;
}

export const TRANSFER_IN = "transfer_in";
export const TRANSFER_OUT = "transfer_out";

export const STATUS_SUSPENDED = "suspended";
export const STATUS_NORMAL = "normal";

export const PM_TYPE_DEPOSIT = "deposit";
export const PM_TYPE_WITHDRAW = "withdrawal";

// OrderStatus
export const APPROVED: OrderStatus = "approved";
export const INIT: OrderStatus = "init";
export const DECLINED: OrderStatus = "declined";

export interface PublicKeyValue {
    publicKey: string;
}
