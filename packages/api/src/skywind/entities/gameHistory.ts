import { BrandSettings, JackpotStatistic, StartGameSettings } from "./gameprovider";
import {
    BrandFinalizationType,
    GameFinalizationType,
    MerchantGameTokenData,
    SmResultExtraData
} from "@skywind-group/sw-wallet-adapter-core";
import { JurisdictionSettings } from "./jurisdiction";
import { CurrencyFormatConfig } from "./settings";
import { GameSettings } from "./game";

export interface JackpotHistory {
    totalJpContribution?: number;
    totalJpWin?: number;
}

export interface RoundHistory extends JackpotHistory {
    roundId: string;
    gameId?: string;
    brandId: number;
    playerCode: string;
    device?: string;
    gameCode: string;
    currency: string;
    currencyFormatConfig?: CurrencyFormatConfig;
    isTest: boolean;
    bet: number;
    win: number;
    revenue?: number;
    totalEvents?: number;
    balanceBefore?: number;
    balanceAfter?: number;
    broken?: boolean;
    firstTs?: Date;
    ts?: Date;
    finished?: boolean;
    internalRoundId?: string;
    credit?: number;
    debit?: number;
    ctrl?: number;
    extraData?: any;
    operator_site_id?: number;
    game_id?: string;
    replayUrl?: string;
    sessionId?: number;
    insertedAt?: Date;
    recoveryType?: string;
}

/**
 * Interface for game events.
 */
export interface EventHistory extends JackpotHistory {
    gameId?: string;
    brandId?: number;
    device?: string;
    playerCode?: string;
    gameCode?: string;
    currency: string;
    currencyFormatConfig?: CurrencyFormatConfig;
    roundId?: string;
    sessionId?: string;
    spinNumber: number;
    type?: string;
    gameVersion?: string;
    endOfRound: boolean;
    walletTransactionId?: string;
    win: number;
    bet: number;
    balanceBefore: number;
    balanceAfter: number;
    ts: string;
    result?: string;
    test: boolean;
    isPayment?: boolean;
    credit?: number;
    debit?: number;
    extraData?: any;
    isHidden?: boolean;
    spinHistoryId?: number;
    insertedAt?: string;
    freeBetCoin?: number;
}

export interface HistoryInfo {
    url: string;
    historyRenderType: number;
}

export interface EventHistoryDetails {
    roundId: number;
    spinNumber: number;
    gameId: string;
    gameCode: string;
    gameVersion: string;
    details: string;
    initSettings?: any;
    ts?: string;
    historyInfo?: HistoryInfo;
    jrsdSettings?: JurisdictionSettings;
}

export interface EventHistoryExtendDetails extends EventHistoryDetails, JackpotHistory {
    credit?: EventHistory["credit"];
    debit?: EventHistory["debit"];
    extraData?: EventHistory["extraData"];
    currency: EventHistory["currency"];
    currencyFormatConfig?: EventHistory["currencyFormatConfig"];
}

export interface EventHistoryExtraDetails extends EventHistoryExtendDetails {
    type?: EventHistory["type"];
    balanceBefore?: EventHistory["balanceBefore"];
    balanceAfter?: EventHistory["balanceAfter"];
    win?: EventHistory["win"];
    bet?: EventHistory["bet"];
    device?: EventHistory["device"];
}

export interface HistoryInfo {
    url: string;
    historyRenderType: number;
}

export interface GHVisualizationOptions {
    spinNumber?: number;
    language?: string;
    timezone?: string;
    ttl?: number;
    firstTs?: string;
    ts?: string;
    gameCode?: string;
    showRoundInfo?: boolean;
    currency?: string;
}

export interface GameHistoryVisualizationDetailsTokenData {
    rId: number;
    sId?: number;
    eId: number;
    timezone?: string;
    firstTs?: string; // required to get spin details not only for last month
    ts?: string;
}

export interface GameVersionDetails {
    gameId: string;
    gameVersion: string;
    initSettings: Record<string, any>;
    jrsdSettings?: JurisdictionSettings;
    historyInfo?: { url: string };
}

export interface GameReplayDetails {
    gameId: string;
    gameVersion: string;
    initSettings: Record<string, any>;
    jrsdSettings?: JurisdictionSettings;
    settings?: StartGameSettings;
    gameSettings?: GameSettings;
    brandSettings?: BrandSettings;
    jurisdictionCode?: string;
}

export interface GameHistoryVisualisation {
    imageUrl: string;
    ttl?: number;
}

export const HISTORY_URL_SEARCH_STRING = "index.html";
export const HISTORY_URL_REPLACE_STRING = "history.html";

export type ForceFinishResult = "force-finished";
export type RevertResult = "reverted";
export type RetryResult = "finished";
export type FinalizeResult = "finalized" | "finalization-requested";

export interface ForceFinishRoundResponse {
    result: ForceFinishResult;
}

export interface RevertRoundResponse {
    result: RevertResult;
}

export interface RetryRoundResponse {
    result: RetryResult;
}

export interface FinalizeResponse {
    result: FinalizeResult;
    roundStatistics?: RoundStatistic;
}

export interface RoundStatistic {
    totalBet: number;
    totalWin: number;
    totalEvents: number;
    balanceBefore: number;
    balanceAfter: number;
    broken?: boolean;
    startedAt?: Date;
    finishedAt?: Date;
    totalJpContribution?: number;
    totalJpWin?: number;
    credit?: number;
    debit?: number;
    jpStatistic?: JackpotStatistic;
    smResult?: string;
    smResultExtraData?: SmResultExtraData;
}

export interface StartFinalizeResponse<T extends MerchantGameTokenData = MerchantGameTokenData> {
    gameContextId: string;
    currency: string;
    roundId?: string;
    roundPID?: string;
    gameTokenData?: T;
    roundStatistics?: RoundStatistic;
}

export type UnfinishedRoundStatus = "broken" | "unfinished" | "requireLogout"
    | "brokenIntegration" | "finalizing" | "requireTransferOut";

export interface UnfinishedRoundHistory extends RoundHistory {
    status: UnfinishedRoundStatus;
    pendingSpin?: EventHistory;
    gameContextId: string;
}

export enum FinalizeBrokenPaymentType {
    RETRY = "retry",
    DISABLE = "disable",
    MARK_FINALIZED = "markFinalized"
}

export interface FinalizeGameContextRequest {
    brandId?: number;
    gameContextId?: string;
    finalizeBrokenPayment?: FinalizeBrokenPaymentType;
    roundId?: string;
    round?: RoundHistory;
    gameCode: string;
    gameFinalizationType?: GameFinalizationType;
    operatorSupportsFinalization?: boolean;
    brandFinalizationType?: BrandFinalizationType;
    lockContext?: boolean;
    isManualApiCall?: boolean;
    playerCode?: string;
    closeInSWWalletOnly?: boolean;
    currency?: string;
}

export interface ReactivateGameRequest {
    gameContextId: string;
    brandId: number;
}
