export enum STATUS_TYPE {
    SUCCESS = "success",
    FAILURE = "failure",
    IN_PROGRESS = "in progress"
}
export enum TEST_STATUS {
    PASS = "pass",
    FAIL = "fail"
}

export interface Test {
    title: string;
    status: TEST_STATUS;
    results: Array<TestResult>;
}

export interface RequestBody {
    baseUrl: string;
    url: string;
    data: object;
}

export interface TestLog {
    time: string;
    request: RequestBody;
    responseCode: number;
    responseBody: string | object;
    requestTime: number;
}

export interface TestResult {
    logs: Array<TestLog>;
    err?: string;
    stack?: string;
    title: string;
    status: TEST_STATUS;
    duration: number;
    currentRetry: number;
}

export interface Report {
    end: {
        end: string
        start: string
        tests: number
        passes: number
        suites: number
        pending: number
        duration: number
        failures: number
    };
    start: {
        total: number,
    };
    tests: Array<Test>;
}

export interface TestData {
    code: string;
    type: string;
    pass?: string;
    url?: string;
    gameCode?: string;
    ticket?: string;
}

export interface TestRunInfo {
    id: number;
}

export interface TestReport {
    id?: number;
    merchCode?: string;
    merchType?: string;
    gameCode?: string;
    report?: Report;
    status: STATUS_TYPE;
    updatedAt?: string;
    createdAt?: string;
}

export enum TestReportFormat {
    HTML = "html",
    JSON = "json"
}

export interface TestHistoryReport {
    id: number;
    createdAt: string;
}
