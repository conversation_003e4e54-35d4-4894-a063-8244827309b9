export interface PaymentMethod {
    id: number;
    brandId: number;
    type: string[];
    code: string;
    name: string;
    description: string;
    status: string;

    isSuspended(): boolean;
    toInfo(): PaymentMethodInfo;
}

export interface PaymentMethodInfo {
    type: string[];
    code: string;
    name: string;
    description: string;
    status: string;
}

export interface PaymentMethodCreateData {
    brandId: number;
    type: string[];
    code: string;
    name: string;
    description?: string;
    status?: string;
}

export interface PaymentMethodUpdateData {
    type: string[];
    name: string;
    description?: string;
    status?: string;
}

export interface FindOptions {
    brandId?: number;
    code?: string;
    name?: string;
    type: string;
    status?: string;
}

export interface InitializeDepositData {
    paymentMethodCode: string;
    customerId: string;
    amount?: number;
    currency: string;
    isTest?: boolean;
}

export interface InitializeWithdrawalData {
    paymentMethodCode: string;
    customerId: string;
    amount: number;
    currency: string;
    isTest?: boolean;
}

export interface InitializePaymentInfo {
    paymentMethodCode: string;
    viewType: string;
    url: string;
    isTest?: boolean;
}
