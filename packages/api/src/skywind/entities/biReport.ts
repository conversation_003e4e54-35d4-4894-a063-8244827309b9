export interface BiReport {
    id?: number;
    reportId?: string;
    name?: string;
    caption?: string;
    description?: string;
    workbook?: string;
    reportGroup?: string;
    status?: string;
    permission?: string;
    createdAt?: Date;
    settings?: any;
    ordering?: number;
}

export interface BiSession {
    token?: string;
    entityId?: number;
    name?: string;
    workbook?: string;
    expiredAt?: Date;
    createdAt?: Date;
}

export interface BiUrlCreateData {
    reportId?: string;
    tournamentId?: string;
}

export interface BiReportUrl {
    url?: string;
    baseUrl?: string;
    name?: string;
    workbook?: string;
    trustTicket?: string;
    token?: string;
    expiredAt?: Date;
    settings?: any;
}

export interface BiPermission {
    postfix?: string;
    description?: string;
    permission?: string;
}

export interface BiReportSettings {
    [key: string]: any;
    height?: number;
    language?: string;
    reportId?: string;
    trustServerUrl?: string;
    baseUrl?: string;
    tokenExpiresIn?: number;
}
