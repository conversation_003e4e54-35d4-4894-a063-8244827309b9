import { LABEL_GROUPS_RELATIONS_TYPES, LABEL_GROUPS_TYPES } from "../utils/common";

export interface LabelCreateData {
    title: string;
    groupId: number;
}

export interface Label extends LabelCreateData {
    id?: number;
    group?: LabelGroup;
    labelgroup?: LabelGroup;
    labelEntities?: any;
}

export interface LabelGroup extends LabelGroupCreateData {
    id?: number;
}

export interface LabelGroupCreateData {
    group: string;
    type: LABEL_GROUPS_TYPES;
    relationType: LABEL_GROUPS_RELATIONS_TYPES;
}

export interface GameLabel {
    gameId: number;
    labelId: number;
}
