import { LobbyGameInfo } from "./game";
import { EntitySettings } from "./settings";

export interface LiveManagerUrl {
    url: string;
    path: string;
    socketPath: string;
}

export interface LobbyExternalWidget {
    options?: Record<string, any>;
}

export interface LobbyInternalExternalWidget<T> extends LobbyExternalWidget {
    games?: T[];
}

interface MenuItemTranslation {
    title: string;
    icon?: string;
}

interface MenuItemTranslations {
    [languageCode: string]: MenuItemTranslation;
}

export interface LobbyMenuSubItem extends MenuItemTranslation {
    translations?: MenuItemTranslations;
    widget?: LobbyExternalWidget;
    options?: {
        widgets?: Record<string, LobbyExternalWidget>;
    };
    gameCategoryId?: string;
    showFavoriteGames?: boolean;
    showRecentGames?: boolean;
}

export interface LobbyInternalMenuSubItem<T> extends LobbyMenuSubItem {
    games?: T[];
    widget?: LobbyInternalExternalWidget<T>;
    options?: {
        widgets?: Record<string, LobbyInternalExternalWidget<T>>;
    };
}

export interface LobbyMenuItem extends LobbyMenuSubItem {
    subcategories?: LobbyMenuSubItem[];
}

export type LobbyExtendedMenuSubItem = LobbyInternalMenuSubItem<LobbyGameInfo>;

export interface LobbyExtendedMenuItem extends LobbyExtendedMenuSubItem {
    subcategories?: LobbyExtendedMenuSubItem[];
}

export interface LobbyMeta extends Record<string, any> {
    options?: { key: string, value: any }[];
    updatedAt?: string;
    menuItems?: LobbyMenuItem[];
}

export interface LobbyExtendedMeta extends LobbyMeta {
    social?: EntitySettings["social"];
    useSocialCasinoOperator?: EntitySettings["useSocialCasinoOperator"];
    liveManagerUrl?: LiveManagerUrl;
    menuItems?: LobbyExtendedMenuItem[];
}

export interface LobbyInfo {
    title?: string;
    description?: string;
    status?: string;
    info?: LobbyMeta;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface Lobby extends LobbyInfo {
    id?: number;
    brandId?: number;
    path?: string;
    isDefault?: boolean;
    theme?: {
        key?: string;
        options?: {
            key: string;
            value: unknown;
            css?: string;
            meta?: string;
            token?: string;
        }[]
    }
}

export interface ExtendedLobby extends LobbyInfo {
    id?: number;
    info?: LobbyExtendedMeta;
}

export interface LobbyCreateData {
    title: string;
    description?: string;
    status?: string;
    theme: Lobby["theme"];
    info?: Lobby["info"];
    isDefault?: boolean;
}

export type LobbyUpdateData = Partial<LobbyCreateData>;

/**
 * @deprecated
 */
export interface DownloadableLobby extends Lobby {
    path?: string;
    terminalToken?: string;
}
