import { AnyMerchantAdapter } from "@skywind-group/sw-management-adapters";

import { Entity<PERSON>ame, EntityGameInfo, Game } from "./game";
import { Segment } from "../models/segment";
import { GameLimitsByCurrencyCode } from "../models/gameLimitsConfiguration";
import { SchemaConfiguration } from "../models/schemaConfiguration";
import {
    AdditionalInfo,
    GameLogoutOptions,
    GameWithHashList,
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantGameTokenInfo,
    MerchantGameURLInfo,
    MerchantStartGameTokenData,
    ModuleWithHashList,
    WALLET_TYPE
} from "@skywind-group/sw-wallet-adapter-core";
import { SchemaDefinition } from "./schemaDefinition";
import { Proxy } from "./proxy";

export interface MerchantParams {
    supportTransfer?: boolean;
    supportPlayMoney?: boolean;
    keepAliveSec?: number;
    supportForceFinishAndRevert?: boolean;
    // Some operators can support both promo stored on sw/egp side and promo stored on their operator. Also, some EGP
    // enforce operators to store promo on their side, so some of our clients can choose to store skywind games free
    // bet balance on their side and EGP games free bet balance on EGP side. The purpose of this flag is to allow
    // operators to create promo on EGP side using our API even if isPromoInternal: false if
    // supportBothInternalExternalPromo: true
    supportBothInternalExternalPromo?: boolean;
    forceFinishAndRevertInSWWalletOnly?: boolean;
    isPromoInternal?: boolean;
    proxyUrl?: string;
    serverUrl?: string;
    password?: string;
    sameUrlForTerminalLoginAndTicket?: boolean;
    gameLogoutOptions?: GameLogoutOptions;
    walletType?: WALLET_TYPE;

    [field: string]: any;

    gsId?: string;
    gpId?: string;

    retryPolicy?: {
        delay?: number;
        maxAttempts?: number;
        cancelMaxAttempts?: number;
    };
    certSettings?: MerchantCertSettings;
    regulatorySettings?: RegulatorySettings; // regulation related settings
    // instruction for game client to replace currency code that is displayed to player
    gameClientCurrencyReplacement?: {
        [field: string]: string;
    };
    // param for POP integration to adjust game start process
    // so that verifyPlayer occurs on game start and not on get game url
    verifyPlayerOnGameStart?: boolean;
    // indicates whether merchant arcade games support wallet per game (not one internal wallet for all action games)
    // it works only if merchant has supportTransfer == true and only for POP
    walletPerGame?: boolean;
    // if true, it will add jp contribution amount to debit requests of jp games (applicable to seamless integration)
    reportJPContributionOnDebitForSeamless?: boolean;
    // if true, it will add jp win statistic to credit requests of jp games (applicable to seamless integration)
    reportJPWinStatisticOnCreditForSeamless?: boolean;
    // true will correspond to ON Gamble feature
    gamble?: boolean;
    // if true will add 'distributionType' to deferred payment request
    supportPromoDistributionType?: boolean;
    // define field that should be send to operator with bonus payment request
    bonusApiAdditionalFields?: MerchantBonusApiAdditionalFields;
    // use player ip in game launch country restrictions
    gameRestrictionsUseIp?: boolean;
    // if true, it will exclude jp contribution amount for debit requests of jp games
    excludeJPContributionOnDebit?: boolean;
    // if true, it will exclude jp win statistic for credit requests of jp games
    excludeJPWinStatisticOnCredit?: boolean;
}

export interface MerchantCertSettings {
    useCert: boolean;
    settings: {
        cert: any;
        key: any;
        password: any;
        ca?: any;
    };
}

export type MerchantRegulation = "romanian" | "italian" | "spanish" | "greek" | "lithuanian" | "swiss" | "brazilian";

export interface RegulatorySettings {
    merchantRegulation?: MerchantRegulation;
    // title to use for POP notification message that is shown to player
    notificationTitle?: string;
    operatorName?: string; // value to provide for some operators (such as POP) under Italian regulation
    operatorLicenseId?: string; // value to provide for some operators (such as POP) under Italian regulation
}

export interface MrchGetGameTokenDataParams {
    isNewLimits?: boolean;

    [field: string]: any;
}

export interface MerchantBonusApiAdditionalFields {
    // Mean that 'operation_ts' should be send with deferred payment request to operator
    supportOperationTs?: boolean;
}

export const MERCHANT_REGULATION = {
    romanian: "romanian",
    italian: "italian",
    spanish: "spanish",
    greek: "greek",
    lithuanian: "lithuanian",
    swiss: "swiss",
    brazilian: "brazilian"
};

export interface Merchant {
    brandKey?: string;
    brandId: number;
    brandTitle: string;
    type: string;
    code: string;
    params: MerchantParams;
    isTest?: boolean;
    proxyId?: number;
    proxy?: Proxy;
    lastTestsPassing?: Date;
    version: number;
    createdAt?: Date;
    updatedAt?: Date;

    entity?: any;

    getAdapter(): Promise<AnyMerchantAdapter>;

    toInfo(): MerchantInfo;

    getGameTokenInfo(startToken: MerchantStartGameTokenData,
                     currency: string,
                     entityGame: EntityGame,
                     deviceId?: string,
                     additionalInfo?: AdditionalInfo): Promise<MerchantGameTokenInfo<MerchantGameTokenData>>;

    createGameUrl(gameCode: string,
                  entityGame: EntityGame,
                  initRequest: MerchantGameInitRequest): Promise<MerchantGameURLInfo>;
}

export interface MerchantInfo {
    brandId: number;
    type: string;
    code: string;
    params: MerchantParams;
    isTest?: boolean;
    proxy?: Proxy;
    proxyId?: number;
    lastTestsPassing?: Date;
    brandTitle?: string;
}

export interface MerchantMarketplaceGameList {
    games: EntityGameInfo[];
    languages: string[];
    currencies: string[];
    jurisdictions: string[];
    merchantCodes?: string[];
}

export interface MerchantMarketplaceGameWithSchema {
    game: Game;
    schemaDefinition: SchemaDefinition;
    schemaConfiguration: SchemaConfiguration;
}

export enum REGION {
    ASIA = "asia",
    EU = "eu"
}

export interface CreateBetConfigurationRequest {
    gameCode: string;
    merchantType?: string;
    merchantTypes?: string;
    merchantCode: string;
    title?: string;
    description?: string;
    externalBrandId?: string;
    segmentId?: string;
    segment?: Segment;
    gameLimits: GameLimitsByCurrencyCode;
    region?: REGION;
}

export interface GetGameCriticalFilesInfoRequest {
    regulation: MerchantRegulation;
    games: string[]; // list of gameCodes [sw_mrmnky, sw_888t]
}

export interface GetPlatformCriticalFilesInfoRequest {
    regulation: MerchantRegulation;
}

export interface HashCriticalFilesRequest {
    regulation: MerchantRegulation;
}

export interface GamesListWithHashes {
    games: GameWithHashList[];
}

export interface ModulesListWithHashes {
    modules: ModuleWithHashList[];
}
