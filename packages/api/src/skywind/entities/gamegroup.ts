import { EntityGame } from "./game";

export interface GameGroupFilterOptions {
    totalBetMultiplier: number;
    highestWin: number;
    limitFeaturesToMaxTotalStake: boolean;
}

export interface GameGroupFilter {
    id?: number;
    groupId?: number;
    maxTotalBet?: number;
    minTotalBet?: number;
    defTotalBet?: number;
    winCapping?: number;
    maxExposure?: number;
    games: string[]; // if empty then for all
    currencies: string[]; // if empty then for all
    updatedAt?: Date;
    createdAt?: Date;
    ignoreInvalid?: boolean;

    // virtual properties
    group?: GameGroupInfo;
    maxBetWillDecreased?: boolean;
    minBetWillIncreased?: boolean;
}

export interface GameGroupInfo {
    id?: number;
    name: string;
    description?: string;
    isDefault?: boolean;
    isOwner?: boolean;
    filter?: GameGroupFilter;
}

export enum LimitType {
    GAME_GROUP,
    GAME_GROUP_FILTERS,
    ENTITY_GAME_FILTERS,
    JURISDICTION_FILTERS
}

export type LimitsCustomizations = LimitType[];

// tslint:disable-next-line:no-empty-interface
export interface Limits {
}

export interface StakedLimits extends Limits {
    stakeAll: number[];
    stakeDef: number;
    stakeMax: number;
    stakeMin: number;
}

export interface SlotGameLimits extends StakedLimits {
    maxTotalStake: number;
    winMax: number;
    defaultTotalStake?: number;
    coins?: number[];
    defaultCoin?: number;
    alignCurrencies?: boolean;
    addLowerStakesOfAlignedCurrencies?: boolean;
}

export interface ActionGameLimits extends Limits {
    coinsRate: number;
    stakeDef?: number;
    coins?: number[];
}

export interface RoomGameLimits extends StakedLimits {
    totalStakeMin?: number;
    totalStakeMax?: number;
    bets?: {
        [betKey: string]: {
            min: number;
            max: number;
        };
    };
    isDefaultRoom?: boolean;
    order?: number;
}

export interface MultiRoomGameLimits extends Limits {
    [field: string]: RoomGameLimits;
}

export type TableGameLimits = RoomGameLimits | MultiRoomGameLimits;

export type CurrencyCode = string;

export interface LimitsByCurrencyCode {
    [field: string]: Limits;
}

export interface GameGroupData {
    name: string;
    description?: string;
}

export interface UpdateGameGroupData {
    description?: string;
}

export interface GameGroupAttributes {
    id?: number;
    brandId: number;
    name: string;
    description?: string;
    isDefault?: boolean;
    createdAt?: Date;
    updatedAt?: Date;

    filters?: any;
}

export interface GameGroupAndGame {
    gameGroup: GameGroupAttributes;
    entityGame: EntityGame;
}

export interface GameGroupFindOptions {
    name?: string;
    isDefault?: boolean;
}
