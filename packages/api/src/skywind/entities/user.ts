import { Role } from "./role";
import { BaseEntity } from "./entity";
import { PasswordHistoryRecord } from "../services/user/user";
import { TIME_CONSTS } from "../utils/common";
import { SECURITY_AUTH_TYPE } from "../services/security";

export type PermissionsList = Array<string>;

export type ForcePasswordChangePeriodType = "minutely" | "hourly" | "daily" | "weekly" | "monthly" | "yearly";

export const MAP_FORCE_PASSWORD_CHANGE_PERIOD_TYPE = new Map<string, number>([
    ["minutely", TIME_CONSTS.MINUTE_TICKS],
    ["hourly", TIME_CONSTS.HOUR_TICKS],
    ["daily", TIME_CONSTS.DAY_TICKS],
    ["weekly", TIME_CONSTS.WEEK_TICKS],
    ["monthly", TIME_CONSTS.MONTH_TICKS],
    ["yearly", TIME_CONSTS.YEAR_TICKS]
]);

export interface UserPermissions {
    grantedPermissions?: PermissionsList;
}

export interface TFASupport {
    defaultTwoFAType?: string;
    authInfo?: AuthInfo;
    tfaIsConfigured(): boolean;
    generateTFAToken(): Promise<string>;
}

export enum UserType {
    BO = "bo", // back office
    OPERATOR_API = "operator_api", // operator api
    STUDIO = "studio_user"
}

export enum UserStatus {
    NORMAL = "normal",
    SUSPENDED = "suspended",
    LOCKED_BY_AUTH = "locked_by_auth"
}

export interface User extends UserPermissions, TFASupport {
    id: number;
    entityId: number;
    username: string;
    salt: string;
    password: string;
    email: string;
    phone: string;
    status: UserStatus;
    firstName: string;
    lastName: string;
    role?: Role;
    roles?: Role[];
    lastLogin?: Date;
    passwordChangedAt?: Date;
    passwordHistory?: PasswordHistoryRecord[];
    userType?: UserType;
    customData: UserCustomData;

    isSuspended(): boolean;
    save(): Promise<UserInfo>;
    toInfo(): UserInfo;
    toDetailedInfo(): DetailedUserInfo;
    isSuperAdmin(): boolean;
    toProfileInfo(entity: BaseEntity): Promise<UserWithBlockingInfo>;
    hasPhone(): boolean;
    hasSuperAdminRole(): boolean;
}

export interface UserInfo {
    username: string;
    status: string;
}

export interface UserTypeInfo {
    userType: string;
}

export interface UserWithBlockingInfo extends DetailedUserInfo {
    blocking: BlockingInfo;
}

export interface BlockingInfo {
    changePasswordTillDate: Date;
}

export interface DetailedUserInfo extends UserInfo {
    email: string;
    phone: string;
    entity?: string;
    firstName?: string;
    lastName?: string;
    id?: number;
    grantedPermissions: string[];
    lastLogin: Date;
    passwordChangedAt: Date;
    createdAt: Date;
    updatedAt: Date;
    twoFAInfo?: { defaultType: string; authTypes: string[] };
    userType?: UserType;
    customData: UserCustomData;
}

export interface LoginInfo extends AccessTokenInfo {
    accessToken: string;
    grantedPermissions?: {
        permissions: PermissionsList
    };
}

export interface AccessTokenInfo {
    key: string;
    username: string;
    lastPasswordUpdate?: Date;
    userId?: number;
    entityId?: number;
    sessionId?: string;
    isSuperAdmin?: boolean;
    userType?: UserType;
}

export interface UpdateStatusesData {
    status: UserStatus;
    id: string[];
}

export interface AuthInfo {
    gaSecret?: string;
    defaultAuthType?: SECURITY_AUTH_TYPE;
    authTypes?: string[];
    forcePasswordChangePeriod?: number;
    forcePasswordChangePeriodType?: ForcePasswordChangePeriodType;
}

export interface UserCustomData {
    [key: string]: any;
}
