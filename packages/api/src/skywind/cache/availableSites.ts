import config from "../config";
import { Models } from "../models/models";
import { AvailableSiteModel } from "../models/availableSites";
import { BaseEntity } from "../entities/entity";
import { Cache } from "./cache";

const AvailableSitesCache = new Cache<string, AvailableSiteModel>("available-sites", searchInDb, {
    stdTTL: config.availableSites.cache.ttl,
    checkperiod: config.availableSites.cache.checkPeriod
});

export async function findOne(entity: BaseEntity, url: string) {
    const key = `${entity.id}_${url}`;
    return AvailableSitesCache.find(key, entity.id, url);
}

export function reset() {
    AvailableSitesCache.reset();
}

async function searchInDb(id, entityId: number, url: string) {
    const query = buildQueryToGet(entityId, url);
    return Models.AvailableSiteModel.findOne(query);
}

function buildQueryToGet(entityId: number, url: string): any {
    return {
        where: {
            entityId,
            url
        }
    };
}
