import { Merchant } from "../entities/merchant";
import { BrandEntity } from "../entities/brand";
import { Cache } from "./cache";
import { getMerchantCRUDService } from "../services/merchant";

const merchantCache: Cache<string, Merchant> = new Cache("merchant", (key, brand) => {
    return getMerchantCRUDService().findOne(brand);
});

export async function findOne(brand: BrandEntity): Promise<Merchant> {
    const key = `${brand.id}`;
    return merchantCache.find(key, brand);
}

/**
 * Cache by type and code used for cases when we need to get merchant but we don't have its id.
 * For example: we want to add 'sw-entity-id' to logs of launch requests, but we only have merchant code and type.
 * Look at setMerchantAuthContext middleware.
 */
const merchantCacheByTypeAndCode: Cache<string, Merchant> =
    new Cache("merchant-by-type-and-code", (key, merchantType, merchantCode) => {
        return getMerchantCRUDService().findOneByTypeAndCode(merchantType, merchantCode);
    });

/**
 * Returns a merchant based on its type and code
 * @param merchantType
 * @param merchantCode
 */
export async function findByTypeAndCode(merchantType: string, merchantCode: string): Promise<Merchant> {
    const key = `${merchantType}_${merchantCode}`;
    return merchantCacheByTypeAndCode.find(key, merchantType, merchantCode);
}

/**
 * Reset caches if a merchant was changed
 */
export function reset() {
    merchantCacheByTypeAndCode.reset();
    merchantCache.reset();
}
