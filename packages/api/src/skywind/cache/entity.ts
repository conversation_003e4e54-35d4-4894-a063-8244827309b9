import * as EntityService from "../services/entity";
import { BaseEntity, FindEntityOptions } from "../entities/entity";
import { cloneDeep } from "lodash";
import config from "../config";
import { retry } from "@skywind-group/sw-utils";
import * as redis from "../storage/redis";
import { Redis as RedisClient } from "ioredis";
import * as uuid from "uuid";
import logger from "../utils/logger";
import { ConcurrentCache } from "./cache";

const log = logger("entity-cache");

class EntityTreeCache extends ConcurrentCache<BaseEntity> {
    public static readonly REDIS_CHANNEL = "channel:entities-tree";
    private readonly pub: RedisClient;
    private readonly sub: RedisClient;
    public readonly uuid = uuid.v4();

    constructor() {
        super(config.entitiesCache.ttl, log);
        this.sub = redis.create();
        this.pub = redis.create();
        this.listenInvalidates();
    }

    public async findOne<T extends BaseEntity>(options: FindEntityOptions,
                                               path?: string,
                                               raiseErrorIfNotFound: boolean = false,
                                               clone: boolean = false): Promise<T> {

        const result = await EntityService.findOneInHierarchy<T>(options, () => this.get(), path, raiseErrorIfNotFound);
        return clone ? cloneDeep(result) : result;
    }

    public async findById<T extends BaseEntity>(entityId: number): Promise<T> {
        return this.findOne<T>({ id: entityId });
    }

    public reset() {
        this.notifyInvalidated();
        super.reset();
    }

    protected retrieve(): Promise<BaseEntity> {
        return retry(config.entitiesCache.queryRetries, async () => this.findMaster());
    }

    private async findMaster(): Promise<BaseEntity> {
        return EntityService.findOne({});
    }

    private listenInvalidates() {
        this.sub.subscribe(EntityTreeCache.REDIS_CHANNEL);
        this.sub.on("message", (channel, message) => {
            if (EntityTreeCache.REDIS_CHANNEL === channel && this.uuid !== message) {
                super.reset();
            }
        });

        this.sub.on("error", () => {
            super.reset();
        });
    }

    private notifyInvalidated() {
        return this.pub.publish(EntityTreeCache.REDIS_CHANNEL, this.uuid);
    }
}

export default new EntityTreeCache();
