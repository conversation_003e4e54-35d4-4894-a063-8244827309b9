import * as redis from "../storage/redis";
import { Redis as RedisClient } from "ioredis";
import config from "../config";
import { logging } from "@skywind-group/sw-utils";
import * as NodeCache from "node-cache";
import Logger = logging.Logger;

export interface CacheConfig {
    stdTTL?: number;
    checkperiod?: number;
    useClones?: boolean;
}

export class Cache<ID, T> {
    protected cache: any;
    protected channel: string;
    protected sub: RedisClient;
    protected pub: RedisClient;
    protected connected: boolean = false;

    constructor(name: string, protected search: Function, cacheConfig?: CacheConfig) {
        this.channel = "cache:" + name;
        this.sub = redis.create();
        this.pub = redis.create();
        this.initNodeCache(cacheConfig);
    }

    private initNodeCache(cacheConfig?: CacheConfig) {
        if ((cacheConfig?.stdTTL || 0) >= 0) {
            this.cache = new NodeCache({
                stdTTL: config.cache.ttl,
                checkperiod: config.cache.checkPeriod,
                useClones: false,
                ...cacheConfig
            });

            this.listenInvalidates();
        }
    }

    public async find(id: ID, ...args): Promise<T> {
        if (this.cache && this.connected) {
            let value: T = this.cache.get(id);

            if (!value) {
                value = await this.search.call(this.search, id, ...args);
                if (value) {
                    this.cache.set(id, value);
                } else {
                    this.cache.del(id);
                }
            }

            return value;
        } else {
            return this.search.call(this.search, id, ...args);
        }
    }

    public reset(id?: ID) {
        if (this.cache) {
            if (id) {
                this.cache.del(id);
            } else {
                this.cache.flushAll();
            }
            this.notifyInvalidated(id);
        }
    }

    public keys(): string[] {
        return this.cache.keys();
    }

    private listenInvalidates() {
        this.sub.subscribe(this.channel);
        this.sub.on("message", (channel, message) => {
            if (this.channel === channel) {
                const msg = JSON.parse(message);
                if (msg.id) {
                    this.cache.del(msg.id);
                } else {
                    this.cache.flushAll();
                }
            }
        });

        this.sub.on("error", () => {
            if (this.cache && this.connected) {
                this.cache.flushAll();
            }
            this.connected = false;
        });

        this.sub.on("connect", (args) => {
            this.connected = true;
        });
    }

    private notifyInvalidated(id?: ID) {
        this.pub.publish(this.channel, JSON.stringify({ id: id }));
    }
}
/* Hand-made cache that implemented to prevent multiple parallel queries */
export abstract class ConcurrentCache<T> {

    private root: T;
    private lastUpdate: number;
    private updating: Promise<T>;
    private updated: boolean;

    protected constructor(private ttl: number, private log: Logger) {
        // empty
    }

    protected abstract retrieve(): Promise<T>;

    protected async get(): Promise<T> {
        this.checkUpdated();
        if (!this.root || this.lastUpdate + this.ttl  * 1000 < Date.now()) {
            if (!this.updating) {
                this.updating = this.update();
            }
            return this.updating;
        } else {
            return this.root;
        }
    }

    public reset(): void {
        this.root = undefined;
    }

    private checkUpdated(): void {
        if (this.updated) {
            this.updated = false;
            this.updating = undefined;
        }
    }

    private async update(): Promise<T> {
        try {
            this.root = await this.retrieve();
            this.lastUpdate = Date.now();
        } catch (e) {
            this.log.error(e);
        } finally {
            this.updated = true;
        }

        return this.root;
    }
}
