import { Cache } from "./cache";
import { AuditSummaryDBInstance } from "../models/auditSummary";
import config from "../config";
import { AuditSummary } from "../entities/auditSummary";
import { Models } from "../models/models";
import { Op } from "sequelize";

const AuditSummaryModel = Models.AuditSummaryModel;

const AuditSummaryCache = new Cache<string, AuditSummaryDBInstance>("audit-summary", searchInDb, {
    stdTTL: config.auditSummary.cache.ttl,
    checkperiod: config.auditSummary.cache.checkPeriod
});

async function searchInDb(id, search: { path: string, method: string }) {
    const conditions = search ? { path: { [Op.eq]: search.path }, method: { [Op.eq]: search.method } } : { id };
    const dbInstance = await AuditSummaryModel.findOne({
        where: conditions
    });
    return dbInstance ? dbInstance : undefined;
}

export async function findOne(auditSummary: AuditSummary): Promise<AuditSummaryDBInstance> {
    const key = `${auditSummary.method}_${auditSummary.path}`;
    return AuditSummaryCache.find(key, auditSummary);
}

export async function findById(id: number): Promise<AuditSummaryDBInstance> {
    return AuditSummaryCache.find(id.toString());
}

export async function saveById(auditSummary: AuditSummary): Promise<AuditSummaryDBInstance> {
    return AuditSummaryCache.find(auditSummary.id.toString(), auditSummary);
}

export function reset() {
    AuditSummaryCache.reset();
}
