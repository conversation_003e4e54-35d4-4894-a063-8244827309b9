import { Cache } from "./cache";
import config from "../config";
import { ExtendedLobby } from "../entities/lobby";
import { getExtendedLobbies, getExtendedLobby } from "../services/lobby";
import { BaseEntity } from "../entities/entity";
import { gameCategoryGamesCache } from "../services/gameCategory/gameCategoryGamesService";
import { gameCategoryCache } from "../services/gameCategory/gameCategoryService";
import { ENTITY_GAME_STATUS } from "../utils/common";
import { LobbyGamesParams } from "../entities/game";

function key(params?: LobbyGamesParams) {
    const gameStatuses = params?.gameStatuses ?? [ENTITY_GAME_STATUS.NORMAL];
    const includeGamesLimits = Boolean(params?.includeGamesLimits === true || params?.includeGamesLimitRanges === true);
    const currency = includeGamesLimits ? params?.currency : "";
    const gameGroup = includeGamesLimits ? `${params?.gameGroupName ?? ""}-${params?.gameGroupId ?? ""}` : "";
    return `${gameStatuses.join()}-${currency}-${gameGroup}`;
}

export async function findAll(entity: BaseEntity): Promise<ExtendedLobby[]> {
    return lobbiesCache.find(`${entity.id}`, entity);
}

export async function findOne(entity: BaseEntity, id: number, params?: LobbyGamesParams): Promise<ExtendedLobby> {
    return lobbyCache.find(`${entity.id}-${id}-${key(params)}`, entity, id, params);
}

export async function reset(): Promise<void> {
    await lobbyCache.reset();
    await lobbiesCache.reset();
    await gameCategoryCache.reset();
    await gameCategoryGamesCache.reset();
}

const options = {
    stdTTL: config.lobbies.cache.ttl,
    checkperiod: config.lobbies.cache.checkPeriod
};
const lobbiesCache = new Cache<string, ExtendedLobby[]>(
    "lobbies",
    async (key: string, entity: BaseEntity) =>
        getExtendedLobbies(entity),
    options
);
const lobbyCache = new Cache<string, ExtendedLobby>(
    "lobby",
    async (key: string, entity: BaseEntity, id: number, params?: LobbyGamesParams) =>
        getExtendedLobby(entity, id, params),
    options
);
