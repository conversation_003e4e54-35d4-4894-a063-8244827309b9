import { Cache } from "./cache";

import { MerchantTypeDBInstance } from "../models/merchantType";
import { MerchantTypeImpl } from "../services/merchantType";
import { Models } from "../models/models";

const MERCHANT_TYPES_ID = "merchant_types_id";
const MerchantTypeModel = Models.MerchantTypeModel;

const findCache = async() => {
    const merchantTypesDB: MerchantTypeDBInstance[] = await MerchantTypeModel.findAll();
    if (merchantTypesDB) {
        return merchantTypesDB.map(merchantTypeDB => new MerchantTypeImpl(merchantTypeDB));
    }
};

const merchantTypesCache: Cache<string, any> = new Cache(
    "merchant-types-info",
    findCache
);

export async function findOne(type: string): Promise<MerchantTypeImpl> {
    const merchantTypes = await merchantTypesCache.find(MERCHANT_TYPES_ID);

    return merchantTypes.find(merchantType => merchantType.type === type);
}

export async function findAll(): Promise<MerchantTypeImpl[]> {
    const merchantTypes = await merchantTypesCache.find(MERCHANT_TYPES_ID);
    return merchantTypes || undefined;
}

export function reset() {
    merchantTypesCache.reset();
}
