import { Cache } from "./cache";
import { BaseEntity } from "../entities/entity";
import getMerchantTestPlayerService from "../services/merchantTestPlayer";
import { MerchantTestPlayer } from "../entities/merchantTestPlayer";

const merchantTestPlayerCache: Cache<string, MerchantTestPlayer> = new Cache("merchant-test-players",
    (key, brand, playerCode: string) => {
        return getMerchantTestPlayerService(brand).findOne(playerCode);
    });

export async function findOne(brand: BaseEntity, playerCode: string): Promise<MerchantTestPlayer> {
    const key = `${brand.id}_${playerCode}`;
    return merchantTestPlayerCache.find(key, brand, playerCode);
}

export function reset() {
    merchantTestPlayerCache.reset();
}
