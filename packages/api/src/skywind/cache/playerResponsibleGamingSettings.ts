import { Cache } from "./cache";
import PlayerResponsibleGamingServiceImpl, {
    PlayerResponsibleGamingImpl
} from "../services/playerResponsibleGaming";

const settingsCache: Cache<string, any> = new Cache("player-responsible-gaming",
    async(key: string, playerCode: string, brandId: number, gamingJurisdiction: string) => {
        const settings = await PlayerResponsibleGamingServiceImpl.getPlayerRespGamingSettings(
            playerCode, brandId, gamingJurisdiction);
        return settings ? settings : undefined;
    });

export async function findOne<T extends PlayerResponsibleGamingImpl>(playerCode: string,
                                                                     brandId: number,
                                                                     gamingJurisdiction: string): Promise<T> {
    const key = `${playerCode}-${brandId}-${gamingJurisdiction}`;
    return settingsCache.find(key, playerCode, brandId, gamingJurisdiction);
}

export function reset(playerCode: string,
                      brandId: number,
                      gamingJurisdiction: string) {
    const key = `${playerCode}-${brandId}-${gamingJurisdiction}`;
    settingsCache.reset(key);
}
