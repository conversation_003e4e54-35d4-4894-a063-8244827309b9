import config from "../config";
import { Models } from "../models/models";
import { Cache } from "./cache";
import { Role } from "../entities/role";
import { RoleImpl } from "../services/role";

const cache = new Cache<string, Role>("roles", searchInDb, {
    stdTTL: config.roles.cache.ttl,
    checkperiod: config.roles.cache.checkPeriod
});

export async function get(...ids: number[]) {
    const result = await Promise.all(ids.map(id => cache.find(`${id}`)));
    return result.filter(r => !!r);
}

export function reset(id?: string) {
    cache.reset(id);
}

async function searchInDb(id) {
    const role = await Models.RoleModel.findByPk(id);
    return role && new RoleImpl(role);
}
