import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import {
    EntityGameFeatures,
    EntityGameUrlParams,
    Game,
    GAME_CODE_PATTERN,
    GameSettings,
    LimitFiltersByCurrency
} from "../entities/game";
import { get as getEntityModel } from "./entity";
import { get as getProviderGameModel } from "./gameprovider";
import { getSchemaDefinitionModel } from "./schemaDefinition";
import { sequelize as db } from "../storage/db";
import { GAME_TYPES, HISTORY_RENDER_TYPE } from "../utils/common";
import { getDeploymentGroupModel } from "./deploymentGroup";
import { NonFunctionProperties } from "../entities/typeUtils";

const EntityModel = getEntityModel();
const GameProviderModel = getProviderGameModel();
const SchemaDefinitionModel = getSchemaDefinitionModel();
const DeploymentGroupModel = getDeploymentGroupModel();

type GameAttributes = NonFunctionProperties<Game> & { version?: number; entityGames?: any; };
export interface GameDBInstance extends Model<
        InferAttributes<GameDBInstance>,
        InferCreationAttributes<GameDBInstance>
    >,
    GameAttributes {
    gameProvider?: any;
}

const gameSchema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    code: { type: DataTypes.STRING, unique: true, validate: { notEmpty: true, is: GAME_CODE_PATTERN } },
    title: { type: DataTypes.STRING, validate: { notEmpty: true } },
    type: {
        type: DataTypes.ENUM(
            GAME_TYPES.slot, GAME_TYPES.action, GAME_TYPES.table, GAME_TYPES.external, GAME_TYPES.live
        ),
        allowNull: false, validate: { notEmpty: true }
    },
    url: { type: DataTypes.STRING, allowNull: true },
    providerId: {
        type: DataTypes.INTEGER,
        field: "provider_id",
        references: {
            model: GameProviderModel,
            key: "id",
        },
    },
    providerGameCode: {
        type: DataTypes.STRING,
        field: "provider_game_code",
        allowNull: false,
        validate: { notEmpty: true, is: GAME_CODE_PATTERN },
    },
    status: {
        type: DataTypes.ENUM("available", "unavailable"),
        allowNull: false,
        validate: { notEmpty: true },
    },
    defaultInfo: {
        type: DataTypes.JSONB,
        field: "default_info"
    },
    info: DataTypes.JSONB,
    limits: DataTypes.JSONB,
    features: DataTypes.JSONB,
    clientFeatures: {
        type: DataTypes.JSONB,
        field: "client_features",
    },
    version: DataTypes.INTEGER,
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
    },
    historyRenderType: {
        type: DataTypes.INTEGER,
        defaultValue: HISTORY_RENDER_TYPE.OLD_AND_HISTORY_FROM_GAME,
        field: "history_render_type",
        allowNull: false,
        validate: { notEmpty: true }
    },
    historyUrl: { type: DataTypes.STRING, allowNull: true, field: "history_url" },
    limitsGroup: { field: "limits_group", type: DataTypes.STRING, allowNull: true },
    countries: { type: DataTypes.JSONB, allowNull: true },
    totalBetMultiplier: { type: DataTypes.DECIMAL, field: "total_bet_multiplier" },
    schemaDefinitionId: {
        type: DataTypes.INTEGER,
        field: "schema_definition_id",
        allowNull: true,
        references: {
            model: SchemaDefinitionModel,
            key: "id"
        }
    },
    deploymentGroupId: {
        type: DataTypes.INTEGER,
        field: "deployment_group_id"
    },
    defaultClientVersion: { type: DataTypes.STRING, field: "default_client_version" },
    physicalTableId: {
        type: DataTypes.INTEGER,
        field: "physical_table_id",
        allowNull: true,
    },
};

export type IGameModel = ModelStatic<GameDBInstance>;
const GameModel: IGameModel = db.define<GameDBInstance, GameAttributes>(
    "game",
    gameSchema,
    { underscored: true });

GameModel.belongsTo(GameProviderModel, { foreignKey: "providerId" });
GameModel.belongsTo(SchemaDefinitionModel, { foreignKey: "schemaDefinitionId" });
GameModel.belongsTo(DeploymentGroupModel, { foreignKey: "deployment_group_id", onDelete: "NO ACTION" });

export function getGameModel(): IGameModel {
    return GameModel;
}

export interface EntityGameAttributes {
    id?: number;
    entityId?: number;
    gameId?: number;
    parentEntityGameId?: number;
    status?: string;
    settings?: GameSettings;
    urlParams?: EntityGameUrlParams;
    limitFilters?: LimitFiltersByCurrency;
    externalGameId?: string;
    domain?: string;
    title?: string;
    features?: EntityGameFeatures;
    /*game?: Game;
    entity?: BaseEntity;*/
}

export interface EntityGameDBInstance extends Model<
        InferAttributes<EntityGameDBInstance>,
        InferCreationAttributes<EntityGameDBInstance>
    >,
    EntityGameAttributes {
    game: GameDBInstance;
    entity: any;
}

export type IEntityGameModel = ModelStatic<EntityGameDBInstance>;
const EntityGameModel: IEntityGameModel =
    db.define<EntityGameDBInstance, EntityGameAttributes>(
        "entityGame",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            entityId: {
                type: DataTypes.INTEGER,
                field: "entity_id",
                unique: "entityIdGameId",
                references: {
                    model: EntityModel,
                    key: "id",
                },
            },
            gameId: {
                type: DataTypes.INTEGER,
                field: "game_id",
                unique: "entityIdGameId",
                references: {
                    model: GameModel,
                    key: "id",
                },
            },
            settings: {
                type: DataTypes.JSONB,
            },
            urlParams: {
                type: DataTypes.JSONB,
                field: "url_params"
            },
            status: {
                type: DataTypes.ENUM("normal", "suspended", "test", "hidden"),
                field: "status",
                defaultValue: "normal",
                allowNull: false,
                validate: { notEmpty: true },
                comment: "Status of a game for specific entity: normal, suspended or test",
            },
            parentEntityGameId: {
                type: DataTypes.INTEGER,
                field: "parent_entity_game_id"
            },
            limitFilters: {
                type: DataTypes.JSONB,
                field: "limit_filters"
            },
            externalGameId: {
                type: DataTypes.STRING,
                field: "external_game_id"
            },
            title: DataTypes.STRING,
            domain: DataTypes.STRING,
            features: DataTypes.JSONB,
        },
        {
            underscored: true,
            tableName: "entity_games",
        }
    );
EntityGameModel.belongsTo(GameModel, { foreignKey: "gameId" });
EntityGameModel.belongsTo(EntityModel, { foreignKey: "entityId" });
EntityGameModel.belongsTo(EntityGameModel, { foreignKey: "parentEntityGameId", onDelete: "NO ACTION" });

GameModel.hasMany(EntityGameModel);

export function getEntityGame(): IEntityGameModel {
    return EntityGameModel;
}
