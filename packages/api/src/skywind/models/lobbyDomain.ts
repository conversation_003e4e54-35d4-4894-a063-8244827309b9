import { Model, ModelStatic, DataTypes, InferAttributes, InferCreationAttributes } from "sequelize";
import { sequelize as db } from "../storage/db";
import { LobbyDomainAttributes } from "../entities/lobbyDomain";

export interface LobbyDomainDBInstance extends Model<
        InferAttributes<LobbyDomainDBInstance>,
        InferCreationAttributes<LobbyDomainDBInstance>
    >,
    LobbyDomainAttributes {
}

type ILobbyDomainModel = ModelStatic<LobbyDomainDBInstance>;
const LobbyDomainModel: ILobbyDomainModel = db.define<LobbyDomainDBInstance, LobbyDomainAttributes>(
    "LobbyDomainModel",
    {
        id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            field: "id"
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
            field: "name"
        },
        isActive: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
            field: "is_active"
        },
        createdAt: {
            type: DataTypes.DATE,
            field: "created_at",
        },
        updatedAt: {
            type: DataTypes.DATE,
            field: "updated_at",
        },
    },
    {
        tableName: "lobby_domains"
    }
);

export const getLobbyDomainModel = () => LobbyDomainModel;
