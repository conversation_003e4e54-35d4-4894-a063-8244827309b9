import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import {
    EntityGameDBInstance,
    GameDBInstance,
    getEntityGame as getEntityGameModel,
    getGameModel as getGameModel
} from "./game";
import { EntityGameInfo } from "../entities/game";
import { sequelize as db } from "../storage/db";
import { GameGroupInfo, LimitsByCurrencyCode, SlotGameLimits } from "../entities/gamegroup";
import { GameGroupDBInstance, get as getGameGroupModel } from "./gamegroup";
import { FORBIDDEN_CURRENCIES, GAME_TYPES } from "../utils/common";
import { getCurrencyExchange } from "../services/currencyExchange";
import { Currencies } from "@skywind-group/sw-currency-exchange";

const gameGroupModel = getGameGroupModel();
export interface GameGroupLimit {
    gamegroupId: number;
    entityGameId: number;
    overrideDefault?: boolean;
    limits?: LimitsByCurrencyCode;
    gameId?: number;

    game?: GameDBInstance;
    entityGame?: EntityGameDBInstance;
    gamegroup?: GameGroupDBInstance;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface GameGroupLimitsWithGame {
    overrideDefault?: boolean;
    limits?: LimitsByCurrencyCode;
    game: EntityGameInfo;
    gamegroup: GameGroupInfo;
}

export interface GameGroupLimitDBInstance extends Model<
        InferAttributes<GameGroupLimitDBInstance>,
        InferCreationAttributes<GameGroupLimitDBInstance>
    >,
    GameGroupLimit {
    toInfo(): GameGroupLimit;
    getLimits(currency: string): Promise<any>;
}
export type GameGroupLimitModel = ModelStatic<GameGroupLimitDBInstance>;
const model: GameGroupLimitModel = db.define<GameGroupLimitDBInstance, GameGroupLimit>(
    "gamegrouplimit",
    {
        gamegroupId: {
            field: "game_group_id",
            type: DataTypes.INTEGER,
            primaryKey: true,
            references: {
                model: gameGroupModel,
                key: "id",
            }
        },
        // we need this field only for consistency: game group contains only games which are enabled for brand
        entityGameId: {
            type: DataTypes.INTEGER,
            field: "entity_game_id",
            primaryKey: true,
        },
        // we need this to simplify relation between game and game group of brand.
        // This redundancy will allow to build much simpler queries
        gameId: {
            type: DataTypes.INTEGER,
            field: "game_id"
        },
        overrideDefault: {
            type: DataTypes.BOOLEAN,
            field: "override_default",
            defaultValue: false
        },
        limits: {
            type: DataTypes.JSONB
        },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        underscored: true,
        tableName: "game_group_limits"
    }
);
model.belongsTo(getEntityGameModel(), { foreignKey: "entityGameId" });
model.belongsTo(getGameModel(), { foreignKey: "gameId" });
model.belongsTo(getGameGroupModel(), { foreignKey: "gamegroupId" });

export function get(): GameGroupLimitModel {
    (model as any).prototype.toInfo = function(): GameGroupLimit {
        return {
            overrideDefault: this.overrideDefault,
            limits: this.limits,
            entityGameId: this.entityGameId,
            gamegroupId: this.gamegroupId,
            gameId: this.gameId
        };
    };
    (model as any).prototype.getLimits = async function(currency: string) {
        if (!this.game || isForbiddenAlignLimits(this.game.get("type"), currency)) {
            return this.limits[currency];
        }

        const slotLimits = (this.limits[currency] || {}) as SlotGameLimits;
        const slotLimitsInEUR = this.limits["EUR"] as SlotGameLimits;

        if (slotLimits && !slotLimits.winMax && slotLimitsInEUR && slotLimitsInEUR.winMax) {
            const service = await getCurrencyExchange();
            slotLimits.winMax = service.exchange(slotLimitsInEUR.winMax, "EUR", currency);
        }

        return slotLimits;
    };
    return model;
}

export function isForbiddenAlignLimits(type: string, currency): boolean {
    return (type !== GAME_TYPES.slot && type !== GAME_TYPES.external)
        || Currencies.get(currency).isVirtual
        || FORBIDDEN_CURRENCIES.includes(currency);
}
