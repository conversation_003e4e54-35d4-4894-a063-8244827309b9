import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { get as getEntityModel } from "./entity";
import { sequelize as db } from "../storage/db";
import { getSchemaDefinitionModel } from "./schemaDefinition";
import { DEFAULT_CURRENCY } from "./currencyMultiplier";

const EntityModel = getEntityModel();
const DefinitionModel = getSchemaDefinitionModel();

export interface SchemaConfiguration {
    id?: number;
    name: string;
    entityId?: number;
    schemaDefinitionId?: number;
    configuration: SchemaConfigurationDefaultValues;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface SchemaConfigurationDefaultValue {
    stakeAll?: number[];
    defaultTotalStake?: number;
    winMax?: number;
    aligned?: boolean;
    coinsRate?: number;
    coins?: number[];
    defaultCoin?: number;
    bets?: {
        [betType: string]: { min?: number, max?: number, exposure?: number, alert?: number, block?: number };
    };

    [key: string]: any;
}

export interface SchemaConfigurationDefaultValues {
    [currency: string]: SchemaConfigurationDefaultValue;
}

export interface SchemaConfigurationDefaultValuesForCurrency {
    aligned?: boolean;
    [set: string]: SchemaConfigurationDefaultValue | boolean;
}

export interface SchemaConfigurationImpl extends Model<
        InferAttributes<SchemaConfigurationImpl>,
        InferCreationAttributes<SchemaConfigurationImpl>
    >,
    SchemaConfiguration {
    // entity: BelongsToCreateAssociationMixin<BaseEntity, SchemaConfigurationImpl>;
    // definition: Sequelize.BelongsToCreateAssociationMixin<SchemaDefinition, SchemaConfigurationImpl>;
    toInfo(): SchemaConfiguration;
    getCurrencyLimits(currency: string,
                      level?: string): SchemaConfigurationDefaultValuesForCurrency | SchemaConfigurationDefaultValue;
    getDefaultLevels(levelsSupported?: boolean): string[];

}
export type SchemaConfigurationModel = ModelStatic<SchemaConfigurationImpl>;
const SchemaConfigurationDBModel: SchemaConfigurationModel = db.define<SchemaConfigurationImpl, SchemaConfiguration>(
    "schema_configurations",
    {
        id: { field: "id", type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        name: { field: "name", type: DataTypes.STRING, allowNull: false },
        entityId: {
            field: "entity_id", type: DataTypes.INTEGER, allowNull: false,
            references: {
                model: EntityModel,
                key: "id"
            }
        },
        schemaDefinitionId: {
            field: "schema_definition_id", type: DataTypes.INTEGER, allowNull: false,
            references: {
                model: DefinitionModel,
                key: "id"
            }
        },
        configuration: { field: "configuration", type: DataTypes.JSONB, allowNull: false },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        timestamps: true, freezeTableName: true,
        indexes: [{
            unique: true, fields: ["entity_id", "schema_definition_id"]
        }]
    }
);

SchemaConfigurationDBModel.belongsTo(EntityModel, { foreignKey: "entityId" });
SchemaConfigurationDBModel.belongsTo(DefinitionModel, { foreignKey: "schemaDefinitionId" });

export function getSchemaConfigurationDBModel(): SchemaConfigurationModel {
    (SchemaConfigurationDBModel as any).prototype.toInfo = function(): SchemaConfiguration {
        return {
            id: this.id,
            name: this.name,
            schemaDefinitionId: this.schemaDefinitionId,
            configuration: this.configuration,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
    (SchemaConfigurationDBModel as any).prototype.getCurrencyLimits =
        function(currency: string,
                 level?: string): SchemaConfigurationDefaultValuesForCurrency | SchemaConfigurationDefaultValue {
        return level ? this.configuration?.[currency]?.[level] : this.configuration?.[currency];
    };

    (SchemaConfigurationDBModel as any).prototype.getDefaultLevels = function(levelsSupported?: boolean): string[] {
        if (levelsSupported) {
            return Object.keys(this.configuration[DEFAULT_CURRENCY]);
        }
    };
    return SchemaConfigurationDBModel;
}
