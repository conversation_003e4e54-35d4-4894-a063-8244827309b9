import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    BindOrReplacements,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { FlatReport } from "../entities/flatReport";

import { get as getEntityModel } from "./entity";

const EntityModel = getEntityModel();
export interface FlatReportDBInstance extends Model<
        InferAttributes<FlatReportDBInstance>,
        InferCreationAttributes<FlatReportDBInstance>
    >,
    FlatReport {
    toInfo(): FlatReport;
}
export type FlatReportModel = ModelStatic<FlatReportDBInstance>;
const model: FlatReportModel = db.define<FlatReportDBInstance, FlatReport>(
    "flat_reports",
    {
        entityId: {
            field: "entity_id",
            type: DataTypes.INTEGER,
            primaryKey: true,
            references: {
                model: EntityModel,
                key: "id",
            },
        },
        report: { field: "report", type: DataTypes.JSONB },
        reportType: { field: "report_type", type: DataTypes.ENUM("ols", "nls", "rtp"), primaryKey: true },
        createdAt: {
            field: "created_at",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        updatedAt: {
            field: "updated_at",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
    },
    {
        timestamps: false,
        freezeTableName: true
    }
);
export function getFlatReportModel(): FlatReportModel {
    (model as any).prototype.toInfo = function(): FlatReport {
        return {
            entityId: this.entityId,
            report: this.report,
            reportType: this.reportType,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
    return model;
}

export async function upsertFlatReport(data: FlatReport & BindOrReplacements, transaction?): Promise<boolean> {
    const rs = await db.query(UPSERT_FLAT_REPORT, { replacements: data, transaction });
    if (rs.length) {
        return true;
    }
    return false;
}

const UPSERT_FLAT_REPORT = "INSERT INTO flat_reports as item " +
    "(\"entity_id\", \"report\", \"report_type\", \"created_at\", \"updated_at\") " +
    "VALUES (:entityId, :report, :reportType, now(), now()) " +
    "ON CONFLICT(entity_id, report_type) " +
    "DO UPDATE SET (\"report\", \"updated_at\")=(:report, now()) RETURNING report;";
