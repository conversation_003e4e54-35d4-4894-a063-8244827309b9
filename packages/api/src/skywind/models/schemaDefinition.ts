import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { GameLimitsPermissionType, Schema, SchemaDefinition } from "../entities/schemaDefinition";

export interface SchemaDefinitionInstance extends Model<
        InferAttributes<SchemaDefinitionInstance>,
        InferCreationAttributes<SchemaDefinitionInstance>
    >,
    SchemaDefinition {
    get betTypes(): string[];
    toInfo(): SchemaDefinition;
    levelsSupported(): boolean;
    getBetTypeSchema(betType: string): Schema;
    getAJVSchemaId(name: string): string;
    getNameFromSchema(schemaId: string): any;
    getBetPropPermission(currency: string, betProp: string): GameLimitsPermissionType;
    getBetPropFromPermission(currency: string, gameLimitProperty: string): string;
}
export type SchemaDefinitionModel = ModelStatic<SchemaDefinitionInstance>;
const model: SchemaDefinitionModel = db.define<SchemaDefinitionInstance, SchemaDefinition>(
    "schema_definitions",
    {
        id: { field: "id", type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        name: { field: "name", type: DataTypes.STRING, allowNull: false },
        schema: { field: "schema", type: DataTypes.JSONB, allowNull: false },
        definitions: { field: "definitions", type: DataTypes.JSONB, allowNull: true },
        permissions: { field: "permissions", type: DataTypes.JSONB, allowNull: true },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
        betTypes: {
            type: DataTypes.VIRTUAL,
            get(): string[] {
                if (!this.levelsSupported()) {
                    return;
                }
                const bets = this.schema.properties?.bets?.properties;
                if (!bets) {
                    return;
                }
                return Object.keys(bets);
            }
        },
    },
    {
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        indexes: [
            {
                name: "idx_schema_definitions_name",
                fields: ["name"],
                unique: true
            }
        ],
        getterMethods: {

        }
    }
);
export function getSchemaDefinitionModel(): SchemaDefinitionModel {
    (model as any).prototype.toInfo = function(): SchemaDefinition {
        return {
            id: this.id,
            name: this.name,
            schema: this.schema,
            definitions: this.definitions,
            permissions: this.permissions,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };

    (model as any).prototype.levelsSupported = function(): boolean {
        return !!this.schema.levels;
    };

    (model as any).prototype.getBetTypeSchema = function(betType: string): Schema {
        if (!this.levelsSupported()) {
            return this.schema;
        }

        const value = this.schema.properties?.bets?.properties?.[betType];

        if (value["$ref"]) {
            return this.definitions[this.getNameFromSchema(value["$ref"])];
        }

        return value as Schema;
    };
    (model as any).prototype.getAJVSchemaId = function(name: string): string {
        return "/" + name.replace(/\s/, "");
    };
    (model as any).prototype.getNameFromSchema = function(schemaId: string) {
        return schemaId.substring(1);
    };

    (model as any).prototype.getBetPropPermission =
        function(currency: string, betProp: string): GameLimitsPermissionType {
        const key = `bets.${betProp}`;
        return this.permissions?.[currency]?.[key];
    };

    (model as any).prototype.getBetPropFromPermission = function(currency: string, gameLimitProperty: string): string {
        const [, betProp] = gameLimitProperty.split(".");

        return betProp;
    };
    return model;
}
