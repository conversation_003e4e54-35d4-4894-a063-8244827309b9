import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { getPromotionModel } from "./promotion";
import { sequelize as db } from "../storage/db";

const PromotionModel = getPromotionModel();

export interface BonusCoinRewardDBAttributes {
    id?: number;
    promoId: number;
    amount: number;
    redeemMinAmount: number;
    redeemMaxAmount: number;
    exchangeRates: any;
    games: string[];
    expirationPeriod?: number;
    expirationPeriodType?: string;
    insertedAt?: Date;
}

export interface BonusCoinRewardDBInstance extends Model<
        InferAttributes<BonusCoinRewardDBInstance>,
        InferCreationAttributes<BonusCoinRewardDBInstance>
    >,
    BonusCoinRewardDBAttributes {
}
export type IBonusCoinRewardModel = ModelStatic<BonusCoinRewardDBInstance>;
const PromoBonusCoinRewardModel: IBonusCoinRewardModel =
    db.define<BonusCoinRewardDBInstance, BonusCoinRewardDBAttributes>(
        "promotion_reward_bonus_coins",
        {
            id: { field: "id", type: DataTypes.BIGINT, allowNull: false, autoIncrement: true, primaryKey: true },
            promoId: {
                field: "promo_id", type: DataTypes.INTEGER, allowNull: false, references: {
                    model: PromotionModel,
                    key: "id",
                }
            },
            amount: { field: "amount", type: DataTypes.BIGINT, allowNull: false },
            redeemMinAmount: { field: "redeem_min_amount", type: DataTypes.BIGINT, allowNull: false },
            redeemMaxAmount: { field: "redeem_max_amount", type: DataTypes.BIGINT, allowNull: true },
            exchangeRates: { field: "exchange_rates", type: DataTypes.JSONB, allowNull: false },
            games: { field: "games", type: DataTypes.JSONB, allowNull: false },
            expirationPeriod: { field: "expiration_period", type: DataTypes.INTEGER, allowNull: false },
            expirationPeriodType: {
                field: "expiration_period_type",
                type: "enum_promotions_interval_type",
                allowNull: false,
            },
            insertedAt: {
                field: "inserted_at",
                type: DataTypes.DATE,
                allowNull: true,
                defaultValue: DataTypes.NOW,
            },
        },
        {
            timestamps: false,
            freezeTableName: true,
            indexes: [
                {
                    unique: false,
                    fields: ["inserted_at"],
                    name: "idx_promotion_reward_bonus_coins_inserted_at"
                },
                {
                    unique: false,
                    fields: ["promo_id"],
                    name: "idx_promotion_reward_bonus_coins_promo_id"
                }
            ]
        }
    );
PromoBonusCoinRewardModel.belongsTo(PromotionModel, { foreignKey: "promoId", targetKey: "id", onDelete: "CASCADE" });
PromotionModel.hasMany(PromoBonusCoinRewardModel, { as: "bonusCoinRewards", foreignKey: "promoId" });

export function getBonusCoinRewardModel(): IBonusCoinRewardModel {
    return PromoBonusCoinRewardModel;
}
