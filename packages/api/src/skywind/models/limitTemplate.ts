import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { Limits } from "../entities/gamegroup";
import { sequelize as db } from "../storage/db";

export interface LimitTemplateData {
    name: string;
    template: Limits;
}

export interface LimitTemplate extends LimitTemplateData {
    id?: number;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface LimitTemplateDB extends Model<
        InferAttributes<LimitTemplateDB>,
        InferCreationAttributes<LimitTemplateDB>
    >,
    LimitTemplate {
    toInfo(): LimitTemplate;
}
export type LimitTemplateModel = ModelStatic<LimitTemplateDB>;
const LimitTemplateDBModel: LimitTemplateModel = db.define<LimitTemplateDB, LimitTemplate>(
    "limit_templates",
    {
        id: { field: "id", type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        name: { field: "name", type: DataTypes.STRING, allowNull: false, unique: true },
        template: { field: "template", type: DataTypes.JSONB, allowNull: false },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        timestamps: true,
        freezeTableName: true
    }
);
export function getLimitTemplateDBModel(): LimitTemplateModel {
    (LimitTemplateDBModel as any).prototype.toInfo = function(): LimitTemplate {
        return {
            id: this.id,
            name: this.name,
            template: this.template,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
    return LimitTemplateDBModel;
}
