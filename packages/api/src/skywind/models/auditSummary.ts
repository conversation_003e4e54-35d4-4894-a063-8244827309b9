import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { ACTION_METHOD } from "../utils/common";
import { AuditSummary, AuditSummaryInfo } from "../entities/auditSummary";

export interface AuditSummaryDBInstance extends Model<
        InferAttributes<AuditSummaryDBInstance>,
        InferCreationAttributes<AuditSummaryDBInstance>
    >,
    AuditSummary {
    toInfo(): AuditSummaryInfo;
}
export type IAuditSummaryModel = ModelStatic<AuditSummaryDBInstance>;
const AuditSummaryModel = db.define<AuditSummaryDBInstance, AuditSummary>(
    "audits_summary",
    {
        id: { field: "id", type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        eventName: { field: "event_name", type: DataTypes.STRING, allowNull: false },
        summary: { field: "summary", type: DataTypes.STRING, allowNull: false },
        path: { field: "path", type: DataTypes.STRING, allowNull: false },
        method: {
            field: "method",
            type: DataTypes.ENUM(
                ACTION_METHOD.GET,
                ACTION_METHOD.POST,
                ACTION_METHOD.PUT,
                ACTION_METHOD.PATCH,
                ACTION_METHOD.DELETE,
                ACTION_METHOD.CRON,
                ACTION_METHOD.SERVICE),
            allowNull: false
        },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        underscored: true,
        tableName: "audits_summary",
        indexes: [
            { name: "audits_summary_type_code_key", unique: true, fields: [ "path", "method" ] },
        ],
    }
);
export function getAuditSummaryModel(): IAuditSummaryModel {
    (AuditSummaryModel as any).prototype.toInfo = function(): AuditSummaryInfo {
        return {
            id: this.id,
            eventName: this.eventName,
            summary: this.summary,
            path: this.path,
            method: this.method
        };
    };
    return AuditSummaryModel;
}
