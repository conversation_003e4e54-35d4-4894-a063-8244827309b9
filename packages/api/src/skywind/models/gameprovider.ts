import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { GameProvider } from "../entities/gameprovider";
import { sequelize as db } from "../storage/db";
import { NonFunctionProperties } from "../entities/typeUtils";

type GameProviderAttributes = NonFunctionProperties<GameProvider> & { version?: number; };
export interface GameProviderDBInstance extends Model<
        InferAttributes<GameProviderDBInstance>,
        InferCreationAttributes<GameProviderDBInstance>
    >,
    GameProviderAttributes {
}

const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    user: { type: DataTypes.STRING, validate: { notEmpty: true, is: /^[\w-]+$/ } },
    code: { type: DataTypes.STRING, unique: true, validate: { notEmpty: true, is: /^[\w-]+$/ } },
    title: { type: DataTypes.STRING, validate: { notEmpty: true } },
    secret: { type: DataTypes.STRING, allowNull: false, validate: { notEmpty: true } },
    status: { type: DataTypes.ENUM("normal", "suspended"), allowNull: false, validate: { notEmpty: true } },
    isTest: { type: DataTypes.BOOLEAN, allowNull: false, field: "is_test", defaultValue: false },
    version: DataTypes.INTEGER,
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
    },
    // boolean flag that claims that bet win history of this provider's game operations must be stored at our DB
    mustStoreExtHistory: { field: "must_store_ext_history", type: DataTypes.BOOLEAN, defaultValue: false }
};

export type IGameProviderModel = ModelStatic<GameProviderDBInstance>;
const model: IGameProviderModel = db.define<GameProviderDBInstance>(
    "gameProvider",
    schema,
    {
        tableName: "game_providers",
        underscored: true,
    });

export function get(): IGameProviderModel {
    return model;
}
