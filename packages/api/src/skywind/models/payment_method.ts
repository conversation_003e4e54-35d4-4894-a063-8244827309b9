import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { get as getEntityModel } from "./entity";
import { sequelize as db } from "../storage/db";

const EntityModel = getEntityModel();

export interface PaymentMethodAttributes {
    id: number;
    brandId: number;
    code: string;
    name: string;
    description: string;
    status: string;
    type?: any;
}

export interface PaymentMethodDBInstance extends Model<
        InferAttributes<PaymentMethodDBInstance>,
        InferCreationAttributes<PaymentMethodDBInstance>
    >,
    PaymentMethodAttributes {
}

const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    brandId: {
        type: DataTypes.INTEGER,
        field: "brand_id",
        allowNull: false,
        references: {
            model: EntityModel,
            key: "id",
        },
    },
    type: {
        type: DataTypes.JSONB,
        field: "operation_type",
        allowNull: false,
    },
    code: {
        type: DataTypes.STRING,
        field: "code",
        allowNull: false,
    },
    name: {
        type: DataTypes.STRING,
        field: "name",
        allowNull: false,
    },
    description: {
        type: DataTypes.STRING,
        field: "description",
        allowNull: true,
    },
    status: {
        type: DataTypes.ENUM("normal", "suspended"),
        field: "status",
        allowNull: false,
    },
};

export type IPaymentMethodModel = ModelStatic<PaymentMethodDBInstance>;
const model: IPaymentMethodModel =
    db.define<PaymentMethodDBInstance, PaymentMethodAttributes>(
        "payment_method",
        schema,
        {
            underscored: true,
            indexes: [
                { fields: ["brand_id"] },
                { name: "payment_method_brandId_code_key", unique: true, fields: ["brand_id", "code"] },
            ],
        }
    );

export function get(): IPaymentMethodModel {
    return model;
}
