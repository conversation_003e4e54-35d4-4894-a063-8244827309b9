import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { MerchantType } from "../entities/merchantType";

export interface MerchantTypeDBInstance extends Model<
        InferAttributes<MerchantTypeDBInstance>,
        InferCreationAttributes<MerchantTypeDBInstance>
    >,
    MerchantType {
}

const schema = {
    type: { type: DataTypes.STRING, primaryKey: true },
    url: { type: DataTypes.STRING, allowNull: true },
    mainDomainUrl: { type: DataTypes.STRING, allowNull: true, field: "main_domain_url" },
    schema: { type: DataTypes.JSONB, allowNull: false }
};

export type IMerchantTypeModel = ModelStatic<MerchantTypeDBInstance>;
const MerchantType: IMerchantTypeModel = db.define<MerchantTypeDBInstance, MerchantType>(
    "MerchantType",
    schema, {
        underscored: true,
        tableName: "merchant_types"
    }
);

export function getMerchantTypeModel(): IMerchantTypeModel {
    return MerchantType;
}
