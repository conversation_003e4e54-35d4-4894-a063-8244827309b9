import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    BindOrReplacements,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { get as getEntityModel } from "./entity";
import { STATUS_NORMAL, STATUS_SUSPENDED } from "../entities/payment";

const EntityModel = getEntityModel();

export interface BlockedMerchantPlayer {
    code: string;
    brandId: number;
    isBlocked: boolean;
    status?: string;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface BlockedMerchantPlayerDBInstance extends Model<
        InferAttributes<BlockedMerchantPlayerDBInstance>,
        InferCreationAttributes<BlockedMerchantPlayerDBInstance>
    >,
    BlockedMerchantPlayer {
    get status(): string;
}
export type IBlockedMerchantPlayerModel = ModelStatic<BlockedMerchantPlayerDBInstance>;
const blockedMerchantPlayerModel: IBlockedMerchantPlayerModel =
    db.define<BlockedMerchantPlayerDBInstance, BlockedMerchantPlayer>(
        "merchant_blocked_players",
        {
            brandId: {
                field: "brand_id",
                type: DataTypes.INTEGER,
                primaryKey: true,
                references: {
                    model: EntityModel,
                    key: "id",
                }
            },
            code: { field: "code", type: DataTypes.STRING, primaryKey: true },
            isBlocked: { field: "is_blocked", type: DataTypes.BOOLEAN, allowNull: false, defaultValue: true },
            createdAt: { field: "created_at", type: DataTypes.DATE },
            updatedAt: { field: "updated_at", type: DataTypes.DATE },
        },
        {
            timestamps: true,
            freezeTableName: true,
            getterMethods: {
                status(): string {
                    return this.isBlocked ? STATUS_SUSPENDED : STATUS_NORMAL;
                }
            }
        }
    );
export function getBlockedMerchantPlayer(): IBlockedMerchantPlayerModel {
    return blockedMerchantPlayerModel;
}

export async function upsertMerchantBlockedPlayer(data: BlockedMerchantPlayer & BindOrReplacements,
                                                  transaction?): Promise<boolean> {
    const rs = await db.query(UPSERT_MERCHANT_BLOCKED_PLAYER, { replacements: data, transaction });
    if (rs.length) {
        return true;
    }
    return false;
}

const UPSERT_MERCHANT_BLOCKED_PLAYER = "INSERT INTO merchant_blocked_players as item " +
    "(\"brand_id\", \"code\", \"is_blocked\", \"created_at\", \"updated_at\") " +
    "VALUES (:brandId, :code, :isBlocked, now(), now()) " +
    "ON CONFLICT(brand_id, code) " +
    "DO UPDATE SET (\"is_blocked\", \"updated_at\")=(:isBlocked, now()) RETURNING is_blocked;";
