import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { get as getPlayerModel } from "./player";
import { sequelize as db } from "../storage/db";

const PlayerModel = getPlayerModel();

export interface ResetPasswordAttributes {
    id?: number;
    guid?: string;
    playerId?: number;
    expiredAt?: Date;
}

export interface ResetPasswordDBInstance extends Model<
        InferAttributes<ResetPasswordDBInstance>,
        InferCreationAttributes<ResetPasswordDBInstance>
    >,
    ResetPasswordAttributes {
}

const schema = {
    id: {
        type: DataTypes.INTEGER,
        field: "id",
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
    },
    guid: {
        type: DataTypes.UUID,
        field: "guid",
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
    },
    playerId: {
        type: DataTypes.INTEGER,
        field: "player_id",
        allowNull: false,
        references: {
            model: PlayerModel,
            key: "id",
        },
    },
    expiredAt: {
        type: DataTypes.DATE,
        field: "expired_at",
        allowNull: false,
    },
};

type IResetPasswordModel = ModelStatic<ResetPasswordDBInstance>;
const model: IResetPasswordModel = db.define<ResetPasswordDBInstance, ResetPasswordAttributes>(
    "player_password_reset",
    schema,
    {
        indexes: [
            { fields: ["player_id"] },
        ],
        createdAt: false,
        updatedAt: false,
    }
);

model.belongsTo(PlayerModel, { foreignKey: "playerId" });

export function get(): IResetPasswordModel {
    return model;
}
