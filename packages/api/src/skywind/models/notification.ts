import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
    ForeignKey,
} from "sequelize";
import { UserModel } from "./user";
import { sequelize as db } from "../storage/db";
import { NotificationInfo } from "../entities/notification";

export class NotificationModel extends Model<
    InferAttributes<NotificationModel>,
    InferCreationAttributes<NotificationModel>
> {
    declare id: CreationOptional<number>;
    declare message: string;
    declare author: ForeignKey<UserModel["id"]>;
    declare ts: Date;
    declare status: string;
    declare lifetime: Date;

    public toInfo(): NotificationInfo {
        return {
            message: this.message,
            id: this.id,
            ts: this.ts,
            status: this.status,
        };
    }
}

const schema = {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: "Id of message, it automatically generates by Seguelize",
    },
    message: {
        type: DataTypes.STRING(1024),
        field: "message",
        allowNull: true,
        comment: "Raw text of message. No formatted yet",
    },
    author: {
        type: DataTypes.INTEGER,
        field: "author",
        allowNull: false,
        references: {
            model: UserModel,
            key: "id",
        },
        comment: "Id of user who sent notification",
    },
    ts: {
        type: DataTypes.DATE,
        field: "ts",
        comment: "Time when notification created",
    },
    status: {
        type: DataTypes.ENUM("normal", "suspended"),
        field: "status",
        defaultValue: "normal",
        allowNull: false,
        validate: { notEmpty: true },
        comment: "Notification status: normal or suspended",
    },
    lifetime: {
        type: DataTypes.DATE,
        field: "lifetime",
        comment: "Time when notification become outdated",

    },
};

NotificationModel.init(
    schema,
    {
        tableName: "notifications",
        modelName: "notification",
        sequelize: db,
        indexes: [
            { fields: ["id"] },
        ],
        createdAt: false,
        updatedAt: false,
    }
);

export function get() {
    return NotificationModel;
}
