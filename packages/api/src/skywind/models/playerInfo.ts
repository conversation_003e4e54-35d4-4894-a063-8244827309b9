import {
    Association,
    CreationOptional,
    DataTypes,
    ForeignKey,
    InferAttributes,
    InferCreationAttributes,
    Model,
    NonAttribute,
} from "sequelize";
import { NICKNAME_MAX_LENGTH, NICKNAME_MIN_LENGTH } from "../utils/common";
import { EntityModel } from "./entity";
import { sequelize as db } from "../storage/db";
import { EntityCountryRestrictions } from "../entities/settings";
import { PlayerInfo } from "../entities/playerInfo";

export class PlayerInfoModel extends Model<
    InferAttributes<PlayerInfoModel, { omit: "entity" }>,
    InferCreationAttributes<PlayerInfoModel, { omit: "entity" }>
> {
    declare brandId: ForeignKey<EntityModel["id"]>;
    declare playerCode: string;
    declare nickname: string;
    declare isVip: boolean;
    declare isTracked: boolean;
    declare isPublicChatBlock: boolean;
    declare isPrivateChatBlock: boolean;
    declare isMerchantPlayer: boolean;
    declare hasWarn: boolean;
    declare nicknameChangeAttempts: number;
    declare restrictedIpCountries: EntityCountryRestrictions;
    declare noBetNoChat: boolean; // flag which means auto-disable chat using special rules from live games
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    declare entity?: NonAttribute<EntityModel>;

    declare static associations: {
        entity: Association<PlayerInfoModel, EntityModel>;
    };

    toInfo(isNeedRealBrandId?: boolean): PlayerInfo {
        return {
            brandId: isNeedRealBrandId ? `${this.brandId}` : this.brandId,
            playerCode: this.playerCode,
            isMerchantPlayer: this.isMerchantPlayer,
            isVip: this.isVip,
            isPublicChatBlock: this.isPublicChatBlock,
            isPrivateChatBlock: this.isPrivateChatBlock,
            isTracked: this.isTracked,
            nickname: this.nickname,
            hasWarn: this.hasWarn,
            nicknameChangeAttempts: this.nicknameChangeAttempts,
            restrictedIpCountries: this.restrictedIpCountries,
            noBetNoChat: this.noBetNoChat
        };
    }
}

PlayerInfoModel.init(
    {
        brandId: {
            field: "brand_id",
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: EntityModel,
                key: "id",
            },
        },
        playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: false },
        nickname: {
            field: "nickname",
            type: DataTypes.STRING,
            allowNull: true,
            validate: { len: [NICKNAME_MIN_LENGTH, NICKNAME_MAX_LENGTH] }
        },
        isVip: { field: "is_vip", type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
        createdAt: { field: "created_at", type: DataTypes.DATE, },
        updatedAt: { field: "updated_at", type: DataTypes.DATE, },
        isTracked: {
            field: "is_tracked",
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
        isPublicChatBlock: {
            field: "is_public_chat_block",
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
        isPrivateChatBlock: {
            field: "is_private_chat_block",
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
        isMerchantPlayer: {
            field: "is_merchant_player",
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
        hasWarn: {
            field: "has_warn",
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
        nicknameChangeAttempts: {
            field: "nickname_change_attempts",
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0
        },
        restrictedIpCountries: {
            field: "restricted_ip_countries",
            type: DataTypes.JSONB,
            allowNull: true
        },
        noBetNoChat: {
            field: "no_bet_no_chat",
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
    },
    {
        sequelize: db,
        tableName: "players_info",
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        indexes: [
            {
                name: "idx_player_info_nickname_brand_id_player_code",
                fields: ["brand_id", "player_code"],
                unique: true
            },
            {
                name: "idx_player_info_brand_id_nickname",
                fields: ["brand_id", "nickname"],
                unique: false
            },
            {
                name: "idx_player_info_nickname_brand_id",
                fields: ["brand_id"]
            },
            {
                name: "idx_player_info_player_code",
                fields: ["player_code"]
            }
        ]
    }
);
PlayerInfoModel.belongsTo(EntityModel, { foreignKey: "brandId" });

export function getPlayerInfoModel() {
    return PlayerInfoModel;
}
