import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { AuditSession } from "../entities/auditSession";

export interface AuditSessionDBInstance extends Model<
        InferAttributes<AuditSessionDBInstance>,
        InferCreationAttributes<AuditSessionDBInstance>
    >,
    AuditSession {
    toInfo(): AuditSession;
}
const schema = {
    id: { field: "id", type: DataTypes.UUID, autoIncrement: false, primaryKey: true },
    initiatorName: { field: "initiator_name", type: DataTypes.STRING, allowNull: false },
    entityId: { field: "entity_id", type: DataTypes.INTEGER, allowNull: false },
    startedAt: {
        field: "started_at",
        type: DataTypes.DATE,
    },
    finishedAt: {
        field: "finished_at",
        type: DataTypes.DATE,
    },
    createdAt: {
        field: "created_at",
        type: DataTypes.DATE,
    },
    updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE,
    },
};
export type AuditSessionModel = ModelStatic<AuditSessionDBInstance>;
const auditSessionModel: AuditSessionModel = db.define<AuditSessionDBInstance, AuditSession>(
    "audits_session",
    schema,
    {
        underscored: true,
        tableName: "audits_session",
    }
);
export function getAuditSessionModel(): AuditSessionModel {
    (auditSessionModel as any).prototype.toInfo = function() {
        return {
            id: this.id,
            initiatorName: this.initiatorName,
            entityId: this.entityId,
            startedAt: this.startedAt,
            finishedAt: this.finishedAt
        };
    };
    return auditSessionModel;
}

export interface LoginAuditSessionDBInstance extends Model<
        InferAttributes<LoginAuditSessionDBInstance>,
        InferCreationAttributes<LoginAuditSessionDBInstance>
    >,
    AuditSession {
}
export type LoginAuditSessionModel = ModelStatic<LoginAuditSessionDBInstance>;
const loginAuditSessionModel: LoginAuditSessionModel = db.define<LoginAuditSessionDBInstance, AuditSession>(
    "audits_login_session",
    {
        id: { field: "id", type: DataTypes.UUID, autoIncrement: false, primaryKey: true },
        initiatorName: { field: "initiator_name", type: DataTypes.STRING, allowNull: false },
        entityId: { field: "entity_id", type: DataTypes.INTEGER, allowNull: false },
        startedAt: { field: "started_at", type: DataTypes.DATE },
        finishedAt: { field: "finished_at", type: DataTypes.DATE },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        underscored: true,
        tableName: "audits_login_session",
    }
);

export function getLoginAuditSessionModel(): LoginAuditSessionModel {
    return loginAuditSessionModel;
}
