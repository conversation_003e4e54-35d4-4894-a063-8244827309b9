import { Model, ModelStatic, DataTypes, InferAttributes, InferCreationAttributes } from "sequelize";
import { sequelize as db } from "../storage/db";
import { getLimitLevelModel, LimitLevel, LimitLevelDBInstance } from "./limitLevels";
import { getSchemaDefinitionModel } from "./schemaDefinition";
import { BaseEntity } from "../entities/entity";
import { get as getEntityModel } from "./entity";

const LimitLevelModel = getLimitLevelModel();
const EntityModel = getEntityModel();
const SchemaDefinitionModel = getSchemaDefinitionModel();

export interface EntityGameLimitLevel {
    id?: number;
    levelId: number;
    currency?: string;
    isDefault?: boolean;
    hidden?: boolean;
    inherited?: boolean;
    schemaConfigurationId?: number;
    schemaDefinitionId?: number;

    entityId?: number;
    gameCode?: string;
    level?: LimitLevel;
    owned?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface EntityGameLimitLevelDBInstance extends Model<
        InferAttributes<EntityGameLimitLevelDBInstance>,
        InferCreationAttributes<EntityGameLimitLevelDBInstance>
    >,
    EntityGameLimitLevel {
    limit_level: LimitLevelDBInstance;
    toInfo(entity?: BaseEntity): EntityGameLimitLevel;
}
export type EntityLimitLevelModel = ModelStatic<EntityGameLimitLevelDBInstance>;
const model: EntityLimitLevelModel = db.define<EntityGameLimitLevelDBInstance, EntityGameLimitLevel>(
    "entity_game_limit_levels",
    {
        id: { field: "id", type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        levelId: {
            field: "level_id",
            type: DataTypes.INTEGER,
            allowNull: false,
            references: { model: LimitLevelModel, key: "id" }
        },
        gameCode: {
            field: "game_code",
            allowNull: true,
            type: DataTypes.STRING,
        },
        entityId: {
            field: "entity_id",
            allowNull: false,
            type: DataTypes.INTEGER,
            references: { model: EntityModel, key: "id" }
        },
        schemaDefinitionId: {
            field: "schema_definition_id",
            allowNull: true,
            type: DataTypes.INTEGER,
            references: { model: SchemaDefinitionModel, key: "id" }
        },
        currency: { field: "currency", type: DataTypes.STRING, allowNull: true },
        isDefault: { field: "is_default", type: DataTypes.BOOLEAN, defaultValue: false },
        hidden: { field: "hidden", type: DataTypes.BOOLEAN, defaultValue: false },
        inherited: { field: "inherited", type: DataTypes.BOOLEAN, defaultValue: false },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        timestamps: true,
        freezeTableName: true
    }
);
model.belongsTo(LimitLevelModel, { foreignKey: "level_id" });
model.belongsTo(EntityModel, { foreignKey: "entity_id" });
model.belongsTo(SchemaDefinitionModel, { foreignKey: "schema_definition_id" });

LimitLevelModel.hasMany(model, { foreignKey: "level_id"});

export function getEntityLimitLevelModel(): EntityLimitLevelModel {
    (model as any).prototype.toInfo = function(entity?: BaseEntity): EntityGameLimitLevel {
        const info: EntityGameLimitLevel = {
            id: this.id,
            levelId: this.levelId,
            entityId: this.entityId,
            gameCode: this.gameCode,
            isDefault: this.isDefault || false,
            hidden: this.hidden || false,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };

        if (this.limit_level) {
            info.level = this.limit_level.toInfo();
        }

        if (entity) {
            info.owned = entity.id === this.entityId;
        }

        if (this.currency) {
            info.currency = this.currency;
        }

        if (this.inherited) {
            info.inherited = this.inherited;
        }

        return info;
    };
    return model;
}
