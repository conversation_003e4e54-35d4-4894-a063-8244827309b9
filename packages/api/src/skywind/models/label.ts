import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { getGameModel } from "./game";
import { getPromotionModel } from "./promotion";
import { get as getEntityModel } from "./entity";
import { getLabelGroupModel } from "./labelGroup";
import { GameLabel, Label } from "../entities/label";
import { sequelize as db } from "../storage/db";

const LabelGroupModel = getLabelGroupModel();
const GameModel = getGameModel();
const PromotionModel = getPromotionModel();
const EntityModel = getEntityModel();

export interface LabelDBInstance extends Model<
        InferAttributes<LabelDBInstance>,
        InferCreationAttributes<LabelDBInstance>
    >,
    Label {
}

const labelSchema = {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    },
    title: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: { notEmpty: true },
        comment: "Label title",
    },
    groupId: {
        type: DataTypes.INTEGER,
        field: "group_id",
        references: {
            model: LabelGroupModel,
            key: "id",
        },
        allowNull: false,
        validate: { notEmpty: true },
        comment: "Label groupId",
    },
};

export type ILabelModel = ModelStatic<LabelDBInstance>;
const LabelModel: ILabelModel = db.define<LabelDBInstance, Label>(
    "label",
    labelSchema,
    {
        underscored: true,
        timestamps: true,
        indexes: [
            {
                name: "labels_title_groupid_ukey",
                unique: true,
                fields: ["title", "group_id"],
            }
        ],
    }
);

LabelModel.belongsTo(LabelGroupModel, { foreignKey: "groupId", as: "group" });

export function getLabelModel(): ILabelModel {
    return LabelModel;
}

export interface GameLabelDBInstance extends Model<
        InferAttributes<GameLabelDBInstance>,
        InferCreationAttributes<GameLabelDBInstance>
    >,
    GameLabel {
}

const gameLabelSchema = {
    gameId: {
        type: DataTypes.INTEGER,
        field: "game_id",
        primaryKey: true,
        references: {
            model: GameModel,
            key: "id",
        },
    },
    labelId: {
        type: DataTypes.INTEGER,
        field: "label_id",
        primaryKey: true,
        references: {
            model: LabelModel,
            key: "id",
        },
    },
};

export type IGameLabelModel = ModelStatic<GameLabelDBInstance>;
const GameLabelModel: IGameLabelModel = db.define<GameLabelDBInstance, GameLabel>(
    "gameLabel",
    gameLabelSchema,
    {
        underscored: true,
        timestamps: false,
        tableName: "game_labels",
    }
);

LabelModel.belongsToMany(GameModel, { through: GameLabelModel });
GameModel.belongsToMany(LabelModel, { through: GameLabelModel });

export function getGameLabelModel(): IGameLabelModel {
    return GameLabelModel;
}

export interface PromoLabelDBAttributes {
    labelId: number;
    promoId: number;
}

export interface PromotionLabelDBInstance extends Model<
        InferAttributes<PromotionLabelDBInstance>,
        InferCreationAttributes<PromotionLabelDBInstance>
    >,
    PromoLabelDBAttributes {
}
export type IPromotionLabelModel = ModelStatic<PromotionLabelDBInstance>;
const PromotionLabelModel: IPromotionLabelModel = db.define<PromotionLabelDBInstance, PromoLabelDBAttributes>(
    "promotion_labels",
    {
        labelId: {
            field: "label_id",
            type: DataTypes.BIGINT,
            allowNull: false,
            primaryKey: true,
            references: {
                model: LabelModel,
                key: "id"
            }
        },
        promoId: {
            field: "promo_id",
            type: DataTypes.INTEGER,
            allowNull: false,
            primaryKey: true,
            references: {
                model: PromotionModel,
                key: "id"
            }
        },
    },
    {
        timestamps: false,
        freezeTableName: true,
    }
);

LabelModel.belongsToMany(PromotionModel,
    { through: PromotionLabelModel, foreignKey: "labelId", as: "labelPromotions" });
PromotionModel.belongsToMany(LabelModel,
    { through: PromotionLabelModel, as: "promotionLabels", foreignKey: "promoId" });

export function getPromotionLabelModel(): IPromotionLabelModel {
    return PromotionLabelModel;
}

export interface EntityLabelDBAttributes {
    labelId: number;
    entityId: number;
}

export interface EntityLabelDBInstance extends Model<
        InferAttributes<EntityLabelDBInstance>,
        InferCreationAttributes<EntityLabelDBInstance>
    >,
    EntityLabelDBAttributes {
}
export type IEntityLabelModel = ModelStatic<EntityLabelDBInstance>;
const EntityLabelModel = db.define<EntityLabelDBInstance, EntityLabelDBAttributes>(
    "entity_labels",
    {
        labelId: {
            field: "label_id",
            type: DataTypes.BIGINT,
            allowNull: false,
            primaryKey: true,
            references: {
                model: LabelModel,
                key: "id"
            },
        },
        entityId: {
            field: "entity_id",
            type: DataTypes.INTEGER,
            allowNull: false,
            primaryKey: true,
            references: {
                model: EntityModel,
                key: "id"
            },
        },
    },
    {
        timestamps: false,
        freezeTableName: true,
    }
);

LabelModel.belongsToMany(EntityModel,
    { through: EntityLabelModel, foreignKey: "labelId", as: "labelEntities" });
EntityModel.belongsToMany(LabelModel,
    { through: EntityLabelModel, as: "entityLabels", foreignKey: "entityId" });

export function getEntityLabelModel(): IEntityLabelModel {
    return EntityLabelModel;
}
