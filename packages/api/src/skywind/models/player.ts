import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    Sequelize,
} from "sequelize";
import { get as getEntityModel } from "./entity";
import { get as getAgentModel } from "./agent";
import { sequelize as db } from "../storage/db";
import { get as getGameGroupModel } from "./gamegroup";
import { COMMENT_MAX_LENGTH, NICKNAME_MAX_LENGTH, NICKNAME_MIN_LENGTH } from "../utils/common";
import { PlayerAttributes, SELF_EXCLUSION_COUNT_DB_FIELD } from "../entities/player";

const EntityModel = getEntityModel();
const AgentModel = getAgentModel();
const GameGroupModel = getGameGroupModel();

export interface PlayerDBInstance extends Model<
        InferAttributes<PlayerDBInstance>,
        InferCreationAttributes<PlayerDBInstance>
    >,
    PlayerAttributes {
}

function defineModel(connectInstance: Sequelize) {
    return connectInstance.define<PlayerDBInstance, PlayerAttributes>(
        "player",
        {
            id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
            brandId: {
                type: DataTypes.INTEGER,
                field: "brand_id",
                allowNull: false,
                references: {
                    model: EntityModel,
                    key: "id",
                },
            },
            agentId: {
                type: DataTypes.INTEGER,
                field: "agent_id",
                allowNull: true,
                references: {
                    model: AgentModel,
                    key: "id",
                },
            },
            gamegroupId: {
                type: DataTypes.INTEGER,
                field: "gamegroupId",
                allowNull: true,
                references: {
                    model: GameGroupModel,
                    key: "id",
                },
            },
            code: {
                type: DataTypes.STRING,
                field: "code",
                allowNull: false,
                validate: {
                    notEmpty: true
                },
            },
            status: {
                type: DataTypes.ENUM("normal", "suspended"),
                field: "status",
                allowNull: false,
                validate: { notEmpty: true },
            },
            firstName: { type: DataTypes.STRING, field: "first_name", allowNull: true },
            lastName: { type: DataTypes.STRING, field: "last_name", allowNull: true },
            nickname: { type: DataTypes.STRING, field: "nickname", allowNull: true,
                validate: { len: [NICKNAME_MIN_LENGTH, NICKNAME_MAX_LENGTH] } },
            email: {
                type: DataTypes.STRING,
                allowNull: true,
                validate: { notEmpty: true, isEmail: true },
            },
            password: { type: DataTypes.STRING, field: "password", allowNull: true },
            salt: { type: DataTypes.STRING, field: "salt", allowNull: true },
            isPasswordTemp: { type: DataTypes.BOOLEAN, allowNull: true, field: "is_password_temp" },
            currency: { type: DataTypes.STRING, field: "currency", allowNull: false, validate: { notEmpty: true } },
            country: { type: DataTypes.STRING, field: "country", allowNull: false, validate: { notEmpty: true } },
            language: { type: DataTypes.STRING, field: "language", allowNull: false, validate: { notEmpty: true } },
            isTest: { type: DataTypes.BOOLEAN, allowNull: false, field: "is_test", defaultValue: false },
            customData: { type: DataTypes.JSONB, field: "custom_data" },
            selfExclusionCount: { type: DataTypes.INTEGER, field: SELF_EXCLUSION_COUNT_DB_FIELD, defaultValue: 0 },
            comments: { type: DataTypes.TEXT, validate: { len: [0, COMMENT_MAX_LENGTH] } },
            lastLogin: {
                type: DataTypes.DATE,
                allowNull: true,
                field: "last_login",
            },
            createdAt: {
                type: DataTypes.DATE,
                field: "created_at",
            },
            updatedAt: {
                type: DataTypes.DATE,
                field: "updated_at",
            },
            deactivatedAt: {
                type: DataTypes.DATE,
                field: "deactivated_at",
                allowNull: true
            },
            isVip: { type: DataTypes.BOOLEAN, allowNull: false, field: "is_vip", defaultValue: false },
        },
        {
            defaultScope: {
                attributes: { exclude: ["comments"] }
            },
            indexes: [
                { fields: ["brand_id"] },
                { fields: ["agent_id"] },
                { unique: true, fields: ["brand_id", "code"] },
                { unique: true, fields: ["brand_id", "email"] },
                { unique: false, fields: ["brand_id", "nickname"] },
            ],
        }
    );
}

function belongsTo(instance: ModelStatic<PlayerDBInstance>) {
    instance.belongsTo(GameGroupModel);
    instance.belongsTo(EntityModel, { foreignKey: "brandId" });
    instance.belongsTo(AgentModel, { foreignKey: "agentId" });
}

export type IPlayerModel = ModelStatic<PlayerDBInstance>;
const model: IPlayerModel = defineModel(db);
belongsTo(model);

export function get(): IPlayerModel {
    return model;
}
