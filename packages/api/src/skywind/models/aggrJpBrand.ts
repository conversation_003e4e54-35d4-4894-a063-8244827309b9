import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelizeJpSlave as db } from "../storage/db";

export interface AggrJpBrandContributions {
    dateHour: Date;
    brandId: number;
    gameCode: string;
    currency: string;
    seedAmount: number;
    progressiveAmount: number;
    totalBetAmount: number;
    jpWinAmount: number;
    totalBetCount: number;
    jpWinCount: number;
    firstActivity: Date;
    lastActivity: Date;
    seedWin: number;
    progressiveWin: number;
}

export interface AggrJpBrandContributionsDBInstance extends Model<
        InferAttributes<AggrJpBrandContributionsDBInstance>,
        InferCreationAttributes<AggrJpBrandContributionsDBInstance>
    >,
    AggrJpBrandContributions {
}

const schema = {
    dateHour: {
        field: "date_hour",
        type: DataTypes.DATE,
        allowNull: false,
        primaryKey: true,
    },
    brandId: { field: "brand_id", type: DataTypes.INTEGER, allowNull: false, primaryKey: true },
    gameCode: { field: "game_code", type: DataTypes.STRING, allowNull: false, primaryKey: true },
    currency: { field: "currency_code", type: DataTypes.CHAR(3), allowNull: false },
    seedAmount: { field: "seed_amount_jp_cur", type: DataTypes.DECIMAL, allowNull: false },
    progressiveAmount: { field: "progressive_amount_jp_cur", type: DataTypes.DECIMAL, allowNull: false },
    totalBetAmount: { field: "total_bet_amount_jp_cur", type: DataTypes.DECIMAL, allowNull: false },
    jpWinAmount: { field: "jp_win_amount_jp_cur", type: DataTypes.DECIMAL, allowNull: false },
    totalBetCount: { field: "total_bet_count", type: DataTypes.DECIMAL, allowNull: false },
    jpWinCount: { field: "jp_win_count", type: DataTypes.DECIMAL, allowNull: false },
    firstActivity: {
        field: "first_activity",
        type: DataTypes.DATE,
    },
    lastActivity: {
        field: "last_activity",
        type: DataTypes.DATE,
    },
    seedWin: { field: "seed_win", type: DataTypes.DECIMAL, allowNull: true, defaultValue: 0 },
    progressiveWin: { field: "progressive_win", type: DataTypes.DECIMAL, allowNull: true, defaultValue: 0 }
};
export type IAggrJpBrandContributionsModel = ModelStatic<AggrJpBrandContributionsDBInstance>;
const model: IAggrJpBrandContributionsModel = db.define<AggrJpBrandContributionsDBInstance, AggrJpBrandContributions>(
    "bo_aggr_jp_brand_contributions",
    schema,
    {
        freezeTableName: true,
        timestamps: false,
    }
);

export function getAggrJpBrandContributionsModel() {
    return model;
}
