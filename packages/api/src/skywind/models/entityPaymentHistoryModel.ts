import { Model, ModelStatic, DataTypes, InferAttributes, InferCreationAttributes, literal } from "sequelize";
import { sequelize as db } from "../storage/db";

export const TRANSFER_TYPE = {
    ENTITY_TO_ENTITY: "ent-ent",
    ENTITY_TO_PLAYER: "ent-plr",
    PLAYER_TO_ENTITY: "plr-ent",
    ENTITY_TO_PLAYER_EXT: "ent-plr_ext",
    PLAYER_TO_ENTITY_EXT: "plr-ent_ext"
};

export interface EntityPaymentHistory {
    id?: number;
    from: string;
    to: string;
    amount: number;
    currency: string;
    ts: Date;
    transferType: string;
    isTest: boolean;
    initiatorName?: string;
    insertedAt?: Date;
}

export interface EntityPaymentModelDBInstance extends Model<
        InferAttributes<EntityPaymentModelDBInstance>,
        InferCreationAttributes<EntityPaymentModelDBInstance>
    >,
    EntityPaymentHistory {
}
export type IEntityPaymentModel = ModelStatic<EntityPaymentModelDBInstance>;
const entityPaymentModel: IEntityPaymentModel = db.define<EntityPaymentModelDBInstance, EntityPaymentHistory>(
    "wallet_entity_payment_log",
    {
        id: { field: "id", type: DataTypes.BIGINT, autoIncrement: true, primaryKey: true },
        from: { field: "transfer_from", type: DataTypes.STRING },
        to: { field: "transfer_to", type: DataTypes.STRING },
        amount: { field: "amount", type: DataTypes.DECIMAL, allowNull: false, defaultValue: 0 },
        currency: { field: "currency", type: DataTypes.CHAR(3), allowNull: false },
        ts: { field: "ts", type: DataTypes.DATE, allowNull: false },
        transferType: {
            field: "transfer_type",
            type: DataTypes.ENUM(TRANSFER_TYPE.ENTITY_TO_ENTITY,
                TRANSFER_TYPE.ENTITY_TO_PLAYER,
                TRANSFER_TYPE.PLAYER_TO_ENTITY,
                TRANSFER_TYPE.ENTITY_TO_PLAYER_EXT,
                TRANSFER_TYPE.PLAYER_TO_ENTITY_EXT),
            allowNull: false
        },

        isTest: { field: "is_test", type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
        insertedAt: {
            field: "inserted_at",
            type: DataTypes.DATE,
            defaultValue: literal("NOW()")
        },
        initiatorName: { type: DataTypes.STRING, field: "initiator_name", },
    },
    {
        timestamps: false,
        freezeTableName: true,
        underscored: true,
        indexes: [
            {
                unique: false,
                fields: ["inserted_at"],
                name: "idx_wallet_entity_payment_log_inserted_at"
            },
            {
                unique: false,
                fields: ["ts"],
                name: "idx_wallet_entity_payment_log_ts"
            }
        ]
    }
);
export function getEntityPaymentHistoryModel(): IEntityPaymentModel {
    return entityPaymentModel;
}
