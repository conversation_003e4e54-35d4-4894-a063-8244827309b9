import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    Association,
} from "sequelize";
import { Audit, AUDIT_INITIATOR } from "../entities/audit";
import { EntityModel as EntityDBInstance, get as getEntityModel } from "./entity";
import { sequelize as db, sequelizeSlave } from "../storage/db";
import { AuditSummaryDBInstance, getAuditSummaryModel } from "./auditSummary";
import { AuditSessionDBInstance, getAuditSessionModel, getLoginAuditSessionModel } from "./auditSession";

const EntityModel = getEntityModel();
const AuditSummaryModel = getAuditSummaryModel();
const AuditSessionModel = getAuditSessionModel();
const LoginAuditSessionModel = getLoginAuditSessionModel();

export interface AuditDBInstance extends Model<
        InferAttributes<AuditDBInstance>,
        InferCreationAttributes<AuditDBInstance>
    >,
    Audit {
    associations: {
        entity: Association<AuditDBInstance, EntityDBInstance>;
        auditsSummary: Association<AuditDBInstance, AuditSummaryDBInstance>;
        auditsSession: Association<AuditDBInstance, AuditSessionDBInstance>;
    };
}

const schema = {
    auditId: {
        type: DataTypes.INTEGER,
        field: "audit_id",
        autoIncrement: true,
        primaryKey: true,
    },
    entityId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        field: "entity_id",
        references: {
            model: EntityModel,
            key: "id",
        },
    },
    ts: {
        type: DataTypes.DATE,
        allowNull: false,
        field: "ts",
    },
    history: {
        type: DataTypes.JSONB,
        allowNull: true,
        field: "history",
    },
    initiatorType: {
        type: DataTypes.ENUM(AUDIT_INITIATOR.USER, AUDIT_INITIATOR.PLAYER, AUDIT_INITIATOR.SYSTEM),
        allowNull: false,
        field: "initiator_type",
    },
    initiatorName: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "initiator_name",
        validate: { notEmpty: true, is: /^[\w-:@.,\d]+$/ },
    },
    ip: {
        type: DataTypes.INET,
        allowNull: false,
        field: "ip",
    },
    userAgent: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "user_agent",
    },
    initiatorServiceName: {
        type: DataTypes.STRING,
        field: "initiator_service_name",
        allowNull: true
    },
    initiatorIssueId: {
        type: DataTypes.STRING,
        field: "initiator_issue_id",
        allowNull: true
    },
    auditsSummaryId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        field: "audits_summary_id"
    },
    auditsSessionId: {
        type: DataTypes.UUID,
        allowNull: true,
        field: "audits_session_id"
    }
};

export type IAuditModel = ModelStatic<AuditDBInstance>;
const AuditModel: IAuditModel = db.define<AuditDBInstance, Audit>(
    "audit",
    schema,
    {
        underscored: true,
        timestamps: false,
    }
);

AuditModel.belongsTo(EntityModel, { foreignKey: "entityId" });
AuditModel.belongsTo(AuditSummaryModel, { foreignKey: "auditsSummaryId", as: "auditsSummary" });
AuditModel.belongsTo(AuditSessionModel, { foreignKey: "auditsSessionId", as: "auditsSession" });

export function get(): IAuditModel {
    return AuditModel;
}

const AuditSlaveModel: IAuditModel = sequelizeSlave.define<AuditDBInstance, Audit>(
    "audit",
    schema,
    {
        underscored: true,
        timestamps: false,
    }
);

export function getAuditSlaveModel(): IAuditModel {
    return AuditSlaveModel;
}

const LoginAuditModel: IAuditModel = db.define<AuditDBInstance, Audit>(
    "audits_login",
    schema,
    {
        underscored: true,
        timestamps: false,
        freezeTableName: true
    }
);

LoginAuditModel.belongsTo(EntityModel, { foreignKey: "entityId" });
LoginAuditModel.belongsTo(AuditSummaryModel, { foreignKey: "auditsSummaryId", as: "auditsSummary" });
LoginAuditModel.belongsTo(LoginAuditSessionModel, { foreignKey: "auditsSessionId" });

export function getLoginAuditModel(): IAuditModel {
    return LoginAuditModel;
}
