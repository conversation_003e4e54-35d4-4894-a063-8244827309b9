import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    Op,
    CreationOptional,
    ForeignKey,
    NonAttribute,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { AVAILABLE_SITE_STATUSES, AvailableSite } from "../entities/availableSite";
import { EntityModel } from "./entity";

export class AvailableSiteModel extends Model<
    InferAttributes<AvailableSiteModel>,
    InferCreationAttributes<AvailableSiteModel>
> {
    declare id: CreationOptional<number>;
    declare entityId: ForeignKey<EntityModel["id"]>;
    declare title: string;
    declare url: string;
    declare status: AVAILABLE_SITE_STATUSES;
    declare operatorSiteGroupName: string;
    declare isDefault: boolean;
    declare externalCode: string;
    declare insertedAt: CreationOptional<Date>;

    get isAuthorized(): NonAttribute<boolean> {
        return this.status === AVAILABLE_SITE_STATUSES.NORMAL;
    }

    public toInfo(): AvailableSite {
        const info: AvailableSite = {
            id: this.id,
            entityId: this.entityId,
            status: this.status,
            title: this.title,
            insertedAt: this.insertedAt,
            url: this.url,
            isDefault: this.isDefault
        };

        if (this.operatorSiteGroupName) {
            info.operatorSiteGroupName = this.operatorSiteGroupName;
        }

        if (this.externalCode) {
            info.externalCode = this.externalCode;
        }
        return info;
    }
}

const schema =  {
    id: {
        field: "id",
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: "Available Site auto-incremented id",
    },
    title: {
        field: "title",
        type: DataTypes.STRING,
        comment: "Title or description of Available Site",
    },
    url: {
        field: "url",
        type: DataTypes.STRING,
        unique: true,
        comment: "Url of Available Site",
    },
    entityId: {
        field: "entity_id",
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: EntityModel,
            key: "id",
        },
        comment: "Entity ID from \"entities\" table",
    },
    status: {
        field: "status",
        type: DataTypes.ENUM(...Object.values(AVAILABLE_SITE_STATUSES)),
        defaultValue: "normal",
        allowNull: false,
        validate: { notEmpty: true },
        comment: "Status of a Available Site: normal or suspended",
    },
    insertedAt: {
        field: "inserted_at",
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
    },
    isDefault: {
        field: "is_default",
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Indicate if this is the default site for this operator"
    },
    operatorSiteGroupName: {
        field: "operator_site_group_name",
        type: DataTypes.STRING,
        comment: "This column is textual and will be used to group several sites for reporting. For example, " +
            "if I have the following sites: 'casino.bwin.com' and 'sports.bwin.com' and " +
            "I want to group them in the reporting under 'bwin.com' in the site group name " +
            "I will put for both of them 'bwin.com'"
    },
    externalCode: {
        field: "external_code",
        type: DataTypes.STRING,
        comment: "This will be used for external code identification of a site (for example GVC will send us a " +
            "field in the game launch which called \"gameContext\" which contains their site code and we will " +
            "store it in this column and search by it)"
    },
};
AvailableSiteModel.init(
    schema,
    {
        modelName: "available_sites",
        tableName: "available_sites",
        sequelize: db,
        freezeTableName: true,
        timestamps: false,
        indexes: [
            { fields: ["entity_id"] },
            {
                name: "idx_available_sites_entity_id_code_key_and_code_is_not_null",
                unique: true,
                fields: ["entity_id", "external_code"],
                where: {
                    external_code: {
                        [Op.ne]: null
                    }
                }
            }
        ]
    }
)
export type IAvailableSiteModel = ModelStatic<AvailableSiteModel>;

export function getAvailableSiteModel() {
    return AvailableSiteModel;
}
