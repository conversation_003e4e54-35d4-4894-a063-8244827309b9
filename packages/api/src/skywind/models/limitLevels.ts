import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { encodeId } from "../utils/publicid";

export interface LimitLevel {
    id?: number;
    title: string;
    entityId: number;
    createdAt?: Date;
    updatedAt?: Date;
    
    pid?: string;
    entity_game_limit_levels?: any;
}

export interface LimitLevelDBInstance extends Model<
        InferAttributes<LimitLevelDBInstance>,
        InferCreationAttributes<LimitLevelDBInstance>
    >,
    LimitLevel {
    toInfo(includePid?: boolean): LimitLevel;
    get pid(): string;
}
export type LimitLevelModel = ModelStatic<LimitLevelDBInstance>;
const model: LimitLevelModel = db.define<LimitLevelDBInstance, LimitLevel>(
    "limit_levels",
    {
        id: { field: "id", type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        title: { field: "title", type: DataTypes.STRING, allowNull: false },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
        entityId: { field: "entity_id", type: DataTypes.INTEGER, allowNull: false },
    },
    {
        timestamps: true,
        freezeTableName: true,
        getterMethods: {
            pid(): string {
                return encodeId(this.id);
            },
        }
    }
);
export function getLimitLevelModel(): LimitLevelModel {
    (model as any).prototype.toInfo = function(includePid: boolean = false): LimitLevel {
        const info: LimitLevel = {
            id: this.id,
            entityId: this.entityId,
            title: this.title,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };

        if (includePid) {
            info.pid = this.pid;
        }

        return info;
    };
    return model;
}
