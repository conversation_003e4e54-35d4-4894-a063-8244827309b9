import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelizeSlave as slaveDb } from "../storage/db";
import { RoundHistory } from "../entities/gameHistory";

export type RecoveryType = "force-finish" | "revert" | "finalize";

export interface RoundHistoryDBInstance extends Model<
        InferAttributes<RoundHistoryDBInstance>,
        InferCreationAttributes<RoundHistoryDBInstance>
    >,
    RoundHistory {
    get finished(): boolean;
    get revenue(): number;
}
const schema = {
    roundId: { field: "id", type: DataTypes.BIGINT, allowNull: false, primaryKey: true },
    brandId: { field: "brand_id", type: DataTypes.INTEGER, allowNull: false },
    playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: false },
    gameId: { field: "game_id", type: DataTypes.STRING, allowNull: false },
    gameCode: { field: "game_code", type: DataTypes.STRING, allowNull: false },
    device: { field: "device_id", type: DataTypes.STRING, allowNull: false },
    currency: { field: "currency", type: DataTypes.CHAR(3), allowNull: false },
    win: { field: "total_win", type: DataTypes.DECIMAL, defaultValue: 0, allowNull: false },
    bet: { field: "total_bet", type: DataTypes.DECIMAL, defaultValue: 0, allowNull: false },
    balanceBefore: { field: "balance_before", type: DataTypes.DECIMAL, allowNull: true },
    balanceAfter: { field: "balance_after", type: DataTypes.DECIMAL, allowNull: true },
    totalEvents: { field: "total_events", type: DataTypes.DECIMAL, defaultValue: 0, allowNull: false },
    firstTs: {
        field: "started_at",
        type: DataTypes.DATE,
        allowNull: false,
    },
    ts: {
        field: "finished_at",
        type: DataTypes.DATE,
        allowNull: true,
    },
    isTest: { field: "test", type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
    sessionId: { field: "session_id", type: DataTypes.BIGINT },
    insertedAt: {
        field: "inserted_at",
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: DataTypes.NOW,
    },
    recoveryType: {
        field: "recovery_type",
        type: DataTypes.ENUM("force-finish", "revert", "finalize"),
        allowNull: true
    },
    totalJpContribution: { field: "total_jp_contribution", type: DataTypes.DECIMAL, allowNull: true },
    totalJpWin: { field: "total_jp_win", type: DataTypes.DECIMAL, allowNull: true },
    debit: { field: "debit", type: DataTypes.DECIMAL, allowNull: true },
    credit: { field: "credit", type: DataTypes.DECIMAL, allowNull: true },
    ctrl: { field: "ctrl", type: DataTypes.INTEGER, allowNull: true },
    extraData: { field: "extra_data", type: DataTypes.JSONB, allowNull: true },
};
export type IRoundHistoryModel = ModelStatic<RoundHistoryDBInstance>;
const RoundHistoryDBModel = slaveDb.define<RoundHistoryDBInstance, RoundHistory>(
    "rounds_history",
    schema,
    {
        timestamps: false,
        freezeTableName: true,
        getterMethods: {
            finished(): boolean {
                return !!this.ts;
            },
            revenue(): number {
                return this.bet - this.win;
            },
        }
    }
);
export function getRoundHistoryModel(): IRoundHistoryModel {
    return RoundHistoryDBModel;
}
