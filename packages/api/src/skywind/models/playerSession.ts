import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { get as getPlayerModel } from "./player";
import { sequelize as db } from "../storage/db";

const PlayerModel = getPlayerModel();

export interface SessionAttributes {
    id?: number;
    sessionId?: string;
    playerId?: number;
    startedAt?: Date;
    finishedAt?: Date;
    finished?: boolean;
}

export interface SessionDBInstance extends Model<
        InferAttributes<SessionDBInstance>,
        InferCreationAttributes<SessionDBInstance>
    >,
    SessionAttributes {
}

const schema = {
    id: {
        type: DataTypes.INTEGER,
        field: "id",
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
    },
    sessionId: {
        type: DataTypes.UUID,
        field: "session_id",
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
    },
    playerId: {
        type: DataTypes.INTEGER,
        field: "player_id",
        allowNull: false,
        references: {
            model: PlayerModel,
            key: "id",
        },
    },
    startedAt: {
        type: DataTypes.DATE,
        field: "started_at",
        allowNull: false,
    },
    finishedAt: {
        type: DataTypes.DATE,
        field: "finished_at",
        allowNull: true,
    },
    finished: {
        type: DataTypes.BOOLEAN,
        field: "finished",
        allowNull: false,
        defaultValue: false,
    },
};

export type ISessionModel = ModelStatic<SessionDBInstance>;
const model: ISessionModel = db.define<SessionDBInstance, SessionAttributes>(
    "player_session",
    schema,
    {
        indexes: [
            { fields: ["player_id"] },
        ],
        createdAt: false,
        updatedAt: false,
    }
);

model.addHook("beforeValidate", function (instance) {
    if (!(instance as any).startedAt) {
        (instance as any).startedAt = new Date();
    }
});

model.belongsTo(PlayerModel, { foreignKey: "playerId" });

export function get(): ISessionModel {
    return model;
}
