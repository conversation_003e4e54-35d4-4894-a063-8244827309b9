import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    ForeignKey,
    CreationOptional,
    NonAttribute,
    Association
} from "sequelize";
import { Lobby, LobbyMeta } from "../entities/lobby";
import { EntityModel } from "./entity";
import { sequelize as db } from "../storage/db";

export class LobbyModel extends Model<
    InferAttributes<LobbyModel, { omit: "brand"}>,
    InferCreationAttributes<LobbyModel, { omit: "brand"}>
> {
    declare id: CreationOptional<number>;
    declare brandId: ForeignKey<EntityModel["id"]>;
    declare isDefault: boolean;
    declare theme: Lobby["theme"];
    declare title: string;
    declare description: string;
    declare status: string;
    declare info: LobbyMeta;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    declare path?: NonAttribute<string>;
    declare brand?: NonAttribute<EntityModel>;

    declare static associations: {
        brand: Association<LobbyModel, EntityModel>,
    }

    public toInfo(): Lobby {
        const result: Lobby = {
            id: this.id,
            title: this.title,
            description: this.description,
            status: this.status,
            brandId: this.brandId,
            theme: this.theme,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            isDefault: !!this.isDefault,
            info: this.info || undefined,
        }
        const brand = this.brand;
        if (brand) {
            result.path = brand.path.substring(1);
        }
        return result;
    }
}

const schema = {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        title: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: { notEmpty: true, len: [1, 100] as const },
            comment: "Lobby title",
        },
        description: {
            type: DataTypes.TEXT,
            validate: { notEmpty: true },
            comment: "Lobby description",
        },
        brandId: {
            type: DataTypes.INTEGER,
            field: "brand_id",
            allowNull: false,
            validate: { notEmpty: true },
            references: {
                model: EntityModel,
                key: "id",
            },
            comment: "Reference to brand",
        },
        status: {
            type: DataTypes.ENUM("normal", "suspended"),
            field: "status",
            defaultValue: "normal",
            allowNull: false,
            validate: { notEmpty: true },
        },
        theme: {
            field: "theme",
            type: DataTypes.JSONB,
            allowNull: false,
            comment: "Lobby theme"
        },
        info: {
            field: "info",
            type: DataTypes.JSONB,
            allowNull: true,
            comment: "Any meta information about lobby"
        },
        isDefault: {
            type: DataTypes.BOOLEAN,
            field: "is_default",
            defaultValue: false
        },
        createdAt: {
            type: DataTypes.DATE,
            field: "created_at",
        },
        updatedAt: {
            type: DataTypes.DATE,
            field: "updated_at",
        },
    };
LobbyModel.init(
    schema,
    {
        tableName: "lobbies",
        modelName: "lobby",
        sequelize: db,
        underscored: true,
        timestamps: true,
        indexes: [
            {
                name: "lobby_brand_ukey",
                unique: true,
                fields: ["title", "brand_id"],
            },
        ],
    }
)

LobbyModel.belongsTo(EntityModel, { as: "brand" });

export function get() {
    return LobbyModel;
}
