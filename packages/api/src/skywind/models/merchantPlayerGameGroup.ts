import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { get as getGameGroup } from "./gamegroup";
import { get as getMerchant } from "./merchant";

const GameGroupModel = getGameGroup();
const MerchantModel = getMerchant();

export interface MerchantPlayerGameGroupAttributes {
    id?: number;
    merchantId: number;
    playerCode: string;
    gameGroupId: number;
    createdAt?: Date;
    updatedAt?: Date;

    gameGroup?: any;
}

export interface MerchantPlayerGameGroupDBInstance extends Model<
        InferAttributes<MerchantPlayerGameGroupDBInstance>,
        InferCreationAttributes<MerchantPlayerGameGroupDBInstance>
    >,
    MerchantPlayerGameGroupAttributes {
}

const schema = {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true
    },
    merchantId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: { notEmpty: true },
        field: "merchant_id"
    },
    playerCode: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: { notEmpty: true },
        field: "player_code"
    },
    gameGroupId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: { notEmpty: true },
        field: "game_group_id"
    },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
        defaultValue: DataTypes.NOW,
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
        defaultValue: DataTypes.NOW,
    }
};

export type IMerchantPlayerGameGroupModel = ModelStatic<MerchantPlayerGameGroupDBInstance>;
const merchantPlayerGameGroupModel: IMerchantPlayerGameGroupModel =
    db.define<MerchantPlayerGameGroupDBInstance, MerchantPlayerGameGroupAttributes>(
        "merchantPlayerGameGroup",
        schema,
        {
            tableName: "merchant_player_game_group",
            indexes: [
                {
                    name: "merchant_player_game_group_merchant_code_idx",
                    fields: ["merchant_id", "player_code"],
                    unique: true,
                }
            ]
    }
);

merchantPlayerGameGroupModel.belongsTo(GameGroupModel, { foreignKey: "gameGroupId", as: "gameGroup"  });
merchantPlayerGameGroupModel.belongsTo(MerchantModel, { foreignKey: "merchantId", as: "merchant" });

export function getMerchantPlayerGameGroupModel(): IMerchantPlayerGameGroupModel {
        return merchantPlayerGameGroupModel;
}
