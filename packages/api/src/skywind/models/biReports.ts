import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { BiReport } from "../entities/biReport";

export interface BiReportDBInstance extends Model<
        InferAttributes<BiReportDBInstance>,
        InferCreationAttributes<BiReportDBInstance>
    >,
    BiReport {
}
export type IBiReportModel = ModelStatic<BiReportDBInstance>;
const model: IBiReportModel = db.define<BiReportDBInstance, BiReport>(
    "bi_reports",
    {
        id: { field: "id", type: DataTypes.BIGINT, autoIncrement: true, primaryKey: true },
        name: { field: "name", type: DataTypes.STRING, unique: "workbookToName" },
        caption: { field: "caption", type: DataTypes.STRING },
        description: { field: "description", type: DataTypes.STRING },
        workbook: { field: "workbook", type: DataTypes.STRING, unique: "workbookToName" },
        reportGroup: { field: "report_group", type: DataTypes.STRING },
        status: { field: "status", type: DataTypes.ENUM("normal", "suspended"), defaultValue: "normal" },
        permission: { field: "permission", type: DataTypes.STRING },
        createdAt: {
            field: "created_at",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        settings: { field: "settings", type: DataTypes.JSONB, allowNull: false, defaultValue: {} },
        ordering: { field: "ordering", type: DataTypes.INTEGER, defaultValue: 0 },
    },
    {
        timestamps: false,
        freezeTableName: true
    }
);
export function getBiReportModel(): IBiReportModel {
    return model;
}
