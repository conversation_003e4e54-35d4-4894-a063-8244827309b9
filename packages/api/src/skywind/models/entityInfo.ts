import { CreationOptional, DataTypes, ForeignKey, InferAttributes, InferCreationAttributes, Model } from "sequelize";
import { sequelize as db } from "../storage/db";
import { EntityModel } from "./entity";
import { EntityInfo } from "../entities/entityInfo";

export class EntityInfoModel extends Model<
    InferAttributes<EntityInfoModel>,
    InferCreationAttributes<EntityInfoModel>
> {
    declare id: CreationOptional<number>;
    declare entityId: ForeignKey<EntityModel["id"]>;
    declare type: string;
    declare data: any;
    declare insertedAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    toInfo(): EntityInfo {
        return {
            id: this.id,
            entityId: this.entityId,
            type: this.type,
            data: this.data,
            insertedAt: this.insertedAt,
            updatedAt: this.updatedAt,
        }
    }
}

const schema = {
    id: {
        field: "id",
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: "Entity Info auto-incremented id",
    },

    entityId: {
        field: "entity_id",
        type: DataTypes.INTEGER,
        unique: "entityIdInfoType",
        allowNull: false,
        references: {
            model: EntityModel,
            key: "id",
        },
        comment: "Entity ID from \"entities\" table",
    },

    type: {
        field: "info_type",
        type: DataTypes.STRING,
        unique: "entityIdInfoType",
        allowNull: false,
        comment: "Type of stored information",
    },

    data: {
        field: "data",
        allowNull: false,
        type: DataTypes.JSONB,
        comment: "Any additional information about entity",
    },

    insertedAt: {
        field: "inserted_at",
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
    },

    updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
    },
};

EntityInfoModel.init(
    schema,
    {
        modelName: "entity_info",
        tableName: "entity_info",
        sequelize: db,
        freezeTableName: true,
        timestamps: false,
        indexes: [
            {
                name: "idx_entity_info_entity_id",
                fields: ["entity_id"]
            }
        ]
    }
)

export function getEntityInfoModel() {
    return EntityInfoModel;
}
