import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { gameServerDB as db } from "../storage/db";
/**
 * Game init settings
 */
export interface GameInitSettings {
    gameId: string;
    version: string;
    data: any;
}

export interface GameInitSettingsDBInstance extends Model<
        InferAttributes<GameInitSettingsDBInstance>,
        InferCreationAttributes<GameInitSettingsDBInstance>
    >,
    GameInitSettings {
}
export type IGameInitSettingsModel = ModelStatic<GameInitSettingsDBInstance>;
const model: IGameInitSettingsModel = db.define<GameInitSettingsDBInstance, GameInitSettings>(
    "game_init_settings",
    {
        gameId: { field: "game_id", type: DataTypes.STRING, primaryKey: true },
        version: { field: "version", type: DataTypes.STRING, primaryKey: true },
        data: { field: "data", type: DataTypes.JSONB, allowNull: false },
    },
    {
        timestamps: false,
        freezeTableName: true
    }
);
export function getGameInitSettingsModel(): IGameInitSettingsModel {
    return model;
}
