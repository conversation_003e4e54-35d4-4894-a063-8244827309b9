import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { get as getEntityModel } from "./entity";
import { GameRTP, RtpConfigurator } from "../entities/game";

const EntityModel = getEntityModel();

export interface GameRtpHistory {
    id?: number;
    entityId?: number;
    gameId: number;
    gameCode: string;
    rtpInfo?: GameRTP;
    rtpConfigurator?: RtpConfigurator;
    ts?: Date;
}

export interface GameRtpHistoryDB extends Model<
        InferAttributes<GameRtpHistoryDB>,
        InferCreationAttributes<GameRtpHistoryDB>
    >,
    GameRtpHistory {
    toInfo(): GameRtpHistory;
}
export type GameRtpHistoryModel = ModelStatic<GameRtpHistoryDB>;
const gameRtpHistoryModel: GameRtpHistoryModel = db.define<GameRtpHistoryDB, GameRtpHistory>(
    "game_rtp_history",
    {
        id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        entityId: { field: "entity_id", type: DataTypes.INTEGER, allowNull: true },
        gameId: { field: "game_id", type: DataTypes.INTEGER, allowNull: false },
        rtpInfo: { field: "rtp_info", type: DataTypes.JSONB, allowNull: false },
        rtpConfigurator: { field: "rtp_deduction", type: DataTypes.JSONB },
        ts: {
            field: "ts",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        gameCode: { field: "game_code", type: DataTypes.STRING, allowNull: false },
    },
    {
        underscored: true,
        timestamps: false,
        tableName: "game_rtp_history"
    }
);
gameRtpHistoryModel.belongsTo(EntityModel, { foreignKey: "entityId" });

export function getGameRtpHistoryModel(): GameRtpHistoryModel {
    (gameRtpHistoryModel as any).prototype.toInfo = function(): GameRtpHistory {
        return {
            id: this.id,
            entityId: this.entityId,
            gameId: this.gameId,
            gameCode: this.gameCode,
            rtpInfo: this.rtpInfo,
            rtpConfigurator: this.rtpConfigurator,
            ts: this.ts
        };
    };
    return gameRtpHistoryModel;
}
