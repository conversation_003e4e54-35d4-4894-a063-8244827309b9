import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { getPromotionModel } from "./promotion";
import { sequelize as db } from "../storage/db";

const PromotionModel = getPromotionModel();

export interface FreebetRewardDBAttributes {
    id?: number;
    promoId: number;
    freebetAmount: number;
    games: any;
    expirationPeriod?: number;
    expirationPeriodType?: string;
    expirationDate?: Date;
    insertedAt?: Date;
}

export interface FreebetRewardDBInstance extends Model<
        InferAttributes<FreebetRewardDBInstance>,
        InferCreationAttributes<FreebetRewardDBInstance>
    >,
    FreebetRewardDBAttributes {
}
export type IFreebetRewardModel = ModelStatic<FreebetRewardDBInstance>;
const PromoFreebetRewardModel: IFreebetRewardModel = db.define<FreebetRewardDBInstance, FreebetRewardDBAttributes>(
    "promotion_reward_freebets",
    {
        id: { field: "id", type: DataTypes.BIGINT, allowNull: false, autoIncrement: true, primaryKey: true },
        promoId: {
            field: "promo_id",
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: PromotionModel,
                key: "id",
            }
        },
        freebetAmount: { field: "amount", type: DataTypes.INTEGER, allowNull: false },
        games: { field: "games", type: DataTypes.JSONB, allowNull: false },
        expirationPeriod: { field: "expiration_period", type: DataTypes.INTEGER, allowNull: true },
        expirationPeriodType: {
            field: "expiration_period_type",
            type: "enum_promotions_interval_type",
            allowNull: true
        },
        expirationDate: { field: "expiration_date", type: DataTypes.DATE, allowNull: true },
        insertedAt: {
            field: "inserted_at",
            type: DataTypes.DATE,
            allowNull: true,
            defaultValue: DataTypes.NOW,
        },
    },
    {
        timestamps: false,
        freezeTableName: true,
        indexes: [
            {
                unique: false,
                fields: ["inserted_at"],
                name: "idx_promotion_reward_freebet_inserted_at"
            },
            {
                unique: false,
                fields: ["promo_id"],
                name: "idx_promotion_reward_freebet_promo_id"
            }
        ]
    }
);
PromoFreebetRewardModel.belongsTo(PromotionModel, { foreignKey: "promoId", targetKey: "id", onDelete: "CASCADE" });
PromotionModel.hasMany(PromoFreebetRewardModel, { as: "freebetRewards", foreignKey: "promoId" });

export function getFreebetRewardModel(): IFreebetRewardModel {
    return PromoFreebetRewardModel;
}
