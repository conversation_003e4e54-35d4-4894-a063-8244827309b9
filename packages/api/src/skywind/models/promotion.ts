import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    Op,
} from "sequelize";
import { UserModel } from "./user";
import { get as getEntityModel } from "./entity";
import { sequelize as db } from "../storage/db";
import { PROMO_TYPE } from "@skywind-group/sw-management-promo-wallet";
import { PROMO_OWNER, PROMO_REWARD_INTERVAL_TYPE, PromotionAttributes } from "../entities/promotion";
import { BaseEntity } from "../entities/entity";
import { FreebetRewardDBInstance } from "./promotionFreebetReward";
import { BonusCoinRewardDBInstance } from "./promotionBonusCoinReward";
import { LabelDBInstance } from "./label";
import { PromotionToPlayerInstance } from "./promotionPlayer";

const EntityModel = getEntityModel();

export interface PromotionDBInstance extends Model<
        InferAttributes<PromotionDBInstance>,
        InferCreationAttributes<PromotionDBInstance>
    >,
    PromotionAttributes {
    // TODO: I think it is deprecated
    operatorId?: number;
    createdUser: UserModel;
    updatedUser: UserModel;
    brand: BaseEntity;
    freebetRewards?: FreebetRewardDBInstance[];
    bonusCoinRewards?: BonusCoinRewardDBInstance[];
    promotionLabels?: LabelDBInstance[];
    players?: PromotionToPlayerInstance[];
}
const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    title: { type: DataTypes.STRING, allowNull: false, validate: { notEmpty: true } },
    type: {
        field: "type",
        type: DataTypes.ENUM(PROMO_TYPE.FREEBET, PROMO_TYPE.BONUS_COIN),
        allowNull: false
    },
    active: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
    },
    archived: { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
    everStarted: { type: DataTypes.BOOLEAN, field: "ever_started" },
    startDate: {
        field: "start_dt",
        type: DataTypes.DATE,
    },
    startRewardOnGameOpen: {
        field: "start_reward_on_game_open",
        type: DataTypes.BOOLEAN,
        allowNull: false, defaultValue: false
    },
    endDate: {
        field: "end_dt",
        type: DataTypes.DATE,
    },
    timezone: { type: DataTypes.STRING },
    brandId: {
        type: DataTypes.INTEGER,
        field: "brand_id",
        references: {
            model: EntityModel,
            key: "id",
        },
        allowNull: false,
    },
    createdUserId: {
        type: DataTypes.INTEGER,
        field: "creator_id",
        allowNull: true,
        references: {
            model: UserModel,
            key: "id",
        },
    },
    updatedUserId: {
        type: DataTypes.INTEGER,
        field: "updater_id",
        allowNull: true,
        references: {
            model: UserModel,
            key: "id",
        },
    },
    description: { type: DataTypes.TEXT },
    conditions: {
        type: DataTypes.JSONB,
        allowNull: true,
        validate: { notEmpty: true }
    },
    intervalType: {
        field: "interval_type",
        type: DataTypes.ENUM(PROMO_REWARD_INTERVAL_TYPE.MINUTELY,
            PROMO_REWARD_INTERVAL_TYPE.HOURLY,
            PROMO_REWARD_INTERVAL_TYPE.DAILY,
            PROMO_REWARD_INTERVAL_TYPE.WEEKLY,
            PROMO_REWARD_INTERVAL_TYPE.MONTHLY)
    },
    daysOfWeek: { field: "days_of_week", type: DataTypes.JSONB },
    daysOfMonth: { field: "days_of_month", type: DataTypes.JSONB },
    timeOfDay: {
        field: "time_of_day",
        type: DataTypes.TIME,
    },

    createdAt: {
        field: "created_at",
        type: DataTypes.DATE,
    },
    updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE,
    },

    customerIds: { field: "customer_ids", type: DataTypes.JSONB },
    owner: {
        field: "owner",
        type: DataTypes.ENUM(PROMO_OWNER.OPERATOR, PROMO_OWNER.SKYWIND),
        defaultValue: PROMO_OWNER.OPERATOR
    },
    version: { type: DataTypes.INTEGER, defaultValue: 0 },
    externalId: { field: "external_id", type: DataTypes.STRING, allowNull: true }
};

export type IPromotionModel = ModelStatic<PromotionDBInstance>;
const PromotionModel: IPromotionModel = db.define<PromotionDBInstance, PromotionAttributes>(
    "promotion",
    schema,
    {
        indexes: [
            { fields: ["brand_id"] },
            { fields: ["creator_id"] },
            { fields: ["updater_id"] },
            { fields: ["start_dt", "end_dt"] },
            {
                name: "idx_promotions_external_id_and_brand_id",
                unique: true,
                fields: ["external_id", "brand_id"],
                where: {
                    external_id: {
                        [Op.ne]: null
                    }
                }
            },
            { fields: ["created_at"] },
        ]
    }
);

PromotionModel.belongsTo(EntityModel, { as: "brand" });

PromotionModel.belongsTo(UserModel, { as: "createdUser", foreignKey: "creator_id" });
PromotionModel.belongsTo(UserModel, { as: "updatedUser", foreignKey: "updater_id" });

export function getPromotionModel(): IPromotionModel {
    return PromotionModel;
}
