import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { GameCategory } from "../entities/gamecategory";
import { get as getEntityModel } from "./entity";
import { sequelize as db } from "../storage/db";
import { GAME_CATEGORY_TYPE } from "../utils/common";

const EntityModel = getEntityModel();

export interface GameCategoryDBInstance extends Model<
        InferAttributes<GameCategoryDBInstance>,
        InferCreationAttributes<GameCategoryDBInstance>
    >,
    GameCategory {
}

export type IGameCategoryModel = ModelStatic<GameCategoryDBInstance>;
const GameCategoryModel: IGameCategoryModel = db.define<GameCategoryDBInstance, GameCategory>(
        "game_category",
        {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            title: {
                type: DataTypes.STRING,
                allowNull: false,
                validate: { notEmpty: true, len: [1, 100] },
                comment: "Game category title",
            },
            icon: {
                type: DataTypes.TEXT,
                allowNull: false,
                comment: "Game category icon",
            },
            description: {
                type: DataTypes.TEXT,
                comment: "Game category description",
            },
            brandId: {
                type: DataTypes.INTEGER,
                field: "brand_id",
                allowNull: false,
                validate: { notEmpty: true },
                references: {
                    model: EntityModel,
                    key: "id",
                },
                comment: "Reference to brand",
            },
            status: {
                type: DataTypes.ENUM("normal", "suspended"),
                allowNull: false
            },
            items: DataTypes.JSONB,
            translations: DataTypes.JSONB,
            type: {
                allowNull: false,
                validate: { notEmpty: true },
                type: DataTypes.ENUM(
                    GAME_CATEGORY_TYPE.GENERAL,
                    GAME_CATEGORY_TYPE.GAMESTORE
                )
            },
            ordering: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            }
        },
        {
            underscored: true,
            tableName: "game_categories",
            timestamps: true,
            indexes: [
                {
                    name: "game_category_brand_ukey",
                    unique: true,
                    fields: ["title", "brand_id"],
                },
                { fields: ["brand_id"] },
            ],
        }
    );

GameCategoryModel.belongsTo(EntityModel, { as: "brand" });

export function get(): IGameCategoryModel {
    return GameCategoryModel;
}
