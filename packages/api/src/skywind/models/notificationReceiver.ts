import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
    ForeignKey,
    Association,
    NonAttribute,
} from "sequelize";
import { NotificationModel } from "./notification";
import { UserModel } from "./user";
import { sequelize as db } from "../storage/db";
import { NotificationReceiverInfo } from "../entities/notification";

export class NotificationReceiversModel extends Model<
    InferAttributes<NotificationReceiversModel>,
    InferCreationAttributes<NotificationReceiversModel>
> {
    declare id: CreationOptional<number>;
    declare notificationId: ForeignKey<NotificationModel["id"]>;
    declare receiver: ForeignKey<UserModel["id"]>;
    declare status: string;
    // declare isNew: boolean;
    declare unread: boolean;

    declare notification?: NonAttribute<NotificationModel>;

    declare static associations: {
        notification: Association<NotificationReceiversModel, NotificationModel>;
    };

    public toInfo(): NotificationReceiverInfo {
        const { message, ts, author } = this.notification;
        return {
            id: this.id,
            status: this.status,
            unread: this.unread,
            message,
            ts,
            authorId: author
        }
    }
}

const schema = {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: "id, automatically generates by Seguelize",
    },
    notificationId: {
        type: DataTypes.INTEGER,
        references: {
            model: NotificationModel,
            key: "id",
        },
        field: "notification_id",
        allowNull: false,
        comment: "Reference to notification",
    },
    receiver: {
        type: DataTypes.INTEGER,
        field: "receiver",
        references: {
            model: UserModel,
            key: "id",
        },
        allowNull: false,
        comment: "Id of user-receiver",
    },
    status: {
        type: DataTypes.ENUM("normal", "suspended"),
        field: "status",
        defaultValue: "normal",
        allowNull: false,
        validate: { notEmpty: true },
        comment: "Message status: normal or suspended",
    },
    unread: {
        type: DataTypes.BOOLEAN,
        field: "unread",
        defaultValue: true,
        comment: "Whether a message was read or not",
    },
};
NotificationReceiversModel.init(
    schema,
    {
        modelName: "notification_receiver",
        tableName: "notification_receivers",
        sequelize: db,
        indexes: [
            { fields: ["receiver"] },
        ],
        createdAt: false,
        updatedAt: false,
    }
);

NotificationReceiversModel.belongsTo(NotificationModel, { foreignKey: "notificationId" });

export function get() {
    return NotificationReceiversModel;
}
