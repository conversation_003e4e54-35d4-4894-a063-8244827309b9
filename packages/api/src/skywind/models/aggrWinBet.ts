import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelizeSlave as slaveDb } from "../storage/db";
import { get as getEntityModel } from "./entity";

const EntityModel = getEntityModel();

export interface AggrWinBet {
    brandId: number;
    gameCode: string;
    playerCode: string;
    paymentDateHour?: Date;
    paymentDateDay?: Date;
    currency?: string;
    playedGames?: number;
    totalBets?: number;
    totalWins?: number;
    paymentDate?: Date;
    exchangeRate?: number;
    totalJpWins?: number;
    totalFreebetWins?: number;
    debits?: number;
    credits?: number;
}

export interface AggrWinBetDBInstance extends Model<
        InferAttributes<AggrWinBetDBInstance>,
        InferCreationAttributes<AggrWinBetDBInstance>
    >,
    AggrWinBet {
}

const schema = {
    paymentDateHour: {
        field: "payment_date_hour", type: DataTypes.DATE, allowNull: false, primaryKey: true
    },
    paymentDateDay: {
        field: "payment_date_day", type: DataTypes.DATE, allowNull: false
    },
    brandId: {
        field: "brand_id",
        type: DataTypes.INTEGER, allowNull: false, primaryKey: true,
        references: {
            model: EntityModel,
            key: "id",
        }
    },
    gameCode: { field: "game_code", type: DataTypes.STRING, allowNull: false, primaryKey: true },
    playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: false, primaryKey: true },
    currency: {
        field: "currency_code",
        type: DataTypes.CHAR(3),
        allowNull: false,
        primaryKey: true,
        validate: { isUppercase: true }
    },
    playedGames: { field: "played_games_qty", type: DataTypes.INTEGER, allowNull: false },
    totalBets: { field: "total_bets", type: DataTypes.DECIMAL, allowNull: false },
    totalWins: { field: "total_wins", type: DataTypes.DECIMAL, allowNull: false },
    paymentDate: { field: "last_payment_ts", type: DataTypes.DATE, allowNull: false },
    exchangeRate: {
        field: "exchange_rate",
        type: DataTypes.DECIMAL,
        allowNull: false,
        defaultValue: 1,
    },
    totalJpWins: {
        field: "total_jp_wins",
        type: DataTypes.DECIMAL,
        allowNull: true,
    },
    totalFreebetWins: {
        field: "total_freebet_wins",
        type: DataTypes.DECIMAL,
        allowNull: true,
    },
    debits: { field: "debit", type: DataTypes.DECIMAL },
    credits: { field: "credit", type: DataTypes.DECIMAL }
};

export type IAggrWinBetModel = ModelStatic<AggrWinBetDBInstance>;
const model: IAggrWinBetModel = slaveDb.define<AggrWinBetDBInstance, AggrWinBet>(
    "bo_aggr_win_bets",
    schema,
    {
        freezeTableName: true,
        timestamps: false,
        indexes: [
            { fields: ["brand_id"] }
        ]
    }
);

export function getAggrWinBetModel() {
    return model;
}
