import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { get as getEntityModel } from "./entity";
import { MerchantTestPlayer, SOURCE } from "../entities/merchantTestPlayer";

const EntityModel = getEntityModel();

export interface TestMerchantPlayerDBInstance extends Model<
        InferAttributes<TestMerchantPlayerDBInstance>,
        InferCreationAttributes<TestMerchantPlayerDBInstance>
    >,
    MerchantTestPlayer {
    toInfo(): MerchantTestPlayer;
}
const schema = {
        brandId: {
            field: "brand_id",
            type: DataTypes.INTEGER,
            primaryKey: true,
            references: {
                model: EntityModel,
                key: "id",
            }
        },
        code: { field: "code", type: DataTypes.STRING, primaryKey: true },
        createdAt: {
            field: "created_at",
            type: DataTypes.DATE,
        },
        updatedAt: {
            field: "updated_at",
            type: DataTypes.DATE,
        },
        startDate: {
            field: "start_date",
            type: DataTypes.DATE,
        },
        endDate: {
            field: "end_date",
            type: DataTypes.DATE,
        },
        source: { field: "source", type: DataTypes.ENUM(SOURCE.INTEGRATION, SOURCE.SUPPORT) }
    };
export type ITestMerchantPlayer = ModelStatic<TestMerchantPlayerDBInstance>;
const model: ITestMerchantPlayer = db.define<TestMerchantPlayerDBInstance, MerchantTestPlayer>(
    "merchant_test_players",
    schema,
    {
        timestamps: true,
        freezeTableName: true
    }
);
export function getTestMerchantPlayer(): ITestMerchantPlayer {
    (model as any).prototype.toInfo = function(): MerchantTestPlayer {
        return {
            code: this.code,
            brandId: this.brandId,
            source: this.source,
            startDate: this.startDate,
            endDate: this.endDate,
            updatedAt: this.updatedAt,
            createdAt: this.createdAt
        };
    };
    return model;
}
