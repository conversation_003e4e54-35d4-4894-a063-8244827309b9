import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize, sequelizeSlave } from "../storage/db";
import { OrderStatus } from "../entities/payment";

export enum PaymentDeclineReason {
    TRANSACTION_NOT_FOUND = "transactionNotFound",
    INSUFFICIENT_ENTITY_BALANCE = "insufficientEntityBalance",
    INSUFFICIENT_BALANCE = "insufficientBalance",
    BAD_TRANSACTION_ID = "badTransactionId",
    INTERNAL_ERROR = "internalError"
}

export interface PaymentAttributes {
    id?: number;
    trxId: string;
    extTrxId?: string;
    brandId: string;
    playerCode: string;
    orderId?: string;
    orderDate?: Date;
    orderInfo?: any;
    orderStatus?: OrderStatus;
    orderType: string;
    currencyCode: string;
    amount: number;
    paymentMethodCode?: string;
    startDate: Date;
    endDate: Date;
    processedBy?: string;
    marks?: any;
    isTest?: boolean;
    playerBalanceAfter?: number;
    declineReason?: PaymentDeclineReason;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface PaymentDBInstance extends Model<
        InferAttributes<PaymentDBInstance>,
        InferCreationAttributes<PaymentDBInstance>
    >,
    PaymentAttributes {
}

const schema = {
    id: { field: "id", type: DataTypes.BIGINT, autoIncrement: true, primaryKey: true },
    trxId: { field: "trx_id", type: DataTypes.CHAR(28), unique: true, allowNull: false },
    extTrxId: { field: "ext_trx_id", type: DataTypes.STRING, allowNull: true },
    brandId: { field: "brand_id", type: DataTypes.STRING, allowNull: false },
    playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: false },
    orderId: { field: "order_id", type: DataTypes.STRING },
    orderDate: { field: "order_date", type: DataTypes.DATE, allowNull: false },
    orderInfo: { field: "order_info", type: DataTypes.JSONB },
    orderStatus: { field: "order_status", type: DataTypes.STRING, allowNull: false },
    orderType: { field: "order_type", type: DataTypes.STRING, allowNull: false },
    paymentMethodCode: { field: "payment_method_code", type: DataTypes.STRING },
    currencyCode: { field: "currency_code", type: DataTypes.CHAR(3), allowNull: false },
    amount: { type: DataTypes.DECIMAL, allowNull: false },
    playerBalanceAfter: { field: "player_balance_after", type: DataTypes.DECIMAL, allowNull: true },
    startDate: { field: "start_date", type: DataTypes.DATE, allowNull: false },
    endDate: { field: "end_date", type: DataTypes.DATE },
    processedBy: { field: "processed_by", type: DataTypes.STRING },
    marks: { field: "marks", type: DataTypes.JSONB },
    declineReason: { field: "decline_reason", type: DataTypes.ENUM(...Object.values(PaymentDeclineReason)) },
    isTest: { type: DataTypes.BOOLEAN, allowNull: false, field: "is_test", defaultValue: false },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
    },
};

export type IPaymentModel = ModelStatic<PaymentDBInstance>;
const payments: IPaymentModel =
    sequelize.define<PaymentDBInstance, PaymentAttributes>(
        "payments",
        schema,
        {
            underscored: true,
            indexes: [
                { name: "payments_extrx_brand_key", unique: true, fields: ["ext_trx_id", "brand_id"] },
                { name: "idx_payments_order_status", fields: ["order_status"] },
            ],
        }
    );

export function get(): IPaymentModel {
    return payments;
}

const paymentsSlave: IPaymentModel =
    sequelizeSlave.define<PaymentDBInstance, PaymentAttributes>("payments", schema);

export function getSlaveModel(): IPaymentModel {
    return paymentsSlave;
}
