import { Model, ModelStatic, DataTypes, InferAttributes, InferCreationAttributes } from "sequelize";
import { sequelize as db } from "../storage/db";
import { Proxy } from "../entities/proxy";

export interface ProxyDBInstance extends Model<
        InferAttributes<ProxyDBInstance>,
        InferCreationAttributes<ProxyDBInstance>
    >,
    Proxy {
    toInfo(): Proxy;
}
export type ProxyModel = ModelStatic<ProxyDBInstance>;
const model: ProxyModel = db.define<ProxyDBInstance, Proxy>(
    "proxies",
    {
        id: { field: "id", type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        url: { field: "url", type: DataTypes.STRING, unique: true },
        description: { field: "description", type: DataTypes.STRING },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        timestamps: true,
        freezeTableName: true,
    }
);
export function getProxyModel(): ProxyModel {
    (model as any).prototype.toInfo = function(): Proxy {
        return {
            url: this.url,
            id: this.id,
            description: this.description,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
    return model;
}
