import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db} from "../storage/db";
import { LABEL_GROUPS_RELATIONS_TYPES, LABEL_GROUPS_TYPES } from "../utils/common";

export interface LabelGroupAttributes {
    id?: number;
    group: string;
    type: LABEL_GROUPS_TYPES;
    relationType: LABEL_GROUPS_RELATIONS_TYPES;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface LabelGroupDBInstance extends Model<
        InferAttributes<LabelGroupDBInstance>,
        InferCreationAttributes<LabelGroupDBInstance>
    >,
    LabelGroupAttributes {
}

const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    group: { 
        type: DataTypes.STRING,
        allowNull: false, 
        validate: { notEmpty: true }, 
        field: "label_group" 
    },
    type: {
        type: DataTypes.ENUM(...Object.values(LABEL_GROUPS_TYPES)),
        allowNull: false,
        validate: { notEmpty: true },
        comment: "Label group type: either game, or entity",
        field: "label_type"
    },
    relationType: {
        type: DataTypes.STRING(1),
        field: "relation_type",
        allowNull: false,
        validate: { notEmpty: true },
        comment: "Label group relationType: o: one to one, m: one to many."
    },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
    }
};
export type ILabelGroupModel = ModelStatic<LabelGroupDBInstance>;
const LabelGroup: ILabelGroupModel = db.define<LabelGroupDBInstance, LabelGroupAttributes>(
    "labelgroup",
    schema,
    {
        underscored: true,
        tableName: "label_groups",
        timestamps: false
    }
);

export function getLabelGroupModel(): ILabelGroupModel {
    return LabelGroup;
}
