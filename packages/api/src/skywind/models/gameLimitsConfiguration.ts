import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    Op,
    Association,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { getSchemaDefinitionModel } from "./schemaDefinition";
import { getEntityGame } from "./game";
import { EntityModel as EntityDBInstance, get as getEntityModel } from "./entity";
import { getSchemaConfigurationDBModel } from "./schemaConfiguration";
import { GameGroupDBInstance, get as getGameGroupModel } from "./gamegroup";
import { getSegmentModel, Segment, SegmentDBInstance } from "./segment";
import { BaseEntity } from "../entities/entity";
import { LIMITS_CONFIGURATION_STATUS } from "../services/gameLimits/helper";

const SchemaDefinitionModel = getSchemaDefinitionModel();
const SchemaConfigurationModel = getSchemaConfigurationDBModel();
const EntityGameModel = getEntityGame();
const EntityModel = getEntityModel();
const GameGroupModel = getGameGroupModel();
const SegmentModel = getSegmentModel();

export interface GameLimitsConfigurationDB {
    id?: number;

    title?: string;
    description?: string;

    externalBrandId?: string;
    status?: LIMITS_CONFIGURATION_STATUS;

    schemaDefinitionId?: number;
    schemaConfigurationId?: number;
    entityId?: number;
    entityGameId?: number;
    gameCode?: string;
    gameGroupId?: number;
    segmentId?: number;

    createdAt?: Date;
    updatedAt?: Date;
}

export interface GameLimitsConfiguration extends GameLimitsConfigurationDB, GameLimitsConfigurationInStorage {
}

export interface GameLimitsConfigurationInfo extends GameLimitsConfiguration {
    segment?: Segment;
    gameGroupName?: string;
}

export interface GameLimitsConfigurationDetailedInfo extends GameLimitsConfigurationInfo {
    status: LIMITS_CONFIGURATION_STATUS;

    gameLimits?: GameLimitsByCurrencyCode;
    filters?: GameLimitsFilters;
}

export interface MarketplaceGameLimitsConfiguration extends GameLimitsConfigurationDetailedInfo {
    merchantCode?: string;
    isDefault?: boolean;
}

export interface CreateGameLimitsConfiguration extends UpdateGameLimitsConfiguration {
    schemaDefinitionId?: number;
    gameCode?: string;
    gameGroupName?: string;
    status?: LIMITS_CONFIGURATION_STATUS;

    externalBrandId?: string;
    segment?: Segment;

    levels?: string[];
    defaultLevel?: string;
}

export interface GameLimitsConfigurationInStorage {
    gameLimits?: GameLimitsByCurrencyCode;
    filters?: GameLimitsFilters;
    status?: LIMITS_CONFIGURATION_STATUS;

    levels?: string[];
    defaultLevel?: string;

    processing?: boolean;
    entityPath?: string;
}

export interface UpdateGameLimitsInStorage {
    gameLimits?: string;
    filters?: string;
    levels?: string;
    defaultLevel?: string;

    processing?: boolean;
}

export interface UpdateGameLimitsConfiguration {
    title?: string;
    description?: string;

    gameLimits?: GameLimitsByCurrencyCode;
    filters?: GameLimitsFilters;

    levels?: string[];
    defaultLevel?: string;

    status?: LIMITS_CONFIGURATION_STATUS;
    baseCurrency?: string;
}

export interface SetGameLimitsConfigurationStatus {
    status: LIMITS_CONFIGURATION_STATUS;
    gameCode?: string;
    merchantType: string;
    currency?: string;
    entityId?: number;
    isDefault?: boolean;
    externalBrandId?: string;
}

export type GameLimits = SlotGameLimits | ActionGameLimits | RoomGameLimits | MultiRoomGameLimits;

export interface StakedLimitsGameLimits {
    stakeAll?: number[];
    defaultTotalStake?: number; // stakeDef * totalBetMultiplier
}

export interface SlotGameLimits extends StakedLimitsGameLimits {
    maxTotalStake?: number; // for most of games: gameTotalMultiplier x maxCoin
    winMax?: number;
}

export interface ActionGameLimits {
    coinsRate?: number;
    stakeDef?: number; // coinsRate * some from coins
    coins?: number[];
    defaultCoin?: number; // from coins
}

export interface RoomGameLimits extends StakedLimitsGameLimits {
    totalStakeMin?: number; // any combination of values sum from stakeAll
    totalStakeMax?: number; // any combination of values sum from stakeAll
    bets?: BetsLimits;
    isDefaultRoom?: boolean;
    order?: number;

    alert?: number;
    block?: number;
}

export interface BetTypeRoomLimits {
    min?: number;
    max?: number;

    payout?: number;
    exposure?: number;

    alert?: number;
    block?: number;
}

export interface BetsLimits {
    [betKey: string]: BetTypeRoomLimits;
}

export interface MultiRoomGameLimits {
    [field: string]: RoomGameLimits;
}

export interface GameLimitsFilters {
    [field: string]: {
        minTotalBet?: number; // multiplied on totalBetMultiplier
        maxTotalBet?: number; // multiplied on totalBetMultiplier
    };
}

export interface GameLimitsByCurrencyCode {
    [field: string]: GameLimits;
}

export interface GameLimitsPropertyExtended {
    value: any;
    custom?: boolean;
    inherited?: boolean;
    calculated?: boolean;
    calculatedFromBase?: boolean;
}

export interface GameLimitsExtended {
    [field: string]: GameLimitsPropertyExtended;
}

export interface GameLimitsExtendedByCurrencyCode {
    [field: string]: GameLimitsExtended;
}

export interface GameLimitsConfigurationDBInstance extends Model<
        InferAttributes<GameLimitsConfigurationDBInstance>,
        InferCreationAttributes<GameLimitsConfigurationDBInstance>
    >,
    GameLimitsConfigurationDB {
    entity: BaseEntity;
    gameGroup: GameGroupDBInstance;
    segment: SegmentDBInstance;
    gameGroupName?: string;
    toInfo(): any;
    toDetailedInfo(limitsFromRedis: GameLimitsConfigurationInStorage): any;
    associations: {
        entity: Association<GameLimitsConfigurationDBInstance, EntityDBInstance>;
        gameGroup: Association<GameLimitsConfigurationDBInstance, GameGroupDBInstance>;
        segment: Association<GameLimitsConfigurationDBInstance, SegmentDBInstance>;
    };
}
export type GameLimitsConfigurationModel = ModelStatic<GameLimitsConfigurationDBInstance>;
const model: GameLimitsConfigurationModel = db.define<GameLimitsConfigurationDBInstance, GameLimitsConfigurationDB>(
    "game_limits_configurations",
    {
        id: { field: "id", type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        schemaDefinitionId: {
            field: "schema_definition_id",
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: SchemaDefinitionModel,
                key: "id",
            },
        },
        schemaConfigurationId: { field: "schema_configuration_id", type: DataTypes.INTEGER, allowNull: true },
        entityId: {
            field: "entity_id", type: DataTypes.INTEGER, allowNull: false,
            references: {
                model: EntityModel,
                key: "id"
            }
        },
        entityGameId: {
            field: "entity_game_id",
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: EntityGameModel,
                key: "id"
            }
        },
        gameCode: { field: "game_code", type: DataTypes.STRING, allowNull: true },
        gameGroupId: {
            field: "game_group_id",
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: GameGroupModel,
                key: "id"
            }
        },
        segmentId: {
            field: "segment_id",
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: SegmentModel,
                key: "id"
            }
        },
        title: {
            field: "title",
            type: DataTypes.STRING,
            allowNull: true
        },
        description: {
            field: "description",
            type: DataTypes.STRING,
            allowNull: true
        },
        externalBrandId: {
            field: "external_brand_id",
            type: DataTypes.STRING,
            allowNull: true
        },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        indexes: [
            {
                name: "idx_game_limits_def_id_ent_id",
                fields: ["schema_definition_id", "entity_id"],
                unique: true,
                where: {
                    "entity_game_id": { [Op.eq]: null },
                    "game_group_id": { [Op.eq]: null }
                }
            },
            {
                name: "idx_game_limits_def_id_ent_id_entity_game_id",
                fields: ["schema_definition_id", "entity_id", "entity_game_id"],
                unique: true,
                where: {
                    "entity_game_id": { [Op.ne]: null },
                    "game_group_id": { [Op.eq]: null },
                    "segment_id": { [Op.eq]: null }
                }
            },
            {
                name: "idx_game_limits_def_id_ent_id_game_group_id",
                fields: ["schema_definition_id", "entity_id", "game_group_id"],
                unique: true,
                where: {
                    "entity_game_id": { [Op.eq]: null },
                    "game_group_id": { [Op.ne]: null }
                }
            },
            {
                name: "idx_game_limits_def_id_ent_id_entity_game_id_game_group_id",
                fields: ["schema_definition_id", "entity_id", "entity_game_id", "game_group_id"],
                unique: true,
                where: {
                    "entity_game_id": { [Op.ne]: null },
                    "game_group_id": { [Op.ne]: null }
                }
            }
        ]
    }
);
model.belongsTo(EntityModel, { foreignKey: "entityId" });
model.belongsTo(SchemaDefinitionModel, { foreignKey: "schemaDefinitionId" });
model.belongsTo(SchemaConfigurationModel, { foreignKey: "schemaConfigurationId" });
model.belongsTo(EntityGameModel, { foreignKey: "entityGameId" });
model.belongsTo(GameGroupModel, { foreignKey: "gameGroupId", as: "gameGroup" });
model.belongsTo(SegmentModel, { foreignKey: "segmentId" });

export function getGameLimitsConfigurationModel(): GameLimitsConfigurationModel {
    (model as any).prototype.toInfo = function() {
        return {
            id: this.id,
            schemaDefinitionId: this.schemaDefinitionId,
            schemaConfigurationId: this.schemaConfigurationId,
            entityId: this.entityId,
            entityGameId: this.entityGameId,
            gameCode: this.gameCode,
            gameGroupId: this.gameGroupId,
            segmentId: this.segmentId,

            gameGroupName: (this.gameGroup && this.gameGroup.get("name")) || undefined,
            segment: (this.segment && this.segment.toInfo()) || undefined,

            title: this.title,
            description: this.description,

            externalBrandId: this.externalBrandId,

            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
    (model as any).prototype.toDetailedInfo = function(limitsFromRedis: GameLimitsConfigurationInStorage) {
        const info = this.toInfo();
        const status = limitsFromRedis && !limitsFromRedis.processing
            ? limitsFromRedis.status || LIMITS_CONFIGURATION_STATUS.ACTIVE
            : LIMITS_CONFIGURATION_STATUS.BROKEN;

        return {
            ...info,
            status,
            ...limitsFromRedis
        };
    };
    return model;
}
