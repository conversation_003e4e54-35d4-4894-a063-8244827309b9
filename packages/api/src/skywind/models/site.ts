import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
} from "sequelize";
import { Site } from "../entities/site";
import { sequelize as db} from "../storage/db";

export class SiteModel extends Model<
    InferAttributes<SiteModel>,
    InferCreationAttributes<SiteModel>
> {
    declare id: CreationOptional<number>;
    declare name: string;
    declare description: string;
    declare type: string;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toInfo(): Site {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            type: this.type,
        };
    }
}

const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    name: DataTypes.STRING,
    description: DataTypes.STRING,
    type: { type: DataTypes.ENUM("cte"), allowNull: false },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
    },
};

SiteModel.init(
    schema,
    {
        modelName: "site",
        tableName: "sites",
        sequelize: db,
        underscored: true,
    }
);

export function get() {
    return SiteModel;
}
