import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { BiReportDomains } from "../entities/biReportDomains";
import { NonFunctionProperties } from "../entities/typeUtils";

type BiReportDomainsAttributes = NonFunctionProperties<BiReportDomains>;
export interface BiReportDomainsDBInstance extends Model<
        InferAttributes<BiReportDomainsDBInstance>,
        InferCreationAttributes<BiReportDomainsDBInstance>
    >,
    BiReportDomainsAttributes {
}

export type IBiReportDomainsModel = ModelStatic<BiReportDomainsDBInstance>;
const model: IBiReportDomainsModel = db.define<BiReportDomainsDBInstance, BiReportDomainsAttributes>(
    "bi_report_domains",
    {
        id: { field: "id", type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        trustServerUrl: { field: "trust_server_url", type: DataTypes.STRING, allowNull: false },
        baseUrl: { field: "base_url", type: DataTypes.STRING, allowNull: false },
        isSelected: { field: "is_selected", type: DataTypes.BOOLEAN, defaultValue: false, allowNull: false },
        version: { field: "version", type: DataTypes.INTEGER, defaultValue: 0, allowNull: false },
        createdAt: {
            field: "created_at",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        updatedAt: {
            field: "updated_at",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
    },
    {
        timestamps: false,
        freezeTableName: true
    }
);
export function getBiReportDomainsModel(): IBiReportDomainsModel {
    return model;
}
