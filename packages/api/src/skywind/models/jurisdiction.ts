import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { get as getUserModel } from "./user";
import { get as getEntityModel } from "./entity";
import { sequelize as db } from "../storage/db";
import {
    AllowedJackpotConfigurationLevel,
    EntityJurisdiction,
    Jurisdiction,
} from "../entities/jurisdiction";

const UserModel = getUserModel();
const EntityModel = getEntityModel();

export interface JurisdictionDBInstance extends Model<
        InferAttributes<JurisdictionDBInstance>,
        InferCreationAttributes<JurisdictionDBInstance>
    >,
    Jurisdiction {
}
const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    title: { type: DataTypes.STRING, comment: "Title of jurisdiction" },
    code: { type: DataTypes.STRING, allowNull: false, validate: { notEmpty: true }, unique: true },
    createdUserId: {
        type: DataTypes.INTEGER,
        field: "creator_id",
        allowNull: true,
        references: {
            model: UserModel,
            key: "id",
        },
    },
    updatedUserId: {
        type: DataTypes.INTEGER,
        field: "updater_id",
        allowNull: true,
        references: {
            model: UserModel,
            key: "id",
        },
    },
    description: { type: DataTypes.TEXT, comment: "Jurisdiction description" },
    settings: { field: "settings", type: DataTypes.JSONB, allowNull: false, comment: "Jurisdiction settings" },
    createdAt: { field: "created_at", type: DataTypes.DATE },
    updatedAt: { field: "updated_at", type: DataTypes.DATE },
    allowedCountries: {
        field: "allowed_countries",
        type: DataTypes.JSONB,
        allowNull: true
    },
    restrictedCountries: {
        field: "restricted_countries",
        type: DataTypes.JSONB,
        allowNull: true
    },
    defaultCountry: {
        field: "default_country",
        type: DataTypes.STRING,
        allowNull: true
    },
    allowedJackpotConfigurationLevel: {
        field: "allowed_jackpot_configuration_level",
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: AllowedJackpotConfigurationLevel.NO_RESTRICTIONS
    }
};

export type IJurisdictionModel = ModelStatic<JurisdictionDBInstance>;
const JurisdictionModel: IJurisdictionModel = db.define<JurisdictionDBInstance, Jurisdiction>(
    "jurisdiction",
    schema,
    {
        indexes: [
            { fields: ["creator_id"] },
            { fields: ["updater_id"] }
        ],
        comment: "Stores jurisdiction settings"
    }
);

export function get(): IJurisdictionModel {
    return JurisdictionModel;
}

// Jurisdiction to entity mapping:
export interface EntityJurisdictionDBInstance extends Model<
        InferAttributes<EntityJurisdictionDBInstance>,
        InferCreationAttributes<EntityJurisdictionDBInstance>
    >,
    EntityJurisdiction {
    jurisdiction: JurisdictionDBInstance;
}
const JurisdictionEntitySchema = {
    jurisdictionId: {
        primaryKey: true,
        type: DataTypes.INTEGER,
        field: "jurisdiction_id",
        references: {
            model: JurisdictionModel,
            key: "id",
            as: "jurisdiction"
        } as any,
        comment: "Jurisdiction id this jurisdiction mapping belongs to"
    },
    entityId: {
        primaryKey: true,
        type: DataTypes.INTEGER,
        field: "entity_id",
        references: {
            model: EntityModel,
            key: "id"
        },
        comment: "Entity to which this jurisdiction"
    },
    createdAt: { field: "created_at", type: DataTypes.DATE },
};

export type IEntityJurisdictionModel = ModelStatic<EntityJurisdictionDBInstance>;
const EntityJurisdictionModel: IEntityJurisdictionModel = db.define<EntityJurisdictionDBInstance, EntityJurisdiction>(
    "JurisdictionToEntityMapping",
    JurisdictionEntitySchema,
    {
        underscored: true,
        updatedAt: false,
        tableName: "jurisdiction_entity",
        indexes: [
            { fields: ["jurisdiction_id", "entity_id"] }
        ],
        comment: "Maps jurisdiction to entity"
    }
);

EntityJurisdictionModel.belongsTo(JurisdictionModel, { foreignKey: "jurisdictionId" });
EntityJurisdictionModel.belongsTo(EntityModel, { foreignKey: "entityId" });

export function getEntityJurisdictionModel(): IEntityJurisdictionModel {
    return EntityJurisdictionModel;
}
