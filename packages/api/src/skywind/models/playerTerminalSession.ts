import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { get as getPlayerModel } from "./player";
import { get as getPlayerSessionModel } from "./playerSession";
import { get as getTerminalModel } from "./terminal";
import { sequelize as db } from "../storage/db";

const PlayerModel = getPlayerModel();
const PlayerSessionModel = getPlayerSessionModel();
const TerminalModel = getTerminalModel();

export interface PlayerTerminalAttributes {
    id?: number;
    playerId?: number;
    terminalId?: number;
    playerSessionId?: string;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface PlayerTerminalSessionDBInstance extends Model<
        InferAttributes<PlayerTerminalSessionDBInstance>,
        InferCreationAttributes<PlayerTerminalSessionDBInstance>
    >,
    PlayerTerminalAttributes {
}

const playerTerminalSchema = {
    id: {
        type: DataTypes.INTEGER,
        field: "id",
        autoIncrement: true,
        primaryKey: true,
        comment: "Auto-incremented id of instance",
    },
    playerId: {
        type: DataTypes.INTEGER,
        field: "player_id",
        allowNull: false,
        validate: { notEmpty: true },
        references: {
            model: PlayerModel,
            key: "id",
        },
        comment: "Reference to player",
    },
    terminalId: {
        type: DataTypes.INTEGER,
        field: "terminal_id",
        allowNull: false,
        validate: { notEmpty: true },
        references: {
            model: TerminalModel,
            key: "id",
        },
        comment: "Reference to terminal",
    },
    playerSessionId: {
        type: DataTypes.INTEGER,
        field: "player_session_id",
        allowNull: false,
        validate: { notEmpty: true },
        references: {
            model: PlayerSessionModel,
            key: "id",
        },
        comment: "Reference to player session",
    },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
        comment: "When instance is created. Automatically added to db by Sequelize.",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
        comment: "When instance is updated. Automatically changed in db by Sequelize.",
    },
};

export type IPlayerTerminalSessionModel = ModelStatic<PlayerTerminalSessionDBInstance>;
const PlayerTerminalSessionModel: IPlayerTerminalSessionModel =
    db.define<PlayerTerminalSessionDBInstance, PlayerTerminalAttributes>(
        "player_terminals",
        playerTerminalSchema,
        {
            underscored: true,
            timestamps: true,
        }
    );

PlayerTerminalSessionModel.belongsTo(TerminalModel, { as: "terminal" });
TerminalModel.hasMany(PlayerTerminalSessionModel);
PlayerTerminalSessionModel.belongsTo(PlayerModel);
PlayerModel.hasMany(PlayerTerminalSessionModel);

export function get(): IPlayerTerminalSessionModel {
    return PlayerTerminalSessionModel;
}
