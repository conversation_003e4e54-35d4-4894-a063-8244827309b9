import { Model, ModelStatic, DataTypes, InferAttributes, InferCreationAttributes, CreationOptional } from "sequelize";
import { sequelize as db } from "../storage/db";
import { lazy } from "@skywind-group/sw-utils";
import { DeploymentGroupAttributes, DeploymentGroupType } from "../entities/deploymentGroup";

export class DeploymentGroupModel extends Model<
    InferAttributes<DeploymentGroupModel>,
    InferCreationAttributes<DeploymentGroupModel>
> {
    declare id: CreationOptional<number>;
    declare route: string;
    declare type: DeploymentGroupType;
    declare description: string;

    public toInfo(): DeploymentGroupAttributes {
        return {
            id: this.id,
            route: this.route,
            description: this.description,
            type: this.type
        };
    }
}

const depGroupSchema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    route: { type: DataTypes.STRING, allowNull: false, unique: true },
    type: { type: DataTypes.ENUM(...Object.values(DeploymentGroupType)), allowNull: false, field: "group_type" },
    description: DataTypes.STRING,
};

const depGroupModel = lazy(() =>
    DeploymentGroupModel.init(
        depGroupSchema,
        {
            tableName: "deployment_groups",
            sequelize: db,
            timestamps: false,
        }
    )
);
export type IDeploymentGroupModel = ModelStatic<DeploymentGroupModel>;
export function getDeploymentGroupModel(): IDeploymentGroupModel {
    return depGroupModel.get();
}
