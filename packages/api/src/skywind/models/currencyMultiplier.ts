import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { get as getEntityModel } from "./entity";
import { BaseEntity } from "../entities/entity";
import { sequelize as db } from "../storage/db";

const EntityModel = getEntityModel();

export interface CurrencyMultiplier extends CurrencyMultiplierData {
    id?: number;
    entityId?: number;

    createdAt?: Date;
    updatedAt?: Date;
}

export interface CurrencyMultiplierData {
    externalBrandId?: string;
    baseCurrency?: string;
    multipliers?: Multiplier[];
}

interface Multiplier {
    currencyCode: string;
    currencyMultiplier: number;
}

export const DEFAULT_CURRENCY: string = "EUR";

export interface CurrencyMultiplierDBInstance extends Model<
        InferAttributes<CurrencyMultiplierDBInstance>,
        InferCreationAttributes<CurrencyMultiplierDBInstance>
    >,
    CurrencyMultiplier {
    entity: BaseEntity;
    toInfo(): CurrencyMultiplier;
}
export type CurrencyMultiplierModel = ModelStatic<CurrencyMultiplierDBInstance>;
const model: CurrencyMultiplierModel = db.define<CurrencyMultiplierDBInstance, CurrencyMultiplier>(
    "currency_multipliers",
    {
        id: { field: "id", type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        entityId: {
            field: "entity_id",
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: EntityModel,
                key: "id"
            }
        },
        externalBrandId: { field: "external_brand_id", type: DataTypes.STRING, allowNull: true },
        baseCurrency: { field: "base_currency", type: DataTypes.STRING, defaultValue: DEFAULT_CURRENCY },
        multipliers: { field: "multipliers", type: DataTypes.JSONB, allowNull: false },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    { timestamps: true, underscored: true, freezeTableName: true }
);

model.belongsTo(EntityModel, { foreignKey: "entityId" });

export function getCurrencyMultiplierModel(): CurrencyMultiplierModel {
    (model as any).prototype.toInfo = function (): CurrencyMultiplier {
        return {
            id: this.id,
            entityId: this.entityId,
            externalBrandId: this.externalBrandId,
            baseCurrency: this.baseCurrency,
            multipliers: this.multipliers,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
    return model;
}
