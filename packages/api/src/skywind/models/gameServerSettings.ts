import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { GameServerSettings, RangeTypeInstance } from "../entities/gameServerSettings";

export class GameServerSettingsModel extends Model<
    InferAttributes<GameServerSettingsModel>,
    InferCreationAttributes<GameServerSettingsModel>
> {
    declare name: string;
    declare description: string | null;
    declare roundIdRange: [RangeTypeInstance, RangeTypeInstance];
    declare sessionIdRange: [RangeTypeInstance, RangeTypeInstance];

    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toInfo(): GameServerSettings {
        return {
            name: this.name,
            description: this.description,
            roundIdRange: this.prepareRangeInfo(this.roundIdRange),
            sessionIdRange: this.prepareRangeInfo(this.sessionIdRange),
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }

    private prepareRangeInfo(range: [RangeTypeInstance, RangeTypeInstance]): [string, string] {
        return [range?.[0]?.value, range?.[1]?.value];
    }
}

const schema = {
    name: { type: DataTypes.STRING, primaryKey: true },
    description: DataTypes.STRING,
    roundIdRange: {
        type: DataTypes.RANGE(DataTypes.BIGINT),
        allowNull: false,
        field: "round_id_range"
    },
    sessionIdRange: {
        type: DataTypes.RANGE(DataTypes.BIGINT),
        allowNull: false,
        field: "session_id_range"
    },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
        defaultValue: DataTypes.NOW,
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
        defaultValue: DataTypes.NOW,
    }
};

export type IGameServerSettingsModel = ModelStatic<GameServerSettingsModel>;
GameServerSettingsModel.init(
    schema,
    {
        tableName: "game_server_settings",
        sequelize: db,
        underscored: true,
    }
);

export function get(): IGameServerSettingsModel {
    return GameServerSettingsModel;
}
