import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { get as getEntityModel } from "./entity";
import { get as getLobbyModel } from "./lobby";
import { get as getPlayerModel } from "./player";
import { sequelize as db } from "../storage/db";

const EntityModel = getEntityModel();
const LobbyModel = getLobbyModel();
const PlayerModel = getPlayerModel();

export interface TerminalAttributes {
    id?: number;
    title?: string;
    status?: string;
    brandId?: number;
    playerId?: number;
    lobbyId?: number;
    createdAt?: Date;
    updatedAt?: Date;
    player?: any;
}

export interface TerminalDBInstance extends Model<
        InferAttributes<TerminalDBInstance>,
        InferCreationAttributes<TerminalDBInstance>
    >,
    TerminalAttributes {
}

const terminalSchema = {
    id: {
        type: DataTypes.INTEGER,
        field: "id",
        autoIncrement: true,
        primaryKey: true,
        comment: "Terminal auto-incremented id",
    },
    title: {
        type: DataTypes.STRING,
        field: "title",
        allowNull: false,
        validate: { notEmpty: true },
        comment: "Terminal title",
    },
    status: {
        type: DataTypes.ENUM("normal", "suspended"),
        field: "status",
        defaultValue: "normal",
        allowNull: false,
        validate: { notEmpty: true },
    },
    brandId: {
        type: DataTypes.INTEGER,
        field: "brand_id",
        allowNull: false,
        validate: { notEmpty: true },
        references: {
            model: EntityModel,
            key: "id",
        },
        comment: "Reference to brand",
    },
    playerId: {
        type: DataTypes.INTEGER,
        field: "player_id",
        allowNull: true,
        references: {
            model: PlayerModel,
            key: "id",
        },
        comment: "Reference to logged player or null",
    },
    lobbyId: {
        type: DataTypes.INTEGER,
        field: "lobby_id",
        allowNull: false,
        validate: { notEmpty: true },
        references: {
            model: LobbyModel,
            key: "id",
        },
        comment: "Reference to lobby",
    },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
        comment: "When terminal is created. Automatically added to db by Sequelize.",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
        comment: "When terminal is updated. Automatically changed in db by Sequelize.",
    },
};

export type ITerminalModel = ModelStatic<TerminalDBInstance>;
const TerminalModel: ITerminalModel = db.define<TerminalDBInstance, TerminalAttributes>(
    "terminals",
    terminalSchema,
    {
        underscored: true,
        timestamps: true,
        indexes: [
            {
                name: "terminal_brand_ukey",
                unique: true,
                fields: ["title", "brand_id"],
            },
        ],
    }
);

TerminalModel.belongsTo(EntityModel, { as: "brand" });
TerminalModel.belongsTo(LobbyModel, { as: "lobby" });
TerminalModel.belongsTo(PlayerModel, { as: "player" });

export function get(): ITerminalModel {
    return TerminalModel;
}
