import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelizeSlave as slaveDb } from "../storage/db";

export interface WinBet {
    id?: number;
    trxId: string;
    brandId: number;
    paymentDate: Date;
    currency: string;
    bet: number;
    win: number;
    betRollback?: boolean;
    gameId: string;
    gameCode: string;
    playerCode: string;
    insertedAt?: Date;
    transactionType?: any;
    isTest?: boolean;
}

export interface WinBetDBInstance extends Model<
        InferAttributes<WinBetDBInstance>,
        InferCreationAttributes<WinBetDBInstance>
    >,
    WinBet {
}

export type IWinBetModel = ModelStatic<WinBetDBInstance>;
const model: IWinBetModel = slaveDb.define<WinBetDBInstance, WinBet>("wallet_win_bet",
    {
        id: { field: "id", type: DataTypes.BIGINT, autoIncrement: true, primaryKey: true },
        trxId: { field: "trx_id", type: DataTypes.CHAR(28), primaryKey: false },
        brandId: { field: "brand_id", type: DataTypes.INTEGER, primaryKey: false },
        currency: { field: "currency", type: DataTypes.CHAR(3), primaryKey: false },
        paymentDate: { field: "payment_date", type: DataTypes.DATE, primaryKey: false },
        bet: { field: "bet", type: DataTypes.DECIMAL, allowNull: false, defaultValue: 0 },
        win: { field: "win", type: DataTypes.DECIMAL, allowNull: false, defaultValue: 0 },
        betRollback: { field: "bet_rollback", type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
        gameId: { field: "game_id", type: DataTypes.STRING, allowNull: true },
        gameCode: { field: "game_code", type: DataTypes.STRING, allowNull: true },
        playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: false, validate: {
                notEmpty: true,
                len: [6, 32],
            },
        },
        transactionType: { field: "transaction_type", type: DataTypes.ENUM("free_bet", "jackpot") },
        isTest: { field: "is_test", type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
        insertedAt: {
            field: "inserted_at",
            type: DataTypes.DATE,
            allowNull: true,
            defaultValue: DataTypes.NOW,
        }
    },
    {
        freezeTableName: true,
        timestamps: false,
    }
);

export function getWinBetModel() {
    return model;
}
