import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";

export interface CurrencyRate {
    currencyCode: string;
    date: string;
    rate: string;
    timestamp: string;
}

export interface CurrencyRateDBInstance extends Model<
        InferAttributes<CurrencyRateDBInstance>,
        InferCreationAttributes<CurrencyRateDBInstance>
    >,
    CurrencyRate {
}

export type ICurrencyRateModel = ModelStatic<CurrencyRateDBInstance>;
export const CurrencyRateModel: ICurrencyRateModel = db.define<CurrencyRateDBInstance, CurrencyRate>(
    "currency_rate",
    {
        currencyCode: {
            type: DataTypes.STRING,
            field: "currency_code",
            allowNull: false,
            validate: { len: [3, 3] },
            primaryKey: true,
        },
        date: {
            type: DataTypes.DATEONLY,
            field: "rate_date",
            allowNull: false,
            primaryKey: true,
        },
        rate: {
            type: DataTypes.DECIMAL,
            field: "rate",
            allowNull: false,
        },
        timestamp: {
            type: DataTypes.BIGINT,
            field: "ts",
            allowNull: false,
        },
    },
    {
        createdAt: false,
        updatedAt: false,
    }
);

export function get(): ICurrencyRateModel {
    return CurrencyRateModel;
}
