import { Model, DataTypes, InferAttributes, InferCreationAttributes, CreationOptional } from "sequelize";
import { sequelize as db } from "../storage/db";
import { DynamicDomain, StaticDomain } from "../entities/domain";

export class DynamicDomainModel extends Model<
    InferAttributes<DynamicDomainModel>,
    InferCreationAttributes<DynamicDomainModel>
> {
    declare id: CreationOptional<number>;
    declare domain: string;
    declare environment: string | null;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toInfo(): DynamicDomain {
        return {
            id: this.id,
            domain: this.domain,
            environment: this.environment,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
}

DynamicDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: false, unique: true },
        environment: { field: "environment", type: DataTypes.STRING, allowNull: false },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "dynamic_domains",
        sequelize: db,
        underscored: true,
    }
);

export function getDynamicDomainModel() {
    return DynamicDomainModel;
}

export class StaticDomainModel extends Model<
    InferAttributes<StaticDomainModel>,
    InferCreationAttributes<StaticDomainModel>
> {
    declare id: CreationOptional<number>;
    declare domain: string;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toInfo(): StaticDomain {
        return {
            id: this.id,
            domain: this.domain,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
}

StaticDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: false, unique: true },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "static_domains",
        sequelize: db,
    }
);

export function getStaticDomainModel() {
    return StaticDomainModel;
}
