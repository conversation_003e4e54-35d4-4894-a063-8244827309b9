import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { getEntityGame } from "./game";
import { sequelize as db } from "../storage/db";
import { LIMITS_CONFIGURATION_STATUS } from "../services/gameLimits/helper";

const EntityGameModel = getEntityGame();

export interface SegmentDimensions {
    currency?: string[];
    clientPlatform?: string[];
    vipLevel?: string[];

    [field: string]: string[]; // custom fields
}

export interface Segment {
    id?: number;
    externalId?: string;

    entityGameId?: number;
    externalResellerId?: number;

    priority?: number;
    segment: SegmentDimensions;

    matchCount?: number;

    status?: LIMITS_CONFIGURATION_STATUS;

    createdAt?: Date;
    updatedAt?: Date;
}

export interface SegmentSearchData {
    entityGameId?: number;

    mode?: SEGMENT_SEARCH_MODE;
    segment: SegmentDimensions;
    externalResellerId?: number;
    externalId?: string;
}

export interface SegmentValues {
    [field: string]: string[];
}

export interface PlayerSegmentValues {
    [field: string]: string;
}

export interface QueryForSegment {
    queries: string[];
    replacements: {
        [field: string]: any
    };
}

export enum SEGMENT_SEARCH_MODE {
    LIKE = "like",
    EXACT = "exact"
}

type SegmentInfo = Required<Omit<Segment, "externalResellerId" | "matchCount" | "status">>;
export interface SegmentDBInstance extends Model<
        InferAttributes<SegmentDBInstance>,
        InferCreationAttributes<SegmentDBInstance>
    >,
    Segment {
    toInfo(): SegmentInfo;
}
export type SegmentModel = ModelStatic<SegmentDBInstance>;
const model: SegmentModel = db.define<SegmentDBInstance, Segment>(
    "segments",
    {
        id: { field: "id", type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        externalId: { field: "external_id", type: DataTypes.STRING, allowNull: true },
        priority: { field: "priority", type: DataTypes.INTEGER, defaultValue: 0 },
        externalResellerId: { field: "external_reseller_id", type: DataTypes.INTEGER, allowNull: true },
        entityGameId: {
            field: "entity_game_id",
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: EntityGameModel,
                key: "id"
            }
        },
        segment: { field: "segment", type: DataTypes.JSONB, allowNull: true },
        status: { field: "status", type: DataTypes.STRING, defaultValue: "active" },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE }
    },
    { timestamps: true, underscored: true, freezeTableName: true }
);

model.belongsTo(EntityGameModel, { foreignKey: "entityGameId" });

export function getSegmentModel(): SegmentModel {
    (model as any).prototype.toInfo = function (): SegmentInfo {
        return {
            id: this.id,
            externalId: this.externalId,
            entityGameId: this.entityGameId,
            priority: this.priority,
            segment: this.segment,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
    return model;
}
