import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelizeJpSlave as db } from "../storage/db";

export interface AggrJpPlayerContributions {
    dateHour: Date;
    brandId: number;
    gameCode: string;
    playerCode: string;
    currency: string;
    jackpotId: string;
    pool: string;
    seedAmount: number;
    progressiveAmount: number;
    totalBetAmount: number;
    jpWinAmount: number;
    jpCurrency: string;
    seedAmountJpCurrency: number;
    progressiveAmountJpCurrency: number;
    totalBetAmountJpCurrency: number;
    jpWinAmountJpCurrency: number;
    totalBetCount: number;
    jpWinCount: number;
    firstActivity: Date;
    lastActivity: Date;
    seedWin: number;
    progressiveWin: number;
}

export interface AggrJpPlayerContributionsDBInstance extends Model<
        InferAttributes<AggrJpPlayerContributionsDBInstance>,
        InferCreationAttributes<AggrJpPlayerContributionsDBInstance>
    >,
    AggrJpPlayerContributions {
}

const schema = {
    dateHour: { field: "date_hour", type: DataTypes.DATE, allowNull: false, primaryKey: true },
    brandId: { field: "brand_id", type: DataTypes.INTEGER, allowNull: false, primaryKey: true },
    gameCode: { field: "game_code", type: DataTypes.STRING, allowNull: false, primaryKey: true },
    playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: false, primaryKey: true },
    currency: { field: "currency_code", type: DataTypes.CHAR(3), allowNull: false },
    jackpotId: { field: "jackpot_id", type: DataTypes.STRING, allowNull: false },
    pool: { field: "pool", type: DataTypes.STRING, allowNull: false },
    seedAmount: { field: "seed_amount", type: DataTypes.DECIMAL, allowNull: false },
    progressiveAmount: { field: "progressive_amount", type: DataTypes.DECIMAL, allowNull: false },
    totalBetAmount: { field: "total_bet_amount", type: DataTypes.DECIMAL, allowNull: false },
    jpWinAmount: { field: "jp_win_amount", type: DataTypes.DECIMAL, allowNull: false },
    jpCurrency: { field: "jp_currency_code", type: DataTypes.CHAR(3), allowNull: false },
    seedAmountJpCurrency: { field: "seed_amount_jp_cur", type: DataTypes.DECIMAL, allowNull: false },
    progressiveAmountJpCurrency: { field: "progressive_amount_jp_cur", type: DataTypes.DECIMAL, allowNull: false },
    totalBetAmountJpCurrency: { field: "total_bet_amount_jp_cur", type: DataTypes.DECIMAL, allowNull: false },
    jpWinAmountJpCurrency: { field: "jp_win_amount_jp_cur", type: DataTypes.DECIMAL, allowNull: false },
    totalBetCount: { field: "total_bet_count", type: DataTypes.INTEGER, allowNull: false },
    jpWinCount: { field: "jp_win_count", type: DataTypes.INTEGER, allowNull: false },
    firstActivity: { field: "first_activity", type: DataTypes.DATE },
    lastActivity: { field: "last_activity", type: DataTypes.DATE },
    seedWin: { field: "seed_win", type: DataTypes.DECIMAL, allowNull: true, defaultValue: 0 },
    progressiveWin: { field: "progressive_win", type: DataTypes.DECIMAL, allowNull: true, defaultValue: 0 }
};

export type IAggrJpPlayerContributionsModel = ModelStatic<AggrJpPlayerContributionsDBInstance>;
const model: IAggrJpPlayerContributionsModel =
    db.define<AggrJpPlayerContributionsDBInstance, AggrJpPlayerContributions>(
    "bo_aggr_jp_player_contributions",
    schema,
    {
        freezeTableName: true,
        timestamps: false,
    }
);

export function getAggrJpPlayerContributionsModel() {
    return model;
}
