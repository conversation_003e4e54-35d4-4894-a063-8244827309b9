import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { get as getUserModel } from "./user";

const UserModel = getUserModel();

export interface RefreshTokenAttributes {
    id?: number;
    token?: string;
    userId?: number;
    clientId?: number;
    scopes?: string[];
    expiresAt?: Date;
}
export interface RefreshTokenDBInstance extends Model<
    InferAttributes<RefreshTokenDBInstance>,
    InferCreationAttributes<RefreshTokenDBInstance>
>, RefreshTokenAttributes {
}

const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    token: {
        type: DataTypes.STRING,
        field: "token",
        allowNull: false
    },
    userId: {
        type: DataTypes.INTEGER,
        field: "user_id",
        allowNull: false,
        references: {
            model: UserModel,
            key: "id"
        }
    },
    clientId: {
        type: DataTypes.INTEGER,
        field: "client_id",
        allowNull: false
    },
    scopes: {
        field: "scopes",
        allowNull: true,
        type: DataTypes.JSONB,
        defaultValue: []
    },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
    },
    expiresAt: {
        type: DataTypes.DATE,
        field: "expires_at",
    }
};

export type IRefreshTokenModel = ModelStatic<RefreshTokenDBInstance>;
const refreshTokenModel = db.define<RefreshTokenDBInstance, RefreshTokenAttributes>(
    "oauth_refresh_tokens",
    schema,
    {
        timestamps: true,
        updatedAt: false
    }
);

refreshTokenModel.belongsTo(UserModel, { foreignKey: "userId" });

export function get(): IRefreshTokenModel {
    return refreshTokenModel;
}
