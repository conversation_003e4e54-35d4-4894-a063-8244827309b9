import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";

export interface StakeRange {
    currency: string;
    coinBets: number[];
    lowerStakes?: number[];
    createdAt?: Date;
    updatedAt?: Date;
}

export interface StakeRangeDBInstance extends Model<
    InferAttributes<StakeRangeDBInstance>,
    InferCreationAttributes<StakeRangeDBInstance>
>, StakeRange {
    toInfo(): StakeRange;
}

export type StakeRangeModel = ModelStatic<StakeRangeDBInstance>;
const model: StakeRangeModel = db.define<StakeRangeDBInstance, StakeRange>(
    "stake_ranges",
    {
        currency: { field: "currency", type: DataTypes.STRING, primaryKey: true },
        coinBets: { field: "coin_bets", type: DataTypes.JSONB, allowNull: false },
        createdAt: {
            field: "created_at",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        updatedAt: {
            field: "updated_at",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        lowerStakes: { field: "lower_stakes", type: DataTypes.JSONB, allowNull: true },
    },
    { timestamps: false }
);

export function getStakeRangeModel(): StakeRangeModel {
    (model as any).prototype.toInfo = function(): StakeRange {
        return {
            currency: this.currency,
            coinBets: this.coinBets,
            lowerStakes: this.lowerStakes,
        };
    };
    return model;
}
