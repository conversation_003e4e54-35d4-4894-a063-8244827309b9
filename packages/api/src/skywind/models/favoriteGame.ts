import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
    ForeignKey
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { FavoriteGame } from "../entities/game";
import { EntityModel } from "./entity";

export class FavoriteGameModel extends Model<
    InferAttributes<FavoriteGameModel>,
    InferCreationAttributes<FavoriteGameModel>
> {
    declare entityId: ForeignKey<EntityModel["id"]>;
    declare playerCode: string;
    declare gameCode: string;
    declare updatedAt: CreationOptional<Date>;
    declare createdAt: CreationOptional<Date>;
    declare isFavorite: boolean;
    declare isRecently: boolean;

    public toFavoriteGame(): string {
        return this.gameCode;
    };
    public toRecentlyGame(): { gameCode: string, updatedAt: Date } {
        return {
            gameCode: this.gameCode,
            updatedAt: this.updatedAt
        };
    };
    public toInfo(): FavoriteGame {
        return {
            entityId: this.entityId,
            playerCode: this.playerCode,
            gameCode: this.gameCode,
            isFavorite: this.isFavorite,
            isRecently: this.isRecently,
            updatedAt: this.updatedAt
        };
    }
}

const schema = {
    entityId: {
        field: "entity_id",
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        onUpdate: "NO ACTION",
        onDelete: "CASCADE",
        references: {
            model: EntityModel,
            key: "id",
        },
        comment: "Reference to Entity"
    },
    playerCode: {
        field: "player_code",
        type: DataTypes.STRING,
        allowNull: false,
        primaryKey: true,
        comment: "Reference to Player"
    },
    gameCode: {
        field: "game_code",
        type: DataTypes.STRING,
        allowNull: false,
        primaryKey: true,
        comment: "Reference to Game"
    },
    isFavorite: {
        field: "is_favorite",
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
    },
    isRecently: {
        field: "is_recently",
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
    },
    updatedAt: {
        field: "updated_at",
        type: DataTypes.DATE,
    },
    createdAt: {
        field: "created_at",
        type: DataTypes.DATE,
    },
};

FavoriteGameModel.init(
    schema,
    {
        sequelize: db,
        underscored: true,
        tableName: "favorite_games_by_player",
        modelName: "favorite_games_by_player",
    }
);

export function getFavoriteGamesModel() {
    return FavoriteGameModel;
}
