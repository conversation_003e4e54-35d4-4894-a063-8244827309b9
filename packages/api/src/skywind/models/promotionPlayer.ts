import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { getPromotionModel } from "./promotion";
import { get as getPlayerModel } from "./player";
import { get as getEntityModel } from "./entity";

const PromotionModel = getPromotionModel();
const PlayerModel = getPlayerModel();
const EntityModel = getEntityModel();

export enum PromotionToPlayerStatus {
    PENDING = "pending",        // player added to inactive promo
    CONFIRMED = "confirmed",    // player added to active promo
    STARTED = "started",        // promo started for player
    FINISHED = "finished",      // promo finished for player
    REVOKED = "revoked"         // player removed from active promo
}

export interface PromotionToPlayerOperation {
    operationId: number;
    promoStatus?: string;
    isNew?: boolean;
    update?: PromotionToPlayer;
    batchUpdate?: PromotionToPlayer;
}

export interface PromotionToPlayer {
    id?: number;
    promotionId?: number;
    playerId?: number;
    playerCode?: string;
    playerCurrency?: string;
    activeTransactionId?: string;
    activeOperation?: PromotionToPlayerOperation;
    status?: PromotionToPlayerStatus;
    finishStatus?: "expired" | "finished" | string;
    data?: any;
    startedAt?: Date;
    rewardedAt?: Date;
    expireAt?: Date;
    playedAt?: Date;
    finishedAt?: Date;
    createdAt?: Date;
    updatedAt?: Date;
    brandId?: number;
    external_id?: string;
}

export interface PromotionToPlayerUpdate {
    id?: number;
    promotionId?: number;
    playerCode?: string;
    status?: PromotionToPlayerStatus;
    finishStatus?: "expired" | "finished" | string;
    playedAt?: Date;
    finishedAt?: Date;
    insertedAt?: Date;
}

export class PromotionToPlayerImpl implements PromotionToPlayer {
    public id: number;
    public activeTransactionId: string;
    public activeOperation: PromotionToPlayerOperation;
    public playerId: number;
    public playerCode: string;
    public playerCurrency: string;
    public promotionId: number;
    public status: PromotionToPlayerStatus;
    public finishStatus: string;
    public data: object;
    public startedAt: Date;
    public rewardedAt: Date;
    public expireAt?: Date;
    public brandId?: number;

    public isRunning() {
        return this.status === PromotionToPlayerStatus.STARTED && this.expireAt > new Date();
    }
    public isPending() {
        return this.status === PromotionToPlayerStatus.PENDING;
    }
    public isRevoked() {
        return this.status === PromotionToPlayerStatus.REVOKED;
    }
    public hasFinished() {
        return this.status !== PromotionToPlayerStatus.PENDING &&
            this.status !== PromotionToPlayerStatus.CONFIRMED &&
            !this.isRunning();
    }
}

export interface PromotionToPlayerInstance extends Model<
        InferAttributes<PromotionToPlayerInstance>,
        InferCreationAttributes<PromotionToPlayerInstance>
    >,
    PromotionToPlayer {
    isRunning(): boolean;
    isPending(): boolean;
    isRevoked(): boolean;
    hasFinished(): boolean;
}
const schema = {
        id: { field: "id", type: DataTypes.BIGINT, autoIncrement: true, primaryKey: true },
        activeTransactionId: { field: "active_transaction_id", type: DataTypes.STRING, allowNull: true, },
        activeOperation: { field: "active_operation", type: DataTypes.JSONB, comment: "Active transaction operation details" },
        playerId: {
            field: "player_id",
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: PlayerModel,
                key: "id",
            },
        },
        playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: false },
        playerCurrency: {
            field: "player_currency",
            type: DataTypes.STRING,
            allowNull: true,
            comment: "Player currency. Can be empty for merchant players if promo not started"
        },
        promotionId: {
            field: "promotion_id",
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: PromotionModel,
                key: "id",
            },
        },
        status: { field: "status", type: DataTypes.STRING, allowNull: false },
        finishStatus: { field: "finish_status", type: DataTypes.STRING, allowNull: true },
        data: { field: "data", type: DataTypes.JSONB, comment: "Additional data" },
        startedAt: {
            field: "started_at",
            type: DataTypes.DATE,
            comment: "Timestamp when promo started for player",
        },
        rewardedAt: {
            field: "rewarded_at",
            type: DataTypes.DATE,
            comment: "Timestamp when promo has been applied to player's wallet",
        },

        expireAt: {
            field: "expire_at",
            type: DataTypes.DATE,
            comment: "Timestamp when promo expires for player",
        },
        playedAt: {
            field: "played_at",
            type: DataTypes.DATE,
            comment: "Timestamp when player first time played promo",
        },
        finishedAt: {
            field: "finished_at",
            type: DataTypes.DATE,
            comment: "Timestamp when player finished playing promo",
        },
        createdAt: {
            field: "created_at",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
            comment: "Timestamp when promo added to player",
        },
        updatedAt: {
            field: "updated_at",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
            comment: "Timestamp when promo modified for player",
        },
        brandId: {
            field: "brand_id",
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: EntityModel,
                key: "id",
            },
        },
    };
export type IPromotionToPlayerModel = ModelStatic<PromotionToPlayerInstance>;
const model = db.define<PromotionToPlayerInstance, PromotionToPlayer>(
    "promotion_players",
    schema,
    {
        timestamps: true,
        underscored: true,
        freezeTableName: true,
        indexes: [
            {
                name: "idx_promotion_players_player_code_promotion_id",
                fields: ["player_code", "promotion_id"],
                unique: true
            },
            {
                name: "idx_promotion_players_promotion_id_status",
                fields: ["promotion_id", "status"]
            }
        ]
    },
);

model.belongsTo(PromotionModel, { foreignKey: "promotionId" });
PromotionModel.hasMany(model, { as: "players", foreignKey: "promotionId" });

model.belongsTo(EntityModel, { foreignKey: "brandId"});
EntityModel.hasMany(model, { as: "promoPlayers", foreignKey: "brandId"});

export function getPromotionToPlayerModel(): IPromotionToPlayerModel {
    (model as any).prototype.isRunning = function() {
        return this.status === PromotionToPlayerStatus.STARTED && this.expireAt > new Date();
    };
    (model as any).prototype.isPending = function() {
        return this.status === PromotionToPlayerStatus.PENDING;
    };
    (model as any).prototype.isRevoked = function() {
        return this.status === PromotionToPlayerStatus.REVOKED;
    };
    (model as any).prototype.hasFinished = function() {
        return this.status !== PromotionToPlayerStatus.PENDING &&
            this.status !== PromotionToPlayerStatus.CONFIRMED &&
            !this.isRunning();
    };
    return model;
}

export interface PromotionToPlayerUpdateImpl extends Model<
        InferAttributes<PromotionToPlayerUpdateImpl>,
        InferCreationAttributes<PromotionToPlayerUpdateImpl>
    >,
    PromotionToPlayerUpdate {
}
export type IPromotionToPlayerUpdateModel = ModelStatic<PromotionToPlayerUpdateImpl>;
const ppUpdateModel = db.define<PromotionToPlayerUpdateImpl, PromotionToPlayerUpdate>(
    "promotion_players_update",
    {
        id: { field: "id", type: DataTypes.BIGINT, autoIncrement: true, primaryKey: true },
        playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: false },
        promotionId: {
            field: "promotion_id",
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: PromotionModel,
                key: "id",
            },
        },
        status: { field: "status", type: DataTypes.STRING },
        finishStatus: { field: "finish_status", type: DataTypes.STRING },
        playedAt: {
            field: "played_at",
            type: DataTypes.DATE,
            comment: "Timestamp when player first time played promo"
        },
        finishedAt: {
            field: "finished_at",
            type: DataTypes.DATE,
            comment: "Timestamp when player finished playing promo"
        },
        insertedAt: {
            field: "inserted_at",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
    },
    {
        freezeTableName: true,
        timestamps: false,
        indexes: [
            {
                name: "idx_promotion_players_update_player_code_promotion_id",
                fields: ["player_code", "promotion_id"]
            }
        ]
    }
);
export function getPromotionToPlayerUpdateModel(): IPromotionToPlayerUpdateModel {
    return ppUpdateModel;
}
