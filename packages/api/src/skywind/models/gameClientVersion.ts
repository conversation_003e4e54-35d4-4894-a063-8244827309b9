import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { lazy } from "@skywind-group/sw-utils";
import { getGameModel } from "./game";
import { getDeploymentGroupModel } from "./deploymentGroup";
import { GameClientFeatures } from "../entities/game";

const deploymentGroupModel = getDeploymentGroupModel();
const gameModel = getGameModel();
export interface GameVersionAttributes {
    id?: number;
    deploymentGroupId: number;
    gameCode: string;
    clientVersion: string;
    clientFeatures: GameClientFeatures;
}

export interface GameVersionDBInstance extends Model<
        InferAttributes<GameVersionDBInstance>,
        InferCreationAttributes<GameVersionDBInstance>
    >,
    GameVersionAttributes {
}

export class GameVersionItem {
    public readonly deploymentGroupId: number;
    public readonly gameCode: string;
    public readonly clientVersion: string;
    public readonly clientFeatures: GameClientFeatures;

    constructor(instance: GameVersionDBInstance) {
        this.deploymentGroupId = instance.get("deploymentGroupId");
        this.gameCode = instance.get("gameCode");
        this.clientVersion = instance.get("clientVersion");
        this.clientFeatures = instance.get("clientFeatures") || {};
    }
}

const gameVersionSchema = {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true
    },
    deploymentGroupId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        field: "deployment_group_id"
    },
    gameCode: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "game_code"
    },
    clientVersion: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "client_version"
    },
    clientFeatures: {
        type: DataTypes.JSONB,
        field: "client_features",
    },
};

const gameVersionsModel = lazy(() => db.define<GameVersionDBInstance, GameVersionAttributes>(
    "game_versions",
    gameVersionSchema,
    {
        timestamps: false,
        indexes: [
            { name: "idx_game_code", unique: false, fields: [ "game_code" ] },
            { name: "idx_game_code_deployment_group_id", unique: true, fields: [ "game_code", "deployment_group_id" ] },

        ],
    })
);

getGameVersionModel().belongsTo(deploymentGroupModel,
    { foreignKey: "deploymentGroupId", targetKey: "id", onDelete: "CASCADE" });
getGameVersionModel().belongsTo(gameModel,
    { foreignKey: "game_code", targetKey: "code", onDelete: "CASCADE" });

type IGameVersionModel = ModelStatic<GameVersionDBInstance>;
export function getGameVersionModel(): IGameVersionModel {
    return gameVersionsModel.get();
}
