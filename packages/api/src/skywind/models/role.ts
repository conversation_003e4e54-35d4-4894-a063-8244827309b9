import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { Role, UserRole } from "../entities/role";
import { get as getEntityModel } from "./entity";
import { get as getUserModel } from "./user";
import { sequelize as db } from "../storage/db";

const EntityModel = getEntityModel();
const UserModel = getUserModel();

export interface RoleDBInstance extends Model<
        InferAttributes<RoleDBInstance>,
        InferCreationAttributes<RoleDBInstance>
    >,
    Role {
}

const roleSchema = {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true
    },
    title: {
        type: DataTypes.STRING,
        validate: { notEmpty: true },
        allowNull: false,
    },
    description: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    permissions: {
        type: DataTypes.JSONB,
        allowNull: false,
    },
    entityId: {
        type: DataTypes.INTEGER,
        references: {
            model: EntityModel,
            key: "id",
        },
        field: "entity_id",
        allowNull: false,
    },
    isShared: {
        type: DataTypes.BOOLEAN,
        field: "is_shared",
        allowNull: false,
    },
};

export type IRoleModel = ModelStatic<RoleDBInstance>;
const RoleModel: IRoleModel = db.define<RoleDBInstance, Role>(
    "roles",
    roleSchema,
    {
        underscored: true,
        indexes: [
            { fields: ["id"] },
            { fields: ["entity_id"] },
        ],
    });

RoleModel.belongsTo(EntityModel, { foreignKey: "entityId" });

export interface UserRoleDBInstance extends Model<
        InferAttributes<UserRoleDBInstance>,
        InferCreationAttributes<UserRoleDBInstance>
    >,
    UserRole {
}

export type IUserRoleModel = ModelStatic<UserRoleDBInstance>;
const UserRoleModel: IUserRoleModel = db.define<UserRoleDBInstance, UserRole>(
    "user_roles",
    {
        userId: {
            type: DataTypes.INTEGER,
            references: {
                model: UserModel,
                key: "id",
            },
            field: "user_id",
            allowNull: false,
        },
        roleId: {
            type: DataTypes.INTEGER,
            references: {
                model: RoleModel,
                key: "id",
            },
            field: "role_id",
            allowNull: false,
        },
    },
    {
        underscored: true,
        updatedAt: false,
        indexes: [
            { fields: ["role_id"] },
        ],
    });

RoleModel.belongsToMany(UserModel, { through: UserRoleModel, onDelete: "NO ACTION", onUpdate: "NO ACTION" });
UserModel.belongsToMany(RoleModel, { through: UserRoleModel, onDelete: "NO ACTION", onUpdate: "NO ACTION" });

export function getUserRoleModel(): IUserRoleModel {
    return UserRoleModel;
}

export function getRoleModel(): IRoleModel {
    return RoleModel;
}
