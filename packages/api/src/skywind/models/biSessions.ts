import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { BiSession } from "../entities/biReport";
import { get as getEntityModel } from "./entity";

const EntityModel = getEntityModel();

export interface BiSessionDBInstance extends Model<
        InferAttributes<BiSessionDBInstance>,
        InferCreationAttributes<BiSessionDBInstance>
    >,
    BiSession {
}
export type IBiSessionModel = ModelStatic<BiSessionDBInstance>;
const model: IBiSessionModel = db.define<BiSessionDBInstance, BiSession>(
    "bi_sessions",
    {
        token: { field: "token", type: DataTypes.STRING, primaryKey: true },
        name: { field: "name", type: DataTypes.STRING },
        workbook: { field: "workbook", type: DataTypes.STRING },
        expiredAt: { field: "expired_at", type: DataTypes.DATE, allowNull: false, },
        createdAt: {
            field: "created_at",
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        entityId: {
            field: "entity_id",
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: EntityModel,
                key: "id",
            },
        },
    },
    {
        timestamps: false,
        freezeTableName: true
    }
);
export function getBiSessionModel(): IBiSessionModel {
    return model;
}
