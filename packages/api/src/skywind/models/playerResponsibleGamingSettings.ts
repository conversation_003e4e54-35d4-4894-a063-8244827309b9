import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { get as getEntityModel } from "./entity";

const EntityModel = getEntityModel();

export type ResponsibleGamingSettingsType = "casino" | "sport_bet";

export interface PlayerResponsibleGaming {
    id?: number;
    playerCode?: string;
    brandId?: number;
    jurisdiction?: string;
    settings?: any;

    createdAt?: string;
}

export interface PlayerResponsibleGamingSuspensionInfo {
    playerCode?: string;
    brandId?: number;
    suspensionTypes?: string[];
    productType?: string;
    createdAt?: string;
    endTime?: Date;
}

export interface PlayerResponsibleGamingSettings {
    id?: number;
    type?: ResponsibleGamingSettingsType;
    settings?: any;

    createdAt?: string;
    updatedAt?: string;
}

export interface ResponsibleGamingSettingsCreate {
    realityCheck?: number;
    lossLimit?: number;
    lossLimitTimeframe?: string;
    depositLimit?: number;
    depositLimitTimeframe?: string;
    casinoTimeoutTillDate?: string;
    selfExclusionTillDate?: string;
}

export interface UKResponsibleGamingSettings {
    realityCheck?: number;
    lossLimit?: number;
    lossLimitTimeframe?: string;
    lossLimitPending?: number;
    lossLimitPendingTimeframe?: string;
    lossLimitPendingDate?: Date;
    depositLimit?: number;
    depositLimitTimeframe?: string;
    depositLimitPending?: number;
    depositLimitPendingTimeframe?: string;
    depositLimitPendingDate?: Date;
    casinoTimeoutTillDate?: Date;
    selfExclusionTillDate?: Date;
}

export interface PlayerResponsibleGamingDBInstance extends Model<
        InferAttributes<PlayerResponsibleGamingDBInstance>,
        InferCreationAttributes<PlayerResponsibleGamingDBInstance>
    >,
    PlayerResponsibleGaming {
    toInfo(): PlayerResponsibleGaming;
}
export type IPlayerResponsibleGamingModel = ModelStatic<PlayerResponsibleGamingDBInstance>;
const PromoPlayerResponsibleGamingModel: IPlayerResponsibleGamingModel =
    db.define<PlayerResponsibleGamingDBInstance, PlayerResponsibleGaming>(
        "player_responsible_gaming",
        {
            id: { field: "id", type: DataTypes.BIGINT, autoIncrement: true, primaryKey: true },
            playerCode: {
                type: DataTypes.STRING,
                field: "player_code",
                allowNull: false,
                validate: {
                    notEmpty: true,
                    len: [6, 32],
                }
            },
            brandId: {
                type: DataTypes.INTEGER,
                field: "brand_id",
                allowNull: false,
                references: {
                    model: EntityModel,
                    key: "id",
                }
            },
            jurisdiction: { field: "jurisdiction", type: DataTypes.STRING, allowNull: false },
            createdAt: { field: "created_at", type: DataTypes.DATE },
        },
        {
            freezeTableName: true,
            updatedAt: false,
            indexes: [
                {
                    unique: true, fields: ["brand_id", "player_code", "jurisdiction"]
                }
            ]
        }
    );
export function getPlayerResponsibleGamingModel(): IPlayerResponsibleGamingModel {
    (PromoPlayerResponsibleGamingModel as any).prototype.toInfo = function(): PlayerResponsibleGaming {
        return {
            playerCode: this.playerCode,
            brandId: this.brandId,
            jurisdiction: this.jurisdiction,
            createdAt: this.createdAt
        };
    };
    return PromoPlayerResponsibleGamingModel;
}

// =====================================================================================================================
export interface PlayerResponsibleGamingSettingsDBInstance extends Model<
        InferAttributes<PlayerResponsibleGamingSettingsDBInstance>,
        InferCreationAttributes<PlayerResponsibleGamingSettingsDBInstance>
    >,
    PlayerResponsibleGamingSettings {
    toInfo(): PlayerResponsibleGamingSettings;
}
export type IPlayerResponsibleGamingSettingsModel = ModelStatic<PlayerResponsibleGamingSettingsDBInstance>;

const PromoPlayerResponsibleGamingSettingsModel: IPlayerResponsibleGamingSettingsModel =
    db.define<PlayerResponsibleGamingSettingsDBInstance, PlayerResponsibleGamingSettings>(
        "player_responsible_gaming_settings",
        {
            id: {
                field: "parent_id",
                type: DataTypes.BIGINT,
                allowNull: false,
                primaryKey: true
            },
            type: {
                field: "settings_type",
                type: DataTypes.ENUM("casino", "sport_bet"),
                primaryKey: true,
                allowNull: false
            },
            settings: { field: "settings", type: DataTypes.JSONB },
            createdAt: { field: "created_at", type: DataTypes.DATE },
            updatedAt: { field: "updated_at", type: DataTypes.DATE },
        },
        {
            freezeTableName: true,
            underscored: true,
            indexes: [
                {
                    unique: false,
                    fields: ["updated_at"]
                }
            ]
        }
    );
export function getPlayerResponsibleGamingSettingsModel(): IPlayerResponsibleGamingSettingsModel {
    (PromoPlayerResponsibleGamingModel as any).prototype.toInfo = function(): PlayerResponsibleGamingSettings {
        return {
            type: this.type,
            settings: this.settings
        };
    };
    return PromoPlayerResponsibleGamingSettingsModel;
}

PromoPlayerResponsibleGamingSettingsModel.belongsTo(PromoPlayerResponsibleGamingModel,
    { foreignKey: "id", onDelete: "CASCADE" });
PromoPlayerResponsibleGamingModel.hasMany(PromoPlayerResponsibleGamingSettingsModel,
    { as: "settings", foreignKey: "id" });
