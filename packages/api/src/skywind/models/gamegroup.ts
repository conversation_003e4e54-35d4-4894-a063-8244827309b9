import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { get as getEntityModel } from "./entity";
import { sequelize as db } from "../storage/db";
import { GameGroupAttributes } from "../entities/gamegroup";

const EntityModel = getEntityModel();

export interface GameGroupDBInstance extends Model<
        InferAttributes<GameGroupDBInstance>,
        InferCreationAttributes<GameGroupDBInstance>
    >,
    GameGroupAttributes {
}

const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    brandId: {
        type: DataTypes.INTEGER,
        field: "brand_id",
        unique: "gameGroupForBrand",
        references: {
            model: EntityModel,
            key: "id",
        },
    },
    name: { type: DataTypes.STRING, unique: "gameGroupForBrand", allowNull: false, validate: { notEmpty: true } },
    description: DataTypes.STRING,
    isDefault: {
        type: DataTypes.BOOLEAN,
        field: "is_default",
        defaultValue: false
    },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
    }
};

export type IGameGroupModel = ModelStatic<GameGroupDBInstance>;
const GameGroup: IGameGroupModel = db.define<GameGroupDBInstance, GameGroupAttributes>(
    "gamegroup",
    schema,
    {
        underscored: true,
        tableName: "game_groups",
    }
);

GameGroup.belongsTo(EntityModel, { as: "brand" });

export function get(): IGameGroupModel {
    return GameGroup;
}
