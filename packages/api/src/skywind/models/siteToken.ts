import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { get as getEntityModel } from "./entity";
import { sequelize as db } from "../storage/db";

const EntityModel = getEntityModel();

export interface SiteTokenAttributes {
    id?: number;
    token?: string;
    brandId?: number;
    status?: string;
    ts?: Date;
}

export interface SiteTokenDBInstance extends Model<
        InferAttributes<SiteTokenDBInstance>,
        InferCreationAttributes<SiteTokenDBInstance>
    >,
    SiteTokenAttributes {
}

const schema = {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: "Id of token, it automatically generates by Seguelize",
    },
    token: {
        type: DataTypes.STRING,
        field: "token",
        allowNull: false,
        comment: "Generated token for site",
    },
    brandId: {
        type: DataTypes.INTEGER,
        field: "brand_id",
        allowNull: false,
        references: {
            model: EntityModel,
            key: "id",
        },
        comment: "Id of brand",
    },
    status: {
        type: DataTypes.ENUM("normal", "suspended"),
        field: "status",
        defaultValue: "normal",
        allowNull: false,
        comment: "Token status: normal or suspended",
    },
    ts: {
        type: DataTypes.DATE,
        field: "ts",
        comment: "Time when token generated",
    },
};

export type ISiteTokenModel = ModelStatic<SiteTokenDBInstance>;
const model: ISiteTokenModel = db.define<SiteTokenDBInstance, SiteTokenAttributes>(
        "site_token",
        schema,
        {
            indexes: [
                { fields: ["brand_id"] },
            ],
            createdAt: false,
            updatedAt: false,
        }
    );

export function get(): ISiteTokenModel {
    return model;
}
