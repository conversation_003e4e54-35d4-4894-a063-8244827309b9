import { Model, ModelStatic, DataTypes, InferAttributes, InferCreationAttributes } from "sequelize";
import { Merchant } from "../entities/merchant";
import { get as getEntityModel } from "./entity";
import { sequelize as db } from "../storage/db";
import { getProxyModel } from "./proxy";
import { NonFunctionProperties } from "../entities/typeUtils";

const EntityModel = getEntityModel();
const ProxyModel = getProxyModel();

type MerchantAttributes = NonFunctionProperties<Omit<Merchant, "brandTitle">>;
export interface MerchantDBInstance extends Model<
        InferAttributes<MerchantDBInstance>,
        InferCreationAttributes<MerchantDBInstance>
    >,
    MerchantAttributes {
}

const schema = {
    brandId: {
        type: DataTypes.INTEGER,
        field: "brand_id",
        allowNull: false,
        autoIncrement: false,
        primaryKey: true
    },
    type: { type: DataTypes.STRING, allowNull: false, validate: { notEmpty: true } },
    code: { type: DataTypes.STRING, allowNull: false, validate: { notEmpty: true } },
    params: { type: DataTypes.JSONB, allowNull: false, validate: { notEmpty: true } },
    isTest: { type: DataTypes.BOOLEAN, allowNull: true, field: "is_test", defaultValue: false },
    version: { type: DataTypes.INTEGER, defaultValue: 1 },
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
    },
    lastTestsPassing: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "last_tests_passing",
    },
    proxyId: { type: DataTypes.INTEGER, field: "proxy_id", allowNull: true }
};

export type IMerchantModel = ModelStatic<MerchantDBInstance>;
const merchantModel: IMerchantModel = db.define<MerchantDBInstance, MerchantAttributes>(
    "merchant",
    schema,
    {
        underscored: true,
        indexes: [
            { name: "merchants_type_code_key", unique: true, fields: [ "type", "code" ] },
        ],
    }
);

merchantModel.belongsTo(ProxyModel, { foreignKey: "proxyId", onDelete: "NO ACTION" });
merchantModel.belongsTo(EntityModel, { foreignKey: "brandId" });

export function get(): IMerchantModel {
    return merchantModel;
}
