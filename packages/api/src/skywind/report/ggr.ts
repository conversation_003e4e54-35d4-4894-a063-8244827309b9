import { BaseEntity } from "../entities/entity";
import { WhereOptions } from "sequelize";
import { Models } from "../models/models";

export interface BrandGGR {
    brandId: number;
    currency: string;
    bet?: number;
    win?: number;
    revenue?: number;
    jackpotWin?: number;
    freBetWin?: number;
}

export const queryParamsKeys = ["currency"];

const BrandGGRModel = Models.BrandGGRModel;

export async function getReportGGR(entity: BaseEntity, filter: WhereOptions<any>): Promise<BrandGGR[]> {
    filter["brand_id"] = entity.id;
    const items = await BrandGGRModel.findAll({
        where: filter,
    });
    return items.map(item => item["dataValues"]);
}
