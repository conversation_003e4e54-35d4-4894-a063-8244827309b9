import { JpReportService } from "./jpReport";
import { JpContributionLogReport, JpReportParams } from "../entities/report";

export class JpContributionReportService extends JpReportService<JpContributionLogReport> {
    protected getRawSql(params: JpReportParams): string {
        return `
        SELECT 
            contribution_amount as "contributionAmount",
            currency as "currency",
            currency_rate as "currencyRate",
            game_code as "gameCode",
            game_data ->> 'roundId' as "roundId",
            inserted_at as "insertedAt",
            player_code as "playerCode",
            pool as "pool",
            progressive as "progressiveAmount",
            seed as "seedAmount",
            trx_date as "trxDate",
            jackpot_id as "jackpotId"
        FROM jp_contribution_log
        WHERE brand_id = :brandId 
        AND trx_date > :trxDate__gt ${params.trxDate__lt ? " AND trx_date < :trxDate__lt" : ""}
        ORDER BY "trx_date" DESC
        LIMIT :limit
        OFFSET :offset`;
    }
}

export default new JpContributionReportService();
