import { QueryResult } from "pg";
import { postgresSlave } from "../storage/postgres";
import currencyRates from "./currency_rates";
import { Currencies, Currency } from "@skywind-group/sw-currency-exchange";

export interface CurrencyReport {
    currency: string;
    playedGames: number;
    bets: number;
    winnings: number;
    ggr: number;
    betsUsd: number;
    winningsUsd: number;
    ggrUsd: number;
}

interface DatesRangeFilter {
    from?: string;
    to?: string;
    ts__gt?: string;
    ts__gte?: string;
    ts__lt?: string;
    ts__lte?: string;
}

interface CurrencyFilter {
    currency?: string;
    currency__in?: string;
}

function filterDates(filter: DatesRangeFilter, column: string): string {
    let text: string = "";

    if (filter.from) {
        text += " AND " + column + " >= to_timestamp(" + filter.from + ") AT TIME ZONE 'UTC'";
    }
    if (filter.to) {
        text += " AND " + column + " <= to_timestamp(" + filter.to + ") AT TIME ZONE 'UTC'";
    }
    if (filter.ts__gt) {
        text += " AND " + column + " > '" + filter.ts__gt + "' AT TIME ZONE 'UTC'";
    }
    if (filter.ts__gte) {
        text += " AND " + column + " >= '" + filter.ts__gte + "' AT TIME ZONE 'UTC'";
    }
    if (filter.ts__lt) {
        text += " AND " + column + " < '" + filter.ts__lt + "' AT TIME ZONE 'UTC'";
    }
    if (filter.ts__lte) {
        text += " AND " + column + " <= '" + filter.ts__lte + "' AT TIME ZONE 'UTC'";
    }

    return text;
}

function filterCurrency({ currency, currency__in }: CurrencyFilter, tableName: string): string {
    let text: string = "";

    if (currency) {
        text += " AND " + tableName + ".currency_code = '" + currency + "'";
    }
    if (currency__in) {
        text += " AND " + tableName + ".currency_code IN ('" + currency__in.split(",").join("', '") + "')";
    }

    return text;
}

export async function getCurrencyReport(brandId: number, filter: Record<string, any>): Promise<CurrencyReport[]> {
    await currencyRates.updateIfNeeded();

    let text: string = "WITH sums AS (SELECT " +
        "currency_rates.currency_code AS currency, " +
        "sum(bet) AS bets, " +
        "sum(win) AS winnings, " +
        "sum(revenue) AS ggr, " +
        "sum(bet * rate) AS bets_usd, " +
        "sum(win * rate) AS winnings_usd, " +
        "sum(revenue * rate) AS ggr_usd, " +
        "sum(finished_rounds) AS played_games " +
        "FROM bo_aggr_brand_currency " +
        "JOIN currency_rates ON" +
        " bo_aggr_brand_currency.currency_code = currency_rates.currency_code AND date_hour::date = rate_date " +
        "WHERE brand_id = " + brandId;

    text += filterDates(filter, "date_hour");
    text += filterCurrency(filter, "bo_aggr_brand_currency");

    text += " GROUP BY currency_rates.currency_code) " +
        "SELECT * FROM sums " +
        "UNION ALL SELECT " +
        "'total_usd', " +
        "NULL, " +
        "NULL, " +
        "NULL, " +
        "sum(bets_usd), " +
        "sum(winnings_usd), " +
        "sum(ggr_usd), " +
        "sum(played_games) " +
        "FROM sums";

    const rs: QueryResult = await postgresSlave.query(text);

    return rs.rows.map(row => ({
        currency: row.currency,
        playedGames: +row.played_games,
        bets: row.bets === null ? undefined : +row.bets,
        winnings: row.winnings === null ? undefined : +row.winnings,
        ggr: row.ggr === null ? undefined : +row.ggr,
        betsUsd: +row.bets_usd,
        winningsUsd: +row.winnings_usd,
        ggrUsd: +row.ggr_usd,
    } as CurrencyReport));
}

export async function getWalletCurrencyReport(
    brandIds: number[],
    filter: Record<string, any>
): Promise<CurrencyReport[]> {
    await currencyRates.updateIfNeeded();

    let whereString = "";
    whereString += `brand_id IN (${brandIds.join(",")})`;
    whereString += filterDates(filter, "payment_date_hour");
    whereString += filterCurrency(filter, "bo_aggr_win_bets");

    const queryString = `
    SELECT 
        bo_aggr_win_bets.currency_code AS currency,
        sum(total_bets) AS bets,
        sum(total_wins) AS winnings,
        sum(total_bets - total_wins) AS ggr,
        sum(total_bets * exchange_rate) AS bets_usd,
        sum(total_wins * exchange_rate) AS winnings_usd,
        sum((total_bets - total_wins) * exchange_rate) AS ggr_usd,
        sum(played_games_qty) AS played_games
    FROM bo_aggr_win_bets
    WHERE ${whereString}
    GROUP BY bo_aggr_win_bets.currency_code`;

    const rs: QueryResult = await postgresSlave.query(queryString);

    const total = {
        currency: "total_usd",
        playedGames: 0,
        betsUsd: 0,
        winningsUsd: 0,
        ggrUsd: 0
    } as CurrencyReport;

    const currencies = rs.rows.map(row => {

        const currency: Currency = Currencies.get(row.currency);

        const betsUsd: number = exchangeToUSDMajorUnits(currency, row.bets_usd);
        const winningsUsd: number = exchangeToUSDMajorUnits(currency, row.winnings_usd);
        const ggrUsd: number = exchangeToUSDMajorUnits(currency, row.ggr_usd);

        total.playedGames += +row.played_games;
        total.betsUsd += betsUsd;
        total.winningsUsd += winningsUsd;
        total.ggrUsd += ggrUsd;

        return {
            currency: row.currency,
            playedGames: +row.played_games,
            bets: row.bets === null ? undefined : currency.toMajorUnits(parseInt(row.bets, 10)),
            winnings: row.winnings === null ? undefined : currency.toMajorUnits(parseInt(row.winnings, 10)),
            ggr: row.ggr === null ? undefined : currency.toMajorUnits(parseInt(row.ggr, 10)),
            betsUsd,
            winningsUsd,
            ggrUsd
        };
    });

    total.betsUsd = +total.betsUsd.toFixed(2);
    total.winningsUsd = +total.winningsUsd.toFixed(2);
    total.ggrUsd = +total.ggrUsd.toFixed(2);

    currencies.push(total);

    return currencies;
}

function exchangeToUSDMajorUnits(currency: Currency, amount: number): number {
    return amount === null ? 0 : +(amount / currency.multiplier).toFixed(2);
}
