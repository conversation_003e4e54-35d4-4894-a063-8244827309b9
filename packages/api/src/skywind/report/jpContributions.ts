import { BaseEntity } from "../entities/entity";
import { DatabaseError, literal, WhereOptions } from "sequelize";
import * as FilterService from "../services/filter";
import {
    getAggrJpPlayerContributionsModel,
    AggrJpPlayerContributions as PlayerContribution,
} from "../models/aggrJpPlayer";
import {
    getAggrJpBrandContributionsModel,
    AggrJpBrandContributions as BrandContribution,
} from "../models/aggrJpBrand";
import { ValidationError } from "../errors";

const AggrJpPlayerContributionsModel = getAggrJpPlayerContributionsModel();
const AggrJpBrandContributionsModel = getAggrJpBrandContributionsModel();

const DEFAULT_SORT_KEY = "dateHour";
const DEFAULT_SORT_ORDER = "DESC";

export const queryParamsKeysPlayerContributions = [
    "playerCode",
    "gameCode",
    "dateHour",
    "currency",
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
];

const sortableKeysPlayerContributions = [
    "dateHour",
    "playerCode",
    "gameCode",
    "seedAmount",
    "progressiveAmount",
    "totalBetAmount",
    "jpWinAmount",
    "seedAmountJpCurrency",
    "progressiveAmountJpCurrency",
    "totalBetAmountJpCurrency",
    "jpWinAmountJpCurrency",
    "totalBetCount",
    "jpWinCount",
    "firstActivity",
    "lastActivity",
];

export const queryParamsKeysBrandContributions = [
    "gameCode",
    "dateHour",
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
];

const sortableKeysBrandContributions = [
    "dateHour",
    "gameCode",
    "seedAmount",
    "progressiveAmount",
    "totalBetAmount",
    "jpWinAmount",
    "totalBetCount",
    "jpWinCount",
    "firstActivity",
    "lastActivity",
];

export async function getPlayersContributions(brand: BaseEntity,
                                              filter: WhereOptions<any>): Promise<PlayerContribution[]> {
    try {
        filter["brand_id"] = brand.id;
        const sortBy = FilterService.getSortKey(filter, sortableKeysPlayerContributions, DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(filter, "sortOrder") || DEFAULT_SORT_ORDER;
        const items = await AggrJpPlayerContributionsModel.findAll({
            attributes: { exclude: ["history_job_id"] },
            where: filter,
            offset: FilterService.valueFromQuery(filter, "offset") || FilterService.DEFAULT_OFFSET,
            limit: FilterService.valueFromQuery(filter, "limit") || FilterService.DEFAULT_LIMIT,
            order: literal(`"${sortBy}" ${sortOrder}`),
        });
        return items.map(item => item["dataValues"]);
    } catch (error) {
        // todo aguzanov is it possible to make validation in service method?
        if (error instanceof DatabaseError) {
            const dbError = error["original"] as DatabaseError;
            if (dbError && dbError["routine"] === "DateTimeParseError") {
                return Promise.reject(new ValidationError(dbError["message"].replace(/"/g, "")));
            }
        }
        return Promise.reject(error);
    }
}

export async function getBrandContributions(brand: BaseEntity,
                                            filter: WhereOptions<any>): Promise<BrandContribution[]> {
    try {
        filter["brand_id"] = brand.id;
        const sortBy = FilterService.getSortKey(filter, sortableKeysBrandContributions, DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(filter, "sortOrder") || DEFAULT_SORT_ORDER;
        const items = await AggrJpBrandContributionsModel.findAll({
            attributes: { exclude: ["history_job_id"] },
            where: filter,
            offset: FilterService.valueFromQuery(filter, "offset") || FilterService.DEFAULT_OFFSET,
            limit: FilterService.valueFromQuery(filter, "limit") || FilterService.DEFAULT_LIMIT,
            order: literal(`"${sortBy}" ${sortOrder}`),
        });
        return items.map(item => item["dataValues"]);
    } catch (error) {
        // todo aguzanov is it possible to make validation in service method?
        if (error instanceof DatabaseError) {
            const dbError = error["original"] as DatabaseError;
            if (dbError && dbError["routine"] === "DateTimeParseError") {
                return Promise.reject(new ValidationError(dbError["message"].replace(/"/g, "")));
            }
        }
        return Promise.reject(error);
    }
}
