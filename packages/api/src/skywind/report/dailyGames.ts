import { BaseEntity } from "../entities/entity";
import { col, fn, literal, WhereOptions, Utils } from "sequelize";
import * as FilterService from "../services/filter";
import { getAggrPlayerRoundsModel } from "../models/aggrPlayerRounds";

const aggrPlayerRoundsModel = getAggrPlayerRoundsModel();
const DEFAULT_SORT_KEY = "day";
const DEFAULT_SORT_ORDER = "DESC";

export const queryParamsKeys = [
    "currency",
    "gameCode",
    "ts",
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
];

const sortableKeys = [
    "currency",
    "gameCode",
    "day",
];

export interface DayGameReport {
    date: Date;
    rounds: number;
    players: number;
    bet: number;
    win: number;
    revenue: number;
    currency: string;
}

export async function getDailyGamesReport(brand: BaseEntity, filter: WhereOptions<any>): Promise<DayGameReport[]> {
    filter["brand_id"] = brand.id;

    if (filter["ts"]) {
        filter["date_hour"] = filter["ts"];
        delete filter["ts"];
    }
    const sortBy = FilterService.getSortKey(filter, sortableKeys, DEFAULT_SORT_KEY);
    const sortOrder = FilterService.valueFromQuery(filter, "sortOrder") || DEFAULT_SORT_ORDER;

    const dayFn: Utils.Fn = fn("DATE_TRUNC", "DAY", col("date_hour"));
    const items = await aggrPlayerRoundsModel.findAll({
        attributes: [
            [dayFn, "day"],
            ["game_code", "gameCode"],
            [fn("SUM", col("rounds_qty")), "rounds"],
            [literal("COUNT(DISTINCT(player_code))") as any, "players"],
            [fn("SUM", col("total_bet")), "bet"],
            [fn("SUM", col("total_win")), "win"],
            [fn("SUM", col("total_revenue")), "revenue"],
            ["currency_code", "currency"],
        ],
        group: [dayFn, "game_code", "currency_code"],
        where: filter,
        offset: FilterService.valueFromQuery(filter, "offset") || FilterService.DEFAULT_OFFSET,
        limit: FilterService.valueFromQuery(filter, "limit") || FilterService.DEFAULT_LIMIT,
        order: literal(`"${sortBy}" ${sortOrder}`),
    });
    // TODO" need to fix 'any'
    return items.map(item => item["dataValues"] as any);
}
