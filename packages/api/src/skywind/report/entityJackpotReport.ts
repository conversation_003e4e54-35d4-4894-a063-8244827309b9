import { BaseEntity } from "../entities/entity";
import { sequelize as db } from "../storage/db";
import { lazy, Lazy } from "@skywind-group/sw-utils";
import logger from "../utils/logger";

export enum BusinessEntityConfigurationLevel {
    GLOBAL = "global",
    SHARED = "shared",
    BRAND = "brand"
}

export enum BillingConfigurationLevel {
    NETWORK = "network",
    LOCAL = "local"
}

export interface EntityJackpot {
    gameCode: string;
    jpType: string;
    jpId: string;
    configuredOn: string;
    isInherited: boolean;
    businessEntityConfigurationLevel: BusinessEntityConfigurationLevel;
    billingConfigurationLevel: BillingConfigurationLevel;
}

const JP_QUERY = "SELECT * FROM fnc_entity_jackpots(:entityId)";
const LOG = logger("entity-jackpot-report");

export interface EntityJackpotReportService {
    getJackpotReport(entity: BaseEntity): Promise<EntityJackpot[]>;
}

class EntityJackpotReportServiceImpl implements EntityJackpotReportService {

    public async getJackpotReport(entity: BaseEntity): Promise<EntityJackpot[]> {
        const recordsData = await db.query(JP_QUERY, { replacements: { entityId: entity.id }, raw: true });
        if (!recordsData || !recordsData[0].length) {
            return [];
        }
        return recordsData[0].map(r => this.mapJackpotData(r));
    }

    private mapJackpotData(r: any): EntityJackpot {
        if (r.is_owned === null || r.is_owned === undefined) {
            LOG.warn("Jackpot doesn't exist in jackpot database",
                { jpId: r.jp_id, gameCode: r.game_code, entity: r.configured_on });
        }
        return {
            gameCode: r.game_code,
            jpType: r.jp_type,
            jpId: r.jp_id,
            configuredOn: r.configured_on,
            isInherited: r.jp_is_inherited,
            businessEntityConfigurationLevel: this.getBusinessEntityConfigurationLevel(r),
            billingConfigurationLevel: this.getBillingConfigurationLevel(r)
        };
    }

    private getBusinessEntityConfigurationLevel(r: any): BusinessEntityConfigurationLevel {
        if (r.is_global) {
            return BusinessEntityConfigurationLevel.GLOBAL;
        }
        if (!r.is_used_by_others) {
            return BusinessEntityConfigurationLevel.BRAND;
        }
        return BusinessEntityConfigurationLevel.SHARED;
    }

    private getBillingConfigurationLevel(r: any): BillingConfigurationLevel {
        if (r.is_owned) {
            return BillingConfigurationLevel.LOCAL;
        }
        return BillingConfigurationLevel.NETWORK;
    }
}

export const entityJackpotReportService: Lazy<EntityJackpotReportService> =
    lazy(() => new EntityJackpotReportServiceImpl());
