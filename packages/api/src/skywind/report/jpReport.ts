import { BaseEntity } from "../entities/entity";
import {
    JpReportParams,
    JpReportReplacements,
    JpReport,
    RoundedValue
} from "../entities/report";
import * as FilterService from "../services/filter";
import { executeLongQuery, sequelizeJpSlave as db } from "../storage/db";
import { ValidationError } from "../errors";
import { QueryTypes, DatabaseError, BindOrReplacements, } from "sequelize";

export abstract class JpReportService<T extends JpReport> {
    public async getReport(entity: BaseEntity, params: JpReportParams): Promise<T[]> {
        const replacements = this.getReplacements(entity, params);
        const rawSQL = this.getRawSql(params);

        try {
            return await executeLongQuery(db, async (trx) => {
                const dbRecords = await db.query(rawSQL,
                    {
                        replacements: replacements,
                        type: QueryTypes.SELECT,
                        transaction: trx
                    });
                return dbRecords.map((item: any) => this.sanitizeResultItem(item) as T);
            });
        } catch (err) {

            if (err instanceof DatabaseError) {
                const dbError = err["original"] as DatabaseError;
                if (dbError && dbError["routine"] === "DateTimeParseError") {
                    return Promise.reject(new ValidationError(dbError["message"].replace(/"/g, "")));
                }
            }

            return Promise.reject(err);
        }
    }

    protected getReplacements(entity: BaseEntity, params: JpReportParams): JpReportReplacements & BindOrReplacements {
        const replacements = {
            brandId: entity.id,
            limit: params.limit || FilterService.DEFAULT_LIMIT,
            offset: params.offset || FilterService.DEFAULT_OFFSET,
            trxDate__gt: params.trxDate__gt
        };

        if (params.trxDate__lt) {
            replacements["trxDate__lt"] = params.trxDate__lt;
        }

        return replacements;
    }

    protected abstract getRawSql(params: JpReportParams): string;

    protected sanitizeResultItem(item: RoundedValue) {
        if (Number.isNaN(+item.roundId)) {
            return item;
        }

        return { ...item, roundId: +item.roundId };

    }
}
