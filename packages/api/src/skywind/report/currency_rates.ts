import { QueryResult } from "pg";
import { postgres } from "../storage/postgres";
import { MissingCurrencyRatesError } from "../errors";
import config from "../config";
import logger from "../utils/logger";
import { getCurrencyExchange } from "../services/currencyExchange";
import * as schedule from "node-schedule";
import { sequelize as db } from "../storage/db";
import { ApplicationLock, ApplicationLockId } from "../utils/applicationLock";
import { Transaction } from "sequelize";
import { Currencies } from "@skywind-group/sw-currency-exchange";

const log = logger("currency-rates");

// Set of currencies which are stored in system already in thousands because of their huge exchange rate
const CURRENCY_COEFFICIENTS = new Map<string, number>([
    ["IDS", 1000],
    ["VNS", 1000],

]);
const SPECIAL_CURRENCIES = new Map<string, string>([
    ["IDR", "IDS"],
    ["VND", "VNS"],
]);

class CurrencyRates {
    /**
     * Updates currency exchange rates for the current day if it's needed.
     * @throws {MissingCurrencyRatesError} if there was an error while retrieving the rates
     */
    public async updateIfNeeded() {
        await db.transaction(async (transaction: Transaction) => {
            await ApplicationLock.lock(transaction, ApplicationLockId.UPDATE_CURRENCY_RATES);

            const hasFetched = await this.hasRatesForTodayBeenFetched();
            let doMissingRatesExist: boolean = false;

            if (hasFetched) {
                doMissingRatesExist = await this.doMissingRatesExist();
            }

            if (!hasFetched || doMissingRatesExist) {
                try {
                    await this.updateRates();
                } catch (err) {
                    log.warn(err, "Error retrieving currency exchange rates");
                    throw new MissingCurrencyRatesError();
                }
            } else {
                log.info("Skip currency rates update");
            }
        });
    }

    /**
     * Compares rates from the library and the database and returns the missing.
     * @returns {boolean} comparison result
     */
    private async doMissingRatesExist(): Promise<boolean> {
        const currentRates = await this.getFetchedRatesForToday();
        if (!currentRates) {
            return !!currentRates;
        }
        const neededRates = (await getCurrencyExchange()).getExchangeRatesList();
        const usdRates = neededRates.rates["USD"];

        const missingRatesCount = Object.keys(usdRates)
            .reduce((count: number, code: string) => {
                const isSameCode = (currentRates as Array<any>).some(
                    ({ currency_code }) => code === currency_code);

                return !isSameCode ? ++count : count;
            }, 0);

        return Boolean(missingRatesCount);
    }

    /**
     * Checks the presence of the exchanges rates for the current day based on USD currency.
     * @returns {boolean} true if the USD rate is present, false otherwise
     */
    private async hasRatesForTodayBeenFetched(): Promise<boolean> {
        const rs: QueryResult = await postgres.query("SELECT EXISTS(" +
            "SELECT 1 FROM currency_rates WHERE currency_code = 'USD' AND rate_date = current_date) AS exists");
        return rs.rows[0].exists;
    }

    /**
     * Returns exchanges rates for the current day based on USD currency.
     * @returns {string[] | boolean} exchanges rates
     */
    private async getFetchedRatesForToday(): Promise<string[] | boolean> {
        const rs: QueryResult = await postgres.query(
            "SELECT currency_code FROM currency_rates WHERE rate_date = current_date"
        );
        return rs && rs.rows ? rs.rows : false;
    }

    /**
     * Fetches the currency exchange rates from open-exchange-rates and stores them in the database.
     */
    private async updateRates() {
        const currentRates = (await getCurrencyExchange()).getExchangeRatesList();
        const usdRates = currentRates.rates["USD"];
        if (usdRates) {
            await this.store(this.convertRates(usdRates), currentRates.startTime / 1000);
            log.info("Got new currency exchange rates time: %s", new Date(currentRates.startTime));
        } else {
            log.warn("No currency exchange rates");
        }
    }

    /**
     * Converts the rates to the format to be used for reporting.
     * @param rates
     * @returns {Map<string, number>} converted rates
     */
    private convertRates(rates): Map<string, number> {
        const convertedRates = new Map<string, number>();
        Object.keys(rates).filter(currencyCode => Currencies.exists(currencyCode)).forEach(currencyCode => {
            const rate = rates[currencyCode];
            if (!Number.isNaN(rate)) {
                convertedRates.set(currencyCode, 1 / rate);
                // for IDS and VNS currencies, as amounts in the system already in thousands
                const specialCurrency = SPECIAL_CURRENCIES.get(currencyCode);
                if (specialCurrency) {
                    const coefficient = CURRENCY_COEFFICIENTS.get(specialCurrency) || 1;
                    // calculate inverted rate, so we can use multiplication to convert
                    convertedRates.set(specialCurrency, coefficient / rate);
                }
            }
        });

        return convertedRates;
    }

    /**
     * Stores the currency exchange rates in the database.
     * @param {Map<string, number>} rates currency exchange rates
     * @param {number} timestamp of the rates
     */
    private async store(rates: Map<string, number>, timestamp: number) {
        let text: string = "INSERT INTO currency_rates VALUES ";
        const values: any[] = [];

        const today: Date = new Date();
        let argCounter: number = 0;

        rates.forEach((rate: number, currencyCode: string) => {
            if (argCounter !== 0) {
                text += ", ";
            }
            text += `($${++argCounter}, $${++argCounter}, $${++argCounter}, $${++argCounter})`;
            values.push(currencyCode, today, rate, timestamp);
        });

        text += " ON CONFLICT DO NOTHING";

        await postgres.query(text, values);
    }
}

const currencyRates = new CurrencyRates();
export default currencyRates;

/*
 * We need to schedule the update of currency exchange rates when it is enabled.
 */
let job;

if (config.currencyRatesHistory.enabled()) {
    currencyRates.updateIfNeeded();

    if (job) {
        log.warn("Ignore scheduling currency rate update as it has already been set");
    } else {
        log.info("Schedule currency rate update");
        job = schedule.scheduleJob(config.currencyRatesHistory.schedule, () => {
            return updateCurrencyRates();
        });
    }
} else {
    log.info("Ignore scheduling currency rate update for non-production environment");
}

/**
 * Updates currency exchange rates and re-schedules itself for the next call.
 */
async function updateCurrencyRates() {
    try {
        await currencyRates.updateIfNeeded();
    } catch (err) {
        log.warn(err,
            `Retry currency exchange rates update in ${config.currencyRatesHistory.retryInterval / 60000} minutes`);
        setTimeout(updateCurrencyRates, config.currencyRatesHistory.retryInterval);
    }
}
