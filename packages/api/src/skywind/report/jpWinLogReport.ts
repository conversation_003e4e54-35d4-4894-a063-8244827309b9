import { Currencies } from "@skywind-group/sw-currency-exchange";
import { JpReportParams, JpWinReport, RoundedValue } from "../entities/report";
import { JpReportService } from "./jpReport";

export class JpWinLogReportService<T extends JpWinReport> extends JpReportService<T> {
    protected getRawSql(params: JpReportParams): string {
        return `
        SELECT 
            currency as "currency",
            currency_rate as "currencyRate",
            event_id as "eventId",
            external_id as "externalId",
            game_code as "gameCode",
            initial_seed as "initialSeedAmount",
            jackpot_id as "jackpotId",
            player_code as "playerCode",
            player_currency as "playerCurrency",
            pool as "pool",
            progressive as "progressiveAmount",
            round_id as "roundId",
            seed as "seedAmount",
            total_progressive as "totalProgressiveAmount",
            total_seed as "totalSeedAmount",
            trx_date as "trxDate",
            win_amount as "winAmount",
            (win_amount * currency_rate) as "winAmountCurrency",
            info
        FROM jp_win_log
        WHERE brand_id = :brandId 
        AND trx_date > :trxDate__gt ${params.trxDate__lt ? " AND trx_date < :trxDate__lt" : ""}
        ORDER BY "trx_date" DESC
        LIMIT :limit
        OFFSET :offset`;
    }

    protected sanitizeResultItem(
        item: { currency: string, winAmountCurrency: number } & RoundedValue) {

        const currency = Currencies.get(item.currency);
        item.winAmountCurrency = currency.format(item.winAmountCurrency);

        return super.sanitizeResultItem(item);
    }
}

export default new JpWinLogReportService();
