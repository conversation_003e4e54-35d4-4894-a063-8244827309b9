import { JpWinLogReportService } from "./jpWinLogReport";
import { JpReportParams, JpWinReportV2 } from "../entities/report";

export class JpWinLogReportServiceV2 extends JpWinLogReportService<JpWinReportV2> {
    protected getRawSql(params: JpReportParams): string {
        return `
        SELECT 
            currency as "currency",
            currency_rate as "currencyRate",
            trx_id as "trxId",
            event_id as "eventId",
            external_id as "externalId",
            game_code as "gameCode",
            initial_seed as "initialSeedAmount",
            jackpot_id as "jackpotId",
            player_code as "playerCode",
            player_currency as "playerCurrency",
            pool as "pool",
            progressive as "progressiveAmount",
            round_id as "roundId",
            seed as "seedAmount",
            total_progressive as "totalProgressiveAmount",
            total_seed as "totalSeedAmount",
            trx_date as "trxDate",
            win_amount as "winAmount",
            (win_amount * currency_rate) as "winAmountCurrency",
            info
        FROM jp_win_log
        WHERE brand_id = :brandId 
        AND trx_date > :trxDate__gt ${params.trxDate__lt ? " AND trx_date < :trxDate__lt" : ""}
        ORDER BY "trx_date" DESC
        LIMIT :limit
        OFFSET :offset`;
    }
}

export default new JpWinLogReportServiceV2();
