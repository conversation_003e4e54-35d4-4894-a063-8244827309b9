import * as http from "node:http";
import { Server, type Socket } from "socket.io";
import socketPlayer, { emitError } from "../api/socketPlayer";
import config from "../config";
import { type PlayerTokenInfo, validateToken } from "../services/playerSecurity";
import { measureProvider, metrics } from "../utils/measures";

interface FavoriteGamePayload {
    entityId: number
    playerCode: string
    gameCode: string
    isFavorite: boolean
}

interface RecentlyGamePayload {
    entityId: number
    playerCode: string
    gameCode: string
}

export class IoServerV4 {
    private static io: Server;

    public static createApplication(server: http.Server) {
        if (!IoServerV4.io) {
            IoServerV4.io = IoServerV4.init(server);
        }
    }

    private static init(server: http.Server) {
        const io = new Server(server, {
            path: config.socket.v4.path,
            allowEIO3: true,
        });
        io.on("connection", async function (socket: Socket & { tokenData: PlayerTokenInfo }) {
            const token = socket.handshake?.query?.["sw_player_token"] as string;
            try {
                socket.tokenData = await validateToken(token);
                socket.join(IoServerV4.playerKey(socket.tokenData.brandId, socket.tokenData.playerCode));
                socketPlayer(socket);
            } catch (error) {
                emitError(socket, error);
                socket.disconnect();
            }
        });
        io.sockets.on("connect", (socket: Socket) => {
            measureProvider.setGauge(metrics.WEBSOCKET_CONNECTION_COUNT, Object.keys(io.sockets.sockets.size).length);
            socket.on("disconnect", () => {
                measureProvider.setGauge(metrics.WEBSOCKET_CONNECTION_COUNT, Object.keys(io.sockets.sockets.size).length);
            });
        });
        return io;
    }

    private static playerKey(entityId: number, playerCode: string) {
        return `${entityId}:${playerCode}`;
    }

    public static notifyFavoriteGame({ entityId, playerCode, gameCode, isFavorite }: FavoriteGamePayload) {
        IoServerV4.io?.to(IoServerV4.playerKey(entityId, playerCode)).emit("game-favorite", { gameCode, isFavorite });
    }

    public static notifyRecentlyGame({ entityId, playerCode, gameCode }: RecentlyGamePayload) {
        IoServerV4.io?.to(IoServerV4.playerKey(entityId, playerCode)).emit("game-played", {
            gameCode,
            time: Date.now()
        });
    }
}
