import { suite, test } from "mocha-typescript";
import { expect, should } from "chai";
import * as Errors from "../../skywind/errors";
import { BaseEntity, ChildEntity } from "../../skywind/entities/entity";
import { Jurisdiction } from "../../skywind/entities/jurisdiction";
import { EntitySettings } from "../../skywind/entities/settings";
import {
    validateCreateDefaultCountry,
    validateUpdateDefaultCountry,
    validateCreateCountries,
    validateUpdateCountries,
    validateCountryRestrictions
} from "../../skywind/utils/validateEntityCountries";

const mockEntitySettings: EntitySettings = {
    emailTemplates: {
        passwordRecovery: {
            from: "",
            subject: "",
            html: ""
        },
        changeEmail: {
            from: "",
            subject: "",
            html: ""
        }
    }
};

@suite
class ValidateEntityCountriesSpec {
    public static parentEntity = {
        countries: ["US", "GB", "CA"],
        getCountries: () => ["US", "GB", "CA"],
    } as Partial<BaseEntity> as BaseEntity;

    public static childEntity = {
        parent: 1, // Mock parent ID
        defaultCountry: "US",
        countries: ["US", "GB"],
        getCountries: () => ["US", "GB"],
        child: []
    } as Partial<ChildEntity> as ChildEntity;

    public static childEntityWithNullDefaultCountry = {
        parent: 1, // Mock parent ID
        defaultCountry: null,
        countries: ["US", "GB"],
        getCountries: () => ["US", "GB"],
        child: []
    } as Partial<ChildEntity> as ChildEntity;

    public static jurisdiction = {
        allowedCountries: ["US", "GB", "CA", "AU"],
        restrictedCountries: ["RU", "CN"],
        defaultCountry: undefined
    } as Partial<Jurisdiction> as Jurisdiction;

    public static jurisdictionWithDefault = {
        allowedCountries: ["US", "GB", "CA", "AU"],
        restrictedCountries: ["RU", "CN"],
        defaultCountry: "US"
    } as Partial<Jurisdiction> as Jurisdiction;

    public static before() {
        should();
    }

    // validateCreateDefaultCountry tests
    @test("should allow creating entity with valid default country")
    public createDefaultCountryWithValidCountry() {
        expect(validateCreateDefaultCountry(
            { defaultCountry: "US" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should allow creating entity with null default country when jurisdiction has default")
    public createDefaultCountryWithNullAndJurisdictionDefault() {
        expect(validateCreateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should allow creating entity with undefined default country when jurisdiction has default")
    public createDefaultCountryWithUndefinedAndJurisdictionDefault() {
        expect(validateCreateDefaultCountry(
            { defaultCountry: undefined },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should reject creating entity with null default country when jurisdiction has no default")
    public createDefaultCountryWithNullAndNoJurisdictionDefault() {
        expect(() => validateCreateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country should be defined in jurisdiction or entity");
    }

    @test("should reject creating entity with undefined default country when jurisdiction has no default")
    public createDefaultCountryWithUndefinedAndNoJurisdictionDefault() {
        expect(() => validateCreateDefaultCountry(
            { defaultCountry: undefined },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country should be defined in jurisdiction or entity");
    }

    @test("should reject creating entity with invalid default country code")
    public createDefaultCountryWithInvalidCountry() {
        expect(() => validateCreateDefaultCountry(
            { defaultCountry: "INVALID" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country [INVALID] is invalid");
    }

    // validateUpdateDefaultCountry tests
    @test("should allow updating entity with same default country (no change)")
    public updateDefaultCountryNoChange() {
        expect(validateUpdateDefaultCountry(
            { defaultCountry: "US" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should allow updating entity default country to null when jurisdiction has default")
    public updateDefaultCountryToNull() {
        expect(validateUpdateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should reject updating entity default country to null when jurisdiction has no default")
    public updateDefaultCountryToNullWithoutJurisdictionDefault() {
        expect(() => validateUpdateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country should be defined in jurisdiction or entity");
    }

    @test("should allow updating entity default country to undefined (no change)")
    public updateDefaultCountryToUndefined() {
        expect(validateUpdateDefaultCountry(
            { defaultCountry: undefined },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should allow updating entity to valid default country")
    public updateDefaultCountryToValidCountry() {
        expect(validateUpdateDefaultCountry(
            { defaultCountry: "GB" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should reject updating entity to invalid default country code")
    public updateDefaultCountryToInvalidCountry() {
        expect(() => validateUpdateDefaultCountry(
            { defaultCountry: "INVALID" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country [INVALID] is invalid");
    }

    @test("should reject updating entity with null defaultCountry when jurisdiction has no default and no change is made")
    public updateEntityWithNullDefaultCountryAndNoJurisdictionDefault() {
        expect(() => validateUpdateDefaultCountry(
            { defaultCountry: undefined }, // No change to defaultCountry
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntityWithNullDefaultCountry,
            ValidateEntityCountriesSpec.jurisdiction, // jurisdiction without defaultCountry
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country should be defined in jurisdiction or entity");
    }

    @test("should allow updating entity with null defaultCountry when jurisdiction has default and no change is made")
    public updateEntityWithNullDefaultCountryAndJurisdictionDefault() {
        expect(validateUpdateDefaultCountry(
            { defaultCountry: undefined }, // No change to defaultCountry
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntityWithNullDefaultCountry,
            ValidateEntityCountriesSpec.jurisdictionWithDefault, // jurisdiction with defaultCountry
            mockEntitySettings
        )).to.not.throw;
    }

    // validateCreateCountries tests
    @test("should ignore countries when useCountriesFromJurisdiction is enabled and set to null")
    public createCountriesWithUseCountriesFromJurisdiction() {
        const data = { countries: ["US", "GB"] };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            entitySettings
        )).to.not.throw;

        expect(data.countries).to.be.null;
    }

    @test("should allow creating entity with valid countries list")
    public createCountriesWithValidCountries() {
        const data = { countries: ["US", "GB"] };

        expect(validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should reject creating entity when countries is not an array")
    public createCountriesWithInvalidArray() {
        const data = { countries: "US" as any };

        expect(() => validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockEntitySettings
        )).to.throw(Errors.CountriesIsNotArray);
    }

    @test("should reject creating entity with invalid country code in countries list")
    public createCountriesWithInvalidCountry() {
        const data = { countries: ["US", "INVALID"] };

        expect(() => validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Country [INVALID] is invalid");
    }

    // validateUpdateCountries tests
    @test("should ignore countries when useCountriesFromJurisdiction is enabled and set to undefined")
    public updateCountriesWithUseCountriesFromJurisdiction() {
        const data = { countries: ["US", "GB"] };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            entitySettings
        )).to.not.throw;

        expect(data.countries).to.be.undefined;
    }

    @test("should allow updating entity with same countries list (no change)")
    public updateCountriesNoChange() {
        const data = { countries: ["US", "GB"] };

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should allow updating entity with valid countries list")
    public updateCountriesWithValidCountries() {
        const data = { countries: ["US", "CA"] };

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should reject updating entity when countries is not an array")
    public updateCountriesWithInvalidArray() {
        const data = { countries: "US" as any };

        expect(() => validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.throw(Errors.CountriesIsNotArray);
    }

    @test("should reject updating entity with invalid country code in countries list")
    public updateCountriesWithInvalidCountry() {
        const data = { countries: ["US", "INVALID"] };

        expect(() => validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Country [INVALID] is invalid");
    }

    // validateCountryRestrictions tests
    @test("should reject country with invalid country code")
    public countryRestrictionsWithInvalidCountry() {
        expect(() => validateCountryRestrictions(
            "INVALID",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [INVALID] is invalid");
    }

    @test("should allow country in jurisdiction allowed countries when using jurisdiction countries")
    public countryRestrictionsWithJurisdictionAllowedCountries() {
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test("should reject country not in jurisdiction allowed countries when using jurisdiction countries")
    public countryRestrictionsWithJurisdictionNotInAllowedCountries() {
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(() => validateCountryRestrictions(
            "FR",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [FR] should be in jurisdiction allowed countries");
    }

    @test("should reject country in jurisdiction restricted countries when using jurisdiction countries")
    public countryRestrictionsWithJurisdictionRestrictedCountries() {
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(() => validateCountryRestrictions(
            "RU",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [RU] should be in jurisdiction allowed countries");
    }

    @test("should allow country in parent entity allowed countries when using entity countries")
    public countryRestrictionsWithEntityAllowedCountries() {
        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test("should reject country not in parent entity allowed countries when using entity countries")
    public countryRestrictionsWithEntityNotInAllowedCountries() {
        expect(() => validateCountryRestrictions(
            "FR",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [FR] should be in parent entity countries");
    }

    @test("should reject country in entity settings restricted countries when using entity countries")
    public countryRestrictionsWithEntityRestrictedCountries() {
        const entitySettings = { ...mockEntitySettings, restrictedCountries: ["RU", "CN"] };

        expect(() => validateCountryRestrictions(
            "RU",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [RU] should be in parent entity countries");
    }

    @test("should reject null country value")
    public countryRestrictionsWithNullCountry() {
        expect(() => validateCountryRestrictions(
            null,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [null] is invalid");
    }

    @test("should reject undefined country value")
    public countryRestrictionsWithUndefinedCountry() {
        expect(() => validateCountryRestrictions(
            undefined,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [undefined] is invalid");
    }

    // Edge cases and complex scenarios
    @test("should reject creating entity with empty string as default country")
    public createDefaultCountryWithEmptyString() {
        expect(() => validateCreateDefaultCountry(
            { defaultCountry: "" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country [] is invalid");
    }

    @test("should reject updating entity with empty string as default country")
    public updateDefaultCountryWithEmptyString() {
        expect(() => validateUpdateDefaultCountry(
            { defaultCountry: "" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country [] is invalid");
    }

    @test("should allow creating entity with empty countries array")
    public createCountriesWithEmptyArray() {
        const data = { countries: [] };

        expect(validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should allow updating entity with empty countries array")
    public updateCountriesWithEmptyArray() {
        const data = { countries: [] };

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should allow creating entity with null countries value")
    public createCountriesWithNullValue() {
        const data = { countries: null };

        expect(validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should allow updating entity with null countries value")
    public updateCountriesWithNullValue() {
        const data = { countries: null };

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should allow creating entity with undefined countries value")
    public createCountriesWithUndefinedValue() {
        const data = { countries: undefined };

        expect(validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should allow updating entity with undefined countries value")
    public updateCountriesWithUndefinedValue() {
        const data = { countries: undefined };

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should allow any country when jurisdiction has empty allowed countries list")
    public countryRestrictionsWithJurisdictionEmptyAllowedCountries() {
        const jurisdiction = { ...ValidateEntityCountriesSpec.jurisdiction, allowedCountries: [] };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test("should allow any country when jurisdiction has empty restricted countries list")
    public countryRestrictionsWithJurisdictionEmptyRestrictedCountries() {
        const jurisdiction = { ...ValidateEntityCountriesSpec.jurisdiction, restrictedCountries: [] };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test("should allow any country when jurisdiction has undefined allowed countries")
    public countryRestrictionsWithJurisdictionUndefinedAllowedCountries() {
        const jurisdiction = { ...ValidateEntityCountriesSpec.jurisdiction, allowedCountries: undefined };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test("should allow any country when jurisdiction has undefined restricted countries")
    public countryRestrictionsWithJurisdictionUndefinedRestrictedCountries() {
        const jurisdiction = { ...ValidateEntityCountriesSpec.jurisdiction, restrictedCountries: undefined };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test("should allow any country when entity has empty restricted countries list")
    public countryRestrictionsWithEntityEmptyRestrictedCountries() {
        const entitySettings = { ...mockEntitySettings, restrictedCountries: [] };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test("should allow any country when entity has undefined restricted countries")
    public countryRestrictionsWithEntityUndefinedRestrictedCountries() {
        const entitySettings = { ...mockEntitySettings, restrictedCountries: undefined };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    // Tests for specific user requirements
    @test("should allow null/undefined default country when jurisdiction has default country")
    public createDefaultCountryAllowNullWhenJurisdictionHasDefault() {
        // on create, allow null (or undefined) if jurisdiction.defaultCountry set (no useUseCountriesFromJurisdiction check)
        expect(validateCreateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;

        expect(validateCreateDefaultCountry(
            { defaultCountry: undefined },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should validate default country update rules based on jurisdiction default")
    public updateDefaultCountryAllowNullOnlyWhenJurisdictionHasDefault() {
        // on update, allow null only if jurisdiction.defaultCountry set (no useUseCountriesFromJurisdiction check), allow undefined in any case
        expect(validateUpdateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;

        expect(() => validateUpdateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country should be defined in jurisdiction or entity");

        // undefined should always be allowed on update
        expect(validateUpdateDefaultCountry(
            { defaultCountry: undefined },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.not.throw;

        expect(validateUpdateDefaultCountry(
            { defaultCountry: undefined },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should ignore countries values when useCountriesFromJurisdiction is enabled on create")
    public createCountriesIgnoreValuesWhenUseCountriesFromJurisdiction() {
        // on create, if useCountriesFromJurisdiction=true in parent entity - ignore values (set to null)
        const data = { countries: ["US", "GB", "CA"] };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            entitySettings
        );

        expect(data.countries).to.be.null;
    }

    @test("should ignore countries values when useCountriesFromJurisdiction is enabled on update")
    public updateCountriesIgnoreValuesWhenUseCountriesFromJurisdiction() {
        // on update, if useCountriesFromJurisdiction=true - ignore new values (set to default)
        const data = { countries: ["US", "GB", "CA"] };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            entitySettings
        );

        expect(data.countries).to.be.undefined;
    }

    @test("should allow countries based on entity or jurisdiction allowed countries")
    public countryRestrictionsAllowCountryInEntityCountries() {
        // allow country if in entity.countries or (if entitySettings.useCountriesFromJurisdiction=true - jurisdiction.allowedCountries)
        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.not.throw;

        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };
        expect(validateCountryRestrictions(
            "AU",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test("should reject countries not in entity or jurisdiction allowed countries")
    public countryRestrictionsDisallowCountryNotInEntityCountries() {
        expect(() => validateCountryRestrictions(
            "FR",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [FR] should be in parent entity countries");

        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };
        expect(() => validateCountryRestrictions(
            "FR",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [FR] should be in jurisdiction allowed countries");
    }

    @test("should reject countries in entity or jurisdiction restricted countries")
    public countryRestrictionsDisallowRestrictedCountries() {
        // allow country if not in entitySettings.restrictedCountries or (if entitySettings.useCountriesFromJurisdiction=true - jurisdiction.restrictedCountries)
        const entitySettings = { ...mockEntitySettings, restrictedCountries: ["RU"] };
        expect(() => validateCountryRestrictions(
            "RU",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [RU] should be in parent entity countries");

        const jurisdictionEntitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };
        expect(() => validateCountryRestrictions(
            "RU",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            jurisdictionEntitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [RU] should be in jurisdiction allowed countries");
    }

    // Test for child entity removal validation
    @test("should prevent removing countries that are used by child entities")
    public updateCountriesPreventRemovalWhenUsedByChildren() {
        // Mock the child entity to have the countryExists method
        const mockChildEntity = {
            ...ValidateEntityCountriesSpec.childEntity,
            child: [{
                title: "Child Entity",
                path: "/child",
                name: "child-entity",
                countryExists: (code: string) => code === "GB"
            }],
            getCountries: () => ["US", "GB"]
        };

        const data = { countries: ["US"] }; // Removing GB

        expect(() => validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockChildEntity as any,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "This country code GB in use in child entities. You cannot remove it.");
    }

    @test("should allow removing countries when no child entities exist")
    public updateCountriesAllowRemovalWhenNotUsedByChildren() {
        // Mock the child entity to have no children using the country
        const mockChildEntity = {
            ...ValidateEntityCountriesSpec.childEntity,
            child: [],
            getCountries: () => ["US", "GB"]
        };

        const data = { countries: ["US"] }; // Removing GB

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockChildEntity as any,
            mockEntitySettings
        )).to.not.throw;
    }

    @test("should allow removing countries when child entities don't use them")
    public updateCountriesAllowRemovalWhenChildrenDontUseCountry() {
        // Mock the child entity to have children that don't use the removed country
        const mockChildEntity = {
            ...ValidateEntityCountriesSpec.childEntity,
            child: [{
                title: "Child Entity",
                path: "/child",
                name: "child-entity",
                countryExists: (code: string) => code === "US" // Only uses US, not GB
            }],
            getCountries: () => ["US", "GB"]
        };

        const data = { countries: ["US"] }; // Removing GB

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockChildEntity as any,
            mockEntitySettings
        )).to.not.throw;
    }
}
