#!/bin/bash

# Token Generation Script for SW Currency Exchange API
# This script generates JWT tokens using the same configuration as the application

set -e

# Default configuration values (matching src/skywind/config.ts)
DEFAULT_SECRET="TU8N9oP4pPfrUMaRYkjwBsOyw0hgg39sPsTjONrgnN1ErJbn2"
DEFAULT_ALGORITHM="HS256"
DEFAULT_ISSUER="skywindgroup"
DEFAULT_EXPIRES_IN=300

# Load environment variables or use defaults
SECRET="${INTERNAL_SERVER_TOKEN_SECRET:-$DEFAULT_SECRET}"
ALGORITHM="${INTERNAL_SERVER_TOKEN_ALGORITHM:-$DEFAULT_ALGORITHM}"
ISSUER="${INTERNAL_SERVER_TOKEN_ISSUER:-$DEFAULT_ISSUER}"
EXPIRES_IN="${INTERNAL_SERVER_TOKEN_EXPIRES_IN:-$DEFAULT_EXPIRES_IN}"

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [PAYLOAD]"
    echo ""
    echo "Generate JWT tokens for SW Currency Exchange API"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -s, --secret SECRET     JWT secret (default: from env or built-in)"
    echo "  -a, --algorithm ALG     JWT algorithm (default: $DEFAULT_ALGORITHM)"
    echo "  -i, --issuer ISSUER     JWT issuer (default: $DEFAULT_ISSUER)"
    echo "  -e, --expires SECONDS   Token expiration in seconds (default: $DEFAULT_EXPIRES_IN)"
    echo "  -p, --payload JSON      JSON payload for the token"
    echo "  -f, --file FILE         Read payload from JSON file"
    echo "  --env                   Show current environment configuration"
    echo ""
    echo "Examples:"
    echo "  $0 '{\"userId\": 123, \"role\": \"admin\"}'"
    echo "  $0 -p '{\"service\": \"currency-exchange\"}'"
    echo "  $0 -f payload.json"
    echo "  $0 -e 3600 '{\"temp\": true}'"
    echo ""
    echo "Environment Variables:"
    echo "  INTERNAL_SERVER_TOKEN_SECRET     - JWT secret key"
    echo "  INTERNAL_SERVER_TOKEN_ALGORITHM  - JWT algorithm"
    echo "  INTERNAL_SERVER_TOKEN_ISSUER     - JWT issuer"
    echo "  INTERNAL_SERVER_TOKEN_EXPIRES_IN - Token expiration in seconds"
}

# Function to show current environment configuration
show_env_config() {
    echo "Current Token Configuration:"
    echo "  Secret: ${SECRET:0:10}... (truncated)"
    echo "  Algorithm: $ALGORITHM"
    echo "  Issuer: $ISSUER"
    echo "  Expires In: $EXPIRES_IN seconds"
    echo ""
    echo "Environment Variables:"
    echo "  INTERNAL_SERVER_TOKEN_SECRET=${INTERNAL_SERVER_TOKEN_SECRET:-"(not set)"}"
    echo "  INTERNAL_SERVER_TOKEN_ALGORITHM=${INTERNAL_SERVER_TOKEN_ALGORITHM:-"(not set)"}"
    echo "  INTERNAL_SERVER_TOKEN_ISSUER=${INTERNAL_SERVER_TOKEN_ISSUER:-"(not set)"}"
    echo "  INTERNAL_SERVER_TOKEN_EXPIRES_IN=${INTERNAL_SERVER_TOKEN_EXPIRES_IN:-"(not set)"}"
}

# Function to check if Node.js is available
check_nodejs() {
    if ! command -v node &> /dev/null; then
        echo "Error: Node.js is not installed or not in PATH"
        echo "Please install Node.js to use this script"
        exit 1
    fi
}

# Function to generate token using Node.js
generate_token() {
    local payload="$1"
    
    # Validate JSON payload
    if ! echo "$payload" | node -e "JSON.parse(require('fs').readFileSync(0, 'utf8'))" 2>/dev/null; then
        echo "Error: Invalid JSON payload"
        exit 1
    fi
    
    # Generate token using Node.js
    node -e "
        const jwt = require('jsonwebtoken');
        const payload = JSON.parse('$payload');
        const token = jwt.sign(payload, '$SECRET', {
            algorithm: '$ALGORITHM',
            expiresIn: $EXPIRES_IN,
            issuer: '$ISSUER'
        });
        console.log(token);
    "
}

# Parse command line arguments
PAYLOAD=""
PAYLOAD_FILE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -s|--secret)
            SECRET="$2"
            shift 2
            ;;
        -a|--algorithm)
            ALGORITHM="$2"
            shift 2
            ;;
        -i|--issuer)
            ISSUER="$2"
            shift 2
            ;;
        -e|--expires)
            EXPIRES_IN="$2"
            shift 2
            ;;
        -p|--payload)
            PAYLOAD="$2"
            shift 2
            ;;
        -f|--file)
            PAYLOAD_FILE="$2"
            shift 2
            ;;
        --env)
            show_env_config
            exit 0
            ;;
        -*)
            echo "Error: Unknown option $1"
            show_usage
            exit 1
            ;;
        *)
            if [[ -z "$PAYLOAD" ]]; then
                PAYLOAD="$1"
            else
                echo "Error: Multiple payloads specified"
                show_usage
                exit 1
            fi
            shift
            ;;
    esac
done

# Check if Node.js is available
check_nodejs

# Determine payload source
if [[ -n "$PAYLOAD_FILE" ]]; then
    if [[ ! -f "$PAYLOAD_FILE" ]]; then
        echo "Error: File '$PAYLOAD_FILE' not found"
        exit 1
    fi
    PAYLOAD=$(cat "$PAYLOAD_FILE")
elif [[ -z "$PAYLOAD" ]]; then
    echo "Error: No payload specified"
    echo "Use -p for inline JSON or -f for file, or provide JSON as argument"
    show_usage
    exit 1
fi

# Generate and output the token
echo "Generating token..."
echo "Payload: $PAYLOAD"
echo ""
TOKEN=$(generate_token "$PAYLOAD")
echo "Generated Token:"
echo "$TOKEN"
echo ""
echo "Token Details:"
echo "  Algorithm: $ALGORITHM"
echo "  Issuer: $ISSUER"
echo "  Expires In: $EXPIRES_IN seconds"
echo "  Length: ${#TOKEN} characters"
