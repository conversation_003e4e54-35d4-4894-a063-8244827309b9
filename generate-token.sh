#!/bin/bash

# Token Generation Script for SW Currency Exchange API
# This script generates JWT tokens using standard Unix tools and openssl

set -e

# Default configuration values (matching src/skywind/config.ts)
DEFAULT_SECRET="TU8N9oP4pPfrUMaRYkjwBsOyw0hgg39sPsTjONrgnN1ErJbn2"
DEFAULT_ALGORITHM="HS256"
DEFAULT_ISSUER="skywindgroup"
DEFAULT_EXPIRES_IN=300

# Load environment variables or use defaults
SECRET="${INTERNAL_SERVER_TOKEN_SECRET:-$DEFAULT_SECRET}"
ALGORITHM="${INTERNAL_SERVER_TOKEN_ALGORITHM:-$DEFAULT_ALGORITHM}"
ISSUER="${INTERNAL_SERVER_TOKEN_ISSUER:-$DEFAULT_ISSUER}"
EXPIRES_IN="${INTERNAL_SERVER_TOKEN_EXPIRES_IN:-$DEFAULT_EXPIRES_IN}"

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [PAYLOAD]"
    echo ""
    echo "Generate JWT tokens for SW Currency Exchange API using standard Unix tools"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -s, --secret SECRET     JWT secret (default: from env or built-in)"
    echo "  -a, --algorithm ALG     JWT algorithm: HS256, HS384, HS512 (default: $DEFAULT_ALGORITHM)"
    echo "  -i, --issuer ISSUER     JWT issuer (default: $DEFAULT_ISSUER)"
    echo "  -e, --expires SECONDS   Token expiration in seconds (default: $DEFAULT_EXPIRES_IN)"
    echo "  -p, --payload JSON      JSON payload for the token"
    echo "  -f, --file FILE         Read payload from JSON file"
    echo "  --env                   Show current environment configuration"
    echo ""
    echo "Examples:"
    echo "  $0 '{\"userId\": 123, \"role\": \"admin\"}'"
    echo "  $0 -p '{\"service\": \"currency-exchange\"}'"
    echo "  $0 -f payload.json"
    echo "  $0 -e 3600 '{\"temp\": true}'"
    echo "  $0 -a HS512 '{\"service\": \"secure\"}'"
    echo ""
    echo "Requirements:"
    echo "  - openssl (for HMAC signature generation)"
    echo "  - base64 (for encoding)"
    echo "  - date (for timestamps)"
    echo ""
    echo "Environment Variables:"
    echo "  INTERNAL_SERVER_TOKEN_SECRET     - JWT secret key"
    echo "  INTERNAL_SERVER_TOKEN_ALGORITHM  - JWT algorithm (HS256, HS384, HS512)"
    echo "  INTERNAL_SERVER_TOKEN_ISSUER     - JWT issuer"
    echo "  INTERNAL_SERVER_TOKEN_EXPIRES_IN - Token expiration in seconds"
}

# Function to show current environment configuration
show_env_config() {
    echo "Current Token Configuration:"
    echo "  Secret: ${SECRET:0:10}... (truncated)"
    echo "  Algorithm: $ALGORITHM"
    echo "  Issuer: $ISSUER"
    echo "  Expires In: $EXPIRES_IN seconds"
    echo ""
    echo "Environment Variables:"
    echo "  INTERNAL_SERVER_TOKEN_SECRET=${INTERNAL_SERVER_TOKEN_SECRET:-"(not set)"}"
    echo "  INTERNAL_SERVER_TOKEN_ALGORITHM=${INTERNAL_SERVER_TOKEN_ALGORITHM:-"(not set)"}"
    echo "  INTERNAL_SERVER_TOKEN_ISSUER=${INTERNAL_SERVER_TOKEN_ISSUER:-"(not set)"}"
    echo "  INTERNAL_SERVER_TOKEN_EXPIRES_IN=${INTERNAL_SERVER_TOKEN_EXPIRES_IN:-"(not set)"}"
}

# Function to check if required tools are available
check_dependencies() {
    local missing_tools=()

    if ! command -v openssl &> /dev/null; then
        missing_tools+=("openssl")
    fi

    if ! command -v base64 &> /dev/null; then
        missing_tools+=("base64")
    fi

    if ! command -v date &> /dev/null; then
        missing_tools+=("date")
    fi

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        echo "Error: Missing required tools: ${missing_tools[*]}"
        echo "Please install the missing tools to use this script"
        exit 1
    fi
}

# Function to base64url encode (JWT standard)
base64url_encode() {
    base64 | tr '+/' '-_' | tr -d '='
}

# Function to create JWT header
create_jwt_header() {
    local algorithm="$1"
    echo -n "{\"alg\":\"$algorithm\",\"typ\":\"JWT\"}" | base64url_encode
}

# Function to create JWT payload
create_jwt_payload() {
    local payload="$1"
    local issuer="$2"
    local expires_in="$3"

    # Get current timestamp
    local iat=$(date +%s)
    local exp=$((iat + expires_in))

    # Parse the input payload and add standard JWT claims
    # Remove the closing brace, add JWT claims, then close
    local enhanced_payload=$(echo "$payload" | sed 's/}$//')

    # Check if payload is empty object
    if [[ "$enhanced_payload" == "{" ]]; then
        enhanced_payload="{\"iat\":$iat,\"exp\":$exp,\"iss\":\"$issuer\"}"
    else
        enhanced_payload="${enhanced_payload},\"iat\":$iat,\"exp\":$exp,\"iss\":\"$issuer\"}"
    fi

    echo -n "$enhanced_payload" | base64url_encode
}

# Function to create HMAC signature
create_signature() {
    local data="$1"
    local secret="$2"
    local algorithm="$3"

    case "$algorithm" in
        "HS256")
            echo -n "$data" | openssl dgst -sha256 -hmac "$secret" -binary | base64url_encode
            ;;
        "HS384")
            echo -n "$data" | openssl dgst -sha384 -hmac "$secret" -binary | base64url_encode
            ;;
        "HS512")
            echo -n "$data" | openssl dgst -sha512 -hmac "$secret" -binary | base64url_encode
            ;;
        *)
            echo "Error: Unsupported algorithm: $algorithm"
            echo "Supported algorithms: HS256, HS384, HS512"
            exit 1
            ;;
    esac
}

# Function to validate JSON (basic validation)
validate_json() {
    local json="$1"

    # Basic JSON validation - check for balanced braces and quotes
    local brace_count=$(echo "$json" | tr -cd '{}' | wc -c)
    if [[ $((brace_count % 2)) -ne 0 ]]; then
        return 1
    fi

    # Check if it starts with { and ends with }
    if [[ ! "$json" =~ ^\s*\{.*\}\s*$ ]]; then
        return 1
    fi

    return 0
}

# Function to generate JWT token
generate_token() {
    local payload="$1"

    # Validate JSON payload
    if ! validate_json "$payload"; then
        echo "Error: Invalid JSON payload"
        echo "Payload must be a valid JSON object starting with { and ending with }"
        exit 1
    fi

    # Create JWT components
    local header=$(create_jwt_header "$ALGORITHM")
    local jwt_payload=$(create_jwt_payload "$payload" "$ISSUER" "$EXPIRES_IN")
    local signing_input="${header}.${jwt_payload}"
    local signature=$(create_signature "$signing_input" "$SECRET" "$ALGORITHM")

    # Combine all parts
    echo "${header}.${jwt_payload}.${signature}"
}

# Parse command line arguments
PAYLOAD=""
PAYLOAD_FILE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -s|--secret)
            SECRET="$2"
            shift 2
            ;;
        -a|--algorithm)
            ALGORITHM="$2"
            shift 2
            ;;
        -i|--issuer)
            ISSUER="$2"
            shift 2
            ;;
        -e|--expires)
            EXPIRES_IN="$2"
            shift 2
            ;;
        -p|--payload)
            PAYLOAD="$2"
            shift 2
            ;;
        -f|--file)
            PAYLOAD_FILE="$2"
            shift 2
            ;;
        --env)
            show_env_config
            exit 0
            ;;
        -*)
            echo "Error: Unknown option $1"
            show_usage
            exit 1
            ;;
        *)
            if [[ -z "$PAYLOAD" ]]; then
                PAYLOAD="$1"
            else
                echo "Error: Multiple payloads specified"
                show_usage
                exit 1
            fi
            shift
            ;;
    esac
done

# Check if required tools are available
check_dependencies

# Determine payload source
if [[ -n "$PAYLOAD_FILE" ]]; then
    if [[ ! -f "$PAYLOAD_FILE" ]]; then
        echo "Error: File '$PAYLOAD_FILE' not found"
        exit 1
    fi
    PAYLOAD=$(cat "$PAYLOAD_FILE")
elif [[ -z "$PAYLOAD" ]]; then
    echo "Error: No payload specified"
    echo "Use -p for inline JSON or -f for file, or provide JSON as argument"
    show_usage
    exit 1
fi

# Generate and output the token
echo "Generating token..."
echo "Payload: $PAYLOAD"
echo ""
TOKEN=$(generate_token "$PAYLOAD")
echo "Generated Token:"
echo "$TOKEN"
echo ""
echo "Token Details:"
echo "  Algorithm: $ALGORITHM"
echo "  Issuer: $ISSUER"
echo "  Expires In: $EXPIRES_IN seconds"
echo "  Length: ${#TOKEN} characters"
